package com.jsunicom.gateway.filter;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;

@Component
public class XssAttackFilter implements GlobalFilter, Ordered {
    private static Logger log = LoggerFactory.getLogger(XssAttackFilter.class);

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest serverHttpRequest = exchange.getRequest();
        HttpMethod method = serverHttpRequest.getMethod();
        URI uri = exchange.getRequest().getURI();
        String rawQuery = uri.getRawQuery();
        //过滤get请求
        if (method == HttpMethod.GET && StringUtils.isNotEmpty(rawQuery)) {
            String decode = URLDecoder.decode(rawQuery, "UTF-8");
            if (StringUtils.isBlank(decode)) {
                return chain.filter(exchange);
            }

            // 执行XSS清理
            rawQuery = decode.replace("<", "")
                    .replaceAll(">", "")
                    .replaceAll("'", "")
                    .replaceAll("@", "")
                    .replaceAll("%", "")
                    .replaceAll("\"", "")
                    .replaceAll("\\$", "")
                    .replaceAll("\\(", "")
                    .replaceAll("\\)", "")
                    .replaceAll("\\|", "");

            try {
                //重新构造get request
                URI newUri = UriComponentsBuilder.fromUri(uri)
                        .replaceQuery(rawQuery)
                        .build()
                        .toUri();

                ServerHttpRequest request = exchange.getRequest().mutate()
                        .uri(newUri).build();
                return chain.filter(exchange.mutate().request(request).build());
            } catch (Exception e) {
                log.error("get请求清理xss攻击异常", e);
                throw new IllegalStateException("Invalid URI query: \"" + rawQuery + "\"");
            }
        } else {
            return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return 99998;
    }
}
