<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsunicom.api.mapper.WoSchoolGoodsDao">
    <select id="listGoodsBySchoolId" resultType="com.jsunicom.api.entity.goods.WoGoodsDto">
        SELECT wg.goods_id, wg.goods_name as name, g.pic_url2 as picUrl1, wg.goods_pic_url as goodsPicUrl,g.goods_type
        from wo_school_goods wg
                 left join goods g on g.`id` = wg.goods_id
        WHERE wg.school_id = #{schoolId}
          and wg.goods_id !='2245'
        ORDER BY online_time DESC
    </select>
    <select id="findWoSchoolGoodsInfo" resultType="com.jsunicom.api.model.WoSchoolGoods">
    SELECT *  from wo_school_goods wg
    WHERE wg.school_id =#{schoolId}
     and wg.goods_id =#{goodsId}
  </select>

    <select id="loadGoodsInfo" resultType="com.jsunicom.api.model.WoSchoolGoods">
        select *
        from wo_school_goods
        where goods_id = #{goodsId}
          and school_id = #{schoolId}
    </select>

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        insert into wo_school_goods (school_id, goods_id, delivery_type, goods_price, goods_pic_url, goods_name,
                                     select_flag, goods_postage_pic, devel_channel, effective_method, creative_item_id,
                                     max_select)
        values (#{schoolId}, #{goodsId}, #{deliveryType}, #{goodsPrice}, #{goodsPicUrl}, #{goodsName},
                #{selectFlag}, #{goodsPostagePic}, #{develChannel}, #{effectiveMethod}, #{creativeItemId},
                #{maxSelect})
    </insert>

    <delete id="delete">
    delete from wo_school_goods
    where id = #{id}
  </delete>
</mapper>
