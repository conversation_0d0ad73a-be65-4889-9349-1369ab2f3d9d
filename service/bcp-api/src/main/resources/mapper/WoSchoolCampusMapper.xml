<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.CampusDao">

    <select id="findByPK" resultType="com.jsunicom.api.entity.Campus.Campus">
        SELECT * FROM wo_school_campus
        WHERE campus_id=#{campusId}
    </select>

    <select id="getSchoolInfoList" resultType="com.jsunicom.api.entity.school.SchoolListInfo"
            parameterType="string">
        select distinct wsi.id,
                        wsi.school_name,
                        wsi.login_logo     schoolLogo,
                        wsi.wlcm_introduce schoolDesc,
                        wsi.wlcm_img       bannerUrl,
                        wscb.CAMPUS_ID,
                        wsi.prod_oper_id    prodOperId,
                        wsi.school_name     merchantName,
                        wsi.login_logo      loginLogo,
                        wsi.wlcm_introduce  wlcmIntroduce,
                        wsi.merchant_Id     merchantId,
                        p.id                partnerId,
                        p.partner_name partner_name,
                        p.mbl_nbr mbl_nbr
        from wo_school_info wsi
                 left join wo_school_campus_bind wscb on wsi.MERCHANT_ID = wscb.BIND_ID
                 left join merchant m on m.id = wsi.merchant_Id
                 left join partner p on p.merchant_Id=wsi.MERCHANT_ID and p.is_mer_admin='1'
        where wscb.TYPE = '0'
          and wscb.CAMPUS_ID = #{campusId}
    </select>

    <select id="getCampusList" resultType="com.jsunicom.api.entity.Campus.Campus"
            parameterType="com.jsunicom.api.entity.Campus.CampusBind">
        select
            wsc.campus_id,
            wsc.school_name,
            wsc.campus_name,
            wsc.address,
            wsc.tel,
            wsc.post_code,
            wsc.create_time,
            wsc.update_time,
            wsc.org_code,
            wsc.state,
            wsc.remark
        from
            wo_school_campus_bind wscb
        left join wo_school_campus wsc on wscb.CAMPUS_ID = wsc.CAMPUS_ID
        where
            wscb.bind_id = #{bindId} and wscb.state = '1'
    </select>
    <select id="getCampusListFromProvince" resultType="com.jsunicom.api.entity.Campus.Campus">
        select
            wsc.campus_id,
            wsc.school_name,
            wsc.campus_name,
            wsc.address,
            wsc.tel,
            wsc.post_code,
            wsc.create_time,
            wsc.update_time,
            wsc.org_code,
            wsc.state,
            wsc.remark
        from
            wo_school_campus_bind wscb
        left join wo_school_campus wsc on wscb.CAMPUS_ID = wsc.CAMPUS_ID
        where wscb.state = '1' and wscb.type = '1' and wsc.org_code=#{orgCode}
    </select>
    <select id="getCampusListBySz" resultType="com.jsunicom.api.entity.Campus.Campus"
            parameterType="long">
        select
            wsc.campus_id,
            wsc.school_name,
            wsc.campus_name,
            wsc.address,
            wsc.tel,
            wsc.post_code,
            wsc.create_time,
            wsc.update_time,
            wsc.org_code,
            wsc.state,
            wsc.remark
        from
            wo_sc_youth_innovate_base wscb
                left join wo_school_campus wsc on wscb.CAMPUS_ID = wsc.CAMPUS_ID
        where
            wscb.society_id = #{societyId} and wscb.state = '1'
    </select>
</mapper>
