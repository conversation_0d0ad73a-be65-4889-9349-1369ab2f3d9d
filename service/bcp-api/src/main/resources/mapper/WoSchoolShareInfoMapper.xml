<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.WoSchoolShareInfoMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.api.entity.goods.WoSchoolShareInfo">
    <id column="SHARE_ID" jdbcType="BIGINT" property="shareId" />
    <result column="CAMPUS_ID" jdbcType="BIGINT" property="campusId" />
    <result column="SCHOOL_ID" jdbcType="BIGINT" property="schoolId" />
    <result column="PARTNER_ID" jdbcType="BIGINT" property="partnerId" />
    <result column="GOODS_ID" jdbcType="VARCHAR" property="goodsId" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="NUM_SELECT_MODEL" jdbcType="INTEGER" property="numSelectModel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    SHARE_ID, CAMPUS_ID, SCHOOL_ID, PARTNER_ID, GOODS_ID, REMARK, RESERVE1, RESERVE2, 
    RESERVE3, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, NUM_SELECT_MODEL
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from WO_SCHOOL_SHARE_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from WO_SCHOOL_SHARE_INFO
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from WO_SCHOOL_SHARE_INFO
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfoExample">
    delete from WO_SCHOOL_SHARE_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfo">
    insert into WO_SCHOOL_SHARE_INFO (SHARE_ID, CAMPUS_ID, SCHOOL_ID, 
      PARTNER_ID, GOODS_ID, REMARK, 
      RESERVE1, RESERVE2, RESERVE3, 
      CREATE_BY, CREATE_TIME, UPDATE_BY, 
      UPDATE_TIME, NUM_SELECT_MODEL)
    values (#{shareId,jdbcType=BIGINT}, #{campusId,jdbcType=BIGINT}, #{schoolId,jdbcType=BIGINT}, 
      #{partnerId,jdbcType=BIGINT}, #{goodsId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{reserve1,jdbcType=VARCHAR}, #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{numSelectModel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="shareId" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfo">
    insert into WO_SCHOOL_SHARE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shareId != null">
        SHARE_ID,
      </if>
      <if test="campusId != null">
        CAMPUS_ID,
      </if>
      <if test="schoolId != null">
        SCHOOL_ID,
      </if>
      <if test="partnerId != null">
        PARTNER_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shareId != null">
        #{shareId,jdbcType=BIGINT},
      </if>
      <if test="campusId != null">
        #{campusId,jdbcType=BIGINT},
      </if>
      <if test="schoolId != null">
        #{schoolId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfoExample" resultType="java.lang.Long">
    select count(*) from WO_SCHOOL_SHARE_INFO
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update WO_SCHOOL_SHARE_INFO
    <set>
      <if test="record.shareId != null">
        SHARE_ID = #{record.shareId,jdbcType=BIGINT},
      </if>
      <if test="record.campusId != null">
        CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      </if>
      <if test="record.schoolId != null">
        SCHOOL_ID = #{record.schoolId,jdbcType=BIGINT},
      </if>
      <if test="record.partnerId != null">
        PARTNER_ID = #{record.partnerId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve1 != null">
        RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve2 != null">
        RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve3 != null">
        RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.numSelectModel != null">
        NUM_SELECT_MODEL = #{record.numSelectModel,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update WO_SCHOOL_SHARE_INFO
    set SHARE_ID = #{record.shareId,jdbcType=BIGINT},
      CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      SCHOOL_ID = #{record.schoolId,jdbcType=BIGINT},
      PARTNER_ID = #{record.partnerId,jdbcType=BIGINT},
      GOODS_ID = #{record.goodsId,jdbcType=VARCHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
      CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      NUM_SELECT_MODEL = #{record.numSelectModel,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfo">
    update WO_SCHOOL_SHARE_INFO
    <set>
      <if test="campusId != null">
        CAMPUS_ID = #{campusId,jdbcType=BIGINT},
      </if>
      <if test="schoolId != null">
        SCHOOL_ID = #{schoolId,jdbcType=BIGINT},
      </if>
      <if test="partnerId != null">
        PARTNER_ID = #{partnerId,jdbcType=BIGINT},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="numSelectModel != null">
        NUM_SELECT_MODEL = #{numSelectModel,jdbcType=INTEGER},
      </if>
    </set>
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.api.entity.goods.WoSchoolShareInfo">
    update WO_SCHOOL_SHARE_INFO
    set CAMPUS_ID = #{campusId,jdbcType=BIGINT},
      SCHOOL_ID = #{schoolId,jdbcType=BIGINT},
      PARTNER_ID = #{partnerId,jdbcType=BIGINT},
      GOODS_ID = #{goodsId,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      NUM_SELECT_MODEL = #{numSelectModel,jdbcType=INTEGER}
    where SHARE_ID = #{shareId,jdbcType=BIGINT}
  </update>
</mapper>