<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.PartnerAttachDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner_attach( id,partner_id,cert_type,image_num,image_url,create_by,create_time,
                         update_by,update_time )
        VALUES ( #{id},#{partnerId},#{certType},#{imageNum},#{imageUrl},#{createBy},now(),
                 #{updateBy},now())
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO partner_attach( id,partner_id,cert_type,image_num,image_url,create_by,create_time,
                          update_by,update_time )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.partnerId},#{item.certType},#{item.imageNum},#{item.imageUrl},#{item.createBy},now(),
              #{item.updateBy},now() )
        </foreach>
    </insert>
    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE partner_attach
        <set>
            partner_id=#{partnerId},
            cert_type=#{certType},
            image_num=#{imageNum},
            image_url=#{imageUrl},
            create_by=#{createBy},
            create_time=#{createTime},
            update_time=now(),
            update_by=#{updateBy},
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateIgnoreNull">
        UPDATE partner_attach
        <set>
            update_time=now(),
            <if test="partnerId!= null">partner_id=#{partnerId},</if>
            <if test="certType!= null">cert_type=#{certType},</if>
            <if test="imageNum!= null">image_num=#{imageNum},</if>
            <if test="imageUrl!= null">image_url=#{imageUrl},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
        </set>
        WHERE partner_id=#{partnerId} AND cert_type=#{certType}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE partner_attach
            <set>
                partner_id=#{item.partnerId},
                cert_type=#{item.certType},
                image_num=#{item.imageNum},
                image_url=#{item.imageUrl},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_time=now(),
                update_by=#{item.updateBy},
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner_attach
        WHERE id=#{id}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM partner_attach
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id}
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM partner_attach
        WHERE id=#{id}
    </delete>

    <delete id="deleteByPI">
        DELETE FROM partner_attach
        WHERE partner_id=#{partnerId}
    </delete>

    <delete id="deleteAll">
        DELETE FROM partner_attach
    </delete>

    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner_attach
    </select>

    <select id="findByPK" resultType="com.jsunicom.api.model.PartnerAttach">
        SELECT * FROM partner_attach
        WHERE id=#{id}
    </select>

    <select id="find" resultType="com.jsunicom.api.model.PartnerAttach">
        SELECT id,partner_id,cert_type,image_num,image_url,create_by,create_time,update_by
               ,update_time
         FROM partner_attach
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="partnerId!= null">
               AND partner_id = #{partnerId}
            </if>
            <if test="certType!= null">
               AND cert_type = #{certType}
            </if>
            <if test="imageNum!= null">
               AND image_num = #{imageNum}
            </if>
            <if test="imageUrl!= null">
               AND image_url = #{imageUrl}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
        </where>
    </select>
    <select id="findPhoto" resultType="java.lang.String">
        SELECT image_url FROM partner_attach
        WHERE partner_id=#{partnerId} AND cert_type=#{certType}
    </select>
</mapper>
