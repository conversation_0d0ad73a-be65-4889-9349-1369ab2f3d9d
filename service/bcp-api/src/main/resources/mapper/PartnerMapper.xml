<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.PartnerDao">
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner( id,partner_type,partner_name,acct_no,partner_cert_no,pic1,pic2,pic3,
                             mbl_nbr,profession_type,org_code,prov_code,prov_name,city_code,city_name,
                             area_code,area_name,address,devel_id,emploee_type,is_mer_admin,commis_acct_id,
                             college_id,dormitory_id,reference_id,society_id,merchant_id,sale_mgr_name,
                             sale_mgr_phone,bus_line,bus_grid,invite_code,devel_channel,gzt_rs,state,
                             audit_by,audit_time,audit_refuse_reson,audit_remark,remark,create_by,create_time,
                             update_by,update_time,reserve1,reserve2,reserve3,reserve4,reserve5)
        VALUES ( #{id},#{partnerType},#{partnerName},#{acctNo},#{partnerCertNo},#{pic1},#{pic2},
                 #{pic3},#{mblNbr},#{professionType},#{orgCode},#{provCode},#{provName},#{cityCode},
                 #{cityName},#{areaCode},#{areaName},#{address},#{develId},#{emploeeType},#{isMerAdmin},
                 #{commisAcctId},#{collegeId},#{dormitoryId},#{referenceId},#{societyId},#{merchantId},#{saleMgrName},
                 #{saleMgrPhone},#{busLine},#{busGrid},#{inviteCode},#{develChannel},#{gztRs},#{state},
                 #{auditBy},#{auditTime},#{auditRemark},#{auditRefuseReson},#{remark},#{createBy},#{createTime},
                 #{updateBy},#{updateTime},#{reserve1},#{reserve2},#{reserve3},#{reserve4},#{reserve5})
    </insert>
    <update id="update">
        UPDATE partner
        <set>
            partner_type=#{partnerType},
            partner_name=#{partnerName},
            acct_no=#{acctNo},
            partner_cert_no=#{partnerCertNo},
            pic1=#{pic1},
            pic2=#{pic2},
            pic3=#{pic3},
            mbl_nbr=#{mblNbr},
            profession_type=#{professionType},
            org_code=#{orgCode},
            prov_code=#{provCode},
            prov_name=#{provName},
            city_code=#{cityCode},
            city_name=#{cityName},
            area_code=#{areaCode},
            area_name=#{areaName},
            address=#{address},
            devel_id=#{develId},
            emploee_type=#{emploeeType},
            is_mer_admin=#{isMerAdmin},
            commis_acct_id=#{commisAcctId},
            college_id=#{collegeId},
            dormitory_id=#{dormitoryId},
            reference_id=#{referenceId},
            society_id=#{societyId},
            merchant_id=#{merchantId},
            sale_mgr_name=#{saleMgrName},
            sale_mgr_phone=#{saleMgrPhone},
            bus_line=#{busLine},
            bus_grid=#{busGrid},
            invite_code=#{inviteCode},
            devel_channel=#{develChannel},
            gzt_rs=#{gztRs},
            state=#{state},
            audit_by=#{auditBy},
            audit_time=#{auditTime},
            audit_refuse_reson=#{auditRemark},
            audit_remark=#{auditRefuseReson},
            remark=#{remark},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
            reserve1=#{reserve1},
            reserve2=#{reserve2},
            reserve3=#{reserve3},
            reserve4=#{reserve4},
            reserve5=#{reserve5}
        </set>
        WHERE id=#{id}
    </update>
    <update id="updateIgnoreNull">
        UPDATE partner
        <set>
            <if test="partnerType!= null">partner_type=#{partnerType},</if>
            <if test="partnerName!= null">partner_name=#{partnerName},</if>
            <if test="acctNo!= null">acct_no=#{acctNo},</if>
            <if test="partnerCertNo!= null">partner_cert_no=#{partnerCertNo},</if>
            <if test="pic1!= null">pic1=#{pic1},</if>
            <if test="pic2!= null">pic2=#{pic2},</if>
            <if test="pic3!= null">pic3=#{pic3},</if>
            <if test="mblNbr!= null">mbl_nbr=#{mblNbr},</if>
            <if test="professionType!= null">profession_type=#{professionType},</if>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="provCode!= null">prov_code=#{provCode},</if>
            <if test="provName!= null">prov_name=#{provName},</if>
            <if test="cityCode!= null">city_code=#{cityCode},</if>
            <if test="cityName!= null">city_name=#{cityName},</if>
            <if test="areaCode!= null">area_code=#{areaCode},</if>
            <if test="areaName!= null">area_name=#{areaName},</if>
            <if test="address!= null">address=#{address},</if>
            <if test="develId!= null">devel_id=#{develId},</if>
            <if test="emploeeType!= null">emploee_type=#{emploeeType},</if>
            <if test="isMerAdmin!= null">is_mer_admin=#{isMerAdmin},</if>
            <if test="commisAcctId!= null">commis_acct_id=#{commisAcctId},</if>
            <if test="collegeId!= null">college_id=#{collegeId},</if>
            <if test="dormitoryId!= null">dormitory_id=#{dormitoryId},</if>
            <if test="referenceId!= null">reference_id=#{referenceId},</if>
            <if test="societyId!= null">society_id=#{societyId},</if>
            <if test="merchantId!= null">merchant_id=#{merchantId},</if>
            <if test="saleMgrName!= null">sale_mgr_name=#{saleMgrName},</if>
            <if test="saleMgrPhone!= null">sale_mgr_phone=#{saleMgrPhone},</if>
            <if test="busLine!= null">bus_line=#{busLine},</if>
            <if test="busGrid!= null">bus_grid=#{busGrid},</if>
            <if test="inviteCode!= null">invite_code=#{inviteCode},</if>
            <if test="develChannel!= null">devel_channel=#{develChannel},</if>
            <if test="gztRs!= null">gzt_rs=#{gztRs},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="auditBy!= null">audit_by=#{auditBy},</if>
            <if test="auditTime!= null">audit_time=#{auditTime},</if>
            <if test="auditRemark!= null">audit_refuse_reson=#{auditRemark},</if>
            <if test="auditRefuseReson!= null">audit_remark=#{auditRefuseReson},</if>
            <if test="remark!= null">remark=#{remark},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
            <if test="reserve1!= null">reserve1=#{reserve1},</if>
            <if test="reserve2!= null">reserve2=#{reserve2},</if>
            <if test="reserve3!= null">reserve3=#{reserve3},</if>
            <if test="reserve4!= null">reserve4=#{reserve4},</if>
            <if test="reserve5!= null">reserve5=#{reserve5}</if>

        </set>
        WHERE id=#{id}
    </update>

    <select id="findByPK" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE id=#{id}
    </select>

    <select id="findById" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE id=#{id}
    </select>

    <select id="findByDevelId" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE devel_id=#{develId}
    </select>
    <select id="findPartnerByCertNo" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE partner_cert_no=#{partnerCertNo} limit 1
    </select>
    <select id="findByInviteCode" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE binary invite_code=#{inviteCode}
    </select>
    <select id="findPartnerByMblNbr" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE mbl_nbr=#{mblNbr}
    </select>
    <select id="findPartnerByUserAcct" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT p.* FROM partner p
        WHERE p.acct_no=#{acctNo}
    </select>
    <update id="updateEmploeeType">
        update partner
        set emploee_type = #{type},update_time=now()
        where partner_cert_no = #{certNo}
    </update>
    <select id="checkCertNo" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
        <where>
            <if test="id!= null">
                AND id != #{id}
            </if>
            <if test="partnerCertNo!= null">
                AND partner_cert_no = #{partnerCertNo}
            </if>
        </where>
    </select>
    <select id="findByCertNo" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT id,partner_type,partner_name,scence_id,wx_acct,acct_no,partner_cert_no,mbl_nbr
                 ,intent_bus_code,intent_bus_name,refer_name,refer_phone,profession_type,promot_area,org_code
                 ,industry,prov_code,prov_name,city_code,city_name,area_code,area_name
                 ,address,devel_id,commis_acct_id,create_by,create_time,update_by,update_time
                 ,audit_by,audit_time,state,audit_remark,wo_acct,wo_state,wo_remark
                 ,merchant_id,wx_entp_uid,pricipal_name,pricipal_cert_no,sale_mgr_name,sale_mgr_phone,bus_line
                 ,bus_grid,emploee_type,is_mer_admin,invite_code,gzt_rs,audit_refuse_reson,oper_id,rights_coupon,register_path
          FROM partner
         WHERE partner_cert_no = #{partnerCertNo}
    </select>
    <select id="findPartnerByMerchantIdAll" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
        and state='1' order by create_time desc
    </select>
    <select id="checkPartner" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT * FROM partner
        WHERE partner_cert_no=#{partnerCertNo} OR mbl_nbr=#{mblNbr}
    </select>
    <select id="findPartnerByMerchantId" resultType="com.jsunicom.api.entity.partner.Partner">
        SELECT
            id,partner_type,partner_name,scence_id,wx_acct,acct_no,partner_cert_no,mbl_nbr
              ,intent_bus_code,intent_bus_name,refer_name,refer_phone,profession_type,promot_area,org_code
              ,industry,prov_code,prov_name,city_code,city_name,area_code,area_name
              ,address,devel_id,commis_acct_id,create_by,create_time,update_by,update_time
              ,audit_by,audit_time,state,audit_remark,wo_acct,wo_state,wo_remark
              ,merchant_id,wx_entp_uid,pricipal_name,pricipal_cert_no,sale_mgr_name,sale_mgr_phone,bus_line
              ,bus_grid,emploee_type,is_mer_admin,invite_code,gzt_rs,audit_refuse_reson,oper_id,rights_coupon,register_path
        FROM partner
        where merchant_id = #{merchantId}
        and is_mer_admin='0'
        and state='1' order by create_time desc
    </select>

    <delete id="deleteByPK">
        DELETE FROM partner
        WHERE id=#{id}
    </delete>
</mapper>
