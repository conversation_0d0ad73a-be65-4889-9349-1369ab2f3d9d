<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.MajorManageMapper">
<select id="selectMajorList" resultType="java.util.Map">
    select college.COLLEGE_ID collegeId,college.COLLEGE_NAME collegeName,college.CAMPUS_ID campusId,college.CAMPUS_NAME campusName,
    college.PRE_COOPERATE_FLAG preCooperateFlag, college.PRE_COOPERATE_PHOTO_URL preCooperatePhotoUrl,
    college.SCENE_COOPERATE_FLAG sceneCooperateFlag,college.SCENE_COOPERATE_PHOTO_URL sceneCooperatePhotoUrl,
    college.ORG_CODE orgCode,college.STATE state,college.CREATED_BY createdBy,
    DATE_FORMAT(college.CREATED_TIME,'%Y-%m-%d %H:%i:%s') createdTime,
    college.UPDATED_BY updatedBy,
    DATE_FORMAT(college.UPDATED_TIME,'%Y-%m-%d %H:%i:%s')  updatedTime,college.REMARK remark,
    p.mbl_nbr mbrNbr,org.org_name orgName,p.partner_name partnerName
     from wo_school_campus_college college
     left join wo_school_campus_bind bind on college.CAMPUS_ID=bind.CAMPUS_ID and bind.STATE='1' and bind.TYPE='1'
     left join partner p on bind.BIND_ID=p.id
     left join org_info org on college.ORG_CODE=org.org_code
    where college.state = '1'
    <if test="campusId !=null and campusId !='' ">
     and college.CAMPUS_ID=#{campusId}
    </if>
    <if test="orgCode !=null and orgCode !='' ">
        and college.ORG_CODE=#{orgCode}
    </if>
    <if test="collegeName !=null and collegeName !='' ">
        and college.COLLEGE_NAME like CONCAT ('%',#{collegeName},'%')
    </if>
    <if test="phone !=null and phone !='' ">
        and college.CAMPUS_ID in (select CAMPUS_ID from WO_SCHOOL_CAMPUS_BIND where TYPE='1' and BIND_ID=(select id from partner where mbl_nbr=#{phone}))
    </if>
    order by college.CREATED_TIME desc
</select>
    <select id="selectDormitoryList" resultType="java.util.Map">
        select DORMITORY_ID dormitoryId,dormitory.DORMITORY_NAME dormitoryName,dormitory.CAMPUS_ID campusId,
        dormitory.CAMPUS_NAME campusName,dormitory.ORG_CODE orgCode,dormitory.STATE state,
        dormitory.CREATED_BY createdBy,DATE_FORMAT(dormitory.CREATED_TIME,'%Y-%m-%d %H:%i:%s') createdTime,
        dormitory.UPDATED_BY updatedBy,DATE_FORMAT(dormitory.UPDATED_TIME,'%Y-%m-%d %H:%i:%s') updatedTime,dormitory.REMARK remark,
        p.mbl_nbr mbrNbr,org.org_name orgName,p.partner_name partnerName,
        case
        when tab.val > 0 then '是'
        else '否'
        end AS isPioneerCover
         from wo_school_campus_dormitory dormitory
        left join wo_school_campus_bind bind on dormitory.CAMPUS_ID=bind.CAMPUS_ID and bind.STATE='1' and bind.TYPE='1'
        left join partner p on bind.BIND_ID=p.id
        left join org_info org on dormitory.ORG_CODE=org.org_code
        left join (
        select
        member.DORMITORY_ID dormitoryId,
        sum(member.IS_PIONEER) val
        from
        WO_SC_YI_MEMBER member
        where
        member.STATE = '1'
        group by
        member.DORMITORY_ID) tab on
        dormitory.DORMITORY_ID = tab.dormitoryId
        where dormitory.state = '1'
        <if test="campusId !=null and campusId !='' ">
            and dormitory.CAMPUS_ID=#{campusId}
        </if>
        <if test="orgCode !=null and orgCode !='' ">
            and dormitory.ORG_CODE=#{orgCode}
        </if>
        <if test="dormitoryName !=null and dormitoryName !='' ">
            and dormitory.DORMITORY_NAME like CONCAT ('%',#{dormitoryName},'%')
        </if>
        <if test="phone !=null and phone !='' ">
            and dormitory.CAMPUS_ID in (select CAMPUS_ID from WO_SCHOOL_CAMPUS_BIND where TYPE='1' and BIND_ID=(select id from partner where mbl_nbr=#{phone}))
        </if>
        order by dormitory.CREATED_TIME desc
    </select>
    <select id="selectCampusList" resultType="java.util.Map">
        select CAMPUS_ID  "campusId",
               concat(SCHOOL_NAME,'(',CAMPUS_NAME,')') as "campusName",
               ORG_CODE "orgCode"
        from WO_SCHOOL_CAMPUS
        where 1=1
        <if test="orgCode !=null and orgCode !='' ">
            and ORG_CODE=#{orgCode}
        </if>
        <if test="phone !=null and phone !='' ">
            and CAMPUS_ID in (select CAMPUS_ID from WO_SCHOOL_CAMPUS_BIND where TYPE='1' and BIND_ID=(select id from partner where mbl_nbr=#{phone}))
        </if>
    </select>
    <update id="updateMajor">
        update wo_school_campus_college set
        <if test="collegeName !=null and collegeName != ''">
            COLLEGE_NAME=#{collegeName},
        </if>
        <if test="preFlag !=null and preFlag != ''">
            PRE_COOPERATE_FLAG=#{preFlag},
        </if>
        <if test="preFlagPhoto !=null and preFlagPhoto != ''">
            PRE_COOPERATE_PHOTO_URL=#{preFlagPhoto},
        </if>
        <if test="sceneFlag !=null and sceneFlag != ''">
            SCENE_COOPERATE_FLAG=#{sceneFlag},
        </if>
        <if test="sceneFlagPhoto !=null and sceneFlagPhoto != ''">
            SCENE_COOPERATE_PHOTO_URL=#{sceneFlagPhoto},
        </if>
        UPDATED_BY=#{updatedBy},
        UPDATED_TIME=#{updatedTime}
    where COLLEGE_ID =#{id}
    </update>
    <update id="updateDormitory">
        update wo_school_campus_dormitory
        set
            <if test="dormitoryName !=null and dormitoryName != ''">
                DORMITORY_NAME=#{dormitoryName},
            </if>
        UPDATED_BY=#{updatedBy},
        UPDATED_TIME=#{updatedTime}
        where DORMITORY_ID=#{dormitoryId}
    </update>
    <insert id="insertMajor">
        insert into wo_school_campus_college(COLLEGE_NAME,CAMPUS_ID,CAMPUS_NAME,ORG_CODE,CREATED_BY,CREATED_TIME)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item},#{map.campusId},#{map.campusName},#{map.orgCode},#{map.createBy},#{map.createTime})
    </foreach>
    </insert>
    <select id="selectCount" resultType="java.lang.String">
        select COLLEGE_NAME collegeName from wo_school_campus_college
        where CAMPUS_ID=#{campusId} and state = '1'
        and COLLEGE_NAME in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <insert id="insertDormitor">
        insert into wo_school_campus_dormitory(DORMITORY_NAME,CAMPUS_ID,CAMPUS_NAME,ORG_CODE,CREATED_BY,CREATED_TIME)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item},#{map.campusId},#{map.campusName},#{map.orgCode},#{map.createBy},#{map.createTime})
        </foreach>
    </insert>
    <select id="selectCountDor" resultType="java.lang.String">
        select DORMITORY_NAME from wo_school_campus_dormitory
        where CAMPUS_ID=#{campusId} and state = '1'
        and DORMITORY_NAME in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="selectMajorInfoById" resultType="java.util.Map">
        select COLLEGE_ID collegeId,COLLEGE_NAME collegeName,CAMPUS_ID campusId,CAMPUS_NAME campusName,
    PRE_COOPERATE_FLAG preCooperateFlag, PRE_COOPERATE_PHOTO_URL preCooperatePhotoUrl,
    SCENE_COOPERATE_FLAG sceneCooperateFlag,SCENE_COOPERATE_PHOTO_URL sceneCooperatePhotoUrl
     from wo_school_campus_college where COLLEGE_ID=#{collegeId}
    </select>
</mapper>
