<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.DictDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO dict( id,kind,kind_desc,code,name,status,order_by,
        create_by,create_time,update_by,update_time )
        VALUES ( #{id},#{kind},#{kindDesc},#{code},#{name},#{status},#{orderBy},
        #{createBy},#{createTime},#{updateBy},#{updateTime})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO dict( id,kind,kind_desc,code,name,status,order_by,
                          create_by,create_time,update_by,update_time )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.kind},#{item.kindDesc},#{item.code},#{item.name},#{item.status},#{item.orderBy},
              #{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime} )
        </foreach>
    </insert>

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE dict
        <set>
            kind=#{kind},
            kind_desc=#{kindDesc},
            code=#{code},
            name=#{name},
            status=#{status},
            order_by=#{orderBy},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateIgnoreNull">
        UPDATE dict
        <set>
            <if test="kind!= null">kind=#{kind},</if>
            <if test="kindDesc!= null">kind_desc=#{kindDesc},</if>
            <if test="code!= null">code=#{code},</if>
            <if test="name!= null">name=#{name},</if>
            <if test="status!= null">status=#{status},</if>
            <if test="orderBy!= null">order_by=#{orderBy},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE dict
            <set>
                kind=#{item.kind},
                kind_desc=#{item.kindDesc},
                code=#{item.code},
                name=#{item.name},
                status=#{item.status},
                order_by=#{item.orderBy},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM dict
        WHERE id=#{id}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM dict
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id}
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM dict
        WHERE id=#{id}
    </delete>

    <delete id="deleteAll">
        DELETE FROM dict
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM dict
    </select>

    <select id="findByPK" resultType="com.jsunicom.api.po.Dict">
        SELECT * FROM dict
        WHERE id=#{id}
    </select>

    <select id="findByUK" resultType="com.jsunicom.api.po.Dict">
        SELECT * FROM dict
        WHERE kind=#{kind} AND code=#{code}
    </select>
    <select id="selectBykind" resultType="com.jsunicom.api.po.Dict">
        SELECT * FROM dict
        WHERE kind=#{kind} and status = '1'
    </select>
    <select id="selectByMenu" resultType="java.lang.Integer">
        select count(1) from dict where kind='menuList' and menu=#{menu}
    </select>

    <select id="find" resultType="com.jsunicom.api.po.Dict">
        SELECT id,kind,kind_desc,code,name,status,order_by,create_by
               ,create_time,update_by,update_time
         FROM dict
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="kind!= null">
               AND kind like #{kind}
            </if>
            <if test="kindDesc!= null">
               AND kind_desc like #{kindDesc}
            </if>
            <if test="code!= null">
               AND code like #{code}
            </if>
            <if test="name!= null">
               AND name like #{name}
            </if>
            <if test="status!= null">
               AND status = #{status}
            </if>
            <if test="orderBy!= null">
               AND order_by = #{orderBy}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
        </where>
    </select>
</mapper>
