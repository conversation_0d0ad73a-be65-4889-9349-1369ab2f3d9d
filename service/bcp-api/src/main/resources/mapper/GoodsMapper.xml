<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.GoodsDao">
    <sql id="allColumns">
        id id,
        code code,
        name name,
        product_codes productCodes,
        product_amount productAmount,
        bus_line busLine,
        bus_url busUrl,
        pic_url1 picUrl1,
        pic_url2 picUrl2,
        state state,
        create_by createBy,
        create_time createTime,
        update_by updateBy,
        goods_type goodsType,
        second_type secondType,
        update_time updateTime,
        is_hot isHot,
        goods_area,
        end_time endTime,
        pre_state preState,
        effective_method   effectiveMethod,
        package_content packageContent,
        notice_handling noticeHandling,
        reminder reminder
    </sql>

    <select id="get" parameterType="java.lang.Long" resultType="com.jsunicom.api.entity.goods.Goods">
        SELECT <include refid="allColumns"/>
        FROM goods
        WHERE id = #{id} and state = 'Y'
    </select>

    <select id="findWoSchoolGoodsList" resultType="com.jsunicom.api.entity.goods.WoGoodsDto">
        SELECT g.`id` as goods_id,g.`name`,g.pic_url1,g.pic_url2 as picUrl,g.code,wg.id  from goods g
        LEFT  JOIN
        (SELECT w.goods_id,w.id FROM wo_school_goods w  WHERE w.school_id=#{schoolId})  wg  ON g.id=wg.goods_id
        WHERE g.state = 'Y'
        AND g.bus_line= #{busLine}
        <if test="goodsArea != null and goodsArea != ''">
            AND (g.goods_area = #{goodsArea} or g.goods_area = 'root')
        </if>
        <if test="code != null and code != ''">
            AND (g.code like #{code} or  g.name like #{code})
        </if>
        ORDER BY wg.id DESC,g.update_time DESC
    </select>
</mapper>
