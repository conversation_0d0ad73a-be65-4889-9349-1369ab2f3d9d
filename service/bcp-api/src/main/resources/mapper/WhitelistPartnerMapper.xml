<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.WhitelistPartnerDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO whitelist_partner( id,staff_name,staff_msisdn,org_code,devel_id,cert_no,state,
                         type,create_by,create_time,update_by,update_time )
        VALUES ( #{id},#{staffName},#{staffMsisdn},#{orgCode},#{develId},#{certNo},#{state},
                 #{type},#{createBy},#{createTime},#{updateBy},#{updateTime})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO whitelist_partner( id,staff_name,staff_msisdn,org_code,devel_id,cert_no,state,
                          type,create_by,create_time,update_by,update_time )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.staffName},#{item.staffMsisdn},#{item.orgCode},#{item.develId},#{item.certNo},#{item.state},
              #{item.type},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime} )
        </foreach>
    </insert>

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE whitelist_partner
        <set>
            staff_name=#{staffName},
            staff_msisdn=#{staffMsisdn},
            org_code=#{orgCode},
            devel_id=#{develId},
            cert_no=#{certNo},
            state=#{state},
            type=#{type},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateIgnoreNull">
        UPDATE whitelist_partner
        <set>
            <if test="staffName!= null">staff_name=#{staffName},</if>
            <if test="staffMsisdn!= null">staff_msisdn=#{staffMsisdn},</if>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="develId!= null">devel_id=#{develId},</if>
            <if test="certNo!= null">cert_no=#{certNo},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="type!= null">type=#{type},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE whitelist_partner
            <set>
                staff_name=#{item.staffName},
                staff_msisdn=#{item.staffMsisdn},
                org_code=#{item.orgCode},
                devel_id=#{item.develId},
                cert_no=#{item.certNo},
                state=#{item.state},
                type=#{item.type},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM whitelist_partner
        WHERE id=#{id}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM whitelist_partner
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id}
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM whitelist_partner
        WHERE id=#{id}
    </delete>

    <delete id="deleteWhiteListByCerNo">
        DELETE FROM whitelist_partner
        WHERE cert_no =#{certNo}
    </delete>

    <delete id="deleteAll">
        DELETE FROM whitelist_partner
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM whitelist_partner
    </select>

    <select id="findByPK" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT * FROM whitelist_partner
        WHERE id=#{id}
    </select>

    <select id="find" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT id,staff_name,staff_msisdn,org_code,devel_id,cert_no,state,type
               ,create_by,create_time,update_by,update_time
         FROM whitelist_partner
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="staffName!= null">
               AND staff_name like #{staffName}
            </if>
            <if test="staffMsisdn!= null">
               AND staff_msisdn like #{staffMsisdn}
            </if>
            <if test="orgCode!= null">
               AND org_code = #{orgCode}
            </if>
            <if test="develId!= null">
               AND devel_id like #{develId}
            </if>
            <if test="certNo!= null">
               AND cert_no like #{certNo}
            </if>
            <if test="state!= null">
               AND state = #{state}
            </if>
            <if test="type!= null">
               AND type = #{type}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="isWorking" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT * FROM whitelist_partner
        WHERE cert_no=#{certNo}
        and state = '1'
    </select>

    <select id="checkWhitelistPartner" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT * FROM whitelist_partner
        WHERE cert_no = #{certNo}
    </select>
    <select id="findWhitelistPartnerByCertNo" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT * FROM whitelist_partner
        WHERE cert_no = #{certNo}
    </select>
    <select id="queryStaff" resultType="com.jsunicom.api.model.WhitelistPartner">
        SELECT * FROM whitelist_partner
        WHERE staff_msisdn=#{mblNbr}
    </select>
</mapper>
