<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.ActivitiesBaseInfoMapper">
    <insert id="insertActivity" parameterType="com.jsunicom.api.entity.Campus.ActivitiesBaseInfo" useGeneratedKeys="true" keyProperty="activitiesId">
        INSERT INTO activities_base_info (
            activities_notify_id,
            activity_type,
            activity_name,
            activity_attributes,
            activities_status,
            campus_id,
            campus_name,
            create_user,
            create_time,
            activity_start_time,
            activity_end_time
        ) VALUES (
                     #{activitiesBaseInfo.activitiesNotifyId},
                     #{activitiesBaseInfo.activityType},
                     #{activitiesBaseInfo.activityName},
                     #{activitiesBaseInfo.activityAttributes},
                     #{activitiesBaseInfo.activitiesStatus},
                     #{activitiesBaseInfo.campusId},
                     #{activitiesBaseInfo.campusName},
                     #{activitiesBaseInfo.createUser},
                     #{activitiesBaseInfo.createTime},
                     #{activitiesBaseInfo.activityStartTime},
                     #{activitiesBaseInfo.activityEndTime}
                 )
    </insert>


    <select id="getTeamInfo" resultType="com.jsunicom.api.vo.campus.TeamBaseInfo">
        select
        m.id as merchantId,
        m.merchant_name as merchantName ,
        p.partner_name as partnerName,
        p.id as partnerId,
        (select count(1) from partner where merchant_id = m.id ) as userNums
        from
        wo_school_campus sc
        -- 团队(type=0)、校区经理(type=1)
        inner join wo_school_campus_bind scb on sc.CAMPUS_ID = scb.CAMPUS_ID
        inner join merchant m on m.id=scb.BIND_ID and scb.`TYPE` =0
        -- is_mer_admin 0:团队成员 1:团队长
        inner join partner p on p.merchant_id =m.id and p.is_mer_admin in
        <foreach item="item" collection="isMerAdminList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        where sc.STATE ='1' and scb.STATE ='1' and m.state = '1' and p.state = '1'
        and sc.CAMPUS_ID = #{campusId}
        <if test="memberIds != null and memberIds.size() > 0">
            AND m.id IN
            <foreach item="item" collection="memberIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="getCenterPoint" resultType="java.lang.String">
        select ddrlsax.CENTER_POINT from activities_base_info abi
            inner join wo_school_campus wsc on abi.campus_id = wsc.CAMPUS_ID
            inner join dwd_d_res_level_seven_addr_xy ddrlsax on wsc.ADDR_ID = ddrlsax.ADDR_ID
        where abi.activities_id = #{activitiesId} and ddrlsax.MONTH_ID = #{monthId} and ddrlsax.DAY_ID = #{dayId}
    </select>
    <select id="getActivitiesDetailsByActivitiesId"
            resultType="com.jsunicom.api.vo.campus.ActivitiesDetailsVO">
        SELECT abi.activities_id,
               abi.activities_notify_id,
               abi.activity_type,
               abi.activity_name,
               abi.activity_attributes,
               abi.activities_status,
               abi.activity_start_time,
               abi.activity_end_time,
               abi.campus_id,
               abi.campus_name,
               abi.create_user,
               abi.create_time,
               anbi.activity_notify_name
        FROM activities_base_info abi inner join activities_notify_base_info anbi on abi.activities_notify_id = anbi.activities_notify_id
        WHERE abi.activities_id = #{activitiesId}

    </select>
    <select id="getUserInfoByMblNbr" resultType="com.jsunicom.api.vo.campus.ClockUserInfo">

        select
            mbl_nbr as mblNbr,
            partner_name as partnerName,
            partner_type as partnerType,
            case
                when partner_type = '0' then '校园经理'
                when partner_type = '1' then '青创社成员'
                else '未知' end as partnerTypeName
        from partner where mbl_nbr = #{mblNbr}
    </select>
</mapper>
