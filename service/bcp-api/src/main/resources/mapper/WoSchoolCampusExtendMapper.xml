<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.WoSchoolCampusExtendMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.api.po.WoSchoolCampusExtend">
    <id column="CAMPUS_ID" jdbcType="BIGINT" property="campusId" />
    <result column="IS_BROADBAND_ACCESS" jdbcType="CHAR" property="isBroadbandAccess" />
    <result column="BROADBAND_ACCESS_TYPE" jdbcType="CHAR" property="broadbandAccessType" />
    <result column="BROADBAND_COVERAGE_RATIO" jdbcType="CHAR" property="broadbandCoverageRatio" />
    <result column="JU_STUDENT" jdbcType="INTEGER" property="juStudent" />
    <result column="MD_STUDENT" jdbcType="INTEGER" property="mdStudent" />
    <result column="NEW_JU_STUDENT" jdbcType="INTEGER" property="newJuStudent" />
    <result column="NEW_MD_STUDENT" jdbcType="INTEGER" property="newMdStudent" />
    <result column="SCHOOL_CAMPUS_TYPE" jdbcType="CHAR" property="schoolCampusType" />
    <result column="IS_5G_COVERAGE" jdbcType="CHAR" property="is5gCoverage" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    CAMPUS_ID, IS_BROADBAND_ACCESS, BROADBAND_ACCESS_TYPE, BROADBAND_COVERAGE_RATIO, 
    JU_STUDENT, MD_STUDENT, NEW_JU_STUDENT, NEW_MD_STUDENT, SCHOOL_CAMPUS_TYPE, IS_5G_COVERAGE, 
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, RESERVE1, RESERVE2, RESERVE3
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.api.po.WoSchoolCampusExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_school_campus_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus_extend
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wo_school_campus_extend
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.api.po.WoSchoolCampusExtendExample">
    delete from wo_school_campus_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.api.po.WoSchoolCampusExtend">
    insert into wo_school_campus_extend (CAMPUS_ID, IS_BROADBAND_ACCESS, BROADBAND_ACCESS_TYPE, 
      BROADBAND_COVERAGE_RATIO, JU_STUDENT, MD_STUDENT, 
      NEW_JU_STUDENT, NEW_MD_STUDENT, SCHOOL_CAMPUS_TYPE, 
      IS_5G_COVERAGE, CREATE_BY, CREATE_TIME, 
      UPDATE_BY, UPDATE_TIME, RESERVE1, 
      RESERVE2, RESERVE3)
    values (#{campusId,jdbcType=BIGINT}, #{isBroadbandAccess,jdbcType=CHAR}, #{broadbandAccessType,jdbcType=CHAR}, 
      #{broadbandCoverageRatio,jdbcType=CHAR}, #{juStudent,jdbcType=INTEGER}, #{mdStudent,jdbcType=INTEGER}, 
      #{newJuStudent,jdbcType=INTEGER}, #{newMdStudent,jdbcType=INTEGER}, #{schoolCampusType,jdbcType=CHAR}, 
      #{is5gCoverage,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{reserve1,jdbcType=VARCHAR}, 
      #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.api.po.WoSchoolCampusExtend">
    insert into wo_school_campus_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        CAMPUS_ID,
      </if>
      <if test="isBroadbandAccess != null">
        IS_BROADBAND_ACCESS,
      </if>
      <if test="broadbandAccessType != null">
        BROADBAND_ACCESS_TYPE,
      </if>
      <if test="broadbandCoverageRatio != null">
        BROADBAND_COVERAGE_RATIO,
      </if>
      <if test="juStudent != null">
        JU_STUDENT,
      </if>
      <if test="mdStudent != null">
        MD_STUDENT,
      </if>
      <if test="newJuStudent != null">
        NEW_JU_STUDENT,
      </if>
      <if test="newMdStudent != null">
        NEW_MD_STUDENT,
      </if>
      <if test="schoolCampusType != null">
        SCHOOL_CAMPUS_TYPE,
      </if>
      <if test="is5gCoverage != null">
        IS_5G_COVERAGE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        #{campusId,jdbcType=BIGINT},
      </if>
      <if test="isBroadbandAccess != null">
        #{isBroadbandAccess,jdbcType=CHAR},
      </if>
      <if test="broadbandAccessType != null">
        #{broadbandAccessType,jdbcType=CHAR},
      </if>
      <if test="broadbandCoverageRatio != null">
        #{broadbandCoverageRatio,jdbcType=CHAR},
      </if>
      <if test="juStudent != null">
        #{juStudent,jdbcType=INTEGER},
      </if>
      <if test="mdStudent != null">
        #{mdStudent,jdbcType=INTEGER},
      </if>
      <if test="newJuStudent != null">
        #{newJuStudent,jdbcType=INTEGER},
      </if>
      <if test="newMdStudent != null">
        #{newMdStudent,jdbcType=INTEGER},
      </if>
      <if test="schoolCampusType != null">
        #{schoolCampusType,jdbcType=CHAR},
      </if>
      <if test="is5gCoverage != null">
        #{is5gCoverage,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.api.po.WoSchoolCampusExtendExample" resultType="java.lang.Long">
    select count(*) from wo_school_campus_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_school_campus_extend
    <set>
      <if test="record.campusId != null">
        CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      </if>
      <if test="record.isBroadbandAccess != null">
        IS_BROADBAND_ACCESS = #{record.isBroadbandAccess,jdbcType=CHAR},
      </if>
      <if test="record.broadbandAccessType != null">
        BROADBAND_ACCESS_TYPE = #{record.broadbandAccessType,jdbcType=CHAR},
      </if>
      <if test="record.broadbandCoverageRatio != null">
        BROADBAND_COVERAGE_RATIO = #{record.broadbandCoverageRatio,jdbcType=CHAR},
      </if>
      <if test="record.juStudent != null">
        JU_STUDENT = #{record.juStudent,jdbcType=INTEGER},
      </if>
      <if test="record.mdStudent != null">
        MD_STUDENT = #{record.mdStudent,jdbcType=INTEGER},
      </if>
      <if test="record.newJuStudent != null">
        NEW_JU_STUDENT = #{record.newJuStudent,jdbcType=INTEGER},
      </if>
      <if test="record.newMdStudent != null">
        NEW_MD_STUDENT = #{record.newMdStudent,jdbcType=INTEGER},
      </if>
      <if test="record.schoolCampusType != null">
        SCHOOL_CAMPUS_TYPE = #{record.schoolCampusType,jdbcType=CHAR},
      </if>
      <if test="record.is5gCoverage != null">
        IS_5G_COVERAGE = #{record.is5gCoverage,jdbcType=CHAR},
      </if>
      <if test="record.createBy != null">
        CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reserve1 != null">
        RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve2 != null">
        RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve3 != null">
        RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_school_campus_extend
    set CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      IS_BROADBAND_ACCESS = #{record.isBroadbandAccess,jdbcType=CHAR},
      BROADBAND_ACCESS_TYPE = #{record.broadbandAccessType,jdbcType=CHAR},
      BROADBAND_COVERAGE_RATIO = #{record.broadbandCoverageRatio,jdbcType=CHAR},
      JU_STUDENT = #{record.juStudent,jdbcType=INTEGER},
      MD_STUDENT = #{record.mdStudent,jdbcType=INTEGER},
      NEW_JU_STUDENT = #{record.newJuStudent,jdbcType=INTEGER},
      NEW_MD_STUDENT = #{record.newMdStudent,jdbcType=INTEGER},
      SCHOOL_CAMPUS_TYPE = #{record.schoolCampusType,jdbcType=CHAR},
      IS_5G_COVERAGE = #{record.is5gCoverage,jdbcType=CHAR},
      CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{record.reserve3,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.api.po.WoSchoolCampusExtend">
    update wo_school_campus_extend
    <set>
      <if test="isBroadbandAccess != null">
        IS_BROADBAND_ACCESS = #{isBroadbandAccess,jdbcType=CHAR},
      </if>
      <if test="broadbandAccessType != null">
        BROADBAND_ACCESS_TYPE = #{broadbandAccessType,jdbcType=CHAR},
      </if>
      <if test="broadbandCoverageRatio != null">
        BROADBAND_COVERAGE_RATIO = #{broadbandCoverageRatio,jdbcType=CHAR},
      </if>
      <if test="juStudent != null">
        JU_STUDENT = #{juStudent,jdbcType=INTEGER},
      </if>
      <if test="mdStudent != null">
        MD_STUDENT = #{mdStudent,jdbcType=INTEGER},
      </if>
      <if test="newJuStudent != null">
        NEW_JU_STUDENT = #{newJuStudent,jdbcType=INTEGER},
      </if>
      <if test="newMdStudent != null">
        NEW_MD_STUDENT = #{newMdStudent,jdbcType=INTEGER},
      </if>
      <if test="schoolCampusType != null">
        SCHOOL_CAMPUS_TYPE = #{schoolCampusType,jdbcType=CHAR},
      </if>
      <if test="is5gCoverage != null">
        IS_5G_COVERAGE = #{is5gCoverage,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.api.po.WoSchoolCampusExtend">
    update wo_school_campus_extend
    set IS_BROADBAND_ACCESS = #{isBroadbandAccess,jdbcType=CHAR},
      BROADBAND_ACCESS_TYPE = #{broadbandAccessType,jdbcType=CHAR},
      BROADBAND_COVERAGE_RATIO = #{broadbandCoverageRatio,jdbcType=CHAR},
      JU_STUDENT = #{juStudent,jdbcType=INTEGER},
      MD_STUDENT = #{mdStudent,jdbcType=INTEGER},
      NEW_JU_STUDENT = #{newJuStudent,jdbcType=INTEGER},
      NEW_MD_STUDENT = #{newMdStudent,jdbcType=INTEGER},
      SCHOOL_CAMPUS_TYPE = #{schoolCampusType,jdbcType=CHAR},
      IS_5G_COVERAGE = #{is5gCoverage,jdbcType=CHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR}
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
</mapper>