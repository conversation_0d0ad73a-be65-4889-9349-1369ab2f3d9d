<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.WoScRequestMsglogMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.api.po.WoScRequestMsglog">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="LOG_TIME" jdbcType="TIMESTAMP" property="logTime" />
    <result column="LOG_TYPE" jdbcType="VARCHAR" property="logType" />
    <result column="REQUEST_URL" jdbcType="VARCHAR" property="requestUrl" />
    <result column="REQUEST_TIME" jdbcType="TIMESTAMP" property="requestTime" />
    <result column="REQUEST_STATE" jdbcType="VARCHAR" property="requestState" />
    <result column="RESPONSE_TIME" jdbcType="TIMESTAMP" property="responseTime" />
    <result column="RESPONSE_STATE" jdbcType="VARCHAR" property="responseState" />
    <result column="BODY_STATE" jdbcType="VARCHAR" property="bodyState" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jsunicom.api.po.WoScRequestMsglog">
    <result column="REQUEST_MSG" jdbcType="LONGVARCHAR" property="requestMsg" />
    <result column="RESPONSE_MSG" jdbcType="LONGVARCHAR" property="responseMsg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ID, LOG_TIME, LOG_TYPE, REQUEST_URL, REQUEST_TIME, REQUEST_STATE, RESPONSE_TIME, 
    RESPONSE_STATE, BODY_STATE
  </sql>
  <sql id="Blob_Column_List">
    REQUEST_MSG, RESPONSE_MSG
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.jsunicom.api.po.WoScRequestMsglogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from wo_sc_request_msglog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.jsunicom.api.po.WoScRequestMsglogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_sc_request_msglog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from wo_sc_request_msglog
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wo_sc_request_msglog
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.api.po.WoScRequestMsglogExample">
    delete from wo_sc_request_msglog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.api.po.WoScRequestMsglog">
    insert into wo_sc_request_msglog (ID, LOG_TIME, LOG_TYPE, 
      REQUEST_URL, REQUEST_TIME, REQUEST_STATE, 
      RESPONSE_TIME, RESPONSE_STATE, BODY_STATE, 
      REQUEST_MSG, RESPONSE_MSG)
    values (#{id,jdbcType=BIGINT}, #{logTime,jdbcType=TIMESTAMP}, #{logType,jdbcType=VARCHAR}, 
      #{requestUrl,jdbcType=VARCHAR}, #{requestTime,jdbcType=TIMESTAMP}, #{requestState,jdbcType=VARCHAR}, 
      #{responseTime,jdbcType=TIMESTAMP}, #{responseState,jdbcType=VARCHAR}, #{bodyState,jdbcType=VARCHAR}, 
      #{requestMsg,jdbcType=LONGVARCHAR}, #{responseMsg,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.api.po.WoScRequestMsglog">
    insert into wo_sc_request_msglog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="logTime != null">
        LOG_TIME,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="requestUrl != null">
        REQUEST_URL,
      </if>
      <if test="requestTime != null">
        REQUEST_TIME,
      </if>
      <if test="requestState != null">
        REQUEST_STATE,
      </if>
      <if test="responseTime != null">
        RESPONSE_TIME,
      </if>
      <if test="responseState != null">
        RESPONSE_STATE,
      </if>
      <if test="bodyState != null">
        BODY_STATE,
      </if>
      <if test="requestMsg != null">
        REQUEST_MSG,
      </if>
      <if test="responseMsg != null">
        RESPONSE_MSG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="logTime != null">
        #{logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestTime != null">
        #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestState != null">
        #{requestState,jdbcType=VARCHAR},
      </if>
      <if test="responseTime != null">
        #{responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="responseState != null">
        #{responseState,jdbcType=VARCHAR},
      </if>
      <if test="bodyState != null">
        #{bodyState,jdbcType=VARCHAR},
      </if>
      <if test="requestMsg != null">
        #{requestMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseMsg != null">
        #{responseMsg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.api.po.WoScRequestMsglogExample" resultType="java.lang.Long">
    select count(*) from wo_sc_request_msglog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_sc_request_msglog
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.logTime != null">
        LOG_TIME = #{record.logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.logType != null">
        LOG_TYPE = #{record.logType,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUrl != null">
        REQUEST_URL = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.requestTime != null">
        REQUEST_TIME = #{record.requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestState != null">
        REQUEST_STATE = #{record.requestState,jdbcType=VARCHAR},
      </if>
      <if test="record.responseTime != null">
        RESPONSE_TIME = #{record.responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.responseState != null">
        RESPONSE_STATE = #{record.responseState,jdbcType=VARCHAR},
      </if>
      <if test="record.bodyState != null">
        BODY_STATE = #{record.bodyState,jdbcType=VARCHAR},
      </if>
      <if test="record.requestMsg != null">
        REQUEST_MSG = #{record.requestMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.responseMsg != null">
        RESPONSE_MSG = #{record.responseMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update wo_sc_request_msglog
    set ID = #{record.id,jdbcType=BIGINT},
      LOG_TIME = #{record.logTime,jdbcType=TIMESTAMP},
      LOG_TYPE = #{record.logType,jdbcType=VARCHAR},
      REQUEST_URL = #{record.requestUrl,jdbcType=VARCHAR},
      REQUEST_TIME = #{record.requestTime,jdbcType=TIMESTAMP},
      REQUEST_STATE = #{record.requestState,jdbcType=VARCHAR},
      RESPONSE_TIME = #{record.responseTime,jdbcType=TIMESTAMP},
      RESPONSE_STATE = #{record.responseState,jdbcType=VARCHAR},
      BODY_STATE = #{record.bodyState,jdbcType=VARCHAR},
      REQUEST_MSG = #{record.requestMsg,jdbcType=LONGVARCHAR},
      RESPONSE_MSG = #{record.responseMsg,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_sc_request_msglog
    set ID = #{record.id,jdbcType=BIGINT},
      LOG_TIME = #{record.logTime,jdbcType=TIMESTAMP},
      LOG_TYPE = #{record.logType,jdbcType=VARCHAR},
      REQUEST_URL = #{record.requestUrl,jdbcType=VARCHAR},
      REQUEST_TIME = #{record.requestTime,jdbcType=TIMESTAMP},
      REQUEST_STATE = #{record.requestState,jdbcType=VARCHAR},
      RESPONSE_TIME = #{record.responseTime,jdbcType=TIMESTAMP},
      RESPONSE_STATE = #{record.responseState,jdbcType=VARCHAR},
      BODY_STATE = #{record.bodyState,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.api.po.WoScRequestMsglog">
    update wo_sc_request_msglog
    <set>
      <if test="logTime != null">
        LOG_TIME = #{logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        REQUEST_URL = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestTime != null">
        REQUEST_TIME = #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestState != null">
        REQUEST_STATE = #{requestState,jdbcType=VARCHAR},
      </if>
      <if test="responseTime != null">
        RESPONSE_TIME = #{responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="responseState != null">
        RESPONSE_STATE = #{responseState,jdbcType=VARCHAR},
      </if>
      <if test="bodyState != null">
        BODY_STATE = #{bodyState,jdbcType=VARCHAR},
      </if>
      <if test="requestMsg != null">
        REQUEST_MSG = #{requestMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseMsg != null">
        RESPONSE_MSG = #{responseMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.jsunicom.api.po.WoScRequestMsglog">
    update wo_sc_request_msglog
    set LOG_TIME = #{logTime,jdbcType=TIMESTAMP},
      LOG_TYPE = #{logType,jdbcType=VARCHAR},
      REQUEST_URL = #{requestUrl,jdbcType=VARCHAR},
      REQUEST_TIME = #{requestTime,jdbcType=TIMESTAMP},
      REQUEST_STATE = #{requestState,jdbcType=VARCHAR},
      RESPONSE_TIME = #{responseTime,jdbcType=TIMESTAMP},
      RESPONSE_STATE = #{responseState,jdbcType=VARCHAR},
      BODY_STATE = #{bodyState,jdbcType=VARCHAR},
      REQUEST_MSG = #{requestMsg,jdbcType=LONGVARCHAR},
      RESPONSE_MSG = #{responseMsg,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.api.po.WoScRequestMsglog">
    update wo_sc_request_msglog
    set LOG_TIME = #{logTime,jdbcType=TIMESTAMP},
      LOG_TYPE = #{logType,jdbcType=VARCHAR},
      REQUEST_URL = #{requestUrl,jdbcType=VARCHAR},
      REQUEST_TIME = #{requestTime,jdbcType=TIMESTAMP},
      REQUEST_STATE = #{requestState,jdbcType=VARCHAR},
      RESPONSE_TIME = #{responseTime,jdbcType=TIMESTAMP},
      RESPONSE_STATE = #{responseState,jdbcType=VARCHAR},
      BODY_STATE = #{bodyState,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>