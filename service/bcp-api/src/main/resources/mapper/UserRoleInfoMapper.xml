<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.UserRoleInfoMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.api.entity.goods.UserRoleInfo">
    <id column="user_pid" jdbcType="VARCHAR" property="userPid" />
    <id column="staff_no" jdbcType="VARCHAR" property="staffNo" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="area_level" jdbcType="CHAR" property="areaLevel" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="role_code" jdbcType="VARCHAR" property="roleCode" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="state" jdbcType="VARCHAR" property="state" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    user_pid, staff_no, staff_name, serial_number, area_level, area_code, role_code,
    role_name, create_time, state
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.api.entity.goods.UserRoleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_role_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_role_info
    where user_pid = #{userPid,jdbcType=VARCHAR}
      and staff_no = #{staffNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from user_role_info
    where user_pid = #{userPid,jdbcType=VARCHAR}
      and staff_no = #{staffNo,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.api.entity.goods.UserRoleInfoExample">
    delete from user_role_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.api.entity.goods.UserRoleInfo">
    insert into user_role_info (user_pid, staff_no, staff_name,
      serial_number, area_level, area_code,
      role_code, role_name, create_time,
      state)
    values (#{userPid,jdbcType=VARCHAR}, #{staffNo,jdbcType=VARCHAR}, #{staffName,jdbcType=VARCHAR},
      #{serialNumber,jdbcType=VARCHAR}, #{areaLevel,jdbcType=CHAR}, #{areaCode,jdbcType=VARCHAR},
      #{roleCode,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{state,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.api.entity.goods.UserRoleInfo">
    insert into user_role_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userPid != null">
        user_pid,
      </if>
      <if test="staffNo != null">
        staff_no,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="areaLevel != null">
        area_level,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="roleCode != null">
        role_code,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="state != null">
        state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userPid != null">
        #{userPid,jdbcType=VARCHAR},
      </if>
      <if test="staffNo != null">
        #{staffNo,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="areaLevel != null">
        #{areaLevel,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null">
        #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.api.entity.goods.UserRoleInfoExample" resultType="java.lang.Long">
    select count(*) from user_role_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_role_info
    <set>
      <if test="record.userPid != null">
        user_pid = #{record.userPid,jdbcType=VARCHAR},
      </if>
      <if test="record.staffNo != null">
        staff_no = #{record.staffNo,jdbcType=VARCHAR},
      </if>
      <if test="record.staffName != null">
        staff_name = #{record.staffName,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNumber != null">
        serial_number = #{record.serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.areaLevel != null">
        area_level = #{record.areaLevel,jdbcType=CHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.roleCode != null">
        role_code = #{record.roleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.roleName != null">
        role_name = #{record.roleName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_role_info
    set user_pid = #{record.userPid,jdbcType=VARCHAR},
      staff_no = #{record.staffNo,jdbcType=VARCHAR},
      staff_name = #{record.staffName,jdbcType=VARCHAR},
      serial_number = #{record.serialNumber,jdbcType=VARCHAR},
      area_level = #{record.areaLevel,jdbcType=CHAR},
      area_code = #{record.areaCode,jdbcType=VARCHAR},
      role_code = #{record.roleCode,jdbcType=VARCHAR},
      role_name = #{record.roleName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      state = #{record.state,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.api.entity.goods.UserRoleInfo">
    update user_role_info
    <set>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="areaLevel != null">
        area_level = #{areaLevel,jdbcType=CHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null">
        role_code = #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
    </set>
    where user_pid = #{userPid,jdbcType=VARCHAR}
      and staff_no = #{staffNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.api.entity.goods.UserRoleInfo">
    update user_role_info
    set staff_name = #{staffName,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      area_level = #{areaLevel,jdbcType=CHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      role_code = #{roleCode,jdbcType=VARCHAR},
      role_name = #{roleName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      state = #{state,jdbcType=VARCHAR}
    where user_pid = #{userPid,jdbcType=VARCHAR}
      and staff_no = #{staffNo,jdbcType=VARCHAR}
  </update>

  <select id="selectRoleByPrimary" parameterType="java.lang.String" resultType="java.lang.String">
    select min(ri.role_code) from user_role_info ri where ri.staff_no = #{staffNo,jdbcType=VARCHAR} and ri.role_code in ('1','2','3') and ri.state = '0'
  </select>

  <select id="queryCodeAndRoleByStaffNo" parameterType="java.lang.String" resultType="java.util.Map">
    select area_code as areaCode, role_code as roleCode from user_role_info ri1
    where ri1.state = '0' and ri1.staff_no = #{staffNo,jdbcType=VARCHAR}
      and ri1.role_code = (select min(ri2.role_code) from user_role_info ri2 where ri1.staff_no = ri2.staff_no)
  </select>

</mapper>
