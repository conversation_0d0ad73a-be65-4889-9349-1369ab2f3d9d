<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.api.mapper.PartnerQrCodeDelBakDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner_qr_code_del_bak( id,partner_id,qr_code_type,acct_no,scence_id,wx_acct,qr_code_url,
                         qr_code_time,qr_code_app,update_by,update_time,create_by,create_time,promote_id,ticket ,img_url,operate_by,operate_time)
        VALUES ( #{id},#{partnerId},#{qrCodeType},#{acctNo},#{scenceId},#{wxAcct},#{qrCodeUrl},
                 #{qrCodeTime},#{qrCodeApp},#{updateBy},#{updateTime},#{createBy},#{createTime},#{promoteId},#{ticket},#{imgUrl},#{operateBy},#{operateTime})
    </insert>
    <!-- ============================= UPDATE ============================= -->

    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner_qr_code_del_bak
        WHERE id=#{id} 
    </delete>
    <!-- ============================= SELECT ============================= -->
    <select id="findByPK" resultType="com.jsunicom.api.model.PartnerQrCode">
        SELECT * FROM partner_qr_code_del_bak
        WHERE id=#{id} 
    </select>
</mapper>
