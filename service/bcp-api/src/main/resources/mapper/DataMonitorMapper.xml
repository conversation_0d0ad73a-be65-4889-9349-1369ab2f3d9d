<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.DataMonitorDao">
    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO data_monitor( id,monitor_modules,monitor_type,data_id,
        operate_by,operate_time,operate_desc,previous_operate_desc )
        VALUES ( #{id},#{monitorModules},#{monitorType},#{dataId},
        #{operateBy},#{operateTime},#{operateDesc},#{previousOperateDesc})
    </insert>


    <!-- ============================= SELECT ============================= -->
    <select id="findByPage" resultType="com.jsunicom.api.entity.dataMonitor.DataMonitor">
        SELECT id,monitor_modules,monitor_type,data_id,
        operate_by,operate_time,operate_desc,previous_operate_desc
        FROM data_monitor
        <where>
            <if test="id!= null">
                AND id = #{id}
            </if>
            <if test="monitorModules!= null">
                AND monitor_modules = #{monitorModules}
            </if>
            <if test="monitorType!= null">
                AND monitor_type = #{monitorType}
            </if>
            <if test="dataId!= null">
                AND data_id = #{dataId}
            </if>
            <if test="operateBy!= null">
                AND operate_by = #{operateBy}
            </if>
            <if test="operateTime!= null">
                AND operate_time = #{operateTime}
            </if>
        </where>
    </select>


    <insert id="saveBatchPromotActvMonitor" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.HashMap">
        INSERT INTO data_monitor(id,monitor_modules,monitor_type,data_id,
        operate_by,operate_time,operate_desc,previous_operate_desc )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{monitorModules},#{monitorType},#{item.dataId},
            #{acctNo},SYSDATE(),#{item.operateDesc},null)
        </foreach>
    </insert>



    <insert id="saveBatchGoodsMonitor" useGeneratedKeys="true" keyProperty="autoId" parameterType="java.util.HashMap">
        INSERT INTO data_monitor(id,monitor_modules,monitor_type,data_id,
        operate_by,operate_time,operate_desc,previous_operate_desc )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.autoId},#{monitorModules},#{monitorType},CONCAT(#{item.id},''),
            #{acctNo},SYSDATE(),CONCAT('商品批量下架,商品id为:',#{item.id},',商品名称为:',#{item.name},
            '商品编码为:',#{item.code},',state:',CASE WHEN #{item.state}='Y' THEN '上架' ELSE '下架' END),null)
        </foreach>
    </insert>
</mapper>
