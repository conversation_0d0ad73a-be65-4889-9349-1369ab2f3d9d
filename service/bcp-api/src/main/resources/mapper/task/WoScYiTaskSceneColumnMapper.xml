<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.api.mapper.task.WoScYiTaskSceneColumnMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.api.po.task.WoScYiTaskSceneColumn">
    <result column="TASK_SCENE_TYPE_CODE" jdbcType="CHAR" property="taskSceneTypeCode" />
    <result column="COLUMN_ANAME" jdbcType="VARCHAR" property="columnAname" />
    <result column="COLUMN_NAME" jdbcType="VARCHAR" property="columnName" />
    <result column="COLUMN_CNAME" jdbcType="VARCHAR" property="columnCname" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    TASK_SCENE_TYPE_CODE, COLUMN_ANAME, COLUMN_NAME, COLUMN_CNAME, STATE, REMARK
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.api.po.task.WoScYiTaskSceneColumnExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_sc_yi_task_scene_column
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.jsunicom.api.po.task.WoScYiTaskSceneColumnExample">
    delete from wo_sc_yi_task_scene_column
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.api.po.task.WoScYiTaskSceneColumn">
    insert into wo_sc_yi_task_scene_column (TASK_SCENE_TYPE_CODE, COLUMN_ANAME, COLUMN_NAME, 
      COLUMN_CNAME, STATE, REMARK
      )
    values (#{taskSceneTypeCode,jdbcType=CHAR}, #{columnAname,jdbcType=VARCHAR}, #{columnName,jdbcType=VARCHAR}, 
      #{columnCname,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.api.po.task.WoScYiTaskSceneColumn">
    insert into wo_sc_yi_task_scene_column
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskSceneTypeCode != null">
        TASK_SCENE_TYPE_CODE,
      </if>
      <if test="columnAname != null">
        COLUMN_ANAME,
      </if>
      <if test="columnName != null">
        COLUMN_NAME,
      </if>
      <if test="columnCname != null">
        COLUMN_CNAME,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskSceneTypeCode != null">
        #{taskSceneTypeCode,jdbcType=CHAR},
      </if>
      <if test="columnAname != null">
        #{columnAname,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="columnCname != null">
        #{columnCname,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.api.po.task.WoScYiTaskSceneColumnExample" resultType="java.lang.Long">
    select count(*) from wo_sc_yi_task_scene_column
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_sc_yi_task_scene_column
    <set>
      <if test="record.taskSceneTypeCode != null">
        TASK_SCENE_TYPE_CODE = #{record.taskSceneTypeCode,jdbcType=CHAR},
      </if>
      <if test="record.columnAname != null">
        COLUMN_ANAME = #{record.columnAname,jdbcType=VARCHAR},
      </if>
      <if test="record.columnName != null">
        COLUMN_NAME = #{record.columnName,jdbcType=VARCHAR},
      </if>
      <if test="record.columnCname != null">
        COLUMN_CNAME = #{record.columnCname,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        STATE = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_sc_yi_task_scene_column
    set TASK_SCENE_TYPE_CODE = #{record.taskSceneTypeCode,jdbcType=CHAR},
      COLUMN_ANAME = #{record.columnAname,jdbcType=VARCHAR},
      COLUMN_NAME = #{record.columnName,jdbcType=VARCHAR},
      COLUMN_CNAME = #{record.columnCname,jdbcType=VARCHAR},
      STATE = #{record.state,jdbcType=VARCHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>