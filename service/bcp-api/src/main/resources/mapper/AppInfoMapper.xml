<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsunicom.api.mapper.AppInfoDao">
	<resultMap id="BaseResultMap" type="com.jsunicom.api.entity.app.AppInfo">
		<result column="APP_ID" property="appId" jdbcType="VARCHAR" />
		<result column="APP_SECRET" property="appSecret" jdbcType="VARCHAR" />
		<result column="TOKEN" property="token" jdbcType="VARCHAR" />
		<result column="ACCESS_TOKEN" property="accessToken" jdbcType="VARCHAR" />
		<result column="ENCODING_AES_KEY" property="encodingAesKey" jdbcType="VARCHAR" />
		<result column="API_TICKET" property="apiTicket" jdbcType="VARCHAR" />
		<result column="APP_TYPE" property="appType" jdbcType="TINYINT" />
		<result column="MESSAGING_MODE" property="messagingMode" jdbcType="TINYINT"  />
		<result column="APP_NAME" property="appName" jdbcType="VARCHAR" />
		<result column="ACCESS_TOKEN_LAST_UPDATE_TIME" property="accessTokenLastUpdateTime" jdbcType="TIMESTAMP" />
		<result column="API_TICKET_LAST_UPDATE_TIME" property="apiTicketLastUpdateTime" jdbcType="TIMESTAMP" />
		<result column="ENABLED" property="enabled" jdbcType="TINYINT" />
	</resultMap>

	<sql id="Base_Column_List">
		APP_ID, APP_SECRET, TOKEN, ACCESS_TOKEN, ENCODING_AES_KEY, API_TICKET, APP_TYPE, MESSAGING_MODE,APP_NAME,
		ACCESS_TOKEN_LAST_UPDATE_TIME, API_TICKET_LAST_UPDATE_TIME,ENABLED
	</sql>

	<select id="list" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from app_info
	</select>

	<select id="get" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from app_info
		where APP_ID = #{appId,jdbcType=VARCHAR}
	</select>

	<update id="update" parameterType="com.jsunicom.api.entity.app.AppInfo">
		update app_info
		<set>
			APP_SECRET = #{appSecret,jdbcType=VARCHAR},
			TOKEN = #{token,jdbcType=VARCHAR},
			ACCESS_TOKEN =
			#{accessToken,jdbcType=VARCHAR},
			ENCODING_AES_KEY = #{encodingAesKey,jdbcType=VARCHAR},
			API_TICKET =
			#{apiTicket,jdbcType=VARCHAR},
			ACCESS_TOKEN_LAST_UPDATE_TIME = #{accessTokenLastUpdateTime,jdbcType=TIMESTAMP},
			API_TICKET_LAST_UPDATE_TIME = #{apiTicketLastUpdateTime,jdbcType=TIMESTAMP},
			ENABLED = #{enabled,jdbcType=TINYINT}
		</set>
		where APP_ID = #{appId,jdbcType=VARCHAR}
	</update>

	<update id="updateAccessToken">
		update app_info
		<set>
			ACCESS_TOKEN =
			#{accessToken,jdbcType=VARCHAR},
			ACCESS_TOKEN_LAST_UPDATE_TIME =
			#{lastUpdateTime,jdbcType=TIMESTAMP}
		</set>
		where APP_ID = #{appId,jdbcType=VARCHAR}
	</update>

	<update id="updateApiTicket">
		update app_info
		<set>
			API_TICKET =
			#{apiTicket,jdbcType=VARCHAR},
			API_TICKET_LAST_UPDATE_TIME = #{lastUpdateTime,jdbcType=TIMESTAMP}
		</set>
		where APP_ID = #{appId,jdbcType=VARCHAR}
	</update>
</mapper>
