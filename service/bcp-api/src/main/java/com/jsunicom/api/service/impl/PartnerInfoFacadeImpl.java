package com.jsunicom.api.service.impl;

import com.jsunicom.api.mapper.PartnerInfoMapper;
import com.jsunicom.api.po.PartnerInfo;
import com.jsunicom.api.po.PartnerInfoExample;
import com.jsunicom.api.service.PartnerInfoFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PartnerInfoFacadeImpl implements PartnerInfoFacade {

    @Autowired
    private PartnerInfoMapper partnerInfoMapper;

    public long countByExample()
    {
        PartnerInfoExample example = new PartnerInfoExample();
        PartnerInfoExample.Criteria criteria = example.createCriteria();


        return partnerInfoMapper.countByExample(example);
    }

    @Override
    public int updateByPrimaryKey(PartnerInfo record)
    {
        return partnerInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public PartnerInfo selectByPrimaryKey(Long id)
    {
        return partnerInfoMapper.selectByPrimaryKey(id);
    }


    @Override
    public int updateByPrimaryKeySelective(PartnerInfo record) {
        return partnerInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<PartnerInfo> getPartnerInfoList(PartnerInfo record)
    {
        PartnerInfoExample example = new PartnerInfoExample();
        PartnerInfoExample.Criteria criteria = example.createCriteria();
        if(record.getSocietyId() != null){
            criteria.andSocietyIdEqualTo(record.getSocietyId());
        }
        if(record.getIsMerAdmin() != null){
            String isMerAdminStr = record.getIsMerAdmin();
            String[] splitArray = isMerAdminStr.split("|");
            criteria.andIsMerAdminIn(Arrays.asList(splitArray));
        }
        if(record.getState() != null){
            criteria.andStateEqualTo(record.getState());
        }
        if(record.getPartnerName() != null){
            criteria.andPartnerNameLike("%"+ record.getPartnerName() + "%");
        }

        return partnerInfoMapper.selectByExample(example);
    }


    @Override
    public long countOrByExample(PartnerInfo record)
    {

        PartnerInfoExample example = new PartnerInfoExample();
        PartnerInfoExample.Criteria criteria = example.createCriteria();

        criteria.andMblNbrEqualTo(record.getMblNbr());
        if(record.getState() != null){
            String stateStr = record.getState();
            String[] splitArray = stateStr.split("|");
            criteria.andStateIn(Arrays.asList(splitArray));
        }


        PartnerInfoExample.Criteria criteriaOr = example.or();
        criteriaOr.andPartnerCertNoEqualTo(record.getPartnerCertNo());
        if(record.getState() != null){
            String stateStr = record.getState();
            String[] splitArray = stateStr.split("|");
            criteriaOr.andStateIn(Arrays.asList(splitArray));
        }

        return partnerInfoMapper.countByExample(example);

    }


    @Override
    public long countByExample(PartnerInfo record){
        PartnerInfoExample example = new PartnerInfoExample();
        PartnerInfoExample.Criteria criteria = example.createCriteria();

        if(record.getSocietyId() != null){
            criteria.andSocietyIdEqualTo(record.getSocietyId());
        }

        criteria.andStateEqualTo(record.getState());

        if(record.getPartnerType() != null){
            criteria.andPartnerTypeEqualTo(record.getPartnerType());
        }

        if(record.getMerchantId() != null){
            criteria.andMerchantIdEqualTo(record.getMerchantId());
        }

        return partnerInfoMapper.countByExample(example);
    }


    @Override
    public List<Map> getExportApplicationList(PartnerInfo exportDto) {
        List<Map> resultList = partnerInfoMapper.getExportApplicationList(exportDto);
        return resultList;
    }


    @Override
    public int insertPartnerInfo(PartnerInfo record){
        return partnerInfoMapper.insertSelective(record);
    }


    @Override
    public int deletePartnerInfo(PartnerInfo record){
        return partnerInfoMapper.deleteByPrimaryKey(record.getId());
    }

}
