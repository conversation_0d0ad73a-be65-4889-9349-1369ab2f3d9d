package com.jsunicom.api.entity.app;

import com.lz.lsf.orm.IntegerValuedEnum;

/**
 * 消息加密方式
 *
 * <AUTHOR>
 *
 */
public enum MessagingMode implements IntegerValuedEnum {

    /**
     * 明文模式
     */
    PLAINTEXT(1),

    /**
     * 兼容模式
     */
    COMPATIBLE(2),

    /**
     * 安全模式
     */
    SAFE(3);

    private int value;

    private MessagingMode(int value) {
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }
}
