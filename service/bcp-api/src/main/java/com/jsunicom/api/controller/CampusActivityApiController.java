package com.jsunicom.api.controller;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.entity.Campus.ActivitiesBaseInfo;
import com.jsunicom.api.entity.Campus.ActivitiesUserRelation;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.service.ActivitiesBaseInfoService;
import com.jsunicom.api.service.WoSchoolFacade;
import com.jsunicom.api.vo.campus.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.repository.query.Param;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/campus/activity/api")
public class CampusActivityApiController extends BaseController {

    @Resource
    private WoSchoolFacade woSchoolFacade;

    @Resource
    private ActivitiesBaseInfoService activitiesBaseInfoService;


    @PostMapping(value = "/createActivity", name = "校园活动发起")
    @DecryptApi
    @EncryptApi
    public CustomResult createActivity(HttpServletRequest request, @RequestBody ActivityMessageVO activityMessageVO) {
//        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        int insertRow = activitiesBaseInfoService.createActivity(activityMessageVO);
        if (insertRow < 1) {
            return new CustomResult("9999", "发起活动失败", null);
        }
        return new CustomResult("0000", "发起活动成功", null);
    }

    @PostMapping(value = "/getTeamInfo", name = "查询团队")
    @DecryptApi
    @EncryptApi
    public CustomResult getTeamInfo(HttpServletRequest request,String campusId) {
//        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        List<TeamBaseInfo> teamBaseInfoList = activitiesBaseInfoService.getTeamInfo(campusId);
        if (CollectionUtils.isEmpty(teamBaseInfoList)) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", teamBaseInfoList);
    }
    @PostMapping(value = "/getActivitiesList", name = "查询活动列表")
    @DecryptApi
    @EncryptApi
    public CustomResult getActivitiesList(HttpServletRequest request, PageVo pageVo) {
        log.info("接收参数:{}",pageVo.toString());
//        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        PageInfo<ActivitiesBaseInfo> activitiesBaseInfos = activitiesBaseInfoService.getActivitiesList(pageVo);
        if (CollectionUtils.isEmpty(activitiesBaseInfos.getList())) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", activitiesBaseInfos);
    }

    @PostMapping(value = "/getActivitiesDetailsEffectiveness", name = "查询活动详情-活动效果(活动记录)")
    @DecryptApi
    @EncryptApi
    public CustomResult getActivitiesDetailsEffectiveness(HttpServletRequest request,@RequestBody ActivitiesUserRelation activitiesUserRelation ) {
        ActivityMessageVO activitiesDetailsEffectiveness = activitiesBaseInfoService.getActivitiesDetailsEffectiveness(activitiesUserRelation);
        if (activitiesDetailsEffectiveness == null) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", activitiesDetailsEffectiveness);
    }


    @GetMapping(value = "/getHelpUserInfo", name = "获取帮扶人员信息")
    @DecryptApi
    @EncryptApi
    public CustomResult getActivitiesDetailsrecord(HttpServletRequest request) {
        ArrayList<ActivitiesUserRelation> activitiesUserRelations = new ArrayList<>();
        //todo 暂时写死,之后获取帮扶人员信息
        ActivitiesUserRelation activitiesUserRelation = new ActivitiesUserRelation();
        activitiesUserRelation.setUserId("123456");
        activitiesUserRelation.setUserName("张旭旭");
        activitiesUserRelations.add(activitiesUserRelation);
        if (activitiesUserRelation == null) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", activitiesUserRelation);
    }

    //队长确认收到
    @PostMapping(value = "/confirmationReceived", name = "队长确认收到")
    @DecryptApi
    @EncryptApi
    public CustomResult confirmationReceived(HttpServletRequest request, @Param("activitiesId") String activitiesId,@Param("userId") String userId ) {


        int updateRows = activitiesBaseInfoService.confirmationReceived(activitiesId,userId);
        if ( updateRows < 1) {
            return new CustomResult("9999", "确认失败", null);
        }
        return new CustomResult("0000", "已确认", null);
    }



     //打卡范围限定  punchClockRange
     @PostMapping(value = "/punchClockRange", name = "打卡范围限定")
     @DecryptApi
     @EncryptApi
     public CustomResult punchClockRange(HttpServletRequest request, PunchRangeVO punchRangeVO ) {


         Boolean aBoolean = activitiesBaseInfoService.punchClockRange(punchRangeVO);
         if ( !aBoolean) {
             return new CustomResult("9999", "不在打卡范围内", aBoolean);
         }
         return new CustomResult("0000", "活动签到", aBoolean);
     }

     //打卡
     @PostMapping(value = "/punchTheClock", name = "打卡")
     @DecryptApi
     @EncryptApi
     public CustomResult punchTheClock(HttpServletRequest request, @RequestBody PunchRangeVO punchRangeVO ) {
         //        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
         // todo 获取登录人信息,改变打卡状态
         Partner partner = new Partner();
         int updateRows = activitiesBaseInfoService.punchTheClock(partner);
         if ( updateRows < 0) {
             return new CustomResult("9999", "打卡失败", null);
         }
         return new CustomResult("0000", "打卡成功", null);
     }

    @GetMapping(value = "/getAddress",name = "打卡-根据经纬度返回地址信息")
    @DecryptApi
    @EncryptApi
    public CustomResult getAddress(@RequestParam String lat, @RequestParam String lng) {

        JSONObject addressByLatLng = activitiesBaseInfoService.getAddressByLatLng(lat, lng);
        if (addressByLatLng == null) {
            return new CustomResult("9999", "请重试", null);
        }
        return new CustomResult("0000", "查询成功", addressByLatLng);

    }

    @PostMapping(value = "/getUserInfoByMblNbr",name = "根据电话号码查询人员信息")
    @DecryptApi
    @EncryptApi
    public CustomResult CustomResult(@Param("MblNbr") String mblNbr ) {

        ClockUserInfo clockUserInfo =  activitiesBaseInfoService.getUserInfoByMblNbr(mblNbr);

        if (clockUserInfo == null) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", clockUserInfo);
    }


}
