package com.jsunicom.api.entity.Campus;

import com.jsunicom.api.model.base.BasicModel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
public class CampusBind extends BasicModel {
    private static final long serialVersionUID = 1L;
    public static final String TYPE_0 = "0";// 团队 bindId 是 merchant_id
    public static final String TYPE_1 = "1";//校区经理 bindId 是 parent_id
    public static final String STATE_1 = "1";//生效

    private Long campusId;   //null 
    private Long bindId;   //null
    private String state;   //null 
    private Timestamp createTime;   //null
    private Timestamp updateTime;   //null
    private String type;   //null
    private String remark;   //null 

    public void init(){
        if(null == createTime)
            createTime = new Timestamp(new Date().getTime());
        if(StringUtils.isEmpty(state))
            state=STATE_1;
    }
}

