package com.jsunicom.api.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.api.common.ApiResultEntity;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.common.result.ResultUtil;
import com.jsunicom.api.entity.Campus.Campus;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.po.*;
import com.jsunicom.api.service.*;
import com.jsunicom.api.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/woschool/innovate/")
public class WoScYouthInnovateController extends BaseController{

    @Autowired
    private WoScYouthInnovateFacade woschoolInnovateFacade;


    @Autowired
    private MerchantInfoFacada merchantInfoFacada;

    @Autowired
    private PartnerInfoFacade partnerInfoFacade;

    @Autowired
    private CheckInfoService checkInfoService;

    @Autowired
    private ImageOperService imageOperService;

    @Autowired
    private CampusFacade campusService;

    @Autowired
    private SchoolCampusCollegeFacade schoolCampusCollegeFacade;

    @Autowired
    private SmsFacade smsService;

    @Value("${common.object.cos.urlSuffix:0}")
    private String urlSuffix;

    @RequestMapping(value = "/createWoScYouthInnovate", method = RequestMethod.POST, name = "创建社团")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity createWoScYouthInnovate(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入createWoScYouthInnovate方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyName") || !hashMap.containsKey("campusId")
            ){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            WoScYouthInnovateBase record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), WoScYouthInnovateBase.class);

            Partner partner = getCurrentUser(request);
            if(null != partner){
                record.setCreatedBy(partner.getMblNbr());
            }

            record.setCreatedTime(new Date());
            woschoolInnovateFacade.insertSelective(record);

        }catch (Exception e){
            log.error("createWoScYouthInnovate方法异常："+e.getMessage());
            error = "创建社团异常";
            return  getErrorEntity("创建社团异常"+e.getMessage());
        }

        return getSuccessEntity();

    }

    @RequestMapping(value = "/updateWoScYouthInnovate", method = RequestMethod.POST, name = "编辑社团")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity updateWoScYouthInnovate(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入updateWoScYouthInnovate方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyId") || !hashMap.containsKey("updatedBy")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            WoScYouthInnovateBase record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), WoScYouthInnovateBase.class);

            Partner partner = getCurrentUser(request);
            if(null != partner){
                record.setUpdatedBy(partner.getMblNbr());
            }

            record.setUpdatedTime(new Date());
            woschoolInnovateFacade.updateInnovateInfo(record);

        }catch (Exception e){
            log.error("createWoScYouthInnovate方法异常："+e.getMessage());
            error = "更新社团异常";
            return  getErrorEntity("更新社团异常"+e.getMessage());
        }

        return getSuccessEntity();

    }


    @RequestMapping(value = "/deleteWoScYouthInnovate", method = RequestMethod.POST, name = "删除社团")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity deleteWoScYouthInnovate(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入deleteWoScYouthInnovate方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyId") ){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            Long societyId = Long.parseLong(hashMap.get("societyId").toString());

            long merchantCount = merchantInfoFacada.countByExample(societyId);
            if(merchantCount > 0){
                error = "该社团下已经存在团队，不能删除！";
                return  getErrorEntity(error);
            }

            woschoolInnovateFacade.deleteInnovateInfo(societyId);

        }catch (Exception e){
            log.error("createWoScYouthInnovate方法异常："+e.getMessage());
            error = "删除社团异常！";
            return  getErrorEntity("删除社团异常："+e.getMessage());
        }

        return getSuccessEntity();

    }




    @RequestMapping(value = "/getInnovateList", method = RequestMethod.POST, name = "社团列表查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity geInnovateList(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入geInnovateList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("campusId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }
            int pageNum = 1;
            int pageSize = 500;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            Long campusId = Long.parseLong(hashMap.get("campusId").toString());

            List<WoInnovateListInfo> resultList =  woschoolInnovateFacade.selectInnovateList(campusId);

            PageInfo<WoInnovateListInfo> pageInfo = new PageInfo<>(resultList);

            // 图片需要特殊处理
           for(WoInnovateListInfo temp : resultList){

               String url = temp.getSocietyLogoUrl();
               temp.setSocietyLogoUrl(urlSuffix+url);

                Long societyId = temp.getSocietyId();
                PartnerInfo record = new PartnerInfo();
                record.setSocietyId(String.valueOf(societyId));
                record.setPartnerType("2");
                record.setState("1");
                long  total = partnerInfoFacade.countByExample(record);
                temp.setMembers(total);
            }

            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", pageInfo.getList());
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("geInnovateList方法异常："+e.getMessage());
            error = "社团列表查询异常";
            return  getErrorEntity("社团列表查询异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/createInnovateLabor", method = RequestMethod.POST, name = "社长注册")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity createInnovateLabor(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createInnovateLabor方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("partnerCertNo") || !hashMap.containsKey("createBy") || !hashMap.containsKey("mblNbr")
                    || !hashMap.containsKey("partnerName") || !hashMap.containsKey("societyId")|| !hashMap.containsKey("verificationCode")
                    ||  !hashMap.containsKey("orgCode") || !hashMap.containsKey("campusId") ||  !hashMap.containsKey("collegeId")
                    ||  !hashMap.containsKey("pic1") ||  !hashMap.containsKey("pic2")  ||  !hashMap.containsKey("pic3")
                    ){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }


            String mobile = hashMap.get("mblNbr").toString();
            String verificationCode = hashMap.get("verificationCode").toString();

            smsService.checkCode(mobile, verificationCode);

            // 国政通校验：校验姓名与证件号是否一致
            boolean gztTag = checkInfoService.checkGztCode(hashMap);
            if(!gztTag){
                return ApiResultEntity.FAILURE("身份信息校验失败");
            }


            // d)校验注册人员信息是否在表中待审核、已审核状态存在，如果已存在给出提示
            PartnerInfo record = new PartnerInfo();
            record.setMblNbr(hashMap.get("mblNbr").toString());
            record.setState("0|1");
            record.setPartnerCertNo(hashMap.get("partnerCertNo").toString());
            long partnerCount = partnerInfoFacade.countOrByExample(record);
            if(partnerCount>0){
                return getErrorEntity("该用户已存在");
            }

            PartnerInfo partnerRecord = JSONObject.parseObject(JSONObject.toJSONString(hashMap), PartnerInfo.class);

            Partner partner = getCurrentUser(request);
            if(null != partner){
                partnerRecord.setCreateBy(partner.getMblNbr());
                partnerRecord.setOrgCode(partner.getOrgCode());
            }

            partnerRecord.setState("0");
            partnerRecord.setPartnerType("2");
            partnerRecord.setAcctNo(partnerRecord.getMblNbr());
            partnerRecord.setIsMerAdmin("2");
            partnerRecord.setCreateTime(new Date());
            partnerRecord.setBusLine("20");
            partnerRecord.setReserve1("1");
            partnerInfoFacade.insertPartnerInfo(partnerRecord);


        }catch (Exception e){
            log.error("createWoScYouthInnovate方法异常："+e.getMessage());
            error = "创建社团异常";
            return  getErrorEntity("创建社团异常:"+e.getMessage());
        }

        return getSuccessEntity();


    }


    @RequestMapping(value = "/changeInnovateLabor", method = RequestMethod.POST, name = "更换社长和队长")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity changeInnovateLabor(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createInnovateLabor方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("oldPartnerId") || !hashMap.containsKey("newPartnerId")
                    || !hashMap.containsKey("updateBy") || !hashMap.containsKey("operTag")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            long oldPartnerId = Long.parseLong(hashMap.get("oldPartnerId").toString());
            PartnerInfo oldPartnerInfo = partnerInfoFacade.selectByPrimaryKey(oldPartnerId);

            String oldIsMerAdmin = oldPartnerInfo.getIsMerAdmin();
            Long oldMerchantId = oldPartnerInfo.getMerchantId();
            String oldSocietyId = oldPartnerInfo.getSocietyId();


            long newPartnerId = Long.parseLong(hashMap.get("newPartnerId").toString());
            PartnerInfo newPartnerInfo = partnerInfoFacade.selectByPrimaryKey(newPartnerId);

            // 是否实名  1 是  0 否
            String newReserve1 = newPartnerInfo.getReserve1();
            if(StringUtil.isEmpty(newReserve1) || "0".equals(newReserve1)){
                error = "变更的角色，未进行实名，不可变更！";
                return  getErrorEntity(error);
            }

            String newIsMerAdmin = newPartnerInfo.getIsMerAdmin();
            Long newMerchantId = newPartnerInfo.getMerchantId();
            String newSocietyId = newPartnerInfo.getSocietyId();

            // 1 团队变更  0 社团变更
            String operTag =  hashMap.get("operTag").toString();
            String updateBy =  hashMap.get("updateBy").toString();


            oldPartnerInfo.setIsMerAdmin(newIsMerAdmin);
            oldPartnerInfo.setMerchantId(newMerchantId);
            oldPartnerInfo.setSocietyId(newSocietyId);
            oldPartnerInfo.setUpdateTime(new Date());

            newPartnerInfo.setIsMerAdmin(oldIsMerAdmin);
            newPartnerInfo.setMerchantId(oldMerchantId);
            newPartnerInfo.setSocietyId(oldSocietyId);
            newPartnerInfo.setUpdateTime(new Date());

            Partner partner = getCurrentUser(request);
            if(null != partner){
                newPartnerInfo.setUpdateBy(partner.getMblNbr());
                oldPartnerInfo.setUpdateBy(partner.getMblNbr());
            }else{
                newPartnerInfo.setUpdateBy(updateBy);
                oldPartnerInfo.setUpdateBy(updateBy);
            }

            partnerInfoFacade.updateByPrimaryKey(newPartnerInfo);

            partnerInfoFacade.updateByPrimaryKey(oldPartnerInfo);

        }catch (Exception e){
            log.error("changeInnovateLabor方法异常："+e.getMessage());
            error = "更换社长异常";
            return  getErrorEntity("更换社长异常"+e.getMessage());
        }

        return getSuccessEntity();


    }


    @RequestMapping(value = "/getPartnerList", method = RequestMethod.POST, name = "社团成员列表")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getPartnerList(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入getPartnerList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }
            int pageNum = 1;
            int pageSize = 500;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            String societyId = hashMap.get("societyId").toString();
            PartnerInfo record = new PartnerInfo();
            record.setSocietyId(societyId);
            record.setState("1");
            record.setIsMerAdmin("0|1");
            if(hashMap.containsKey("partnerName")){
                record.setPartnerName(hashMap.get("partnerName").toString());
            }

            List<PartnerInfo> partnerInfoList = partnerInfoFacade.getPartnerInfoList(record);
            PageInfo<PartnerInfo> pageInfo = new PageInfo<>(partnerInfoList);

            List<WoInnovateListInfo> resultList = new ArrayList<>();
            for(PartnerInfo partnerInfo : partnerInfoList){
                WoInnovateListInfo partnerInfoTemp = new WoInnovateListInfo();
                partnerInfoTemp.setPartnerId(partnerInfo.getId());
                partnerInfoTemp.setPartnerName(partnerInfo.getPartnerName());
                partnerInfoTemp.setMblNbr(partnerInfo.getMblNbr());
                partnerInfoTemp.setIsMerAdmin(partnerInfo.getIsMerAdmin());
                partnerInfoTemp.setMerchantName(partnerInfo.getMerchantName());
                log.info("partnerInfoTemp:"+ JSONObject.toJSONString(partnerInfoTemp));
                resultList.add(partnerInfoTemp);
            }

            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", resultList);
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getPartnerList方法异常："+e.getMessage());
            error = "获取社团成员列表异常";
            return  getErrorEntity("获取社团成员列表异常:"+e.getMessage());
        }

    }



    @RequestMapping(value = "/getBaseInfoList", method = RequestMethod.POST, name = "院系列表")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getBaseInfoList(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入getBaseInfoList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            HashMap<String, Object> dataMap = new HashMap<>();

            MerchantInfo merchantInfo = null;
            if(hashMap.containsKey("merchantId") && StringUtil.isNotEmpty(hashMap.get("merchantId").toString())){
                Long merchantId =  Long.parseLong(hashMap.get("merchantId").toString());
                merchantInfo = merchantInfoFacada.selectByPrimaryKey(merchantId);
            }

            Long societyId =  Long.parseLong(hashMap.get("societyId").toString());
            WoScYouthInnovateBase  woschoolInnovateBase = woschoolInnovateFacade.selectByPrimaryKey(societyId);
            if( woschoolInnovateBase != null  ){
                long campusId = woschoolInnovateBase.getCampusId();

                JSONArray jsonArray = new JSONArray();

                List<SchoolCampusCollege>  collegeList = schoolCampusCollegeFacade.queryInfoList(campusId);
                for (SchoolCampusCollege schoolCampusCollege : collegeList){
                    String collegeName = schoolCampusCollege.getCollegeName();
                    long collegeId = schoolCampusCollege.getCollegeId();
                    JSONObject object = new JSONObject();
                    object.put("collegeName", collegeName);
                    object.put("collegeId", collegeId);
                    jsonArray.add(object);
                }

                Campus campus = campusService.query(campusId);
                if(campus != null){
                    dataMap.put("campusName", campus.getCampusName());
                    dataMap.put("campusId", campusId);
                }
                dataMap.put("societyName", woschoolInnovateBase.getSocietyName());
                dataMap.put("societyId", societyId);
                dataMap.put("campusList",jsonArray);
                if(merchantInfo != null) {
                    dataMap.put("merchantName", merchantInfo.getMerchantName());
                }
            }

            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getBaseInfoList方法异常："+e.getMessage());
            error = "获取院系列表异常";
            return  getErrorEntity("获取院系列表异常"+e.getMessage());
        }

    }



    @RequestMapping(value = "/checkGztCode", method = RequestMethod.POST, name = "国政通验证")
    public ApiResultEntity checkGztCode(@RequestBody HashMap hashMap, HttpServletRequest request)
    {

        // 国政通校验：校验姓名与证件号是否一致
        boolean gztTag = checkInfoService.checkGztCode(hashMap);
        if(!gztTag){
            return ApiResultEntity.FAILURE("身份信息校验失败");
        }
        return ApiResultEntity.SUCCESS();
    }

    @RequestMapping(value = "/getInnovateInfo", method = RequestMethod.POST, name = "队员管理-社团信息查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getInnovateInfo(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入getInnovateInfo方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            Partner partner = getCurrentUser(request);
            if(null == partner){
                return getErrorEntity("缺少人员注册信息，请联系管理员！");
            }
            String isMerAdmin = partner.getIsMerAdmin();
            if (!"1".equals(isMerAdmin)) {
                // 备注
                return getErrorEntity("您非团队长，没有权限操作！");
            }

            String societyIdStr = partner.getSocietyId();
            if(StringUtil.isEmpty(societyIdStr)){
                return getErrorEntity("缺少团队信息，请确认是否创建团队！");
            }

            int pageNum = 1;
            int pageSize = 500;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            Long societyId = Long.parseLong(partner.getSocietyId());

            List<WoInnovateListInfo> resultList =  woschoolInnovateFacade.selectPartnerList(societyId);

            PageInfo<WoInnovateListInfo> pageInfo = new PageInfo<>(resultList);

            // 图片需要特殊处理
            for(WoInnovateListInfo temp : resultList){

                String url = temp.getSocietyLogoUrl();
                temp.setSocietyLogoUrl(urlSuffix+url);

                Long merchantId = partner.getMerchantId();
                MerchantInfo merchantInfo = merchantInfoFacada.selectByPrimaryKey(merchantId);
                if(null != merchantInfo){
                    temp.setMerchantName(merchantInfo.getMerchantName());
                }else {
                    temp.setMerchantName("");
                }
                temp.setMerchantId(merchantId);

                PartnerInfo record = new PartnerInfo();
                record.setMerchantId(merchantId);
                record.setPartnerType("2");
                record.setState("1");
                long  total = partnerInfoFacade.countByExample(record);
                temp.setMembers(total);
            }

            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", pageInfo.getList());
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getInnovateInfo方法异常："+e.getMessage());
            error = "社团信息查询异常";
            return  getErrorEntity("社团信息查询异常:"+e.getMessage());
        }

    }



}
