package com.jsunicom.api.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.api.entity.goods.Goods;
import com.jsunicom.api.entity.goods.WoGoodsDto;
import com.jsunicom.api.entity.goods.WoScCreativeItemManagement;
import com.jsunicom.api.entity.goods.WoSchoolShareInfo;
import com.jsunicom.api.entity.partner.Partner;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface GoodsFacade {

    // 根据主键查询
    public Goods get(Long id);


    PageInfo<WoGoodsDto> findWoSchoolGoodsList(WoGoodsDto goodsDto, Integer pageNo, Integer pageSize);
    List<WoGoodsDto> findWoSchoolInfo(Long schoolId);
    long addWoSchoolShareInfo(WoSchoolShareInfo goodsInfo, HttpServletRequest request);
    List<WoScCreativeItemManagement> findWoScCreativeItems(WoScCreativeItemManagement info, Partner partner);

}
