package com.jsunicom.api.model;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> by ouyang<PERSON><PERSON> on 2018/1/29.
 * @Date 2018/1/29 10:47
 * 功能描述：xxxx
 */
public class OrgInfoTreeNode implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键
    private Long id;

    // 上级机构编码
    private String parentOrgCode;

    // 机构编码
    private String orgCode;

    // 全路径编码
    private String fullOrgCode;

    // 机构名称
    private String orgName;

    // 企业号的组织编码
    private String wxEntpCode;

    // 状态
    private String state;

    // 创建人
    private String createBy;

    // 创建时间
    private String createTime;

    // 更新人
    private String updateBy;

    // 更新时间
    private String updateTime;

    // 是否是叶子节点
    private String isleaf;

    // 子节点集合
    private List<OrgInfoTreeNode> children;

    // 排序字段
    private int orderBy;

    // 是否为地市
    private String isCity;

    public OrgInfoTreeNode() {
    }

    public OrgInfoTreeNode(String orgCode, String orgName) {
        this.orgName = orgName;
        this.orgCode = orgCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentOrgCode() {
        return parentOrgCode;
    }

    public void setParentOrgCode(String parentOrgCode) {
        this.parentOrgCode = parentOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getFullOrgCode() {
        return fullOrgCode;
    }

    public void setFullOrgCode(String fullOrgCode) {
        this.fullOrgCode = fullOrgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getWxEntpCode() {
        return wxEntpCode;
    }

    public void setWxEntpCode(String wxEntpCode) {
        this.wxEntpCode = wxEntpCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getIsleaf() {
        return isleaf;
    }

    public void setIsleaf(String isleaf) {
        this.isleaf = isleaf;
    }

    public List<OrgInfoTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<OrgInfoTreeNode> children) {
        this.children = children;
    }

    public int getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(int orderBy) {
        this.orderBy = orderBy;
    }

    public String getIsCity() {
        return isCity;
    }

    public void setIsCity(String isCity) {
        this.isCity = isCity;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this);
    }
}
