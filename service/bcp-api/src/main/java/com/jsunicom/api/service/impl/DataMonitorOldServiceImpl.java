package com.jsunicom.api.service.impl;

import com.jsunicom.api.entity.dataMonitor.DataMonitor;
import com.jsunicom.api.mapper.DataMonitorDao;
import com.jsunicom.api.model.base.BasicModel;
import com.jsunicom.api.service.DataMonitorFacade;
import com.jsunicom.api.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-12-16:33
 */
@Slf4j
@Service
public class DataMonitorOldServiceImpl implements DataMonitorFacade {
    @Autowired
    private DataMonitorDao dataMonitorDao;

    @Override
    public DataMonitor save(DataMonitor dataMonitor) {
        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(operateDesc);
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());
        int cnt=0;
        try{
            cnt = dataMonitorDao.save(dataMonitor);
        }catch (Exception e){
            log.error("数据监控保存异常",e);
        }
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    /**
     *
     * add by yangjinsong  2019-5-30
     *
     * **/
    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc,String preOperateDesc) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(operateDesc);
        dataMonitor.setPreviousOperateDesc(preOperateDesc);
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());
        int cnt=0;
        try{
            cnt = dataMonitorDao.save(dataMonitor);
        }catch (Exception e){
            log.error("数据监控保存异常",e);
        }
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }



    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, BasicModel model) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(model.toString());
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());

        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, BasicModel model,BasicModel previousModel) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(model!=null?model.toString():"");
        dataMonitor.setPreviousOperateDesc(previousModel!=null?previousModel.toString():"");
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());

        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }
}
