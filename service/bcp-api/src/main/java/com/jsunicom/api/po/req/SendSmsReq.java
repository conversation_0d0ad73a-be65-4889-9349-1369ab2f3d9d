package com.jsunicom.api.po.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SendSmsReq implements Serializable {
    private static final long serialVersionUID = 1L;

       /**
     * 模板id
     */
    @NotNull(message = "请求参数模板id为空")
    private String templateId;

    /**
     * 短信发送可变的参数，多个参数之间以|分隔；参数不能为空；参数中严禁包含|特殊符号
     */
    private String sendParam;

    /**
     * 接收短信手机号码，多个号码之间以“,”分隔,最多1000个
     */
    @NotNull(message = "请求参数接收短信手机号码为空")
    private String sendTo;

    /**
     * 内网模板参数，非必填，仅用于内外网参数个数不
     */
    private String paramUnicom;

    private String orderId;

    private String checkNumNet;
}
