package com.jsunicom.api.mapper;

import com.jsunicom.api.po.WoSchoolCampusDormitory;
import com.jsunicom.api.po.WoSchoolCampusDormitoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WoSchoolCampusDormitoryMapper {
    long countByExample(WoSchoolCampusDormitoryExample example);

    int deleteByExample(WoSchoolCampusDormitoryExample example);

    int deleteByPrimaryKey(Long dormitoryId);

    int insert(WoSchoolCampusDormitory record);

    int insertSelective(WoSchoolCampusDormitory record);

    List<WoSchoolCampusDormitory> selectByExample(WoSchoolCampusDormitoryExample example);

    List<WoSchoolCampusDormitory> selectByExampleFoCustom(WoSchoolCampusDormitoryExample example);

    WoSchoolCampusDormitory selectByPrimaryKey(Long dormitoryId);

    int updateByExampleSelective(@Param("record") WoSchoolCampusDormitory record, @Param("example") WoSchoolCampusDormitoryExample example);

    int updateByExample(@Param("record") WoSchoolCampusDormitory record, @Param("example") WoSchoolCampusDormitoryExample example);

    int updateByPrimaryKeySelective(WoSchoolCampusDormitory record);

    int updateByPrimaryKey(WoSchoolCampusDormitory record);
}