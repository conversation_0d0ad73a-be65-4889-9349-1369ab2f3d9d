package com.jsunicom.api.mapper;

import com.jsunicom.api.po.SchoolCampusCollege;
import com.jsunicom.api.po.SchoolCampusCollegeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SchoolCampusCollegeMapper {
    long countByExample(SchoolCampusCollegeExample example);

    int deleteByExample(SchoolCampusCollegeExample example);

    int deleteByPrimaryKey(Long collegeId);

    int insert(SchoolCampusCollege record);

    int insertSelective(SchoolCampusCollege record);

    List<SchoolCampusCollege> selectByExample(SchoolCampusCollegeExample example);

    SchoolCampusCollege selectByPrimaryKey(Long collegeId);

    int updateByExampleSelective(@Param("record") SchoolCampusCollege record, @Param("example") SchoolCampusCollegeExample example);

    int updateByExample(@Param("record") SchoolCampusCollege record, @Param("example") SchoolCampusCollegeExample example);

    int updateByPrimaryKeySelective(SchoolCampusCollege record);

    int updateByPrimaryKey(SchoolCampusCollege record);
}