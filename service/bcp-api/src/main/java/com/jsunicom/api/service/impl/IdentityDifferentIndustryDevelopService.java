package com.jsunicom.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.common.utils.EncryptUtil;
import com.jsunicom.api.common.utils.HttpCall;
import com.jsunicom.api.common.utils.RequestUtil;
import com.jsunicom.api.model.dto.PartnerDto;
import com.jsunicom.api.service.IdentityDifferentIndustryDevelopFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: IdentityDifferentIndustryDevelopService
 * @Author: zhaowang
 * @CreateTime: 2023-04-12  16:10
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class IdentityDifferentIndustryDevelopService implements IdentityDifferentIndustryDevelopFacade {
    @Value("${identitySecondAppKey}")
    private String identitySecondAppKey;

    @Value("${identitySecondAppSecret}")
    private String identitySecondAppSecret;

    //访问总部接口--APP_ID
    @Value("${APP_ID_UNI_BSS}")
    private String APP_ID_UNI_BSS;

    @Value("${identitySecond.differentIndustryDevelopUrl}")
    private String differentIndustryDevelopUrl;

    @Override
    public String getDevelopmenterInfoByIdentity(PartnerDto partner) {
        String decryptResponseStr="";
        JSONObject uniBssHead = RequestUtil.createRequestHead(identitySecondAppKey);
        try {
            JSONObject requestAttached = this.createRequestAttached();
            JSONObject developmenterSyncReq = new JSONObject();
            developmenterSyncReq.put("PARTNER_ID",String.valueOf(partner.getId()));
            developmenterSyncReq.put("DEV_TYPE_ID",partner.getDevTypeId());
            //developmenterSyncReq.put("CHNL_ID",partner.getMappingPointCode());
            developmenterSyncReq.put("BUSI_TYPE","0");
            //developmenterSyncReq.put("FUNC_SINGLE_VALUE",partner.getBusinessClassification());
            developmenterSyncReq.put("LINKMAN_NAME",partner.getPartnerName());
            developmenterSyncReq.put("LINKMAN_PHONE",partner.getMblNbr());
            developmenterSyncReq.put("CERT_TYPE","1");
            developmenterSyncReq.put("USER_PID",partner.getPartnerCertNo());
            //developmenterSyncReq.put("OPER_TYPE",partner.getOperateType());
            String provName=partner.getProvName();
            String cityName=partner.getCityName();
            String areaName=partner.getAreaName();
            String address=partner.getAddress();
            String devAreaCode=provName+cityName+areaName;
            developmenterSyncReq.put("DEV_AREA_CODE",devAreaCode);
            developmenterSyncReq.put("LINKMAN_ADDR",address);
            if("02".equals(partner.getDevTypeId())){
                //developmenterSyncReq.put("DEV_NAME",partner.getMerchantName());
            }else{
                developmenterSyncReq.put("DEV_NAME",partner.getPartnerName());
            }
            developmenterSyncReq.put("DATA_SOURCE",APP_ID_UNI_BSS);
            /*if("02".equals(partner.getOperateType()) || "03".equals(partner.getOperateType())){
                developmenterSyncReq.put("DEV_CODE",partner.getDevelId());
            }*/
            JSONObject requestJson = new JSONObject();
            JSONObject uniBssBodyExternal = new JSONObject();
            uniBssBodyExternal.put("DEVELOPMENTER_SYNC_REQ",developmenterSyncReq); //消息体
            uniBssBodyExternal.put("UNI_BSS_ATTACHED",requestAttached); //附加消息
            requestJson.put("UNI_BSS_BODY",uniBssBodyExternal);
            requestJson.put("UNI_BSS_HEAD",uniBssHead);
            EncryptUtil encryptUtil=new EncryptUtil();
            log.info("------开始请求江苏联通能力平台异业发展人导入服务接口，请求url：{},---未加密前的请求报文：{}",differentIndustryDevelopUrl, requestJson.toJSONString());
            String encryptRequestStr=encryptUtil.encryptRequest(requestJson.toJSONString(),identitySecondAppSecret,"json");
            log.info("------请求江苏联通能力平台异业发展人导入服务接口，加密后的请求报文：{}",encryptRequestStr);
            JSONObject encryptRequestJson=JSONObject.parseObject(encryptRequestStr);
            String responseStr = new HttpCall().httpPostWithJSON2(differentIndustryDevelopUrl, encryptRequestJson, "UTF-8", "UTF-8");
            log.info("------江苏联通能力平台异业发展人导入服务接口成功返回,获取返回加密后的报文：{}",responseStr);
            decryptResponseStr=encryptUtil.decryptResponse(responseStr,identitySecondAppSecret,"json");
            log.info("------江苏联通能力平台异业发展人导入服务接口成功返回,请求url：{},---请求报文：{},----返回解密报文：{}",differentIndustryDevelopUrl,requestJson.toJSONString(),decryptResponseStr);
        } catch (Exception e) {
            log.error("------调用江苏联通能力平台异业发展人导入服务接口出现异常，异常信息为：{}",e);
        }
        return decryptResponseStr;
    }

    //创建附加消息
    private JSONObject createRequestAttached() {
        JSONObject UNI_BSS_ATTACHED = new JSONObject();
        UNI_BSS_ATTACHED.put("MEDIA_INFO","");
        return UNI_BSS_ATTACHED;
    }
}
