package com.jsunicom.api.mapper;

import com.jsunicom.api.mapper.basic.BasicDao;
import com.jsunicom.api.model.PartnerPromotActv;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PartnerPromotActvDao extends BasicDao {

    //根据合伙人id删记录  杨劲松  2018-5-10
    @Override
    int deleteByPK(@Param("id") Long id);

    List<PartnerPromotActv> findByPartnerId(@Param("partnerId") Long partnerId);

    int deleteByPartnerId(@Param("partnerId") Long partnerId);


}
