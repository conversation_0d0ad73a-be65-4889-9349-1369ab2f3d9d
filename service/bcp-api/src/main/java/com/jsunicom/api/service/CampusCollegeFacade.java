package com.jsunicom.api.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.api.entity.Campus.Campus;
import com.jsunicom.api.entity.Campus.CampusBind;
import com.jsunicom.api.po.WoSchoolCampusCollege;

import java.util.List;

/**
 * .
 *
 * @ClassName: CampusFacade
 * @Auther: LJ<PERSON>
 * @Date: 2022/3/19 15:33
 * @Version: bcpV1.0
 * @Description: campus facade
 **/
public interface CampusCollegeFacade {

  /**
   *
   * @param campusId
   * @return
   */
  WoSchoolCampusCollege queryInfo(Long campusId);
}
