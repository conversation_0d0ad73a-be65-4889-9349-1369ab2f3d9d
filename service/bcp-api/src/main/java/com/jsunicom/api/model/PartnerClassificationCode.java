package com.jsunicom.api.model;

import com.jsunicom.api.model.base.BasicModel;

import java.util.Date;

public class PartnerClassificationCode extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;   //主键
    private Long parentId;   //父级分类
    private String name;   //分类名称
    private String isLeaf;   //是否叶子结点,Y-是 N-否
    private String desc;   //分类描述
    private String isDefault;   //是否默认选项，Y-是 N-否，统一上级分类下只允许一个默认下级分类
    private String createBy;   //创建人
    private Date createTime;   //创建时间
    private String updateBy;   //更新人
    private Date updateTime;   //更新时间


    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public Long getParentId() {
        return parentId;
   }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
   }

    public String getName() {
        return name;
   }

    public void setName(String name) {
        this.name = name;
   }

    public String getIsLeaf() {
        return isLeaf;
   }

    public void setIsLeaf(String isLeaf) {
        this.isLeaf = isLeaf;
   }

    public String getDesc() {
        return desc;
   }

    public void setDesc(String desc) {
        this.desc = desc;
   }

    public String getIsDefault() {
        return isDefault;
   }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
   }

    public String getCreateBy() {
        return createBy;
   }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
   }

    public Date getCreateTime() {
        return createTime;
   }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
   }

    public String getUpdateBy() {
        return updateBy;
   }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
   }

    public Date getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
   }

}

