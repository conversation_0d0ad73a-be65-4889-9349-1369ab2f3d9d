package com.jsunicom.api.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 路由类配置
 */
@Configuration
public class WhiteListConfig {
    public static final long DEFAULT_TIMEOUT = 30000;

    public static String NACOS_SERVER_ADDR;

    public static String NACOS_NAMESPACE;

    public static String NACOS_USERNAME;

    public static String NACOS_PASSWORD;

    public static String NACOS_WHITELIST_DATA_ID;

    public static String NACOS_WHITELIST_GROUP;

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    public void setNacosServerAddr(String nacosServerAddr){
        NACOS_SERVER_ADDR = nacosServerAddr;
    }

    @Value("${spring.cloud.nacos.discovery.namespace}")
    public void setNacosNamespace(String nacosNamespace){
        NACOS_NAMESPACE = nacosNamespace;
    }

    @Value("${spring.cloud.nacos.config.username}")
    public void setNacosUsername(String username){
        NACOS_USERNAME = username;
    }

    @Value("${spring.cloud.nacos.config.password}")
    public void setNacosPassword(String password){
        NACOS_PASSWORD = password;
    }

    @Value("${nacos.gateway.config.whitelist.data-id}")
    public void setNacosWhitelistDataId(String nacosWhitelistDataId) {
        NACOS_WHITELIST_DATA_ID = nacosWhitelistDataId;
    }
    @Value("${nacos.gateway.config.whitelist.group}")
    public void setNocosWhitelistGroup(String nocosWhitelistGroup) {
        NACOS_WHITELIST_GROUP = nocosWhitelistGroup;
    }
}