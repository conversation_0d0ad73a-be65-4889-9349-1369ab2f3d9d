package com.jsunicom.api.model;


import com.jsunicom.api.model.base.BasicModel;

public class <PERSON>listPartner extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String staffName;
    private String staffMsisdn;
    private String orgCode;
    private String develId;
    private String certNo;
    private String state;
    private String type;
    private String createBy;
    private java.sql.Timestamp createTime;
    private String updateBy;
    private java.sql.Timestamp updateTime;

    private String orgName;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public String getStaffName() {
        return staffName;
   }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
   }

    public String getStaffMsisdn() {
        return staffMsisdn;
   }

    public void setStaffMsisdn(String staffMsisdn) {
        this.staffMsisdn = staffMsisdn;
   }

    public String getOrgCode() {
        return orgCode;
   }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
   }

    public String getDevelId() {
        return develId;
   }

    public void setDevelId(String develId) {
        this.develId = develId;
   }

    public String getCertNo() {
        return certNo;
   }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
   }

    public String getState() {
        return state;
   }

    public void setState(String state) {
        this.state = state;
   }

    public String getType() {
        return type;
   }

    public void setType(String type) {
        this.type = type;
   }

    public String getCreateBy() {
        return createBy;
   }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
   }

    public java.sql.Timestamp getCreateTime() {
        return createTime;
   }

    public void setCreateTime(java.sql.Timestamp createTime) {
        this.createTime = createTime;
   }

    public String getUpdateBy() {
        return updateBy;
   }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
   }

    public java.sql.Timestamp getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(java.sql.Timestamp updateTime) {
        this.updateTime = updateTime;
   }

}

/*List columns as follows:
"id", "staff_name", "staff_msisdn", "org_code", "devel_id", "cert_no", "state", 
"type", "create_by", "create_time", "update_by", "update_time"
*/