package com.jsunicom.api.controller;

import com.jsunicom.api.common.ApiResultEntity;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.common.result.ResultUtil;
import com.jsunicom.api.po.Dict;
import com.jsunicom.api.service.DictFacade;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.common.cos.PublicCosClientUtil;
import com.jsunicom.common.sms.common.MessageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.controller
 * @ClassName: ImageController
 * @Author: zhaowang
 * @CreateTime: 2023-04-12  17:20
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/dict")
public class DictController extends BaseController{
   @Autowired
   private DictFacade dictFacade;

    @GetMapping("/getDictsByKind")
    @DecryptApi
    @EncryptApi
    public ArrayList<Dict> getDictsByKind(@RequestParam(value = "kind") String kind){
        return dictFacade.getDictsByKind(kind);
    }

    @GetMapping("/getDictsByKindNew")
    @DecryptApi
    @EncryptApi
    public CustomResult getDictsByKindNew(@RequestParam(value = "kind") String kind){
        return ResultUtil.success(dictFacade.getDictsByKind(kind));
    }
}
