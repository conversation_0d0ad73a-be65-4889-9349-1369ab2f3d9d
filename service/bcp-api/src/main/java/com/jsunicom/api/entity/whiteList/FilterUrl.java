package com.jsunicom.api.entity.whiteList;

import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 */
public class FilterUrl {
    private String url;
    private HttpMethod method;

    public FilterUrl(String url, HttpMethod method) {
        this.url = url;
        this.method = method;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public HttpMethod getMethod() {
        return method;
    }

    public void setMethod(HttpMethod method) {
        this.method = method;
    }
}
