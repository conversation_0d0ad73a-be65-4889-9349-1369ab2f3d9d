package com.jsunicom.api.mapper;

import com.jsunicom.api.entity.goods.WoGoodsDto;
import com.jsunicom.api.mapper.basic.BasicDao;
import com.jsunicom.api.model.WoSchoolGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-11-11:00
 */
public interface WoSchoolGoodsDao extends BasicDao {
    List<WoSchoolGoods> loadGoodsInfo(@Param("schoolId") Long schoolId, @Param("goodsId") Long goodsId);

    WoSchoolGoods findWoSchoolGoodsInfo(@Param("schoolId") Long schoolId, @Param("goodsId") Long goodsId);

    List<WoGoodsDto> listGoodsBySchoolId(@Param("schoolId") Long schoolId);

}
