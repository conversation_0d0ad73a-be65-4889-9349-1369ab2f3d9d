package com.jsunicom.api.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.common.utils.RsaUtils;

import java.nio.charset.StandardCharsets;

/**
 *  <AUTHOR>
 *  @date 2019-11-27
 */
public class EncryptUtil {

    private final String bodyKey = "UNI_BSS_BODY";

    /**
     * 客户端 公钥 加密 请求报文
     * @param content 请求报文
     * @param publicKey 公钥
     */
    public String encryptRequest(String content, String publicKey, String type) throws Exception {

        type = type.toLowerCase();
        switch (type) {
            case "json": {
                JSONObject cj = JSON.parseObject(content);
                JSONObject bodyObj = cj.getJSONObject(bodyKey);
                if (bodyObj == null) {
                    return content;
                }
                String bstr = bodyObj.toJSONString();
                String ebstr = com.jsunicom.api.common.utils.RsaUtils.encryptBase64(com.jsunicom.api.common.utils.RsaUtils.encryptByPublicKey(bstr.getBytes(StandardCharsets.UTF_8), publicKey));
                cj.put(bodyKey, ebstr);
                content = cj.toJSONString();
                break;
            }
            default:
        }

        return content;
    }

    /**
     * 服务端 私钥 加密 响应报文
     * @param content 响应报文
     * @param privateKey 私钥
     */
    public String encryptResponse(String content, String privateKey, String type) throws Exception {

        type = type.toLowerCase();
        switch (type) {
            case "json": {
                JSONObject cj = JSON.parseObject(content);
                JSONObject bodyObj = cj.getJSONObject(bodyKey);
                if (bodyObj == null) {
                    return content;
                }
                String bstr = bodyObj.toJSONString();
                String ebstr = com.jsunicom.api.common.utils.RsaUtils.encryptBase64(com.jsunicom.api.common.utils.RsaUtils.encryptByPrivateKey(bstr.getBytes(StandardCharsets.UTF_8), privateKey));
                cj.put(bodyKey, ebstr);
                content = cj.toJSONString();
                break;
            }
            default:
        }

        return content;
    }

    /**
     * server端 私钥 解密 请求报文
     * @param content 请求报文
     * @param privateKey 私钥
     */
    public String decryptRequest(String content, String privateKey, String type) throws Exception {

        type = type.toLowerCase();
        switch (type) {
            case "json": {
                JSONObject cj = JSON.parseObject(content);
                String bstr = cj.getString(bodyKey);
                if (bstr == null) {
                    //如果没有body返回原报文
                    return content;
                }
                bstr = new String(com.jsunicom.api.common.utils.RsaUtils.decryptByPrivateKey(com.jsunicom.api.common.utils.RsaUtils.decryptBase64(bstr), privateKey), StandardCharsets.UTF_8);
                JSONObject bj = JSON.parseObject(bstr);
                cj.put(bodyKey, bj);
                content = cj.toJSONString();
                break;
            }
            default:

        }

        return content;
    }

    /**
     * 客户端 公钥 解密 响应报文
     * @param content 响应报文
     * @param publicKey 公钥
     */
    public String decryptResponse(String content, String publicKey, String type) throws Exception {
        type = type.toLowerCase();
        switch (type) {
            case "json": {
                JSONObject cj = JSON.parseObject(content);
                String bstr = cj.getString(bodyKey);
                if (bstr == null) {
                    //如果没有body返回原报文
                    return content;
                }
                bstr = new String(com.jsunicom.api.common.utils.RsaUtils.decryptByPublicKey(RsaUtils.decryptBase64(bstr), publicKey), StandardCharsets.UTF_8);
                JSONObject bj = JSON.parseObject(bstr);
                cj.put(bodyKey, bj);
                content = cj.toJSONString();
                break;
            }
            default:
        }

        return content;
    }

}
