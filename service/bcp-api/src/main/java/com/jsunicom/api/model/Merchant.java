package com.jsunicom.api.model;

import com.jsunicom.api.model.base.BasicModel;

import java.sql.Timestamp;

public class Merchant extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String orgCode;
    private Long campusId;
    private Long societyId;

    private String merchantType;
    private String merchantName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Long getCampusId() {
        return campusId;
    }

    public void setCampusId(Long campusId) {
        this.campusId = campusId;
    }

    public Long getSocietyId() {
        return societyId;
    }

    public void setSocietyId(Long societyId) {
        this.societyId = societyId;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    private String state; //0 删除 1 启用  2 禁用
    private String createBy;
    private java.sql.Timestamp createTime;
    private String updateBy;
    private java.sql.Timestamp updateTime;
}

/*List columns as follows:
"id", "org_code", "org_name", "merchant_type", "merchant_name", "bus_license", "leg_rep_name", 
"leg_rep_phone", "prov_code", "prov_name", "city_code", "city_name", "area_code", "area_name", 
"address", "leg_rep_cert_no", "leg_rep_photo1", "leg_rep_photo2", "bus_license_photo", "industry", "intent_bus_code", 
"intent_bus_name", "state", "create_by", "create_time", "update_by", "update_time", "pid", 
"contact_phone", "bus_permit_photo", "head_office", "wx_entp_did", "wx_entp_pdid", "commis_flag", "rights_flag"
*/