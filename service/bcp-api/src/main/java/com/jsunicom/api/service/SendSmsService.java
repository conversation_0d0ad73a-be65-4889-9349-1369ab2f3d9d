package com.jsunicom.api.service;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.po.req.SendSmsReq;
import com.jsunicom.api.po.rsp.SendSmsRsp;
import com.jsunicom.common.core.util.Result;

import java.util.Map;

public interface SendSmsService {

    //本网异网号码校验
    String checkNumberNet(JSONObject param) throws Exception;

    //本网短信发送接口
    Result noticeSmsindustry(JSONObject param) throws Exception;

    /**
     * 短信发送，总部短信平台
     * @param sendSmsReq
     * @return
     */
    Result<SendSmsRsp> sendSms(SendSmsReq sendSmsReq);

    boolean sendSms(String msgId, JSONObject smsParams);

    void saveSmsWork(Map<String, String> param);
}
