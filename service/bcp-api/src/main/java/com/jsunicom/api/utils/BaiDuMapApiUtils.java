package com.jsunicom.api.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 百度api 服务接口
 * @author: chongzi.and.newFunc
 * @version: 1.0
 * @since: 2022/06/07
 */
@Slf4j
public class BaiDuMapApiUtils {
    /**
     * 百度地图POI的地址09
     */
    private static String serverUrl = "http://************:8219/search";

    /**
     * 百度地图POI的地址-逆地理-内网地址09
     */
    private static String serverUrl2 = "http://************:8022/dugis-baidu/reverse_geocoding/v3/";
    /**
     * 百度地图ak
     */
    private static String ApiAuthorization="baidu-95c79cc925354e57931edf5cbfbef3cb";

    public static JSONObject searchPOI(String provinceName, String cityName, String addrName, int pageSize, int pageNum) {
        Map<Object, Object> paramMap = new HashMap<>();
        paramMap.put("ApiAuthorization",ApiAuthorization);
        paramMap.put("output","json");
        if(StringUtils.isNotEmpty(provinceName)){
            paramMap.put("province",provinceName);
        }
        if(StringUtils.isNotEmpty(cityName)){
            paramMap.put("city",cityName);
        }
        if(StringUtils.isNotEmpty(addrName)){
            paramMap.put("q",addrName);
        }
        paramMap.put("page_size",pageSize);
        paramMap.put("page_num",pageNum);
        log.info("百度地图POI检索请求：", paramMap.toString());
        try {
            String rst = BaiduHttpUtils.doGet(serverUrl, paramMap, 30000, "");
            log.info("百度地图地址解析经纬度返回: ", rst);
            return JSON.parseObject(rst);
        } catch (Exception e) {
            log.info("百度地图地址解析经纬度url失败：" +  e.toString());
            e.printStackTrace();
            return null;
        }
    }

    public static JSONObject searchPOILocation(String lng, String lat, int pageSize, int pageNum) {
        Map<Object, Object> paramMap = new HashMap<>();
        paramMap.put("ApiAuthorization",ApiAuthorization);
        paramMap.put("output","json");
        if(StringUtils.isNotEmpty(lng)&&StringUtils.isNotEmpty(lat)){
            paramMap.put("location",lat+","+lng);
        }

        paramMap.put("page_size",pageSize);
        paramMap.put("page_num",pageNum);
        log.info("百度地图POI检索逆地理请求：", paramMap.toString());
        try {
            String rst = BaiduHttpUtils.doGet(serverUrl2, paramMap, 30000, "");
            log.info("百度地图地址逆地理返回: ", rst);
            return JSON.parseObject(rst);
        } catch (Exception e) {
            log.info("百度地图地址逆地理url失败：" +  e.toString());
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
    /*    searchPOI("陕西省","西安市","联通大厦",10,2);*/
        searchPOILocation("108.8938394872078","34.23195413333516",10,2);
    }

}
