package com.jsunicom.api.service;

import com.jsunicom.api.model.CommisAccount;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service
 * @ClassName: CommisAccountFacade
 * @Author: z<PERSON><PERSON>
 * @CreateTime: 2023-04-12  14:26
 * @Description: TODO
 * @Version: 1.0
 */
public interface CommisAccountFacade {
    /**
     * 根据合伙人ID查询佣金账号
     * @param partnerId
     * @return
     * <AUTHOR>
     */
    public CommisAccount getCommisAccountByPartnerId(String partnerId);

}
