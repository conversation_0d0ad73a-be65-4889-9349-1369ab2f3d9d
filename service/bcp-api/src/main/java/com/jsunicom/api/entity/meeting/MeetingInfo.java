package com.jsunicom.api.entity.meeting;

import com.jsunicom.api.model.base.BasicModel;
import com.jsunicom.api.po.WoScYiMeeting;
import lombok.Data;

import java.util.Date;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.entity.meeting
 * @ClassName: MeetingInfo
 * @Author: z<PERSON><PERSON>
 * @CreateTime: 2023-07-17  17:48
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class MeetingInfo extends WoScYiMeeting {

    private String dealContent;

    private String photoUrl1;

    private String flowId;

    private String flowName;

    private String orderType;

}
