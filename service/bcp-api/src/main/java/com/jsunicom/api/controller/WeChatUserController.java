package com.jsunicom.api.controller;

import com.jsunicom.api.common.ApiResultEntity;
import com.jsunicom.api.common.Constant;
import com.jsunicom.api.common.Constants;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.common.result.ResultUtil;
import com.jsunicom.api.common.utils.SM4Utils;
import com.jsunicom.api.component.WeChatComponent;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.entity.user.UserRoleInfo;
import com.jsunicom.api.mapper.DictDao;
import com.jsunicom.api.po.WoScYiMember;
import com.jsunicom.api.service.InnovateMemberService;
import com.jsunicom.api.service.PartnerFacade;
import com.jsunicom.api.service.UserFacade;
import com.jsunicom.api.service.WoSchoolFacade;
import com.jsunicom.api.utils.DesHelper;
import com.jsunicom.api.utils.RedisUtil;
import com.jsunicom.api.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-19-9:51
 */
@RestController
@RequestMapping(value = "/wechat")
@Slf4j
public class WeChatUserController extends BaseController {

    @Value("${sha.key}")
    private String shakey;

    @Value("${sha.sm4key}")
    private String sm4key;

    @Autowired
    private WoSchoolFacade woSchoolFacade;

    @Resource
    private PartnerFacade partnerFacade;
    @Resource
    private InnovateMemberService memberService;

    @Resource
    private UserFacade userFacade;
    @Resource
    private DictDao dictDao;
    @Autowired
    private RedisUtil redisUtils;

    @Resource
    private WeChatComponent weChatComponent;

    /**
     * 这个url轻易不要改，招联金融企业号认证回调
     *
     * @throws IOException
     */
    @RequestMapping("/zlAuthCallback")
    public ModelAndView zlWechatAuthCallback(String phoneNumber,String redirectUrl, String failedUrl, String code, String state,
                                             HttpServletRequest request, HttpServletResponse response,
                                             ModelAndView mav,String orgCode,String woFlag, String token) throws IOException {
        if (StringUtils.isEmpty(orgCode) || orgCode.equals("undefined")){
            orgCode="";
        }
        //特殊小程序写死了#地址
        if(redirectUrl.contains("youthInnoCo/todoList")){
            redirectUrl=redirectUrl.replace("%23","#");
        }
        HttpSession session = request.getSession();
        log.info("招联金融企业号微信认证回调 redirectSessionTest:{}", session);
        String sessionId = "";
        if(null != session) {
            sessionId = session.getId();
            log.info("招联金融企业号微信认证回调 redirectSessionId:{}", sessionId);
        }
        log.info("招联金融企业号微信认证回调sessionIdTest为空}");
        log.info("招联金融企业号微信认证回调 redirectUrl:{},code:{},state:{}", redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, code, state);

        String userId = null;
        try {
            // TODO 不需要上传
            // userId = weChatComponent.getCorpUserIdByCode(zlWechatAppId, code);
            // userId="15651611072";
            // userId = phoneNumber;
            try {
                //userId=phoneNumber;
                //DesHelper解密后，SM4二次解密
                log.info("手机号解密：开始... ...");
                userId = SM4Utils.decryptEcb(sm4key, DesHelper.tripleDesDecodeECB(shakey, phoneNumber)).replaceAll("\"","");
                log.info("手机号解密：结束  {}",userId);
            } catch (Exception e) {
                log.error("号码已被篡改");
                mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "号码已被篡改!"));
            }
        } catch (Exception e) {
            log.warn("招联金融企业号根据code获取openId异常，原因：", e);
        }
        log.info("WeChatUserController的userId:{}", userId);
        if (userId == null || "".equals(userId)) {
            if (StringUtils.isEmpty(failedUrl)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "您还不是合伙人!"));
            } else {
                mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
            }
        } else {
            //判断session中手机号与操作用户手机号是否同一个，不是的话刷新session
            String sessionPhoneNumber = session.getAttribute(Constants.USER_ID_KEY) == null?"":session.getAttribute(Constants.USER_ID_KEY).toString();
            if (!sessionPhoneNumber.isEmpty() && !sessionPhoneNumber.equals(userId)){
                //重置session
                reGenerateSessionId(request);
            }
            Partner partner = partnerFacade.findByAcctNo(userId);
            log.info("WeChatUserController的partner:{}", partner);

            List<UserRoleInfo> userRoleInfos = userFacade.findByAcctNo(userId);
            log.info("WeChatUserController的partner:{}", partner);
            if (partner != null) {
                WebUtils.getSession(request).setAttribute(Constants.USER_INFO_KEY, partner);
                //userId是手机号
                WebUtils.getSession(request).setAttribute(Constants.USER_ID_KEY, userId);
                mav.setViewName("redirect:" + URLDecoder.decode(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "UTF-8"));
                try {
                    //记录登录态信息
                    if(null == WebUtils.getSession(request)) {
                        log.info("----该seesion为null");
                    }else {
                        log.info("---记录登录态信息，sessionId:{},userId:{},partnerId:{}", WebUtils.getSession(request).getId(),userId,partner.getId());
                    }
                } catch (Exception e) {
                    log.error("---记录登录态信息异常--",e);
                }
            } else if(partner == null && userRoleInfos.size()==1) {
                UserRoleInfo userRoleInfo = userRoleInfos.get(0);
                if (userRoleInfo.getAreaCode() != null) {
                    orgCode = userRoleInfo.getAreaCode();
                }
                WebUtils.getSession(request).setAttribute(Constants.USER_INFO_KEY, userRoleInfo);
                WebUtils.getSession(request).setAttribute(Constants.USER_ID_KEY, userId);
                mav.setViewName("redirect:" + URLDecoder.decode(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "UTF-8"));
                try {
                    //记录登录态信息
                    if(null == WebUtils.getSession(request)) {
                        log.info("----该seesion为null");
                    }else {
                        log.info("---记录登录态信息，sessionId:{},userId:{},staffNo:{}", WebUtils.getSession(request).getId(),userId,userRoleInfo.getStaffNo());
                    }
                } catch (Exception e) {
                    log.error("---记录登录态信息异常--",e);
                }
            }else {
                //
                /*if (StringUtils.isEmpty(failedUrl)) {
                    mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "您还不是合伙人!"));
                } else {
                    mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
                }*/
                WebUtils.getSession(request).setAttribute(Constants.USER_ID_KEY, userId);
                mav.setViewName("redirect:" + URLDecoder.decode(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag, "UTF-8"));
            }
        }
        //判断转跳地址是否在白名单中
        Integer count=dictDao.selectByMenu(redirectUrl);
        if (count==0){
            if (StringUtils.isEmpty(failedUrl)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "警告：检测到异常重定向，您即将访问的链接已被篡改，请不要继续访问此链接。"));
            } else {
                mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
            }
        }
        String redisKey = "bcp.applet_login_" + userId;
        if (redisUtils.hasKey(redisKey)){
            Object o = redisUtils.get(redisKey);
            if (o==null||!token.equals(o)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "token验证异常"));
            }
        }else{
            mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "token验证异常"));
        }
        log.info("招联金融企业号微信认证回调返回结果：{}", mav);
        return mav;
    }

    //青创社回调认证
    @RequestMapping("/qcsAuthCallback")
    @EncryptApi
    public ModelAndView qcsWechatAuthCallback(String phoneNumber,String redirectUrl, String failedUrl, String code, String state,
                                             HttpServletRequest request, HttpServletResponse response,
                                             ModelAndView mav,String orgCode,String woFlag,String flag,String token) throws IOException {

        if (StringUtils.isEmpty(orgCode)){
            orgCode="";
        }
        //特殊小程序写死了#地址
        if(redirectUrl.contains("youthInnoCo/todoList")){
            redirectUrl=redirectUrl.replace("%23","#");
        }
        HttpSession session = request.getSession();
        log.info("青春沃趣享小程序青创社认证回调 redirectSessionTest:{}", session);
        String sessionId = "";
        if(null != session) {
            sessionId = session.getId();
            log.info("青春沃趣享小程序青创社认证回调 redirectSessionId:{}", sessionId);
        }
        log.info("青春沃趣享小程序青创社认证回调sessionIdTest为空}");
        log.info("青春沃趣享小程序青创社认证回调 redirectUrl:{},code:{},state:{}", redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, code, state);

        String userId = null;
        try {
            // TODO 不需要上传
//            userId = weChatComponent.getCorpUserIdByCode(zlWechatAppId, code);
//            userId="15651611072";
//            userId = phoneNumber;
            try {
//                userId = DesHelper.tripleDesDecodeECB(shakey, phoneNumber);
                log.info("手机号解密：开始... ...");
                userId = SM4Utils.decryptEcb(sm4key, DesHelper.tripleDesDecodeECB(shakey, phoneNumber)).replaceAll("\"","");
                log.info("手机号解密：结束  {}",userId);
            } catch (Exception e) {
                log.error("号码已被篡改");
                mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "号码已被篡改!"));
            }
        } catch (Exception e) {
            log.warn("招联金融企业号根据code获取openId异常，原因：", e);
        }
        log.info("WeChatUserController的userId:{}", userId);
        if (userId == null || "".equals(userId)) {
            if (StringUtils.isEmpty(failedUrl)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "您还不是青创社成员!"));
            } else {
                mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
            }
        } else {
            //判断session中手机号与操作用户手机号是否同一个，不是的话刷新session
            String sessionPhoneNumber = session.getAttribute(Constants.USER_ID_KEY) == null?"":session.getAttribute(Constants.USER_ID_KEY).toString();
            if (!sessionPhoneNumber.isEmpty() && !sessionPhoneNumber.equals(userId)){
                //重置session
                reGenerateSessionId(request);
            }
            //判断是不是校区经理
            Partner partner = partnerFacade.findByAcctNo(userId);
            boolean isSchoolManager = woSchoolFacade.isSchoolManager(userId);
            if (partner != null && isSchoolManager) {
                WebUtils.getSession(request).setAttribute(Constants.USER_INFO_KEY,
                        partner);
                WebUtils.getSession(request).setAttribute(Constants.USER_ID_KEY,
                        userId);
                WebUtils.getSession(request).setAttribute(Constants.MEMBER_FLAG_KEY,
                        Constants.MEMBER_FLAG_V1);
                WebUtils.getSession(request).setAttribute(Constants.ROLE_TYPE_KEY,
                        Constant.ROLE_TYPE_SCHOOL);
                mav.setViewName("redirect:" + URLDecoder.decode(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "UTF-8"));

                try { //记录登录态信息
                    if(null == WebUtils.getSession(request)) {
                        log.info("----该seesion为null");
                    }else {
                        log.info("---记录登录态信息，sessionId:{},userId:{},partnerId:{}", WebUtils.getSession(request).getId(),userId,partner.getId());
                    }
                } catch (Exception e) {
                    log.error("---记录登录态信息异常--",e);
                }
            }else{
                List<WoScYiMember> memberList = memberService.findByAcctNo(userId);
                log.info("WeChatUserController的memberList:{}", memberList);
                if (memberList.size()==1) {
                    WoScYiMember member = memberList.get(0);
                    WebUtils.getSession(request).setAttribute(Constants.MEMBER_INFO_KEY,
                            member);
                    WebUtils.getSession(request).setAttribute(Constants.USER_ID_KEY,
                            userId);
                    WebUtils.getSession(request).setAttribute(Constants.MEMBER_FLAG_KEY,
                            Constants.MEMBER_FLAG_V2);
                    String roleType = member.getRoleType();//判断青创社成员角色：1 主席、2 部长、3 骨干
                    if (roleType.equals("1")){
                        WebUtils.getSession(request).setAttribute(Constants.ROLE_TYPE_KEY,Constant.ROLE_TYPE_PRESIDENT);
                    }else if (roleType.equals("2")){
                        WebUtils.getSession(request).setAttribute(Constants.ROLE_TYPE_KEY,Constant.ROLE_TYPE_MINISTER);
                    }else{
                        WebUtils.getSession(request).setAttribute(Constants.ROLE_TYPE_KEY,Constant.ROLE_TYPE_BACKBONE);
                    }


                    mav.setViewName("redirect:" + URLDecoder.decode(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "UTF-8"));

                    try { //记录登录态信息
                        if(null == WebUtils.getSession(request)) {
                            log.info("----该seesion为null");
                        }else {
                            log.info("---记录登录态信息，sessionId:{},userId:{},memberId:{}", WebUtils.getSession(request).getId(),userId,member.getMemberId());
                        }
                    } catch (Exception e) {
                        log.error("---记录登录态信息异常--",e);
                    }
                } else if (memberList.size() > 1){
                    if (StringUtils.isEmpty(failedUrl)) {
                        mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "您的用户信息有误，请联系管理员!"));
                    } else {
                        mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
                    }
                }else {
                    if (StringUtils.isEmpty(failedUrl)) {
                        mav.setViewName("redirect:" + WebUtils.urlRewrite(redirectUrl+"?orgCode="+orgCode+"&woFlag="+woFlag+"&flag="+flag, "您还不是校区经理或青创社成员!"));
                    } else {
                        mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
                    }
                }
            }
        }
        //判断转跳地址是否在白名单中
        /*Integer count=dictDao.selectByMenu(redirectUrl);
        if (count==0){
            if (StringUtils.isEmpty(failedUrl)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "警告：检测到异常重定向，您即将访问的链接已被篡改，请不要继续访问此链接。"));
            } else {
                mav.setViewName("redirect:" + URLDecoder.decode(failedUrl, "UTF-8"));
            }
        }
        String redisKey = "bcp.applet_login_" + userId;
        if (redisUtils.hasKey(redisKey)){
            Object o = redisUtils.get(redisKey);
            if (o==null||!token.equals(o)) {
                mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "token验证异常"));
            }
        }else{
            mav.setViewName("redirect:" + WebUtils.urlRewrite("https://wsl.cuchb.cn/hbxy/schoolOrders/spa-h5/campusAssistant/", "token验证异常"));
        }
        log.info("青春沃趣享小程序青创社认证回调返回结果：{}", mav);*/

        return mav;
    }

    @RequestMapping(value = "/jsSign", method = RequestMethod.GET)
    @EncryptApi
    public ApiResultEntity getJsApiTicket(String appId, String url) {
    /*log.info("开始获取js 签名: 参数 appId :{}, url:{}", appId, url);
    Signature signature = weChatComponent.getAppTicket(appId, url);
    Map<String, Object> result = new HashMap<>();
    result.put("nonceStr", signature.getNonce());
    result.put("signature", signature.getMsgSignature());
    result.put("timestamp", signature.getTimestamp());
    return getSuccessEntity((HashMap<String, Object>) result);*/
        return getSuccessEntity(new HashMap<>());
    }

    @RequestMapping(value = "/getRoleType", method = RequestMethod.GET)
    @EncryptApi
    public CustomResult getRoleType(HttpServletRequest request) {
        try {
            String roleType = getRoleTypeVal(request);
            log.info("获取登录用户角色类型成功，roleType={}",roleType);
            return ResultUtil.success(roleType);
        }catch (Exception e){
            log.error("获取登录用户角色类型失败：{}",e.getMessage());
            return ResultUtil.error("9999","获取登录用户角色类型失败");
        }
    }

    /**
     * 重置sessionid，原session中的数据自动转存到新session中
     * @param request
     */
    public static void reGenerateSessionId(HttpServletRequest request){

        HttpSession session = request.getSession();

        //首先将原session中的数据转移至一临时map中
        Map<String,Object> tempMap = new HashMap();
        Enumeration<String> sessionNames = session.getAttributeNames();
        while(sessionNames.hasMoreElements()){
            String sessionName = sessionNames.nextElement();
            tempMap.put(sessionName, session.getAttribute(sessionName));
        }

        //注销原session，为的是重置sessionId
        session.invalidate();

        //将临时map中的数据转移至新session
        session = request.getSession();
        for(Map.Entry<String, Object> entry : tempMap.entrySet()){
            session.setAttribute(entry.getKey(), entry.getValue());
        }
    }
}
