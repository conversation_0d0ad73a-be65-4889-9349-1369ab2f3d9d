package com.jsunicom.api.model;


import com.jsunicom.api.model.base.BasicModel;

public class PartnerOperAcct extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;   //编号
    private String orgCode;   //地市编码
    private Long partnerId;   //合伙人编码
    private String partnerName;   //合伙人名字
    private String partnerPhone;   //合伙人手机号
    private String partnerDevelId;   //合伙人发展人编码
    private String woOperAcct;   //沃受理工号
    private String cbssOperAcct;   //CBSS营帐工号
    private String bssOperAcct;   //BSS营帐工号
    private String remark;   //备注信息
    private String createBy;   //创建人
    private java.sql.Timestamp createTime;   //创建时间
    private String updateBy;   //更新人
    private java.sql.Timestamp updateTime;   //更新时间

    private String develId;

    public String getDevelId() {
        return develId;
    }

    public void setDevelId(String develId) {
        this.develId = develId;
    }

    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public String getOrgCode() {
        return orgCode;
   }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
   }

    public Long getPartnerId() {
        return partnerId;
   }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
   }

    public String getPartnerName() {
        return partnerName;
   }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
   }

    public String getPartnerPhone() {
        return partnerPhone;
   }

    public void setPartnerPhone(String partnerPhone) {
        this.partnerPhone = partnerPhone;
   }

    public String getPartnerDevelId() {
        return partnerDevelId;
   }

    public void setPartnerDevelId(String partnerDevelId) {
        this.partnerDevelId = partnerDevelId;
   }

    public String getWoOperAcct() {
        return woOperAcct;
   }

    public void setWoOperAcct(String woOperAcct) {
        this.woOperAcct = woOperAcct;
   }

    public String getCbssOperAcct() {
        return cbssOperAcct;
   }

    public void setCbssOperAcct(String cbssOperAcct) {
        this.cbssOperAcct = cbssOperAcct;
   }

    public String getBssOperAcct() {
        return bssOperAcct;
   }

    public void setBssOperAcct(String bssOperAcct) {
        this.bssOperAcct = bssOperAcct;
   }

    public String getRemark() {
        return remark;
   }

    public void setRemark(String remark) {
        this.remark = remark;
   }

    public String getCreateBy() {
        return createBy;
   }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
   }

    public java.sql.Timestamp getCreateTime() {
        return createTime;
   }

    public void setCreateTime(java.sql.Timestamp createTime) {
        this.createTime = createTime;
   }

    public String getUpdateBy() {
        return updateBy;
   }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
   }

    public java.sql.Timestamp getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(java.sql.Timestamp updateTime) {
        this.updateTime = updateTime;
   }

}

