package com.jsunicom.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.api.common.ApiResultEntity;
import com.jsunicom.api.common.Constants;
import com.jsunicom.api.common.RedisKeys;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.utils.IdCardUtils;
import com.jsunicom.api.entity.Campus.CampusBind;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.po.*;
import com.jsunicom.api.service.*;
import com.jsunicom.api.utils.DateTimeUtil;
import com.jsunicom.api.utils.RedisUtil;
import com.jsunicom.api.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/woschool/merchant/")
public class MerchantManageController extends BaseController{
    @Autowired
    private DictFacade dictService;

    @Autowired
    private MerchantInfoFacada merchantInfoFacada;

    @Autowired
    private PartnerInfoFacade partnerInfoFacade;

    @Autowired
    private CheckInfoService checkInfoService;

    @Autowired
    private SmsFacade smsService;

    @Autowired
    private CampusBindFacade campusBindFacade;

    @Autowired
    private SchoolCampusCollegeFacade schoolCampusCollegeFacade;

    @Autowired
    private WoScYouthInnovateFacade woScYouthInnovateFacade;

    @RequestMapping(value = "/getMerchantList", method = RequestMethod.POST, name = "团队列表查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getMerchantList(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入geMerchantList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("societyId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }
            int pageNum = 1;
            int pageSize = 5;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            Long societyId = Long.parseLong(hashMap.get("societyId").toString());

            List<MerchantListInfo> resultList = merchantInfoFacada.selectMerchantList(societyId);

            PageInfo<MerchantListInfo> pageInfo = new PageInfo<>(resultList);
            // 图片需要特殊处理
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", pageInfo.getList());
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        } catch (Exception e) {
            log.error("geMerchantList方法异常：" + e.getMessage());
            error = "团队列表查询异常";
            return getErrorEntity("团队列表查询异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/getMerchantInfoList", method = RequestMethod.POST, name = "团队成员列表查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getMerchantInfoList(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入geMerchantInfoList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("merchantId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }
            int pageNum = 1;
            int pageSize = 5;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            Long merchantId = Long.parseLong(hashMap.get("merchantId").toString());

            List<MerchantListInfo> resultList = merchantInfoFacada.selectMerchantListInfo(merchantId);

            PageInfo<MerchantListInfo> pageInfo = new PageInfo<>(resultList);
            // 图片需要特殊处理
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", pageInfo.getList());
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        } catch (Exception e) {
            log.error("geMerchantInfoList方法异常：" + e.getMessage());
            // error = "团队成员列表查询异常";
            return getErrorEntity("团队成员列表查询异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/updateMerchant", method = RequestMethod.POST, name = "编辑团队")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity updateMerchant(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入updateMerchant方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("merchantId") || !hashMap.containsKey("updateBy")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            MerchantInfo record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), MerchantInfo.class);

            Partner partner = getCurrentUser(request);
            if(null != partner){
                record.setUpdateBy(partner.getMblNbr());
            }

            record.setId(Long.parseLong(hashMap.get("merchantId").toString()));
            record.setUpdateTime(new Date());
            merchantInfoFacada.updateMerchant(record);

            return ApiResultEntity.SUCCESS();

        }catch (Exception e){
            log.error("updateMerchant方法异常："+e.getMessage());
            error = "编辑团队异常";
            return getErrorEntity("编辑团队异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/createMerchantLabor", method = RequestMethod.POST, name = "团队邀请")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity createMerchantLabor(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createMerchantLabor方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {


            if(!hashMap.containsKey("partnerCertNo") || !hashMap.containsKey("createBy") || !hashMap.containsKey("mblNbr")
                    || !hashMap.containsKey("partnerName") || !hashMap.containsKey("societyId")|| !hashMap.containsKey("verificationCode")
                    ||  !hashMap.containsKey("orgCode") || !hashMap.containsKey("campusId") ||  !hashMap.containsKey("collegeId")
                    ||  !hashMap.containsKey("pic1") ||  !hashMap.containsKey("pic2")  ||  !hashMap.containsKey("pic3")
                    || !hashMap.containsKey("merchantName")  || !hashMap.containsKey("merchantType")
            ){
                error = "缺少必传字段";
                return getErrorEntity(error);
            }


            // a)手机验证码校验：校验验证码是否正确。
            boolean verificationTag = checkInfoService.checkCode(hashMap);
            if(!verificationTag){
                return getErrorEntity("验证码错误");
            }


            // 国政通校验：校验姓名与证件号是否一致
            boolean gztTag = checkInfoService.checkGztCode(hashMap);
            if(!gztTag){
                return ApiResultEntity.FAILURE("身份信息校验失败");
            }


            PartnerInfo partnerRecord = JSONObject.parseObject(JSONObject.toJSONString(hashMap), PartnerInfo.class);

            // d)校验注册人员信息是否在表中待审核、已审核状态存在，如果已存在给出提示
            PartnerInfo record = new PartnerInfo();
            record.setMblNbr(partnerRecord.getMblNbr());
            record.setState("0|1");
            record.setPartnerCertNo(partnerRecord.getPartnerCertNo());
            long partnerCount = partnerInfoFacade.countOrByExample(record);
            if(partnerCount>0){
                return getErrorEntity("该用户已存在");
            }

            MerchantInfo recordInfo = JSONObject.parseObject(JSONObject.toJSONString(hashMap), MerchantInfo.class);

            Partner partner = getCurrentUser(request);
            if(null != partner){
                recordInfo.setCreateBy(partner.getMblNbr());
                partnerRecord.setCreateBy(partner.getMblNbr());
                partnerRecord.setOrgCode(partner.getOrgCode());
            }

            recordInfo.setState("1");
            recordInfo.setCreateTime(new Date());
            long id = merchantInfoFacada.createMerchant(recordInfo);


            partnerRecord.setState("0");
            partnerRecord.setMerchantId(id);
            partnerRecord.setAcctNo(partnerRecord.getMblNbr());
            partnerRecord.setIsMerAdmin("1");
            partnerRecord.setCreateTime(new Date());
            partnerRecord.setPartnerType("2");
            partnerRecord.setBusLine("20");
            partnerRecord.setReserve1("1");
            partnerInfoFacade.insertPartnerInfo(partnerRecord);


            // 新增 bind 信息
            CampusBind campusBind = new CampusBind();
            campusBind.setCampusId(recordInfo.getCampusId());
            campusBind.setBindId(id);
            campusBind.setState("1");
            campusBind.setUpdateTime(new Timestamp(new Date().getTime()));
            campusBind.setType("0");
            campusBind.setRemark("创建团队绑定");
            campusBindFacade.save(campusBind);

        } catch (Exception e) {
            log.error("createMerchantLabor方法异常：" + e.getMessage());
            error = "创建团队异常";
            return getErrorEntity( "创建团队异常:"+e.getMessage());
        }

        return getSuccessEntity();


    }


    @RequestMapping(value = "/createMerchantMember", method = RequestMethod.POST, name = "队员邀请")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity createMerchantMember(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createMerchantMember方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("partnerCertNo") || !hashMap.containsKey("createBy") || !hashMap.containsKey("mblNbr")
                    || !hashMap.containsKey("partnerName") ||  !hashMap.containsKey("merchantId") ||  !hashMap.containsKey("orgCode")
                    || !hashMap.containsKey("campusId") ||  !hashMap.containsKey("collegeId") ||  !hashMap.containsKey("societyId")
                    || !hashMap.containsKey("verificationCode")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            // a)手机验证码校验：校验验证码是否正确。
            String mobile = hashMap.get("mblNbr").toString();
            String verificationCode = hashMap.get("verificationCode").toString();

            smsService.checkCode(mobile, verificationCode);

            // 国政通校验：校验姓名与证件号是否一致
            boolean gztTag = checkInfoService.checkGztCode(hashMap);
            if(!gztTag){
                return ApiResultEntity.FAILURE("身份信息校验失败");
            }

            PartnerInfo partnerRecord = JSONObject.parseObject(JSONObject.toJSONString(hashMap), PartnerInfo.class);
            // d)校验注册人员信息是否在表中待审核、已审核状态存在，如果已存在给出提示
            PartnerInfo record = new PartnerInfo();
            record.setMblNbr(partnerRecord.getMblNbr());
            record.setState("0|1");
            record.setPartnerCertNo(partnerRecord.getPartnerCertNo());
            long partnerCount = partnerInfoFacade.countOrByExample(record);
            if(partnerCount>0){
                return getErrorEntity("该用户已存在");
            }

            Partner partner = getCurrentUser(request);
            if(null != partner){
                partnerRecord.setCreateBy(partner.getMblNbr());
                partnerRecord.setOrgCode(partner.getOrgCode());
            }

            partnerRecord.setState("0");
            partnerRecord.setPartnerType("2");
            partnerRecord.setIsMerAdmin("0");
            partnerRecord.setAcctNo(partnerRecord.getMblNbr());
            partnerRecord.setCreateTime(new Date());
            partnerRecord.setBusLine("20");
            partnerRecord.setReserve1("0");
            partnerInfoFacade.insertPartnerInfo(partnerRecord);



        }catch (Exception e){
            log.error("createMerchantMember方法异常："+e.getMessage());
            error = "队员邀请异常";
            return getErrorEntity("队员邀请异常:"+e.getMessage());
        }

        return getSuccessEntity();


    }


    @RequestMapping(value = "/getMerchantMemberList", method = RequestMethod.POST, name = "团队成员列表")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getMerchantMemberList(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入getMerchantMemberList方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("merchantId")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }
            int pageNum = 1;
            int pageSize = 500;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            long merchantId = Long.parseLong(hashMap.get("merchantId").toString());
            PartnerInfo record = new PartnerInfo();
            record.setMerchantId(merchantId);
            record.setState("1");
            record.setIsMerAdmin("0");
            if(hashMap.containsKey("partnerName")){
                record.setPartnerName(hashMap.get("partnerName").toString());
            }

            List<PartnerInfo> partnerInfoList = partnerInfoFacade.getPartnerInfoList(record);
            PageInfo<PartnerInfo> pageInfo = new PageInfo<>(partnerInfoList);

            List<WoInnovateListInfo> resultList = new ArrayList<>();
            for(PartnerInfo partnerInfo : partnerInfoList){
                WoInnovateListInfo partnerInfoTemp = new WoInnovateListInfo();
                partnerInfoTemp.setPartnerId(partnerInfo.getId());
                partnerInfoTemp.setPartnerName(partnerInfo.getPartnerName());
                partnerInfoTemp.setMblNbr(partnerInfo.getMblNbr());
                partnerInfoTemp.setIsMerAdmin(partnerInfo.getIsMerAdmin());
                partnerInfoTemp.setMerchantName(partnerInfo.getMerchantName());
                resultList.add(partnerInfoTemp);
            }

            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", resultList);
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getMerchantMemberList方法异常："+e.getMessage());
            error = "获取团队成员列表异常";
            return getErrorEntity("获取团队成员列表异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/deleteMerchantMember", method = RequestMethod.POST, name = "删除团队成员")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity deleteMerchantMember(@RequestBody HashMap hashMap, HttpServletRequest request){


        log.info("进入deleteMerchantMember方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("partnerId") ){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            PartnerInfo record = new PartnerInfo();
            record.setId(Long.parseLong(hashMap.get("partnerId").toString()));
            partnerInfoFacade.deletePartnerInfo(record);

            return ApiResultEntity.SUCCESS();

        }catch (Exception e){
            log.error("deleteMerchantMember方法异常："+e.getMessage());
            error = "删除团队成员异常";
            return getErrorEntity("删除团队成员异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/getPartnerInfoList", method = RequestMethod.POST, name = "队员管理-成员列表查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getPartnerInfoList(@RequestBody HashMap hashMap, HttpServletRequest request){


        String error = "";
        try {

            Partner partner = getCurrentUser(request);
            if(null == partner){
                return getErrorEntity("缺少人员注册信息，请联系管理员！");
            }
            String isMerAdmin = partner.getIsMerAdmin();
            if (!"1".equals(isMerAdmin)) {
                // 备注
                return getErrorEntity("您非团队长，没有权限操作！");
            }
            Long merchantId = partner.getMerchantId();

            int pageNum = 1;
            int pageSize = 5;
            if(hashMap.containsKey("pageNum")){
                pageNum = (int) hashMap.get("pageNum");
            }
            if(hashMap.containsKey("pageSize")){
                pageSize = (int) hashMap.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            List<MerchantListInfo> resultList = merchantInfoFacada.selectMerchantListInfo(merchantId);

            PageInfo<MerchantListInfo> pageInfo = new PageInfo<>(resultList);

            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("data", pageInfo.getList());
            dataMap.put("totalCount", pageInfo.getTotal());
            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getPartnerInfoList方法异常："+e.getMessage());
            error = e.getMessage();
            return getErrorEntity("成员列表查询失败:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/getPartnerInfo", method = RequestMethod.POST, name = "我的-成员信息查询")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getPartnerInfo(@RequestBody HashMap hashMap, HttpServletRequest request){


        String error = "";
        try {

            Partner partner = getCurrentUser(request);
            if(null == partner){
                return getErrorEntity("缺少人员注册信息，请联系管理员！");
            }

            MyselfPartnerInfo myselfPartnerInfo = new MyselfPartnerInfo();
            HashMap<String, Object> dataMap = new HashMap<>();

            BeanUtils.copyProperties(partner, myselfPartnerInfo);

            if(StringUtil.isNotEmpty(partner.getCollegeId())){
                long collegeId = Long.parseLong(partner.getCollegeId());
                myselfPartnerInfo.setCollegeId(collegeId);
                SchoolCampusCollege  schoolCampusCollegeList  = schoolCampusCollegeFacade.queryInfo(collegeId);
                if(schoolCampusCollegeList != null ){
                    myselfPartnerInfo.setCollegeName(schoolCampusCollegeList.getCollegeName());
                }
            }

            myselfPartnerInfo.setPartnerId(partner.getId());

            if(StringUtil.isEmpty(partner.getReserve1())){
                myselfPartnerInfo.setIsReal("0");
            }else{
                myselfPartnerInfo.setIsReal(partner.getReserve1());
            }

            String  certNo = IdCardUtils.desensitizeIdCardNumber(partner.getPartnerCertNo());
            myselfPartnerInfo.setPartnerCertNo(certNo);

            if(null != partner.getMerchantId()){
                MerchantInfo merchantInfo = merchantInfoFacada.selectByPrimaryKey(partner.getMerchantId());
                if(null != merchantInfo){
                    myselfPartnerInfo.setMerchantType(merchantInfo.getMerchantType());
                }
            }

            dataMap.put("data", myselfPartnerInfo);

            return ApiResultEntity.SUCCESS(dataMap);

        }catch (Exception e){
            log.error("getPartnerInfo方法异常："+e.getMessage());
            error = e.getMessage();
            return getErrorEntity("成员信息查询异常:"+e.getMessage());
        }

    }


    @RequestMapping(value = "/updateRealName", method = RequestMethod.POST, name = "我的-队员实名")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity updateRealName(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入updateRealName方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if( !hashMap.containsKey("partnerId") ||  !hashMap.containsKey("pic1") ||  !hashMap.containsKey("pic2")  ||  !hashMap.containsKey("pic3")
            ){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            long partnerId = Long.parseLong(hashMap.get("partnerId").toString());
            Partner partner = getCurrentUser(request);
            if(null == partner){
                return getErrorEntity("缺少人员注册信息，请联系管理员！");
            }
            if(partnerId != partner.getId().longValue()){
                return getErrorEntity("操作信息不一致，禁止操作。");
            }

            hashMap.put("partnerCertNo", partner.getPartnerCertNo());
            hashMap.put("partnerName", partner.getPartnerName());
            // 国政通校验：校验姓名与证件号是否一致
            boolean gztTag = checkInfoService.checkGztCode(hashMap);
            if(!gztTag){
                return ApiResultEntity.FAILURE("身份信息校验失败");
            }

            partner.setPic1(hashMap.get("pic1").toString());
            partner.setPic2(hashMap.get("pic2").toString());
            partner.setPic3(hashMap.get("pic3").toString());
            partner.setUpdateBy(partner.getAcctNo());
            partner.setUpdateTime(DateTimeUtil.currentTime());
            partner.setReserve1("1");

            PartnerInfo record = new PartnerInfo();
            BeanUtils.copyProperties(partner, record);

            boolean flag = false;
            String switchName = dictService.getNameByKey("switch", "switch_doResgister");
            if (StringUtils.isEmpty(switchName) || Constants.RESULT_FAIL.equals(switchName)) {
                log.info("能人注册接口配置开关已关闭或未配置,返回默认值");
                error = "能人注册接口配置开关已关闭或未配置";
            }else{
                log.info("能人注册接口配置开关已打开：{}，{}", partner.getAcctNo(), partner.getPartnerName());
                String responseBody = woScYouthInnovateFacade.doResgister(partner);
                JSONObject head = JSONObject.parseObject(responseBody).getJSONObject("UNI_BSS_HEAD");
                JSONObject body = JSONObject.parseObject(responseBody).getJSONObject("UNI_BSS_BODY");
                String respCode = head.getString("RESP_CODE");
                if (!"00000".equals(respCode)) {
                    log.error("能人注册失败请检查！！！");
                    error = "能人注册失败";
                } else {
                    JSONObject identityRsp = body.getJSONObject("DO_RESGISTER_RSP");
                    JSONObject root = identityRsp.getJSONObject("ROOT");
                    String code = root.getString("RSP_CODE");
                    if ("0".equals(code)) {
                        log.info("能人注册通过");
                        flag = true;
                    } else {
                        error = root.getString("RSP_DESC");
                        log.info("能人注册失败：{}，{}",code,error);
                    }
                }
            }

            if(flag){
                partnerInfoFacade.updateByPrimaryKey(record);
            }else{
                return  getErrorEntity(error);
            }

        }catch (Exception e){
            log.error("updateRealName方法异常："+e.getMessage());
            error = "实名异常";
            return getErrorEntity( "实名异常:"+e.getMessage());
        }

        return getSuccessEntity();


    }


}
