package com.jsunicom.api.entity.partner;

import com.jsunicom.api.common.Constants;
import com.jsunicom.api.entity.user.UserInfo;
import com.jsunicom.api.model.base.BasicModel;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.sql.Timestamp;
import java.util.ArrayList;

public class Partner extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    @NotEmpty(message = "合伙人类型不能为空")
    private String partnerType;
    @NotEmpty(message = "合伙人名称不能为空")
    private String partnerName;
    private String acctNo;
    @Pattern(regexp = "^1([358][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8}$", message = "手机号码不符合格式")
    private String partnerCertNo;
    private String pic1;
    private String pic2;
    private String pic3;
    private String mblNbr;
    private String professionType;
    private String orgCode;
    private String provCode;
    private String provName;
    private String cityCode;
    private String cityName;
    private String areaCode;
    private String areaName;
    private String address;
    private String develId;
    private String emploeeType;
    private String isMerAdmin;
    private Long commisAcctId;
    private String collegeId;
    private String dormitoryId;
    private String referenceId;
    private String societyId;
    private Long merchantId;
    private String saleMgrName;
    private String saleMgrPhone;
    private String busLine;
    private String busGrid;
    private String inviteCode;
    private String develChannel;
    private String gztRs;
    private String state;
    private String auditBy;
    private java.sql.Timestamp auditTime;
    private String auditRemark;
    private String auditRefuseReson;
    private String remark;
    private String createBy;
    private java.sql.Timestamp createTime;
    private String updateBy;
    private java.sql.Timestamp updateTime;

    private String reserve1;

    private String reserve2;

    private String reserve3;

    private String reserve4;

    private String reserve5;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getPartnerCertNo() {
        return partnerCertNo;
    }

    public void setPartnerCertNo(String partnerCertNo) {
        this.partnerCertNo = partnerCertNo;
    }

    public String getPic1() {
        return pic1;
    }

    public void setPic1(String pic1) {
        this.pic1 = pic1;
    }

    public String getPic2() {
        return pic2;
    }

    public void setPic2(String pic2) {
        this.pic2 = pic2;
    }

    public String getPic3() {
        return pic3;
    }

    public void setPic3(String pic3) {
        this.pic3 = pic3;
    }

    public String getMblNbr() {
        return mblNbr;
    }

    public void setMblNbr(String mblNbr) {
        this.mblNbr = mblNbr;
    }

    public String getProfessionType() {
        return professionType;
    }

    public void setProfessionType(String professionType) {
        this.professionType = professionType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }

    public String getProvName() {
        return provName;
    }

    public void setProvName(String provName) {
        this.provName = provName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDevelId() {
        return develId;
    }

    public void setDevelId(String develId) {
        this.develId = develId;
    }

    public String getEmploeeType() {
        return emploeeType;
    }

    public void setEmploeeType(String emploeeType) {
        this.emploeeType = emploeeType;
    }

    public String getIsMerAdmin() {
        return isMerAdmin;
    }

    public void setIsMerAdmin(String isMerAdmin) {
        this.isMerAdmin = isMerAdmin;
    }

    public Long getCommisAcctId() {
        return commisAcctId;
    }

    public void setCommisAcctId(Long commisAcctId) {
        this.commisAcctId = commisAcctId;
    }

    public String getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(String collegeId) {
        this.collegeId = collegeId;
    }

    public String getDormitoryId() {
        return dormitoryId;
    }

    public void setDormitoryId(String dormitoryId) {
        this.dormitoryId = dormitoryId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getSocietyId() {
        return societyId;
    }

    public void setSocietyId(String societyId) {
        this.societyId = societyId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getSaleMgrName() {
        return saleMgrName;
    }

    public void setSaleMgrName(String saleMgrName) {
        this.saleMgrName = saleMgrName;
    }

    public String getSaleMgrPhone() {
        return saleMgrPhone;
    }

    public void setSaleMgrPhone(String saleMgrPhone) {
        this.saleMgrPhone = saleMgrPhone;
    }

    public String getBusLine() {
        return busLine;
    }

    public void setBusLine(String busLine) {
        this.busLine = busLine;
    }

    public String getBusGrid() {
        return busGrid;
    }

    public void setBusGrid(String busGrid) {
        this.busGrid = busGrid;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public String getDevelChannel() {
        return develChannel;
    }

    public void setDevelChannel(String develChannel) {
        this.develChannel = develChannel;
    }

    public String getGztRs() {
        return gztRs;
    }

    public void setGztRs(String gztRs) {
        this.gztRs = gztRs;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy;
    }

    public Timestamp getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Timestamp auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditRefuseReson() {
        return auditRefuseReson;
    }

    public void setAuditRefuseReson(String auditRefuseReson) {
        this.auditRefuseReson = auditRefuseReson;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getReserve1() {
        return reserve1;
    }

    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1;
    }

    public String getReserve2() {
        return reserve2;
    }

    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2;
    }

    public String getReserve3() {
        return reserve3;
    }

    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3;
    }

    public String getReserve4() {
        return reserve4;
    }

    public void setReserve4(String reserve4) {
        this.reserve4 = reserve4;
    }

    public String getReserve5() {
        return reserve5;
    }

    public void setReserve5(String reserve5) {
        this.reserve5 = reserve5;
    }

    public UserInfo getUserInfo() {
        UserInfo userInfo = new UserInfo();
        if (StringUtils.isEmpty(this.acctNo)) {
            userInfo.setAcctNo(this.mblNbr);
        } else {
            userInfo.setAcctNo(this.acctNo);
        }
        userInfo.setOrgCode(this.orgCode);
        userInfo.setAcctName(this.partnerName);
        userInfo.setMblNbr(this.mblNbr);
        if (StringUtils.equals(Constants.AUDIT_STATE_SUCC, this.getState())) {
            userInfo.setState(Constants.USER_STATE_ENABLE);
        } else {
            userInfo.setState(Constants.USER_STATE_DISABLE);
        }
        return userInfo;
    }

}


/*List columns as follows:
"id", "partner_type", "partner_name", "scence_id", "wx_acct", "acct_no", "partner_cert_no",
"mbl_nbr", "intent_bus_code", "intent_bus_name", "refer_name", "refer_phone", "profession_type", "promot_area",
"org_code", "industry", "prov_code", "prov_name", "city_code", "city_name", "area_code",
"area_name", "address", "devel_id", "commis_acct_id", "create_by", "create_time", "update_by",
"update_time", "audit_by", "audit_time", "state", "audit_remark", "wo_acct", "wo_state",
"wo_remark", "merchant_id", "wx_entp_uid", "pricipal_name", "pricipal_cert_no", "sale_mgr_name", "sale_mgr_phone",
"bus_line", "bus_grid", "emploee_type", "is_mer_admin", "bus_grid_name", "invite_code"
*/
