package com.jsunicom.api.service.impl;

import com.jsunicom.api.common.handler.AppInfoProvider;
import com.jsunicom.api.service.AccessTokenService;
import com.lz.lsf.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-13-15:47
 */
@Slf4j
@Service
public class AccessTokenServiceImpl implements AccessTokenService {
//    @Autowired
    private AppInfoProvider appInfoProvider;

    @Override
    public String get(String appId) throws ServiceException {
        log.debug("Enter get, appId ={}.", appId);
        return appInfoProvider.getAccessToken(appId);
    }
}
