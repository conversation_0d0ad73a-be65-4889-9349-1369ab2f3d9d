package com.jsunicom.api.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.common.result.ResultEnum;
import com.jsunicom.api.common.result.ResultUtil;
import com.jsunicom.api.common.utils.SM4Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.ui.ModelMap;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @BelongsProject: 20230926
 * @BelongsPackage: com.jsunicom.api.filter
 * @Author: zhaowang
 * @CreateTime: 2024-05-08  21:27
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Component
public class DecryptInterceptor extends HandlerInterceptorAdapter {
    @Value("${sha.sm4key}")
    private String sm4key;

    /**
     * Controller之前执行
     * preHandle：拦截于请求刚进入时，进行判断，需要boolean返回值，如果返回true将继续执行，如果返回false，将不进行执行。一般用于登录校验
     * 1.当preHandle方法返回false时，从当前拦截器往回执行所有拦截器的afterCompletion方法，再退出拦截器链。也就是说，请求不继续往下传了，直接沿着来的链往回跑。
     * 2.当preHandle方法全为true时，执行下一个拦截器,直到所有拦截器执行完。再运行被拦截的Controller。然后进入拦截器链，运行所有拦截器的postHandle方法,
     * 完后从最后一个拦截器往回执行所有拦截器的afterCompletion方法.
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {


            if (handler instanceof HandlerMethod) {
                HandlerMethod method = (HandlerMethod) handler;
                // RequireLogin annotation = method.getMethodAnnotation(RequireLogin.class);
                if (method.hasMethodAnnotation(DecryptApi.class)) {
                    // 需要对数据进行加密解密
                    // 1.对application/json类型
                    String contentType = request.getContentType();
                    if (contentType == null && !"GET".equals(request.getMethod())) {
                        // 请求不通过，返回错误信息给客户端
                        /*responseResult(response, response.getWriter(), ResultUtil.error(ResultEnum.ERROR.getrespCode(),"Decrypt failed"));
                        return false;*/
                        contentType="application/json";
                    }
                    String requestBody = null;
                    boolean shouldEncrypt = false;

                    if ((contentType != null && StringUtils.substringMatch(contentType, 0,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE)) || "GET".equals(request.getMethod())) {
                        // 1.application/x-www-form-urlencoded 支持参数在body或者在param
                        shouldEncrypt = true;
                        requestBody = convertFormToString(request);
                        if (requestBody == null || "{}".equals(requestBody)) {
                            requestBody = URLDecoder.decode(convertInputStreamToString(request.getInputStream()),
                                    "UTF-8");
                            List<String> uriToList =
                                    Stream.of(requestBody.split("&")).map(elem -> new String(elem)).collect(Collectors.toList());

                            Map<String, String> uriToListToMap = new HashMap<>();


                            for (String individualElement : uriToList) {
                                if (individualElement.split("=")[0] != null && !"".equals(individualElement.split("=")[0])) {
                                    uriToListToMap.put(individualElement.split("=")[0],
                                            individualElement.substring(individualElement.split("=")[0].length() + 1));
                                }

                            }
                            requestBody = JSONObject.toJSONString(uriToListToMap);
                        }

                    } else if (StringUtils.substringMatch(contentType, 0, MediaType.APPLICATION_JSON_VALUE)) {
                        // application/json 支持加密参数在body
                        shouldEncrypt = true;
                        requestBody = convertInputStreamToString(request.getInputStream());

                    }
                    //|| !requestBody.contains("sig")
                    if (requestBody == null || "{}".equals(requestBody) || !shouldEncrypt ||"".equals(requestBody)) {
                        return true;
                    } else {

//                        String result = decodeApi(JSON.parseObject(requestBody, StdRequestApi.class),
//                                encryptProperties.getPrivateKey());
//                        String result = SM4Utils.decryptEcb("f3332970e025d0531bf31b8c36637d9c", requestBody);
//                        String result = SM4Utils.decryptEcb(sm4key, requestBody);
                        //解密
                        Integer index=21;
                        HashMap hashMap = JSONObject.parseObject(requestBody, HashMap.class);
                        String requestBodyNew=(String)hashMap.get("sig");
                        String bodyNew=requestBodyNew.substring(64);
                        String secKey=requestBodyNew.substring(0,64);
                        String s6Q=secKey.substring(0,index);
                        String secAndIv=secKey.substring(index);
                        String s7ivNew=secAndIv.substring(0,32);
                        String s6New=s6Q+secAndIv.substring(32);
                        String result=SM4Utils.decryptCBC(bodyNew, s6New, s7ivNew);

                        log.info("SM4Utils解密-->"+result);
                        if (result == null) {
                            // 请求不通过，返回错误信息给客户端
                            responseResult(response, response.getWriter(), ResultUtil.error(ResultEnum.ERROR.getrespCode(),"Decrypt failed"));
                            return false;
                        }
                        JSONObject jasonObject = JSONObject.parseObject(result);
                        Map map = (Map) jasonObject;
                        if (request instanceof RequestWrapper) {
                            RequestWrapper requestWrapper = (RequestWrapper) request;
                            requestWrapper.setBody(result);
                            requestWrapper.addAllParameters(map);
                            // requestWrapper = new RequestWrapper(request, map, result);
                            return true;
                        }
                    }
                } else {
                    String contentType = request.getContentType();
                    if (contentType != null && contentType.length() > 0 && StringUtils.substringMatch(contentType, 0,
                            MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                        // 1.application/x-www-form-urlencoded 支持参数在body或者在param
                        String requestBody = convertFormToString(request);
                        if (requestBody == null || "{}".equals(requestBody)) {
                            // 把流数据放进param中,不解密
                            requestBody = URLDecoder.decode(convertInputStreamToString(request.getInputStream()),
                                    "UTF-8");
                            List<String> uriToList =
                                    Stream.of(requestBody.split("&")).map(elem -> new String(elem)).collect(Collectors.toList());

                            Map<String, Object> uriToListToMap = new HashMap<>();

                            for (String individualElement : uriToList) {
                                if (individualElement.split("=")[0] != null && !"".equals(individualElement.split("=")[0])) {
                                    uriToListToMap.put(individualElement.split("=")[0],
                                            individualElement.substring(individualElement.split("=")[0].length() + 1));
                                }
                            }
                            if (request instanceof RequestWrapper) {
                                RequestWrapper requestWrapper = (RequestWrapper) request;
                                requestWrapper.setBody(requestBody);
                                requestWrapper.addAllParameters(uriToListToMap);
                                return true;
                            }
                        }
                    }
                }

                return true;
            }
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage() + "异常地址：" + request.getServletPath());
            responseResult(response, response.getWriter(), ResultUtil.error(ResultEnum.ERROR.getrespCode(),"Decrypt failed"));
            return false;
        }
    }


    /**
     * 返回信息给客户端
     *
     * @param response
     * @param tResponse
     */
    private void responseResult(HttpServletResponse response, CustomResult tResponse) throws IOException {
        response.setContentType("application/json");
        String json = JSONObject.toJSONString(tResponse);
        PrintWriter out = response.getWriter();
        out.print(json);
        out.flush();
        out.close();
    }

    private String convertFormToString(HttpServletRequest request) {
        Map<String, String> result = new HashMap<>(8);
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String name = parameterNames.nextElement();
            result.put(name, request.getParameter(name));
        }
        try {
            return JSON.toJSONString(result);
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }

    private String convertInputStreamToString(InputStream inputStream) throws IOException {
        return StreamUtils.copyToString(inputStream, Charset.forName("UTF-8"));
    }


//    public String decodeApi(StdRequestApi stdRequestApi, String apiPrivateKey) {
//        try {
//            // 1.rsa解密
//            // 2.AES验签
//            // 3.AES解密
//            return deData;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }

    /**
     * 返回信息给客户端
     *
     * @param response
     * @param out
     * @param tResponse
     */
    private void responseResult(HttpServletResponse response, PrintWriter out, CustomResult tResponse) {
        response.setContentType("application/json");
        String json = JSONObject.toJSONString(tResponse);
        out.print(json);
        out.flush();
        out.close();
    }


}
