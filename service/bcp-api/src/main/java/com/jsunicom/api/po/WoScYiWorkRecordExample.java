package com.jsunicom.api.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WoScYiWorkRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYiWorkRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andInstIdIsNull() {
            addCriterion("INST_ID is null");
            return (Criteria) this;
        }

        public Criteria andInstIdIsNotNull() {
            addCriterion("INST_ID is not null");
            return (Criteria) this;
        }

        public Criteria andInstIdEqualTo(String value) {
            addCriterion("INST_ID =", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdNotEqualTo(String value) {
            addCriterion("INST_ID <>", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdGreaterThan(String value) {
            addCriterion("INST_ID >", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdGreaterThanOrEqualTo(String value) {
            addCriterion("INST_ID >=", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdLessThan(String value) {
            addCriterion("INST_ID <", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdLessThanOrEqualTo(String value) {
            addCriterion("INST_ID <=", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdLike(String value) {
            addCriterion("INST_ID like", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdNotLike(String value) {
            addCriterion("INST_ID not like", value, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdIn(List<String> values) {
            addCriterion("INST_ID in", values, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdNotIn(List<String> values) {
            addCriterion("INST_ID not in", values, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdBetween(String value1, String value2) {
            addCriterion("INST_ID between", value1, value2, "instId");
            return (Criteria) this;
        }

        public Criteria andInstIdNotBetween(String value1, String value2) {
            addCriterion("INST_ID not between", value1, value2, "instId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("ORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("ORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("ORDER_ID =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("ORDER_ID <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("ORDER_ID >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("ORDER_ID >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("ORDER_ID <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("ORDER_ID <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("ORDER_ID like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("ORDER_ID not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("ORDER_ID in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("ORDER_ID not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("ORDER_ID between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("ORDER_ID not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("FLOW_ID is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("FLOW_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("FLOW_ID =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("FLOW_ID <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("FLOW_ID >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("FLOW_ID >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("FLOW_ID <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("FLOW_ID <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("FLOW_ID like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("FLOW_ID not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("FLOW_ID in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("FLOW_ID not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("FLOW_ID between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("FLOW_ID not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIsNull() {
            addCriterion("LINK_CODE is null");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIsNotNull() {
            addCriterion("LINK_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andLinkCodeEqualTo(String value) {
            addCriterion("LINK_CODE =", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotEqualTo(String value) {
            addCriterion("LINK_CODE <>", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeGreaterThan(String value) {
            addCriterion("LINK_CODE >", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LINK_CODE >=", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLessThan(String value) {
            addCriterion("LINK_CODE <", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLessThanOrEqualTo(String value) {
            addCriterion("LINK_CODE <=", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLike(String value) {
            addCriterion("LINK_CODE like", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotLike(String value) {
            addCriterion("LINK_CODE not like", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIn(List<String> values) {
            addCriterion("LINK_CODE in", values, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotIn(List<String> values) {
            addCriterion("LINK_CODE not in", values, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeBetween(String value1, String value2) {
            addCriterion("LINK_CODE between", value1, value2, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotBetween(String value1, String value2) {
            addCriterion("LINK_CODE not between", value1, value2, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkNameIsNull() {
            addCriterion("LINK_NAME is null");
            return (Criteria) this;
        }

        public Criteria andLinkNameIsNotNull() {
            addCriterion("LINK_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andLinkNameEqualTo(String value) {
            addCriterion("LINK_NAME =", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotEqualTo(String value) {
            addCriterion("LINK_NAME <>", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameGreaterThan(String value) {
            addCriterion("LINK_NAME >", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameGreaterThanOrEqualTo(String value) {
            addCriterion("LINK_NAME >=", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLessThan(String value) {
            addCriterion("LINK_NAME <", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLessThanOrEqualTo(String value) {
            addCriterion("LINK_NAME <=", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLike(String value) {
            addCriterion("LINK_NAME like", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotLike(String value) {
            addCriterion("LINK_NAME not like", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameIn(List<String> values) {
            addCriterion("LINK_NAME in", values, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotIn(List<String> values) {
            addCriterion("LINK_NAME not in", values, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameBetween(String value1, String value2) {
            addCriterion("LINK_NAME between", value1, value2, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotBetween(String value1, String value2) {
            addCriterion("LINK_NAME not between", value1, value2, "linkName");
            return (Criteria) this;
        }

        public Criteria andDealActIsNull() {
            addCriterion("DEAL_ACT is null");
            return (Criteria) this;
        }

        public Criteria andDealActIsNotNull() {
            addCriterion("DEAL_ACT is not null");
            return (Criteria) this;
        }

        public Criteria andDealActEqualTo(String value) {
            addCriterion("DEAL_ACT =", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActNotEqualTo(String value) {
            addCriterion("DEAL_ACT <>", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActGreaterThan(String value) {
            addCriterion("DEAL_ACT >", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActGreaterThanOrEqualTo(String value) {
            addCriterion("DEAL_ACT >=", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActLessThan(String value) {
            addCriterion("DEAL_ACT <", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActLessThanOrEqualTo(String value) {
            addCriterion("DEAL_ACT <=", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActLike(String value) {
            addCriterion("DEAL_ACT like", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActNotLike(String value) {
            addCriterion("DEAL_ACT not like", value, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActIn(List<String> values) {
            addCriterion("DEAL_ACT in", values, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActNotIn(List<String> values) {
            addCriterion("DEAL_ACT not in", values, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActBetween(String value1, String value2) {
            addCriterion("DEAL_ACT between", value1, value2, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealActNotBetween(String value1, String value2) {
            addCriterion("DEAL_ACT not between", value1, value2, "dealAct");
            return (Criteria) this;
        }

        public Criteria andDealTimeIsNull() {
            addCriterion("DEAL_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDealTimeIsNotNull() {
            addCriterion("DEAL_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDealTimeEqualTo(Date value) {
            addCriterion("DEAL_TIME =", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeNotEqualTo(Date value) {
            addCriterion("DEAL_TIME <>", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeGreaterThan(Date value) {
            addCriterion("DEAL_TIME >", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DEAL_TIME >=", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeLessThan(Date value) {
            addCriterion("DEAL_TIME <", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeLessThanOrEqualTo(Date value) {
            addCriterion("DEAL_TIME <=", value, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeIn(List<Date> values) {
            addCriterion("DEAL_TIME in", values, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeNotIn(List<Date> values) {
            addCriterion("DEAL_TIME not in", values, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeBetween(Date value1, Date value2) {
            addCriterion("DEAL_TIME between", value1, value2, "dealTime");
            return (Criteria) this;
        }

        public Criteria andDealTimeNotBetween(Date value1, Date value2) {
            addCriterion("DEAL_TIME not between", value1, value2, "dealTime");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeIsNull() {
            addCriterion("ACCEPT_USER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeIsNotNull() {
            addCriterion("ACCEPT_USER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeEqualTo(String value) {
            addCriterion("ACCEPT_USER_TYPE =", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeNotEqualTo(String value) {
            addCriterion("ACCEPT_USER_TYPE <>", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeGreaterThan(String value) {
            addCriterion("ACCEPT_USER_TYPE >", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("ACCEPT_USER_TYPE >=", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeLessThan(String value) {
            addCriterion("ACCEPT_USER_TYPE <", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeLessThanOrEqualTo(String value) {
            addCriterion("ACCEPT_USER_TYPE <=", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeLike(String value) {
            addCriterion("ACCEPT_USER_TYPE like", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeNotLike(String value) {
            addCriterion("ACCEPT_USER_TYPE not like", value, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeIn(List<String> values) {
            addCriterion("ACCEPT_USER_TYPE in", values, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeNotIn(List<String> values) {
            addCriterion("ACCEPT_USER_TYPE not in", values, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeBetween(String value1, String value2) {
            addCriterion("ACCEPT_USER_TYPE between", value1, value2, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptUserTypeNotBetween(String value1, String value2) {
            addCriterion("ACCEPT_USER_TYPE not between", value1, value2, "acceptUserType");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoIsNull() {
            addCriterion("ACCEPT_ACCT_NO is null");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoIsNotNull() {
            addCriterion("ACCEPT_ACCT_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoEqualTo(String value) {
            addCriterion("ACCEPT_ACCT_NO =", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoNotEqualTo(String value) {
            addCriterion("ACCEPT_ACCT_NO <>", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoGreaterThan(String value) {
            addCriterion("ACCEPT_ACCT_NO >", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("ACCEPT_ACCT_NO >=", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoLessThan(String value) {
            addCriterion("ACCEPT_ACCT_NO <", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoLessThanOrEqualTo(String value) {
            addCriterion("ACCEPT_ACCT_NO <=", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoLike(String value) {
            addCriterion("ACCEPT_ACCT_NO like", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoNotLike(String value) {
            addCriterion("ACCEPT_ACCT_NO not like", value, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoIn(List<String> values) {
            addCriterion("ACCEPT_ACCT_NO in", values, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoNotIn(List<String> values) {
            addCriterion("ACCEPT_ACCT_NO not in", values, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoBetween(String value1, String value2) {
            addCriterion("ACCEPT_ACCT_NO between", value1, value2, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptAcctNoNotBetween(String value1, String value2) {
            addCriterion("ACCEPT_ACCT_NO not between", value1, value2, "acceptAcctNo");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameIsNull() {
            addCriterion("ACCEPT_USER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameIsNotNull() {
            addCriterion("ACCEPT_USER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameEqualTo(String value) {
            addCriterion("ACCEPT_USER_NAME =", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameNotEqualTo(String value) {
            addCriterion("ACCEPT_USER_NAME <>", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameGreaterThan(String value) {
            addCriterion("ACCEPT_USER_NAME >", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("ACCEPT_USER_NAME >=", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameLessThan(String value) {
            addCriterion("ACCEPT_USER_NAME <", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameLessThanOrEqualTo(String value) {
            addCriterion("ACCEPT_USER_NAME <=", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameLike(String value) {
            addCriterion("ACCEPT_USER_NAME like", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameNotLike(String value) {
            addCriterion("ACCEPT_USER_NAME not like", value, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameIn(List<String> values) {
            addCriterion("ACCEPT_USER_NAME in", values, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameNotIn(List<String> values) {
            addCriterion("ACCEPT_USER_NAME not in", values, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameBetween(String value1, String value2) {
            addCriterion("ACCEPT_USER_NAME between", value1, value2, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptUserNameNotBetween(String value1, String value2) {
            addCriterion("ACCEPT_USER_NAME not between", value1, value2, "acceptUserName");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIsNull() {
            addCriterion("ACCEPT_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIsNotNull() {
            addCriterion("ACCEPT_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeEqualTo(Date value) {
            addCriterion("ACCEPT_TIME =", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotEqualTo(Date value) {
            addCriterion("ACCEPT_TIME <>", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeGreaterThan(Date value) {
            addCriterion("ACCEPT_TIME >", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ACCEPT_TIME >=", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeLessThan(Date value) {
            addCriterion("ACCEPT_TIME <", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeLessThanOrEqualTo(Date value) {
            addCriterion("ACCEPT_TIME <=", value, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeIn(List<Date> values) {
            addCriterion("ACCEPT_TIME in", values, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotIn(List<Date> values) {
            addCriterion("ACCEPT_TIME not in", values, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeBetween(Date value1, Date value2) {
            addCriterion("ACCEPT_TIME between", value1, value2, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andAcceptTimeNotBetween(Date value1, Date value2) {
            addCriterion("ACCEPT_TIME not between", value1, value2, "acceptTime");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeIsNull() {
            addCriterion("COMPLETE_USER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeIsNotNull() {
            addCriterion("COMPLETE_USER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeEqualTo(String value) {
            addCriterion("COMPLETE_USER_TYPE =", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeNotEqualTo(String value) {
            addCriterion("COMPLETE_USER_TYPE <>", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeGreaterThan(String value) {
            addCriterion("COMPLETE_USER_TYPE >", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("COMPLETE_USER_TYPE >=", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeLessThan(String value) {
            addCriterion("COMPLETE_USER_TYPE <", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeLessThanOrEqualTo(String value) {
            addCriterion("COMPLETE_USER_TYPE <=", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeLike(String value) {
            addCriterion("COMPLETE_USER_TYPE like", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeNotLike(String value) {
            addCriterion("COMPLETE_USER_TYPE not like", value, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeIn(List<String> values) {
            addCriterion("COMPLETE_USER_TYPE in", values, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeNotIn(List<String> values) {
            addCriterion("COMPLETE_USER_TYPE not in", values, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeBetween(String value1, String value2) {
            addCriterion("COMPLETE_USER_TYPE between", value1, value2, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteUserTypeNotBetween(String value1, String value2) {
            addCriterion("COMPLETE_USER_TYPE not between", value1, value2, "completeUserType");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoIsNull() {
            addCriterion("COMPLETE_ACCT_NO is null");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoIsNotNull() {
            addCriterion("COMPLETE_ACCT_NO is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoEqualTo(String value) {
            addCriterion("COMPLETE_ACCT_NO =", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoNotEqualTo(String value) {
            addCriterion("COMPLETE_ACCT_NO <>", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoGreaterThan(String value) {
            addCriterion("COMPLETE_ACCT_NO >", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("COMPLETE_ACCT_NO >=", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoLessThan(String value) {
            addCriterion("COMPLETE_ACCT_NO <", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoLessThanOrEqualTo(String value) {
            addCriterion("COMPLETE_ACCT_NO <=", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoLike(String value) {
            addCriterion("COMPLETE_ACCT_NO like", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoNotLike(String value) {
            addCriterion("COMPLETE_ACCT_NO not like", value, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoIn(List<String> values) {
            addCriterion("COMPLETE_ACCT_NO in", values, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoNotIn(List<String> values) {
            addCriterion("COMPLETE_ACCT_NO not in", values, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoBetween(String value1, String value2) {
            addCriterion("COMPLETE_ACCT_NO between", value1, value2, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteAcctNoNotBetween(String value1, String value2) {
            addCriterion("COMPLETE_ACCT_NO not between", value1, value2, "completeAcctNo");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameIsNull() {
            addCriterion("COMPLETE_USER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameIsNotNull() {
            addCriterion("COMPLETE_USER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameEqualTo(String value) {
            addCriterion("COMPLETE_USER_NAME =", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameNotEqualTo(String value) {
            addCriterion("COMPLETE_USER_NAME <>", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameGreaterThan(String value) {
            addCriterion("COMPLETE_USER_NAME >", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("COMPLETE_USER_NAME >=", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameLessThan(String value) {
            addCriterion("COMPLETE_USER_NAME <", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameLessThanOrEqualTo(String value) {
            addCriterion("COMPLETE_USER_NAME <=", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameLike(String value) {
            addCriterion("COMPLETE_USER_NAME like", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameNotLike(String value) {
            addCriterion("COMPLETE_USER_NAME not like", value, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameIn(List<String> values) {
            addCriterion("COMPLETE_USER_NAME in", values, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameNotIn(List<String> values) {
            addCriterion("COMPLETE_USER_NAME not in", values, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameBetween(String value1, String value2) {
            addCriterion("COMPLETE_USER_NAME between", value1, value2, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteUserNameNotBetween(String value1, String value2) {
            addCriterion("COMPLETE_USER_NAME not between", value1, value2, "completeUserName");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagIsNull() {
            addCriterion("COMPLETE_RESULT_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagIsNotNull() {
            addCriterion("COMPLETE_RESULT_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagEqualTo(String value) {
            addCriterion("COMPLETE_RESULT_FLAG =", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagNotEqualTo(String value) {
            addCriterion("COMPLETE_RESULT_FLAG <>", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagGreaterThan(String value) {
            addCriterion("COMPLETE_RESULT_FLAG >", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagGreaterThanOrEqualTo(String value) {
            addCriterion("COMPLETE_RESULT_FLAG >=", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagLessThan(String value) {
            addCriterion("COMPLETE_RESULT_FLAG <", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagLessThanOrEqualTo(String value) {
            addCriterion("COMPLETE_RESULT_FLAG <=", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagLike(String value) {
            addCriterion("COMPLETE_RESULT_FLAG like", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagNotLike(String value) {
            addCriterion("COMPLETE_RESULT_FLAG not like", value, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagIn(List<String> values) {
            addCriterion("COMPLETE_RESULT_FLAG in", values, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagNotIn(List<String> values) {
            addCriterion("COMPLETE_RESULT_FLAG not in", values, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagBetween(String value1, String value2) {
            addCriterion("COMPLETE_RESULT_FLAG between", value1, value2, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteResultFlagNotBetween(String value1, String value2) {
            addCriterion("COMPLETE_RESULT_FLAG not between", value1, value2, "completeResultFlag");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNull() {
            addCriterion("COMPLETE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNotNull() {
            addCriterion("COMPLETE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeEqualTo(Date value) {
            addCriterion("COMPLETE_TIME =", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotEqualTo(Date value) {
            addCriterion("COMPLETE_TIME <>", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThan(Date value) {
            addCriterion("COMPLETE_TIME >", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("COMPLETE_TIME >=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThan(Date value) {
            addCriterion("COMPLETE_TIME <", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("COMPLETE_TIME <=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIn(List<Date> values) {
            addCriterion("COMPLETE_TIME in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotIn(List<Date> values) {
            addCriterion("COMPLETE_TIME not in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeBetween(Date value1, Date value2) {
            addCriterion("COMPLETE_TIME between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("COMPLETE_TIME not between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeIsNull() {
            addCriterion("CREATE_USER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeIsNotNull() {
            addCriterion("CREATE_USER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeEqualTo(String value) {
            addCriterion("CREATE_USER_TYPE =", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeNotEqualTo(String value) {
            addCriterion("CREATE_USER_TYPE <>", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeGreaterThan(String value) {
            addCriterion("CREATE_USER_TYPE >", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("CREATE_USER_TYPE >=", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeLessThan(String value) {
            addCriterion("CREATE_USER_TYPE <", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeLessThanOrEqualTo(String value) {
            addCriterion("CREATE_USER_TYPE <=", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeLike(String value) {
            addCriterion("CREATE_USER_TYPE like", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeNotLike(String value) {
            addCriterion("CREATE_USER_TYPE not like", value, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeIn(List<String> values) {
            addCriterion("CREATE_USER_TYPE in", values, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeNotIn(List<String> values) {
            addCriterion("CREATE_USER_TYPE not in", values, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeBetween(String value1, String value2) {
            addCriterion("CREATE_USER_TYPE between", value1, value2, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateUserTypeNotBetween(String value1, String value2) {
            addCriterion("CREATE_USER_TYPE not between", value1, value2, "createUserType");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoIsNull() {
            addCriterion("CREATE_ACCT_NO is null");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoIsNotNull() {
            addCriterion("CREATE_ACCT_NO is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoEqualTo(String value) {
            addCriterion("CREATE_ACCT_NO =", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoNotEqualTo(String value) {
            addCriterion("CREATE_ACCT_NO <>", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoGreaterThan(String value) {
            addCriterion("CREATE_ACCT_NO >", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("CREATE_ACCT_NO >=", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoLessThan(String value) {
            addCriterion("CREATE_ACCT_NO <", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoLessThanOrEqualTo(String value) {
            addCriterion("CREATE_ACCT_NO <=", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoLike(String value) {
            addCriterion("CREATE_ACCT_NO like", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoNotLike(String value) {
            addCriterion("CREATE_ACCT_NO not like", value, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoIn(List<String> values) {
            addCriterion("CREATE_ACCT_NO in", values, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoNotIn(List<String> values) {
            addCriterion("CREATE_ACCT_NO not in", values, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoBetween(String value1, String value2) {
            addCriterion("CREATE_ACCT_NO between", value1, value2, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateAcctNoNotBetween(String value1, String value2) {
            addCriterion("CREATE_ACCT_NO not between", value1, value2, "createAcctNo");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIsNull() {
            addCriterion("CREATE_USER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIsNotNull() {
            addCriterion("CREATE_USER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameEqualTo(String value) {
            addCriterion("CREATE_USER_NAME =", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotEqualTo(String value) {
            addCriterion("CREATE_USER_NAME <>", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameGreaterThan(String value) {
            addCriterion("CREATE_USER_NAME >", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("CREATE_USER_NAME >=", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLessThan(String value) {
            addCriterion("CREATE_USER_NAME <", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLessThanOrEqualTo(String value) {
            addCriterion("CREATE_USER_NAME <=", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameLike(String value) {
            addCriterion("CREATE_USER_NAME like", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotLike(String value) {
            addCriterion("CREATE_USER_NAME not like", value, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameIn(List<String> values) {
            addCriterion("CREATE_USER_NAME in", values, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotIn(List<String> values) {
            addCriterion("CREATE_USER_NAME not in", values, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameBetween(String value1, String value2) {
            addCriterion("CREATE_USER_NAME between", value1, value2, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateUserNameNotBetween(String value1, String value2) {
            addCriterion("CREATE_USER_NAME not between", value1, value2, "createUserName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("CREATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("CREATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("CREATE_TIME =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("CREATE_TIME <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("CREATE_TIME >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("CREATE_TIME <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("CREATE_TIME in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("CREATE_TIME not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andStatusFlagIsNull() {
            addCriterion("STATUS_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andStatusFlagIsNotNull() {
            addCriterion("STATUS_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andStatusFlagEqualTo(String value) {
            addCriterion("STATUS_FLAG =", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagNotEqualTo(String value) {
            addCriterion("STATUS_FLAG <>", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagGreaterThan(String value) {
            addCriterion("STATUS_FLAG >", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagGreaterThanOrEqualTo(String value) {
            addCriterion("STATUS_FLAG >=", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagLessThan(String value) {
            addCriterion("STATUS_FLAG <", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagLessThanOrEqualTo(String value) {
            addCriterion("STATUS_FLAG <=", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagLike(String value) {
            addCriterion("STATUS_FLAG like", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagNotLike(String value) {
            addCriterion("STATUS_FLAG not like", value, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagIn(List<String> values) {
            addCriterion("STATUS_FLAG in", values, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagNotIn(List<String> values) {
            addCriterion("STATUS_FLAG not in", values, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagBetween(String value1, String value2) {
            addCriterion("STATUS_FLAG between", value1, value2, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusFlagNotBetween(String value1, String value2) {
            addCriterion("STATUS_FLAG not between", value1, value2, "statusFlag");
            return (Criteria) this;
        }

        public Criteria andStatusTimeIsNull() {
            addCriterion("STATUS_TIME is null");
            return (Criteria) this;
        }

        public Criteria andStatusTimeIsNotNull() {
            addCriterion("STATUS_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andStatusTimeEqualTo(Date value) {
            addCriterion("STATUS_TIME =", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotEqualTo(Date value) {
            addCriterion("STATUS_TIME <>", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThan(Date value) {
            addCriterion("STATUS_TIME >", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("STATUS_TIME >=", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThan(Date value) {
            addCriterion("STATUS_TIME <", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("STATUS_TIME <=", value, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeIn(List<Date> values) {
            addCriterion("STATUS_TIME in", values, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotIn(List<Date> values) {
            addCriterion("STATUS_TIME not in", values, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeBetween(Date value1, Date value2) {
            addCriterion("STATUS_TIME between", value1, value2, "statusTime");
            return (Criteria) this;
        }

        public Criteria andStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("STATUS_TIME not between", value1, value2, "statusTime");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateIsNull() {
            addCriterion("DEAL_EVALUATE is null");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateIsNotNull() {
            addCriterion("DEAL_EVALUATE is not null");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateEqualTo(String value) {
            addCriterion("DEAL_EVALUATE =", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateNotEqualTo(String value) {
            addCriterion("DEAL_EVALUATE <>", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateGreaterThan(String value) {
            addCriterion("DEAL_EVALUATE >", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateGreaterThanOrEqualTo(String value) {
            addCriterion("DEAL_EVALUATE >=", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateLessThan(String value) {
            addCriterion("DEAL_EVALUATE <", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateLessThanOrEqualTo(String value) {
            addCriterion("DEAL_EVALUATE <=", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateLike(String value) {
            addCriterion("DEAL_EVALUATE like", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateNotLike(String value) {
            addCriterion("DEAL_EVALUATE not like", value, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateIn(List<String> values) {
            addCriterion("DEAL_EVALUATE in", values, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateNotIn(List<String> values) {
            addCriterion("DEAL_EVALUATE not in", values, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateBetween(String value1, String value2) {
            addCriterion("DEAL_EVALUATE between", value1, value2, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealEvaluateNotBetween(String value1, String value2) {
            addCriterion("DEAL_EVALUATE not between", value1, value2, "dealEvaluate");
            return (Criteria) this;
        }

        public Criteria andDealContentIsNull() {
            addCriterion("DEAL_CONTENT is null");
            return (Criteria) this;
        }

        public Criteria andDealContentIsNotNull() {
            addCriterion("DEAL_CONTENT is not null");
            return (Criteria) this;
        }

        public Criteria andDealContentEqualTo(String value) {
            addCriterion("DEAL_CONTENT =", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentNotEqualTo(String value) {
            addCriterion("DEAL_CONTENT <>", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentGreaterThan(String value) {
            addCriterion("DEAL_CONTENT >", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentGreaterThanOrEqualTo(String value) {
            addCriterion("DEAL_CONTENT >=", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentLessThan(String value) {
            addCriterion("DEAL_CONTENT <", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentLessThanOrEqualTo(String value) {
            addCriterion("DEAL_CONTENT <=", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentLike(String value) {
            addCriterion("DEAL_CONTENT like", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentNotLike(String value) {
            addCriterion("DEAL_CONTENT not like", value, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentIn(List<String> values) {
            addCriterion("DEAL_CONTENT in", values, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentNotIn(List<String> values) {
            addCriterion("DEAL_CONTENT not in", values, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentBetween(String value1, String value2) {
            addCriterion("DEAL_CONTENT between", value1, value2, "dealContent");
            return (Criteria) this;
        }

        public Criteria andDealContentNotBetween(String value1, String value2) {
            addCriterion("DEAL_CONTENT not between", value1, value2, "dealContent");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1IsNull() {
            addCriterion("PHOTO_URL1 is null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1IsNotNull() {
            addCriterion("PHOTO_URL1 is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1EqualTo(String value) {
            addCriterion("PHOTO_URL1 =", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1NotEqualTo(String value) {
            addCriterion("PHOTO_URL1 <>", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1GreaterThan(String value) {
            addCriterion("PHOTO_URL1 >", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1GreaterThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL1 >=", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1LessThan(String value) {
            addCriterion("PHOTO_URL1 <", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1LessThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL1 <=", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1Like(String value) {
            addCriterion("PHOTO_URL1 like", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1NotLike(String value) {
            addCriterion("PHOTO_URL1 not like", value, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1In(List<String> values) {
            addCriterion("PHOTO_URL1 in", values, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1NotIn(List<String> values) {
            addCriterion("PHOTO_URL1 not in", values, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1Between(String value1, String value2) {
            addCriterion("PHOTO_URL1 between", value1, value2, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl1NotBetween(String value1, String value2) {
            addCriterion("PHOTO_URL1 not between", value1, value2, "photoUrl1");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2IsNull() {
            addCriterion("PHOTO_URL2 is null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2IsNotNull() {
            addCriterion("PHOTO_URL2 is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2EqualTo(String value) {
            addCriterion("PHOTO_URL2 =", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2NotEqualTo(String value) {
            addCriterion("PHOTO_URL2 <>", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2GreaterThan(String value) {
            addCriterion("PHOTO_URL2 >", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2GreaterThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL2 >=", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2LessThan(String value) {
            addCriterion("PHOTO_URL2 <", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2LessThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL2 <=", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2Like(String value) {
            addCriterion("PHOTO_URL2 like", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2NotLike(String value) {
            addCriterion("PHOTO_URL2 not like", value, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2In(List<String> values) {
            addCriterion("PHOTO_URL2 in", values, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2NotIn(List<String> values) {
            addCriterion("PHOTO_URL2 not in", values, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2Between(String value1, String value2) {
            addCriterion("PHOTO_URL2 between", value1, value2, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl2NotBetween(String value1, String value2) {
            addCriterion("PHOTO_URL2 not between", value1, value2, "photoUrl2");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3IsNull() {
            addCriterion("PHOTO_URL3 is null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3IsNotNull() {
            addCriterion("PHOTO_URL3 is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3EqualTo(String value) {
            addCriterion("PHOTO_URL3 =", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3NotEqualTo(String value) {
            addCriterion("PHOTO_URL3 <>", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3GreaterThan(String value) {
            addCriterion("PHOTO_URL3 >", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3GreaterThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL3 >=", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3LessThan(String value) {
            addCriterion("PHOTO_URL3 <", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3LessThanOrEqualTo(String value) {
            addCriterion("PHOTO_URL3 <=", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3Like(String value) {
            addCriterion("PHOTO_URL3 like", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3NotLike(String value) {
            addCriterion("PHOTO_URL3 not like", value, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3In(List<String> values) {
            addCriterion("PHOTO_URL3 in", values, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3NotIn(List<String> values) {
            addCriterion("PHOTO_URL3 not in", values, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3Between(String value1, String value2) {
            addCriterion("PHOTO_URL3 between", value1, value2, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andPhotoUrl3NotBetween(String value1, String value2) {
            addCriterion("PHOTO_URL3 not between", value1, value2, "photoUrl3");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}