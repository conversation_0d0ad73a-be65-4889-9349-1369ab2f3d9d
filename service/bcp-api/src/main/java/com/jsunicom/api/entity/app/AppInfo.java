package com.jsunicom.api.entity.app;


import java.util.Date;

public class AppInfo extends AppBaseInfo {

    private static final long serialVersionUID = -4168273831828211791L;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 应用令牌
     */
    private String token;

    /**
     * 服务器地址
     */
    private String url;

    /**
     * 消息传送方式
     */
    private MessagingMode messagingMode;

    /**
     * 票据
     */
    private String accessToken;

    /**
     * 消息加解密密钥
     */
    private String encodingAesKey;

    /**
     * api ticket
     */
    private String apiTicket;

    /**
     * accessToken上一次更新时间
     */
    private Date accessTokenLastUpdateTime;

    /**
     * apiTicket上一次更新时间
     */
    private Date apiTicketLastUpdateTime;

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public MessagingMode getMessagingMode() {
        return messagingMode;
    }

    public void setMessagingMode(MessagingMode messagingMode) {
        this.messagingMode = messagingMode;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getEncodingAesKey() {
        return encodingAesKey;
    }

    public void setEncodingAesKey(String encodingAesKey) {
        this.encodingAesKey = encodingAesKey;
    }

    public String getApiTicket() {
        return apiTicket;
    }

    public void setApiTicket(String apiTicket) {
        this.apiTicket = apiTicket;
    }

    public Date getAccessTokenLastUpdateTime() {
        return accessTokenLastUpdateTime;
    }

    public void setAccessTokenLastUpdateTime(Date accessTokenLastUpdateTime) {
        this.accessTokenLastUpdateTime = accessTokenLastUpdateTime;
    }

    public Date getApiTicketLastUpdateTime() {
        return apiTicketLastUpdateTime;
    }

    public void setApiTicketLastUpdateTime(Date apiTicketLastUpdateTime) {
        this.apiTicketLastUpdateTime = apiTicketLastUpdateTime;
    }

}
