package com.jsunicom.api.model;

import com.jsunicom.api.model.base.BasicModel;

import java.sql.Timestamp;

public class CommisIntoAcct extends BasicModel {
	private static final long serialVersionUID = 1L;

	private Long id;
	private Long partnerId;
	private String orderNo;  //
	private String orgCode;
	private Long commisRuleId;
	private java.math.BigDecimal commisAmt;
	private String remark;
	private String symbol;
	private String state;
	private String createBy;
	private java.sql.Timestamp createTime;
	private String updateBy;
	private java.sql.Timestamp updateTime;
	private String type;
	private String goodsCode;
	private Long merchantId;
	private String merchantName;
	private String orderGoodsNo;//  '商品订单编码',
	private Long commisRuleMerchantId;
	private String level;//订单层级
	private java.sql.Timestamp planPayDate;//计划发放时间


	private String goodsMsisdn;
	private String busiType;

	private String orderPartnerId;

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public String getGoodsMsisdn() {
		return goodsMsisdn;
	}

	public void setGoodsMsisdn(String goodsMsisdn) {
		this.goodsMsisdn = goodsMsisdn;
	}

	public Timestamp getPlanPayDate() {
		return planPayDate;
	}

	public void setPlanPayDate(Timestamp planPayDate) {
		this.planPayDate = planPayDate;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getOrderGoodsNo() {
		return orderGoodsNo;
	}

	public void setOrderGoodsNo(String orderGoodsNo) {
		this.orderGoodsNo = orderGoodsNo;
	}

	public Long getCommisRuleMerchantId() {
		return commisRuleMerchantId;
	}

	public void setCommisRuleMerchantId(Long commisRuleMerchantId) {
		this.commisRuleMerchantId = commisRuleMerchantId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Long partnerId) {
		this.partnerId = partnerId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public Long getCommisRuleId() {
		return commisRuleId;
	}

	public void setCommisRuleId(Long commisRuleId) {
		this.commisRuleId = commisRuleId;
	}

	public java.math.BigDecimal getCommisAmt() {
		return commisAmt;
	}

	public void setCommisAmt(java.math.BigDecimal commisAmt) {
		this.commisAmt = commisAmt;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSymbol() {
		return symbol;
	}

	public void setSymbol(String symbol) {
		this.symbol = symbol;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public java.sql.Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(java.sql.Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public java.sql.Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(java.sql.Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getGoodsCode() {
		return goodsCode;
	}

	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}

	public String getOrderPartnerId() {
		return orderPartnerId;
	}

	public void setOrderPartnerId(String orderPartnerId) {
		this.orderPartnerId = orderPartnerId;
	}
}

/*List columns as follows:
"id", "org_code", "partner_id", "order_id", "busi_id", "commis_rule_id", "commis_amt", 
"remark", "symbol", "state", "create_by", "create_time", "update_by", "update_time"
 */