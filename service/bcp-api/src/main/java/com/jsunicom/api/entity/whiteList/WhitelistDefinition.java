package com.jsunicom.api.entity.whiteList;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

public class WhitelistDefinition {
    @NotEmpty
    private List<String> noAuthenticationRequest = new ArrayList<>();

    private List<String> noAuthorizationRequest = new ArrayList<>();

    private List<String> noEncryptRequest = new ArrayList<>();


    public List<String> getNoEncryptRequest() {
        return noEncryptRequest;
    }

    public void setNoEncryptRequest(List<String> noEncryptRequest) {
        this.noEncryptRequest = noEncryptRequest;
    }

    public List<String> getNoAuthenticationRequest() {
        return noAuthenticationRequest;
    }

    public void setNoAuthenticationRequest(List<String> noAuthenticationRequest) {
        this.noAuthenticationRequest = noAuthenticationRequest;
    }

    public List<String> getNoAuthorizationRequest() {
        return noAuthorizationRequest;
    }

    public void setNoAuthorizationRequest(List<String> noAuthorizationRequest) {
        this.noAuthorizationRequest = noAuthorizationRequest;
    }
}
