package com.jsunicom.api.common.exception;

public abstract class BaseExceptionUtils {
    public BaseExceptionUtils() {
    }

    public static String buildMessage(String message, Throwable cause) {
        if (cause != null) {
            StringBuilder sb = new StringBuilder();
            if (message != null) {
                sb.append(message).append("; ");
            }

            sb.append("mkt exception is ").append(cause);
            return sb.toString();
        } else {
            return message;
        }
    }
}
