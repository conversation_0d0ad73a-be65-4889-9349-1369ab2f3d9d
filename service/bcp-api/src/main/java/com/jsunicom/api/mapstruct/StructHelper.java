package com.jsunicom.api.mapstruct;

import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @program: sx_school_backend
 * @description:
 * @author: huangyx
 * @company:亚信科技（中国）有限公司
 * @create: 2023/07/19 11:42
 **/
@Component
@Named("StructHelper")
public class StructHelper {
    /**
     * Long转换成String
     *
     * @param value
     * @return
     */
    @Named("toLongConvertString")
    public static String toLongConvertString(Long value) {
        return value != null ? String.valueOf(value) : null;
    }

    /**
     * Date转换成String
     *
     * @param value
     * @return
     */
    @Named("toDateConvertString")
    public static String toDateConvertString(Date value) {
        return value != null ? new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( value ) : null;
    }
}
