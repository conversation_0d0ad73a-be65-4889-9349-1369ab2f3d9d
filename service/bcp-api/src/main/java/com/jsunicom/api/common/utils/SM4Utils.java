package com.jsunicom.api.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.utils.DesHelper;
import com.jsunicom.common.core.util.Hex;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;
import java.util.Base64;

/**
 * @BelongsProject: 20230926
 * @BelongsPackage: com.jsunicom.api.common.utils
 * @Author: yanh<PERSON><PERSON>
 * @CreateTime: 2024-05-08  14:28
 * @Description: TODO sm4加密算法工具类
 * @Version: 1.0
 */
public class SM4Utils {
    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String ENCODING = "UTF-8";
    public static final String ALGORITHM_NAME = "SM4";
    // 加密算法/分组加密模式/分组填充方式
    // PKCS5Padding-以8个字节为一组进行分组加密
    // 定义分组加密模式使用：PKCS5Padding
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    // 128-32位16进制；256-64位16进制
    public static final int DEFAULT_KEY_SIZE = 128;

    /**
     * 生成ECB暗号
     *
     * @param algorithmName 算法名称
     * @param mode          模式
     * @param key
     * @return
     * @throws Exception
     * @explain ECB模式（电子密码本模式：Electronic codebook）
     */
    private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }

    /**
     * 自动生成密钥
     *
     * @return
     * @throws
     * @throws
     * @explain
     */
    public static String generateKey() throws Exception {
        return new String(Hex.encode(generateKey(DEFAULT_KEY_SIZE)));
    }

    /**
     * @param keySize
     * @return
     * @throws Exception
     * @explain
     */
    public static byte[] generateKey(int keySize) throws Exception {
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

    /**
     * sm4加密
     *
     * @param hexKey   16进制密钥（忽略大小写）
     * @param paramStr 待加密字符串
     * @return 返回16进制的加密字符串
     * @throws Exception
     * @explain 加密模式：ECB
     * 密文长度不固定，会随着被加密字符串长度的变化而变化
     */
    public static String encryptEcb(String hexKey, String paramStr) throws Exception {
        String cipherText = "";
        // 16进制字符串-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // String-->byte[]
        byte[] srcData = paramStr.getBytes(ENCODING);
        // 加密后的数组
        byte[] cipherArray = encrypt_Ecb_Padding(keyData, srcData);
        // byte[]-->hexString
        cipherText = ByteUtils.toHexString(cipherArray);
        return cipherText;
    }

    /**
     * 加密模式之Ecb
     *
     * @param key
     * @param data
     * @return
     * @throws Exception
     * @explain
     */
    public static byte[] encrypt_Ecb_Padding(byte[] key, byte[] data) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data);
    }

    /**
     * sm4解密
     *
     * @param hexKey     16进制密钥
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用ECB
     */
    public static String decryptEcb(String hexKey, String cipherText) throws Exception {
        // 用于接收解密后的字符串
        String decryptStr = "";
        // hexString-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // hexString-->byte[]
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        // 解密
        byte[] srcData = decrypt_Ecb_Padding(keyData, cipherData);
        // byte[]-->String
        decryptStr = new String(srcData, ENCODING);
        return decryptStr;
    }

    /**
     * 解密
     *
     * @param key
     * @param cipherText
     * @return
     * @throws Exception
     */
    public static byte[] decrypt_Ecb_Padding(byte[] key, byte[] cipherText) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(cipherText);
    }

    /**
     * 校验加密前后的字符串是否为同一数据
     *
     * @param hexKey     16进制密钥（忽略大小写）
     * @param cipherText 16进制加密后的字符串
     * @param paramStr   加密前的字符串
     * @return 是否为同一数据
     * @throws Exception
     */
    public static boolean verifyEcb(String hexKey, String cipherText, String paramStr) throws Exception {
        // 用于接收校验结果
        boolean flag = false;
        // hexString-->byte[]
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        // 将16进制字符串转换成数组
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        // 解密
        byte[] decryptData = decrypt_Ecb_Padding(keyData, cipherData);
        // 将原字符串转换成byte[]
        byte[] srcData = paramStr.getBytes(ENCODING);
        // 判断2个数组是否一致
        flag = Arrays.equals(decryptData, srcData);
        return flag;
    }
    /**
     * CBC模式加密方法
     *
     * @param plaintext   明文
     * @param keyHex      16字节密钥（Hex字符串形式）
     * @param ivHex       16字节初始化向量（Hex字符串形式）
     * @return 加密后的Base64字符串
     * @throws Exception
     */
    public static String encryptCBC(String plaintext, String keyHex, String ivHex) throws Exception {
        // 转换密钥和IV为二进制字节数组
        byte[] keyData = hexStringToBytes(keyHex);
        byte[] ivBytes = hexStringToBytes(ivHex);


        // 创建秘钥与初始化向量
        Key sm4Key = new SecretKeySpec(keyData, ALGORITHM_NAME);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        return encryptC(plaintext, (SecretKey) sm4Key, ivParameterSpec);
    }

    /**
     * CBC模式解密方法
     *
     * @param ciphertext  密文（Base64字符串）
     * @param keyHex      16字节密钥（Hex字符串形式）
     * @param ivHex       16字节初始化向量（Hex字符串形式）
     * @return 解密后的明文字符串
     * @throws Exception
     */
    public static String decryptCBC(String ciphertext, String keyHex, String ivHex) throws Exception {
        // 转换密钥和IV为二进制字节数组
        byte[] keyData = hexStringToBytes(keyHex);
        byte[] ivBytes = hexStringToBytes(ivHex);


        // 创建秘钥与初始化向量
        Key sm4Key = new SecretKeySpec(keyData, ALGORITHM_NAME);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
        return decryptC(ciphertext, (SecretKey) sm4Key,ivParameterSpec);
    }
    private static byte[] hexStringToBytes(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    public static String encryptC(String plaintext, SecretKey secretKey, IvParameterSpec ivParameterSpec) throws Exception {
        Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", "BC");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes("UTF-8"));
        return ByteUtils.toHexString(encryptedBytes);
    }

    // 解密方法
    public static String decryptC(String ciphertext, SecretKey secretKey, IvParameterSpec ivParameterSpec) throws Exception {
        Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", "BC");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
        byte[] decryptedBytes = cipher.doFinal(hexStringToBytes(ciphertext));
        return new String(decryptedBytes, ENCODING);
    }

    public static void main(String[] args) {
        try {

            String newData= "0Rh9EkqlqWE3Yv9wkGO4heegzOlWqsk8BlJupH7QW.2APg1LDaD2D8XxClnUKezwgkKVhzTLsd1qRE6cmYgeKBjkS2_h20ke5w4tvptKVMiNsq.Ciptli4a";
            String key="828ad44bacf6863d71d9fef437767e5a";
//            String s = SM4Utils.decryptEcb(key, newData);
//            System.out.println("s = " + s);
            String mobile="fc05385fad8410ce1d1c4eb83d7cb1f71b815ca5d08735e4744d67d68854cb4f";
            String shakey="D24k81#$Wa9x";
            String s1 = DesHelper.tripleDesDecodeECB(shakey, "17692336645");
            System.out.println("s1 = " + s1);
//            System.out.println("newData = " + newData);
//            String newData=dataLast+s8;
            String secKey=newData.substring(0,64);
            System.out.println("secKey = " + secKey);
            String s6Q=secKey.substring(0,21);
            System.out.println("s6Q = " + s6Q);
            String secAndIv=secKey.substring(21);
            String s7ivNew=secAndIv.substring(0,32);
            System.out.println("s7ivNew = " + s7ivNew);
            String s6New=s6Q+secAndIv.substring(32);
            System.out.println("s6New = " + s6New);
            System.out.println("newData.substring(64) = " + newData.substring(64));
            String s9 = decryptCBC(newData.substring(64), s6New, s7ivNew);
            System.out.println("s9 = " + s9);
            /*JSONObject jsonObject = new JSONObject();
            jsonObject.put("orderType","2");
            String data = jsonObject.toJSONString();
            //生成key
            //String key = generateKey();
            String key = "58d306d25e6a94feb07333ebb397d27a";
            //System.out.println("key:" + key);
            //加密
            String cipher = SM4Utils.encryptEcb("58d306d25e6a94feb07333ebb397d27a", data);
            System.out.println("加密后："+cipher);
            //判断是否正确
            System.out.println(SM4Utils.verifyEcb(key, cipher, data));// true
            //解密
            String res = SM4Utils.decryptEcb(key, cipher);
            System.out.println("解密后："+res);*/
            //String cipher = SM4Utils.encryptEcb("f3332970e025d0531bf31b8c36637d9c", "https://localhost:8085/spa-h5//placeOrder/#/goodsPage?sign=30ba15f20a87fa8f3b1a2be5cf25eeb2240801d5fc386a902418c09f11658a6c091376651843e5a1cc15d5dbdbcdfe8bd285a85cf205b8ed42d2578303a6883355f1e794a8b7eb25a39881086c6e936b");
            //System.out.println("加密后：" + cipher);
            //api工程（生产）接口解密的key  828ad44bacf6863d71d9fef437767e5a
            //api工程（测试）接口解密的key  f3332970e025d0531bf31b8c36637d9c
            /*String jiemi = SM4Utils.decryptEcb("f3332970e025d0531bf31b8c36637d9c", "62d5e219e980debbb329c74fbb3e6f14de0cee82cfe28fae39579df12d0cbdf5f873e10c2f3c13a5476aee828441108f");
            System.out.println("解密后："+jiemi);*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
