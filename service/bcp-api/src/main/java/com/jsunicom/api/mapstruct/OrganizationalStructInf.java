package com.jsunicom.api.mapstruct;

import com.jsunicom.api.entity.organizational.OrganizationalClubsDto;
import com.jsunicom.api.entity.organizational.OrganizationalDepartClubsDto;
import com.jsunicom.api.entity.organizational.OrganizationalMembersDto;
import com.jsunicom.api.vo.organizational.OrganizationalClubsVo;
import com.jsunicom.api.vo.organizational.OrganizationalDepartClubsVo;
import com.jsunicom.api.vo.organizational.OrganizationalMembersVo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrganizationalStructInf {
    List<OrganizationalClubsVo> tranceToOrganizationalClubsVoList(List<OrganizationalClubsDto> organizationalClubsDtoList);
    List<OrganizationalDepartClubsVo> tranceToOrganizationalDepartClubsVoList(List<OrganizationalDepartClubsDto> organizationalDepartClubsDtoList);
    List<OrganizationalMembersVo> tranceToOrganizationalMembersVoList(List<OrganizationalMembersDto> organizationalMembersDtoList);
}
