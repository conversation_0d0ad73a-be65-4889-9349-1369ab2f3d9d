package com.jsunicom.api.po;

import java.io.Serializable;
import java.util.Date;

public class WoScYiMemberPositionChangeRecord implements Serializable {
    private Long changeId;

    private String batchId;

    private Long memberId;

    private String memberName;

    private String beforeRoleType;

    private String beforePositionType;

    private String afterRoleType;

    private String afterPositionType;

    private String changeType;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private String reserve1;

    private String reserve2;

    private String reserve3;

    private static final long serialVersionUID = 1L;

    public Long getChangeId() {
        return changeId;
    }

    public void setChangeId(Long changeId) {
        this.changeId = changeId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName == null ? null : memberName.trim();
    }

    public String getBeforeRoleType() {
        return beforeRoleType;
    }

    public void setBeforeRoleType(String beforeRoleType) {
        this.beforeRoleType = beforeRoleType == null ? null : beforeRoleType.trim();
    }

    public String getBeforePositionType() {
        return beforePositionType;
    }

    public void setBeforePositionType(String beforePositionType) {
        this.beforePositionType = beforePositionType == null ? null : beforePositionType.trim();
    }

    public String getAfterRoleType() {
        return afterRoleType;
    }

    public void setAfterRoleType(String afterRoleType) {
        this.afterRoleType = afterRoleType == null ? null : afterRoleType.trim();
    }

    public String getAfterPositionType() {
        return afterPositionType;
    }

    public void setAfterPositionType(String afterPositionType) {
        this.afterPositionType = afterPositionType == null ? null : afterPositionType.trim();
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType == null ? null : changeType.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getReserve1() {
        return reserve1;
    }

    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1 == null ? null : reserve1.trim();
    }

    public String getReserve2() {
        return reserve2;
    }

    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2 == null ? null : reserve2.trim();
    }

    public String getReserve3() {
        return reserve3;
    }

    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3 == null ? null : reserve3.trim();
    }
}