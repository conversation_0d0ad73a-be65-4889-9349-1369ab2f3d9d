package com.jsunicom.api.common;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/30.
 */
public class Constants {
    /** 登录用户KEY */
    public static final String USER_ID_KEY = "userId";

    /** 登录用户KEY */
    public static final String UNIQUE_ID = "uniqueId";

    /** 登录用户信息KEY */
    public static final String USER_INFO_KEY = "userInfo";

    /** 是否青创社成员标识KEY   1 校区经理，2 青创社成员，3 其它 */
    public static final String MEMBER_FLAG_KEY = "memberFlag";

    /** 是否青创社成员标识value   1 校区经理，2 青创社成员，3 其它 */
    public static final String MEMBER_FLAG_V1 = "1";
    /** 是否青创社成员标识value   1 校区经理，2 青创社成员，3 其它 */
    public static final String MEMBER_FLAG_V2 = "2";
    /** 是否青创社成员标识value   1 校区经理，2 青创社成员，3 其它 */
    public static final String MEMBER_FLAG_V3 = "3";

    /** 角色类型KEY */
    public static final String ROLE_TYPE_KEY = "roleType";

    /** 青创社成员登录信息KEY */
    public static final String MEMBER_INFO_KEY = "memberInfo";

    /** 模拟点赞用户的唯一标识 */
    public static final String GET_DRAW_CHANCE_KEY = "drawChanceKey";

    public final static Integer MAX_EXPIRED_SECONDS = 2592000;
    //佣金规则类型：1-通用（自然人、佣金商户） 2-权益商户佣金
    public static final String COMMIS_RULE_TYPE_NORMAL = "1";
    public static final String COMMIS_RULE_TYPE_RIGHTS = "2";
    //佣金计算的类型，原来判断是否需要计算普通自然人还是商户的佣金
    public static final String COMMIS_RULE_TYPE_PARTENR = "partner";
    public static final String COMMIS_RULE_TYPE_MERCHANT = "merchant";
    //佣金计算是否需要判断裂变等级
    public static final String COMMIS_RULE_JUDGE_LEVEL = "1";//需要判断
    public static final String COMMIS_RULE_NO_JUDGE_LEVEL = "0";//不需要判断
    /**
	 * 1-普通活动类型
	 */
	public static final String PROMOT_TYPE_NORMAL = "1";
	/**
	 * 2-商户专属活动类型
	 */
	public static final String PROMOT_TYPE_MERCHANT_ONLY = "2";
	/**
	 * 3-权益商户通用活动类型
	 */
	public static final String PROMOT_TYPE_RIGHTS = "3";
	/**
	 * 4-商户通用活动类型
	 */
	public static final String PROMOT_TYPE_MERCHANT_NORMAL = "4";

    public final static String AUDIT_STATE_DOING = "0";  //待审核
    public final static String AUDIT_STATE_SUCC = "1"; //审核通过 启用
    public final static String AUDIT_STATE_SUCC_DISABLE = "2"; //审核通过 禁用
    public final static String AUDIT_STATE_FAIL = "3"; //审核拒绝
    public final static String AUDIT_STATE_WAITING = "4"; // 待补充资料
    public final static String AUDIT_STATE_REAUDIT = "5"; // 已补充资料,待重审
    public final static String AUDIT_STATE_CARDBINDING = "6"; // 已补充资料,待重审

    public final static String USER_STATE_ENABLE = "N";
    public final static String USER_STATE_DISABLE = "F";
    public final static String USER_STATE_DELETE = "D";
    /*二维码类型——临时码*/
    public final static String QRCODE_STATE_TEMP = "0";
    /*二维码类型——长期码*/
    public final static String QRCODE_STATE_STATIC = "1";

    //
    public final static String PARTNER_TYPE_PERSON = "0";  // 自合伙人类型然人
    public final static String PARTNER_TYPE_MERCHANT = "1";  // 普通商户
    public final static String PARTNER_TYPE_CHAINMERCHANT = "2";  // 连锁商户
    public final static String PARTNER_TYPE_MERCHANTAP = "3";  // 连锁总店

    public final static String MERCHANT_REMOVED = "0"; //删除
    public final static String MERCHANT_USING = "1";  //在用
    public final static String MERCHANT_FORBIDDEN = "2"; //禁用

    public final static String MERCHANT_TYPE_MAIN = "1";  //总店
    public final static String MERCHANT_TYPE_BRANCH = "2"; //分店
    public final static String MERCHANT_TYPE_NORMAL = "3"; //普通店铺
    public final static String MERCHANT_TYPE_ENTITY = "4";//实体渠道

    public final static String IMAGE_CARD_POSI="0"; //身份证正面
    public final static String IMAGE_CARD_AGA="1"; //身份证反面
    public final static String IMAGE_BUSI_LIC="2"; //营业执照
    public final static String IMAGE_PHOTO="3"; //头像
    public final static String IMAGE_BUSI_PERMIT="4"; //经营许可

    public final static String MER_ADMIN_FALSE="0"; //非店铺管理员
    public final static String MER_ADMIN_TRUE="1"; //店铺管理员

    public final static String EMPLOEE_NOT="0"; //非白名单
    public final static String EMPLOEE_UNIOM="1"; //联通在职
    public final static String EMPLOEE_NOT_UNIOM="2";


    public final static String BUS_LINE_PUBLIC="30"; //条线:公众
    public final static String REGISTER_PATH_JD="2"; //注册来源:jd能人注册

    public final static String REGISTER_PATH_LE_PENG="3"; //注册来源:乐芃注册
    /*国政通校验结果*/
    public final static String GZT_TRUE="0";//校验通过
    public final static String GZT_FALSE="1";//校验不通过
    public final static String GZT_ERROR="2";//校验失败，系统异常
    public final static String GZT_SWITCH_CLOSE="3";//校验开关关闭
    //通用字符串型true or false
    public final static String RESULT_SUCCESS = "1";//成功
    public final static String RESULT_FAIL = "0";//失败

    public final static int COMMIS_WECHAT_BIND_TO_BE_PAY = 1;
    public final static int COMMIS_WECHAT_BIND_HAVE_PAY = 2;
    public final static int COMMIS_WECHAT_BIND_FAILED = 3;

    public final static String PDF_TASK_STATE_CREATE = "0";
    public final static String PDF_TASK_STATE_RUNNING = "1";
    public final static String PDF_TASK_STATE_COMPLETE = "2";
    public final static String PDF_TASK_STATE_ERROR = "3";

    public final static String DOWNLOAD_TASK_STATE_CREATE = "0";
    public final static String DOWNLOAD_TASK_STATE_RUNNING = "1";
    public final static String DOWNLOAD_TASK_STATE_COMPLETE = "2";
    public final static String DOWNLOAD_TASK_STATE_ERROR = "3";

    public final static Map<String, String> cityCodeMap = new HashMap<>();
    public final static Map<String, String> jsToGcityCodeMap = new HashMap<>();

    public final static int WECHAT_EVENT_TYPE_SCAN=1; //扫描

    public final static int WECHAT_EVENT_TYPE_CONCERNS=2; //关注

    public final static int WECHAT_EVENT_TYPE_CANCEL=3; //取消关注

    public final class Wechat{

        public final static int TAG_TARGET_MEMBER = 1; //用户

        public final static int TAG_TARGET_DEPARTMENT = 2; //部门

        public final static String WECHAT_BIND_GOODS_CODE = "100000001"; //微信绑定佣金虚拟商品
    }

    public final class GroupPurchase{

        public final static int NEW = 1; //待发布

        public final static int ON_LINE = 2; //发布
    }

    //核销卡券常量
    public static final class CouponCheckParam{
        public final static int checkResult_init = 0;//核销结果状态：初始状态;
        public final static int checkResult_success = 1;//核销结果状态：成功
        public final static int checkResult_failure = 2;//核销结果状态：失败

        //支付公司的卡卷开头33
        public static final String CARD_FROM_WOPAY = "33";

        //100支付公司核销成功，101输入金额，199支付公司核销失败，99卡券余额不足，200权益核销成功，299权益失败
        public static final String CODE_MESSGAE_SUCCESS_COUPONUSE_FROM_WOPAY = "100";
        public static final String CODE_INPUTMONEY_COUPONUSE_FROM_WOPAY = "101";
        public static final String CODE_MESSAGE_FAILURE_COUPONUSE_FROM_WOPAY = "199";
        public static final String CODE_MESSGAE_NOTENOUGH_COUPONUSE_FROM_WOPAY = "99";

        public static final String CODE_MESSGAE_SUCCESS_COUPONUSE_FROM_RIGHTCENTER = "200";
        public static final String CODE_MESSAGE_FAILURE_COUPONUSE_FROM_RIGHTCENTER = "299";
        public static final String MESSGAE_SUCCESS_COUPONUSE = "核销成功";
        public static final String MESSAGE_FAILURE_COUPONUSE = "核销失败";
        public static final String MESSAGE_NOTENOUGH_COUPONUSE = "卡券余额不足";

    }

    public final static Map<Integer, String> IMG_DIR_MAP = new HashMap<>();

    static {
    	cityCodeMap.put("0510","330");
    	cityCodeMap.put("0025","340");
    	cityCodeMap.put("0511","343");
    	cityCodeMap.put("0518","346");
    	cityCodeMap.put("0515","348");
    	cityCodeMap.put("0527","349");
    	cityCodeMap.put("0516","350");
    	cityCodeMap.put("0517","354");
    	cityCodeMap.put("0513","358");
    	cityCodeMap.put("0514","430");
    	cityCodeMap.put("0519","440");
    	cityCodeMap.put("0523","445");
    	cityCodeMap.put("0512","450");

    	jsToGcityCodeMap.put("330","0510");
    	jsToGcityCodeMap.put("340","0025");
    	jsToGcityCodeMap.put("343","0511");
    	jsToGcityCodeMap.put("346","0518");
    	jsToGcityCodeMap.put("348","0515");
    	jsToGcityCodeMap.put("349","0527");
    	jsToGcityCodeMap.put("350","0516");
    	jsToGcityCodeMap.put("354","0517");
    	jsToGcityCodeMap.put("358","0513");
    	jsToGcityCodeMap.put("430","0514");
    	jsToGcityCodeMap.put("440","0519");
    	jsToGcityCodeMap.put("445","0523");
    	jsToGcityCodeMap.put("450","0512");

		IMG_DIR_MAP.put(1,"coupon");
		IMG_DIR_MAP.put(2,"banner");
		IMG_DIR_MAP.put(3,"promote");
		IMG_DIR_MAP.put(4,"notice");
		IMG_DIR_MAP.put(5,"benefit");
		IMG_DIR_MAP.put(6,"merchant");
		IMG_DIR_MAP.put(7,"goods");
		IMG_DIR_MAP.put(8,"groupPurchase");
	}

    /**
     * 字典类型：回音
     */
    public static final String DICT_KIND_ECHOE = "echoe";
    /**
     * * 自然人协议
     */
    public static final String USER_AGREEMENT_PDF_NATURE = "nature";
    /**
     * * 商户协议
     */
    public static final String USER_AGREEMENT_PDF_MERCHANT = "merchant";
    /**
     * * 店铺协议
     */
    public static final String USER_AGREEMENT_PDF_SHOP = "shop";
    /**
     * * 组员协议
     */
    public static final String USER_AGREEMENT_PDF_MEMBER = "member";
    /**
     * * 店长协议
     */
    public static final String USER_AGREEMENT_PDF_SHOPKEEPER = "shopkeeper";

    public final static String EOYCHARGE_COMMIS_GOODS_CODE = "100000003"; //岁末充值佣金虚拟商品

    /**
     * 京东小店
     */
    public final class Jingdong{
        //京东PartnerRegister表状态
        public final static int PARTNER_JD_STATE_NEW = 0; //初始状态
        public final static int PARTNER_JD_STATE_PASS = 1; //审核通过
        public final static int PARTNER_JD_STATE_NO_PASS = 2; //审核不通过
        public final static int PARTNER_JD_STATE_CHECK_ERROR = 3; //校验报错

        public static final String PROVINCE_CODE_JIANGSU = "34";

        //返回给总部的能人注册状态
        public final static int PARTNER_JD_2_HQ_STATE_PASS = 3; //审核通过
        public final static int PARTNER_JD_2_HQ_STATE_NO_PASS = 5; //审核不通过

    }

    public enum PartnerBasicClassficationType {

        PARTNER_ROOT_CLASSIFICATION(0, "根节点"),
        PARTNER_BASIC_CLASSIFICATION_BD(1, "BD"),
        PARTNER_BASIC_CLASSIFICATION_NATURAL(2, "自然人"),
        PARTNER_BASIC_CLASSIFICATION_COMMIS(3, "佣金商户"),
        PARTNER_BASIC_CLASSIFICATION_RIGHTS(4, "权益商户"),
        PARTNER_BASIC_CLASSIFICATION_WOW(5, "陕西校园"),
        PARTNER_BASIC_CLASSIFICATION_ENTITY(6, "实渠赋能"),
        PARTNER_BASIC_CLASSIFICATION_RIGHTFUTURE(7, "准权益商户"),
        PARTNER_BASIC_CLASSIFICATION_UNKNOWN(8, "未知"),

        PARTNER_BASIC_CLASSIFICATION_O2O(82, "O2O金融商户");
        private long code;

        private String desc;

        public long getCode() {
            return code;
        }

        public String getName() {
            return desc;
        }

        /**
         *
         * @param code
         * @return
         */
        public static String getDesc(long code) {
            for (PartnerBasicClassficationType c : PartnerBasicClassficationType.values()) {
                if (c.getCode() == code) {
                    return c.desc;
                }
            }
            return null;
        }
        private PartnerBasicClassficationType(long code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    //发送合伙人信息到互联网部, 信息来源， 1：手机端发起；2：注册流程
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_SOURCE_PHONE = 1;
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_SOURCE_REGISTER = 2;

    //发送合伙人信息到互联网部, 信息入库状态。 状态，0:初始化，待发送；1：已发送；2：发送成功；3：发送失败
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_STATE_INIT = 0;
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_STATE_PASSED = 1;
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_STATE_PASS_SUCCESS = 2;
    public final static int PARTNER_SYNCHRO_2_INTERNETDEP_STATE_PASS_FAILURE = 3;

    //等级权益
    public final static int LEVEL_RETURNCOMMIS = 1;
    public final static int LEVEL_COMMISQUERY = 2;
    public final static int LEVEL_COMMISOUT = 3;
    public final static int LEVEL_COUPON = 4;
    public final static int LEVEL_EXTRADRAW = 5;
    public final static int LEVEL_DISCOUNT = 6;
    public final static int LEVEL_EXTRAPOINT = 7;
    public final static int LEVEL_MARKETTRAIN = 8;
    public final static int LEVEL_HEADSTUDY = 9;




}
