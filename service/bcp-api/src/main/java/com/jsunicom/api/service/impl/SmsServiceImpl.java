package com.jsunicom.api.service.impl;

import com.jsunicom.api.po.req.SendSmsReq;
import com.jsunicom.api.po.rsp.SendSmsRsp;
import com.jsunicom.api.service.SendSmsService;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.common.jslt.message.common.enums.Message;
import com.jsunicom.common.jslt.message.common.enums.MessageType;
import com.jsunicom.api.common.RedisKeys;
import com.jsunicom.api.service.DictFacade;
import com.jsunicom.api.service.SmsFacade;
import com.jsunicom.api.utils.RedisUtil;
import com.jsunicom.api.utils.SmsUtil;
import com.jsunicom.common.sms.common.MessageDto;
import com.jsunicom.common.sms.sender.MessageSender;
import com.lz.lsf.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl.SmsService
 * @ClassName: SmsServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-10  10:32
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsFacade {
    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    private DictFacade dictService;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private SendSmsService sendSmsService;
//    @Autowired
//    private MessageService messageService;

    @Value("${env.verifyCode.error.mixCount4InputError}")
    private  int mixCount4InputError; //输错验证码最大次数

    @Value("${env.verifyCode.error.lockTime4InputError}")
    private int lockTime4InputError; //输错验证码次数多次后被锁定时间

    @Override
    public Result sendCode(String templateId,String mobile) {
        if(StringUtils.isEmpty(mobile)){
            Result<SendSmsRsp> sendSmsRspResult = new Result<>();
            sendSmsRspResult.setCode(500L);
            sendSmsRspResult.setMsg("手机号为空");
            sendSmsRspResult.setData(null);
            sendSmsRspResult.setSuccess(false);
            return sendSmsRspResult;
        }
        if(!SmsUtil.isMobile(mobile)){
            Result<SendSmsRsp> sendSmsRspResult = new Result<>();
            sendSmsRspResult.setCode(500L);
            sendSmsRspResult.setMsg("请输入正确的手机号");
            sendSmsRspResult.setData(null);
            sendSmsRspResult.setSuccess(false);
            return sendSmsRspResult;
        }

        //60秒内该手机号输错验证码多次，被锁定
        String key4PhoneInputError = RedisKeys.keyForSmsCodeInputError(mobile);
        String count4PhoneInputError = redisUtils.get(key4PhoneInputError) == null?"":redisUtils.get(key4PhoneInputError).toString(); //输错次数
        if(StringUtils.isNotEmpty(count4PhoneInputError) && Integer.parseInt(count4PhoneInputError) >= mixCount4InputError){
            Result<SendSmsRsp> sendSmsRspResult = new Result<>();
            sendSmsRspResult.setCode(500L);
            sendSmsRspResult.setMsg("多次连续输错，被冻结");
            sendSmsRspResult.setData(null);
            sendSmsRspResult.setSuccess(false);
            return sendSmsRspResult;
        }
        String key = RedisKeys.keyForRegisteSmsCode(mobile);
        String codeIf = redisUtils.get(key)==null?"":redisUtils.get(key).toString();
        if(StringUtils.isNotEmpty(codeIf)){
            Result<SendSmsRsp> sendSmsRspResult = new Result<>();
            sendSmsRspResult.setCode(500L);
            sendSmsRspResult.setMsg("请60秒后再试");
            sendSmsRspResult.setData(null);
            sendSmsRspResult.setSuccess(false);
            return sendSmsRspResult;
        }
        String code = SmsUtil.generateVerifyCode();
        String smsSwitch = dictService.getNameByKey("switch","switch_sms");
        if(StringUtils.isNotEmpty(smsSwitch) && "0".equals(smsSwitch)){
            log.info("短信开关关闭，mobile->{},code->{}",mobile,code);
            redisUtils.set(key, code, 120);
            return null ;
        }
        log.info("短信开关打开，mobile->{},code->{}",mobile,code);
        String realSwitch = dictService.getNameByKey("switch","switch_sms_real");
        if(StringUtils.isNotEmpty(realSwitch) && "0".equals(realSwitch)){
            log.info("短信不真实发送，mobile->{},code->{}",mobile,code);
            redisUtils.set(key, code, 120);
            return null ;
        }
        SendSmsReq sendSmsReq = new SendSmsReq();
        sendSmsReq.setTemplateId(templateId);
        sendSmsReq.setSendTo(mobile);
        sendSmsReq.setSendParam(code);
        Result<SendSmsRsp> smsSendResult = sendSmsService.sendSms(sendSmsReq);

//        String response = "";
        log.info("{}获取验证码返回:{}",mobile,smsSendResult);
        redisUtils.set(key,code,120);
        return smsSendResult;
    }

    @Override
    public void checkCode(String mobile, String code) {
        log.info("开始校验验证码，mobile->{},code->{}",mobile,code);
        //使用短信发送渠道
        String smsCheck = dictService.getNameByKey("switch", "sms_check");
        if(null != smsCheck && "0".equals(smsCheck)) {
            log.info("完成校验验证码，mobile->{},code->{}",mobile,code);
        } else {
            if(StringUtils.isEmpty(mobile)){
                throw new BusinessException("SMS011","请输入手机号");
            }
            if(StringUtils.isEmpty(code)){
                throw new BusinessException("SMS013","请输入验证码");
            }
            if(!SmsUtil.isMobile(mobile)){
                throw new BusinessException("SMS012","请输入正确的手机号");
            }

            //在redis中记录该手机号或ip设备输入错误次数
            String key4PhoneInputError = RedisKeys.keyForSmsCodeInputError(mobile);
            String count4PhoneInputError = redisUtils.get(key4PhoneInputError) == null?"":redisUtils.get(key4PhoneInputError).toString(); //输错次数
            if(StringUtils.isNotEmpty(count4PhoneInputError) && Integer.parseInt(count4PhoneInputError) >= mixCount4InputError) {
                throw new BusinessException("SMS1001","该用户连续输错"+ mixCount4InputError + "次验证码，被冻结，请" + lockTime4InputError +"秒后重试");
            }

            String key = RedisKeys.keyForRegisteSmsCode(mobile);
            String codeIf = redisUtils.get(key)==null?"":redisUtils.get(key).toString();
            if(StringUtils.isEmpty(codeIf)){
                throw new BusinessException("SMS014","验证码已失效，请重新获取");
            }

            if(codeIf.equals(code)){
                redisUtils.del(key);
                //标示通过短信验证
                String checkedKey =RedisKeys.keyForChecked(mobile);
                redisUtils.set(checkedKey,"1",3600);
            }else{

                if(StringUtils.isEmpty(count4PhoneInputError)) {
                    redisUtils.set(key4PhoneInputError,"1",lockTime4InputError);
                }else {
                    redisUtils.set(key4PhoneInputError,String.valueOf(Integer.parseInt(count4PhoneInputError) + 1), lockTime4InputError);
                }

                throw new BusinessException("SMS015","验证码不正确");
            }
        }
        log.info("完成校验验证码，mobile->{},code->{}",mobile,code);
    }
    @Override
    public void sendRemindMsg(List<String> mobileList) {

        List<String> phoneNoList = new ArrayList<>();
        for (String mobileNo : mobileList) {
            if (StringUtils.isNotEmpty(mobileNo)) {
                phoneNoList.add(mobileNo);
            }
        }
        String smsSwitch = dictService.getNameByKey("switch", "switch_sms");
        if (StringUtils.isNotEmpty(smsSwitch) && "0".equals(smsSwitch)) {
            log.info("校园短信模板审核-短信开关关闭，mobile->{}", mobileList);
            return;
        }
        log.info("校园短信模板审核-短信开关打开，mobile->{}", mobileList);
        HashMap<String, Object> params = new LinkedHashMap<>();
        params.put("msg_id", "1017");
        Message message = new Message();
        message.setReceivers(phoneNoList);
        message.setParams(params);
        message.setSender("jslt");
        message.setMessageType(MessageType.SMS);

        //使用短信发送渠道
        String sendType = dictService.getNameByKey("switch", "sms_send_type");
        if (!StringUtils.isEmpty(sendType)) {
            message.setSendType(sendType);
        }
        //您好，您的“合伙人”注册验证码为{xxxxxx}。
        //【合伙人】您好，您的“合伙人”注册验证码为{verifyCode}。
        message.setTemplateId("1017");

//        String response = messageService.sendMessage(message);
        String response = "";

        log.info("{}发送短信返回:{}", phoneNoList, response);
    }
}
