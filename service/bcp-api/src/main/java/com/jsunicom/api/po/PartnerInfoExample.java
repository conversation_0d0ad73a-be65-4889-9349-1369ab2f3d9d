package com.jsunicom.api.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PartnerInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PartnerInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeIsNull() {
            addCriterion("partner_type is null");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeIsNotNull() {
            addCriterion("partner_type is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeEqualTo(String value) {
            addCriterion("partner_type =", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeNotEqualTo(String value) {
            addCriterion("partner_type <>", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeGreaterThan(String value) {
            addCriterion("partner_type >", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeGreaterThanOrEqualTo(String value) {
            addCriterion("partner_type >=", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeLessThan(String value) {
            addCriterion("partner_type <", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeLessThanOrEqualTo(String value) {
            addCriterion("partner_type <=", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeLike(String value) {
            addCriterion("partner_type like", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeNotLike(String value) {
            addCriterion("partner_type not like", value, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeIn(List<String> values) {
            addCriterion("partner_type in", values, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeNotIn(List<String> values) {
            addCriterion("partner_type not in", values, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeBetween(String value1, String value2) {
            addCriterion("partner_type between", value1, value2, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerTypeNotBetween(String value1, String value2) {
            addCriterion("partner_type not between", value1, value2, "partnerType");
            return (Criteria) this;
        }

        public Criteria andPartnerNameIsNull() {
            addCriterion("partner_name is null");
            return (Criteria) this;
        }

        public Criteria andPartnerNameIsNotNull() {
            addCriterion("partner_name is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerNameEqualTo(String value) {
            addCriterion("partner_name =", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameNotEqualTo(String value) {
            addCriterion("partner_name <>", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameGreaterThan(String value) {
            addCriterion("partner_name >", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("partner_name >=", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameLessThan(String value) {
            addCriterion("partner_name <", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameLessThanOrEqualTo(String value) {
            addCriterion("partner_name <=", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameLike(String value) {
            addCriterion("partner_name like", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameNotLike(String value) {
            addCriterion("partner_name not like", value, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameIn(List<String> values) {
            addCriterion("partner_name in", values, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameNotIn(List<String> values) {
            addCriterion("partner_name not in", values, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameBetween(String value1, String value2) {
            addCriterion("partner_name between", value1, value2, "partnerName");
            return (Criteria) this;
        }

        public Criteria andPartnerNameNotBetween(String value1, String value2) {
            addCriterion("partner_name not between", value1, value2, "partnerName");
            return (Criteria) this;
        }

        public Criteria andAcctNoIsNull() {
            addCriterion("acct_no is null");
            return (Criteria) this;
        }

        public Criteria andAcctNoIsNotNull() {
            addCriterion("acct_no is not null");
            return (Criteria) this;
        }

        public Criteria andAcctNoEqualTo(String value) {
            addCriterion("acct_no =", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoNotEqualTo(String value) {
            addCriterion("acct_no <>", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoGreaterThan(String value) {
            addCriterion("acct_no >", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("acct_no >=", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoLessThan(String value) {
            addCriterion("acct_no <", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoLessThanOrEqualTo(String value) {
            addCriterion("acct_no <=", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoLike(String value) {
            addCriterion("acct_no like", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoNotLike(String value) {
            addCriterion("acct_no not like", value, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoIn(List<String> values) {
            addCriterion("acct_no in", values, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoNotIn(List<String> values) {
            addCriterion("acct_no not in", values, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoBetween(String value1, String value2) {
            addCriterion("acct_no between", value1, value2, "acctNo");
            return (Criteria) this;
        }

        public Criteria andAcctNoNotBetween(String value1, String value2) {
            addCriterion("acct_no not between", value1, value2, "acctNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoIsNull() {
            addCriterion("partner_cert_no is null");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoIsNotNull() {
            addCriterion("partner_cert_no is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoEqualTo(String value) {
            addCriterion("partner_cert_no =", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoNotEqualTo(String value) {
            addCriterion("partner_cert_no <>", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoGreaterThan(String value) {
            addCriterion("partner_cert_no >", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoGreaterThanOrEqualTo(String value) {
            addCriterion("partner_cert_no >=", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoLessThan(String value) {
            addCriterion("partner_cert_no <", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoLessThanOrEqualTo(String value) {
            addCriterion("partner_cert_no <=", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoLike(String value) {
            addCriterion("partner_cert_no like", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoNotLike(String value) {
            addCriterion("partner_cert_no not like", value, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoIn(List<String> values) {
            addCriterion("partner_cert_no in", values, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoNotIn(List<String> values) {
            addCriterion("partner_cert_no not in", values, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoBetween(String value1, String value2) {
            addCriterion("partner_cert_no between", value1, value2, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andPartnerCertNoNotBetween(String value1, String value2) {
            addCriterion("partner_cert_no not between", value1, value2, "partnerCertNo");
            return (Criteria) this;
        }

        public Criteria andMblNbrIsNull() {
            addCriterion("mbl_nbr is null");
            return (Criteria) this;
        }

        public Criteria andMblNbrIsNotNull() {
            addCriterion("mbl_nbr is not null");
            return (Criteria) this;
        }

        public Criteria andMblNbrEqualTo(String value) {
            addCriterion("mbl_nbr =", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrNotEqualTo(String value) {
            addCriterion("mbl_nbr <>", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrGreaterThan(String value) {
            addCriterion("mbl_nbr >", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrGreaterThanOrEqualTo(String value) {
            addCriterion("mbl_nbr >=", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrLessThan(String value) {
            addCriterion("mbl_nbr <", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrLessThanOrEqualTo(String value) {
            addCriterion("mbl_nbr <=", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrLike(String value) {
            addCriterion("mbl_nbr like", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrNotLike(String value) {
            addCriterion("mbl_nbr not like", value, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrIn(List<String> values) {
            addCriterion("mbl_nbr in", values, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrNotIn(List<String> values) {
            addCriterion("mbl_nbr not in", values, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrBetween(String value1, String value2) {
            addCriterion("mbl_nbr between", value1, value2, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andMblNbrNotBetween(String value1, String value2) {
            addCriterion("mbl_nbr not between", value1, value2, "mblNbr");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeIsNull() {
            addCriterion("profession_type is null");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeIsNotNull() {
            addCriterion("profession_type is not null");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeEqualTo(String value) {
            addCriterion("profession_type =", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeNotEqualTo(String value) {
            addCriterion("profession_type <>", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeGreaterThan(String value) {
            addCriterion("profession_type >", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("profession_type >=", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeLessThan(String value) {
            addCriterion("profession_type <", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeLessThanOrEqualTo(String value) {
            addCriterion("profession_type <=", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeLike(String value) {
            addCriterion("profession_type like", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeNotLike(String value) {
            addCriterion("profession_type not like", value, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeIn(List<String> values) {
            addCriterion("profession_type in", values, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeNotIn(List<String> values) {
            addCriterion("profession_type not in", values, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeBetween(String value1, String value2) {
            addCriterion("profession_type between", value1, value2, "professionType");
            return (Criteria) this;
        }

        public Criteria andProfessionTypeNotBetween(String value1, String value2) {
            addCriterion("profession_type not between", value1, value2, "professionType");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNull() {
            addCriterion("org_code is null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNotNull() {
            addCriterion("org_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeEqualTo(String value) {
            addCriterion("org_code =", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotEqualTo(String value) {
            addCriterion("org_code <>", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThan(String value) {
            addCriterion("org_code >", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("org_code >=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThan(String value) {
            addCriterion("org_code <", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThanOrEqualTo(String value) {
            addCriterion("org_code <=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLike(String value) {
            addCriterion("org_code like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotLike(String value) {
            addCriterion("org_code not like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIn(List<String> values) {
            addCriterion("org_code in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotIn(List<String> values) {
            addCriterion("org_code not in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeBetween(String value1, String value2) {
            addCriterion("org_code between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotBetween(String value1, String value2) {
            addCriterion("org_code not between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeIsNull() {
            addCriterion("prov_code is null");
            return (Criteria) this;
        }

        public Criteria andProvCodeIsNotNull() {
            addCriterion("prov_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvCodeEqualTo(String value) {
            addCriterion("prov_code =", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeNotEqualTo(String value) {
            addCriterion("prov_code <>", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeGreaterThan(String value) {
            addCriterion("prov_code >", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeGreaterThanOrEqualTo(String value) {
            addCriterion("prov_code >=", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeLessThan(String value) {
            addCriterion("prov_code <", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeLessThanOrEqualTo(String value) {
            addCriterion("prov_code <=", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeLike(String value) {
            addCriterion("prov_code like", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeNotLike(String value) {
            addCriterion("prov_code not like", value, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeIn(List<String> values) {
            addCriterion("prov_code in", values, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeNotIn(List<String> values) {
            addCriterion("prov_code not in", values, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeBetween(String value1, String value2) {
            addCriterion("prov_code between", value1, value2, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvCodeNotBetween(String value1, String value2) {
            addCriterion("prov_code not between", value1, value2, "provCode");
            return (Criteria) this;
        }

        public Criteria andProvNameIsNull() {
            addCriterion("prov_name is null");
            return (Criteria) this;
        }

        public Criteria andProvNameIsNotNull() {
            addCriterion("prov_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvNameEqualTo(String value) {
            addCriterion("prov_name =", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameNotEqualTo(String value) {
            addCriterion("prov_name <>", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameGreaterThan(String value) {
            addCriterion("prov_name >", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameGreaterThanOrEqualTo(String value) {
            addCriterion("prov_name >=", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameLessThan(String value) {
            addCriterion("prov_name <", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameLessThanOrEqualTo(String value) {
            addCriterion("prov_name <=", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameLike(String value) {
            addCriterion("prov_name like", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameNotLike(String value) {
            addCriterion("prov_name not like", value, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameIn(List<String> values) {
            addCriterion("prov_name in", values, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameNotIn(List<String> values) {
            addCriterion("prov_name not in", values, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameBetween(String value1, String value2) {
            addCriterion("prov_name between", value1, value2, "provName");
            return (Criteria) this;
        }

        public Criteria andProvNameNotBetween(String value1, String value2) {
            addCriterion("prov_name not between", value1, value2, "provName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andAreaCodeIsNull() {
            addCriterion("area_code is null");
            return (Criteria) this;
        }

        public Criteria andAreaCodeIsNotNull() {
            addCriterion("area_code is not null");
            return (Criteria) this;
        }

        public Criteria andAreaCodeEqualTo(String value) {
            addCriterion("area_code =", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeNotEqualTo(String value) {
            addCriterion("area_code <>", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeGreaterThan(String value) {
            addCriterion("area_code >", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("area_code >=", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeLessThan(String value) {
            addCriterion("area_code <", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeLessThanOrEqualTo(String value) {
            addCriterion("area_code <=", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeLike(String value) {
            addCriterion("area_code like", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeNotLike(String value) {
            addCriterion("area_code not like", value, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeIn(List<String> values) {
            addCriterion("area_code in", values, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeNotIn(List<String> values) {
            addCriterion("area_code not in", values, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeBetween(String value1, String value2) {
            addCriterion("area_code between", value1, value2, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaCodeNotBetween(String value1, String value2) {
            addCriterion("area_code not between", value1, value2, "areaCode");
            return (Criteria) this;
        }

        public Criteria andAreaNameIsNull() {
            addCriterion("area_name is null");
            return (Criteria) this;
        }

        public Criteria andAreaNameIsNotNull() {
            addCriterion("area_name is not null");
            return (Criteria) this;
        }

        public Criteria andAreaNameEqualTo(String value) {
            addCriterion("area_name =", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameNotEqualTo(String value) {
            addCriterion("area_name <>", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameGreaterThan(String value) {
            addCriterion("area_name >", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameGreaterThanOrEqualTo(String value) {
            addCriterion("area_name >=", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameLessThan(String value) {
            addCriterion("area_name <", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameLessThanOrEqualTo(String value) {
            addCriterion("area_name <=", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameLike(String value) {
            addCriterion("area_name like", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameNotLike(String value) {
            addCriterion("area_name not like", value, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameIn(List<String> values) {
            addCriterion("area_name in", values, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameNotIn(List<String> values) {
            addCriterion("area_name not in", values, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameBetween(String value1, String value2) {
            addCriterion("area_name between", value1, value2, "areaName");
            return (Criteria) this;
        }

        public Criteria andAreaNameNotBetween(String value1, String value2) {
            addCriterion("area_name not between", value1, value2, "areaName");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andDevelIdIsNull() {
            addCriterion("devel_id is null");
            return (Criteria) this;
        }

        public Criteria andDevelIdIsNotNull() {
            addCriterion("devel_id is not null");
            return (Criteria) this;
        }

        public Criteria andDevelIdEqualTo(String value) {
            addCriterion("devel_id =", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdNotEqualTo(String value) {
            addCriterion("devel_id <>", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdGreaterThan(String value) {
            addCriterion("devel_id >", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdGreaterThanOrEqualTo(String value) {
            addCriterion("devel_id >=", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdLessThan(String value) {
            addCriterion("devel_id <", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdLessThanOrEqualTo(String value) {
            addCriterion("devel_id <=", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdLike(String value) {
            addCriterion("devel_id like", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdNotLike(String value) {
            addCriterion("devel_id not like", value, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdIn(List<String> values) {
            addCriterion("devel_id in", values, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdNotIn(List<String> values) {
            addCriterion("devel_id not in", values, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdBetween(String value1, String value2) {
            addCriterion("devel_id between", value1, value2, "develId");
            return (Criteria) this;
        }

        public Criteria andDevelIdNotBetween(String value1, String value2) {
            addCriterion("devel_id not between", value1, value2, "develId");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeIsNull() {
            addCriterion("emploee_type is null");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeIsNotNull() {
            addCriterion("emploee_type is not null");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeEqualTo(String value) {
            addCriterion("emploee_type =", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeNotEqualTo(String value) {
            addCriterion("emploee_type <>", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeGreaterThan(String value) {
            addCriterion("emploee_type >", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("emploee_type >=", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeLessThan(String value) {
            addCriterion("emploee_type <", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeLessThanOrEqualTo(String value) {
            addCriterion("emploee_type <=", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeLike(String value) {
            addCriterion("emploee_type like", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeNotLike(String value) {
            addCriterion("emploee_type not like", value, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeIn(List<String> values) {
            addCriterion("emploee_type in", values, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeNotIn(List<String> values) {
            addCriterion("emploee_type not in", values, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeBetween(String value1, String value2) {
            addCriterion("emploee_type between", value1, value2, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andEmploeeTypeNotBetween(String value1, String value2) {
            addCriterion("emploee_type not between", value1, value2, "emploeeType");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminIsNull() {
            addCriterion("is_mer_admin is null");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminIsNotNull() {
            addCriterion("is_mer_admin is not null");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminEqualTo(String value) {
            addCriterion("is_mer_admin =", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminNotEqualTo(String value) {
            addCriterion("is_mer_admin <>", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminGreaterThan(String value) {
            addCriterion("is_mer_admin >", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminGreaterThanOrEqualTo(String value) {
            addCriterion("is_mer_admin >=", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminLessThan(String value) {
            addCriterion("is_mer_admin <", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminLessThanOrEqualTo(String value) {
            addCriterion("is_mer_admin <=", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminLike(String value) {
            addCriterion("is_mer_admin like", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminNotLike(String value) {
            addCriterion("is_mer_admin not like", value, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminIn(List<String> values) {
            addCriterion("is_mer_admin in", values, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminNotIn(List<String> values) {
            addCriterion("is_mer_admin not in", values, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminBetween(String value1, String value2) {
            addCriterion("is_mer_admin between", value1, value2, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andIsMerAdminNotBetween(String value1, String value2) {
            addCriterion("is_mer_admin not between", value1, value2, "isMerAdmin");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdIsNull() {
            addCriterion("commis_acct_id is null");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdIsNotNull() {
            addCriterion("commis_acct_id is not null");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdEqualTo(Long value) {
            addCriterion("commis_acct_id =", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdNotEqualTo(Long value) {
            addCriterion("commis_acct_id <>", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdGreaterThan(Long value) {
            addCriterion("commis_acct_id >", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdGreaterThanOrEqualTo(Long value) {
            addCriterion("commis_acct_id >=", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdLessThan(Long value) {
            addCriterion("commis_acct_id <", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdLessThanOrEqualTo(Long value) {
            addCriterion("commis_acct_id <=", value, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdIn(List<Long> values) {
            addCriterion("commis_acct_id in", values, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdNotIn(List<Long> values) {
            addCriterion("commis_acct_id not in", values, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdBetween(Long value1, Long value2) {
            addCriterion("commis_acct_id between", value1, value2, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCommisAcctIdNotBetween(Long value1, Long value2) {
            addCriterion("commis_acct_id not between", value1, value2, "commisAcctId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdIsNull() {
            addCriterion("college_id is null");
            return (Criteria) this;
        }

        public Criteria andCollegeIdIsNotNull() {
            addCriterion("college_id is not null");
            return (Criteria) this;
        }

        public Criteria andCollegeIdEqualTo(Long value) {
            addCriterion("college_id =", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdNotEqualTo(Long value) {
            addCriterion("college_id <>", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdGreaterThan(Long value) {
            addCriterion("college_id >", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("college_id >=", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdLessThan(Long value) {
            addCriterion("college_id <", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdLessThanOrEqualTo(Long value) {
            addCriterion("college_id <=", value, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdIn(List<Long> values) {
            addCriterion("college_id in", values, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdNotIn(List<Long> values) {
            addCriterion("college_id not in", values, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdBetween(Long value1, Long value2) {
            addCriterion("college_id between", value1, value2, "collegeId");
            return (Criteria) this;
        }

        public Criteria andCollegeIdNotBetween(Long value1, Long value2) {
            addCriterion("college_id not between", value1, value2, "collegeId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdIsNull() {
            addCriterion("dormitory_id is null");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdIsNotNull() {
            addCriterion("dormitory_id is not null");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdEqualTo(Long value) {
            addCriterion("dormitory_id =", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdNotEqualTo(Long value) {
            addCriterion("dormitory_id <>", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdGreaterThan(Long value) {
            addCriterion("dormitory_id >", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dormitory_id >=", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdLessThan(Long value) {
            addCriterion("dormitory_id <", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdLessThanOrEqualTo(Long value) {
            addCriterion("dormitory_id <=", value, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdIn(List<Long> values) {
            addCriterion("dormitory_id in", values, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdNotIn(List<Long> values) {
            addCriterion("dormitory_id not in", values, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdBetween(Long value1, Long value2) {
            addCriterion("dormitory_id between", value1, value2, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andDormitoryIdNotBetween(Long value1, Long value2) {
            addCriterion("dormitory_id not between", value1, value2, "dormitoryId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdIsNull() {
            addCriterion("reference_id is null");
            return (Criteria) this;
        }

        public Criteria andReferenceIdIsNotNull() {
            addCriterion("reference_id is not null");
            return (Criteria) this;
        }

        public Criteria andReferenceIdEqualTo(Long value) {
            addCriterion("reference_id =", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdNotEqualTo(Long value) {
            addCriterion("reference_id <>", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdGreaterThan(Long value) {
            addCriterion("reference_id >", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("reference_id >=", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdLessThan(Long value) {
            addCriterion("reference_id <", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdLessThanOrEqualTo(Long value) {
            addCriterion("reference_id <=", value, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdIn(List<Long> values) {
            addCriterion("reference_id in", values, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdNotIn(List<Long> values) {
            addCriterion("reference_id not in", values, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdBetween(Long value1, Long value2) {
            addCriterion("reference_id between", value1, value2, "referenceId");
            return (Criteria) this;
        }

        public Criteria andReferenceIdNotBetween(Long value1, Long value2) {
            addCriterion("reference_id not between", value1, value2, "referenceId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIsNull() {
            addCriterion("society_id is null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIsNotNull() {
            addCriterion("society_id is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdEqualTo(String value) {
            addCriterion("society_id =", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotEqualTo(String value) {
            addCriterion("society_id <>", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThan(String value) {
            addCriterion("society_id >", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThanOrEqualTo(String value) {
            addCriterion("society_id >=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThan(String value) {
            addCriterion("society_id <", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThanOrEqualTo(String value) {
            addCriterion("society_id <=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLike(String value) {
            addCriterion("society_id like", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotLike(String value) {
            addCriterion("society_id not like", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIn(List<String> values) {
            addCriterion("society_id in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotIn(List<String> values) {
            addCriterion("society_id not in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdBetween(String value1, String value2) {
            addCriterion("society_id between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotBetween(String value1, String value2) {
            addCriterion("society_id not between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdEqualTo(Long value) {
            addCriterion("merchant_id =", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotEqualTo(Long value) {
            addCriterion("merchant_id <>", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThan(Long value) {
            addCriterion("merchant_id >", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("merchant_id >=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThan(Long value) {
            addCriterion("merchant_id <", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThanOrEqualTo(Long value) {
            addCriterion("merchant_id <=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIn(List<Long> values) {
            addCriterion("merchant_id in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotIn(List<Long> values) {
            addCriterion("merchant_id not in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdBetween(Long value1, Long value2) {
            addCriterion("merchant_id between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotBetween(Long value1, Long value2) {
            addCriterion("merchant_id not between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameIsNull() {
            addCriterion("sale_mgr_name is null");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameIsNotNull() {
            addCriterion("sale_mgr_name is not null");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameEqualTo(String value) {
            addCriterion("sale_mgr_name =", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameNotEqualTo(String value) {
            addCriterion("sale_mgr_name <>", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameGreaterThan(String value) {
            addCriterion("sale_mgr_name >", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameGreaterThanOrEqualTo(String value) {
            addCriterion("sale_mgr_name >=", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameLessThan(String value) {
            addCriterion("sale_mgr_name <", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameLessThanOrEqualTo(String value) {
            addCriterion("sale_mgr_name <=", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameLike(String value) {
            addCriterion("sale_mgr_name like", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameNotLike(String value) {
            addCriterion("sale_mgr_name not like", value, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameIn(List<String> values) {
            addCriterion("sale_mgr_name in", values, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameNotIn(List<String> values) {
            addCriterion("sale_mgr_name not in", values, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameBetween(String value1, String value2) {
            addCriterion("sale_mgr_name between", value1, value2, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrNameNotBetween(String value1, String value2) {
            addCriterion("sale_mgr_name not between", value1, value2, "saleMgrName");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneIsNull() {
            addCriterion("sale_mgr_phone is null");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneIsNotNull() {
            addCriterion("sale_mgr_phone is not null");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneEqualTo(String value) {
            addCriterion("sale_mgr_phone =", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneNotEqualTo(String value) {
            addCriterion("sale_mgr_phone <>", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneGreaterThan(String value) {
            addCriterion("sale_mgr_phone >", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("sale_mgr_phone >=", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneLessThan(String value) {
            addCriterion("sale_mgr_phone <", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneLessThanOrEqualTo(String value) {
            addCriterion("sale_mgr_phone <=", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneLike(String value) {
            addCriterion("sale_mgr_phone like", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneNotLike(String value) {
            addCriterion("sale_mgr_phone not like", value, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneIn(List<String> values) {
            addCriterion("sale_mgr_phone in", values, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneNotIn(List<String> values) {
            addCriterion("sale_mgr_phone not in", values, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneBetween(String value1, String value2) {
            addCriterion("sale_mgr_phone between", value1, value2, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andSaleMgrPhoneNotBetween(String value1, String value2) {
            addCriterion("sale_mgr_phone not between", value1, value2, "saleMgrPhone");
            return (Criteria) this;
        }

        public Criteria andBusLineIsNull() {
            addCriterion("bus_line is null");
            return (Criteria) this;
        }

        public Criteria andBusLineIsNotNull() {
            addCriterion("bus_line is not null");
            return (Criteria) this;
        }

        public Criteria andBusLineEqualTo(String value) {
            addCriterion("bus_line =", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineNotEqualTo(String value) {
            addCriterion("bus_line <>", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineGreaterThan(String value) {
            addCriterion("bus_line >", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineGreaterThanOrEqualTo(String value) {
            addCriterion("bus_line >=", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineLessThan(String value) {
            addCriterion("bus_line <", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineLessThanOrEqualTo(String value) {
            addCriterion("bus_line <=", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineLike(String value) {
            addCriterion("bus_line like", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineNotLike(String value) {
            addCriterion("bus_line not like", value, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineIn(List<String> values) {
            addCriterion("bus_line in", values, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineNotIn(List<String> values) {
            addCriterion("bus_line not in", values, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineBetween(String value1, String value2) {
            addCriterion("bus_line between", value1, value2, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusLineNotBetween(String value1, String value2) {
            addCriterion("bus_line not between", value1, value2, "busLine");
            return (Criteria) this;
        }

        public Criteria andBusGridIsNull() {
            addCriterion("bus_grid is null");
            return (Criteria) this;
        }

        public Criteria andBusGridIsNotNull() {
            addCriterion("bus_grid is not null");
            return (Criteria) this;
        }

        public Criteria andBusGridEqualTo(String value) {
            addCriterion("bus_grid =", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridNotEqualTo(String value) {
            addCriterion("bus_grid <>", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridGreaterThan(String value) {
            addCriterion("bus_grid >", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridGreaterThanOrEqualTo(String value) {
            addCriterion("bus_grid >=", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridLessThan(String value) {
            addCriterion("bus_grid <", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridLessThanOrEqualTo(String value) {
            addCriterion("bus_grid <=", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridLike(String value) {
            addCriterion("bus_grid like", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridNotLike(String value) {
            addCriterion("bus_grid not like", value, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridIn(List<String> values) {
            addCriterion("bus_grid in", values, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridNotIn(List<String> values) {
            addCriterion("bus_grid not in", values, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridBetween(String value1, String value2) {
            addCriterion("bus_grid between", value1, value2, "busGrid");
            return (Criteria) this;
        }

        public Criteria andBusGridNotBetween(String value1, String value2) {
            addCriterion("bus_grid not between", value1, value2, "busGrid");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIsNull() {
            addCriterion("invite_code is null");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIsNotNull() {
            addCriterion("invite_code is not null");
            return (Criteria) this;
        }

        public Criteria andInviteCodeEqualTo(String value) {
            addCriterion("invite_code =", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotEqualTo(String value) {
            addCriterion("invite_code <>", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeGreaterThan(String value) {
            addCriterion("invite_code >", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("invite_code >=", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLessThan(String value) {
            addCriterion("invite_code <", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLessThanOrEqualTo(String value) {
            addCriterion("invite_code <=", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLike(String value) {
            addCriterion("invite_code like", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotLike(String value) {
            addCriterion("invite_code not like", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIn(List<String> values) {
            addCriterion("invite_code in", values, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotIn(List<String> values) {
            addCriterion("invite_code not in", values, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeBetween(String value1, String value2) {
            addCriterion("invite_code between", value1, value2, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotBetween(String value1, String value2) {
            addCriterion("invite_code not between", value1, value2, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andDevelChannelIsNull() {
            addCriterion("devel_channel is null");
            return (Criteria) this;
        }

        public Criteria andDevelChannelIsNotNull() {
            addCriterion("devel_channel is not null");
            return (Criteria) this;
        }

        public Criteria andDevelChannelEqualTo(String value) {
            addCriterion("devel_channel =", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelNotEqualTo(String value) {
            addCriterion("devel_channel <>", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelGreaterThan(String value) {
            addCriterion("devel_channel >", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelGreaterThanOrEqualTo(String value) {
            addCriterion("devel_channel >=", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelLessThan(String value) {
            addCriterion("devel_channel <", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelLessThanOrEqualTo(String value) {
            addCriterion("devel_channel <=", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelLike(String value) {
            addCriterion("devel_channel like", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelNotLike(String value) {
            addCriterion("devel_channel not like", value, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelIn(List<String> values) {
            addCriterion("devel_channel in", values, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelNotIn(List<String> values) {
            addCriterion("devel_channel not in", values, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelBetween(String value1, String value2) {
            addCriterion("devel_channel between", value1, value2, "develChannel");
            return (Criteria) this;
        }

        public Criteria andDevelChannelNotBetween(String value1, String value2) {
            addCriterion("devel_channel not between", value1, value2, "develChannel");
            return (Criteria) this;
        }

        public Criteria andGztRsIsNull() {
            addCriterion("gzt_rs is null");
            return (Criteria) this;
        }

        public Criteria andGztRsIsNotNull() {
            addCriterion("gzt_rs is not null");
            return (Criteria) this;
        }

        public Criteria andGztRsEqualTo(String value) {
            addCriterion("gzt_rs =", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsNotEqualTo(String value) {
            addCriterion("gzt_rs <>", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsGreaterThan(String value) {
            addCriterion("gzt_rs >", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsGreaterThanOrEqualTo(String value) {
            addCriterion("gzt_rs >=", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsLessThan(String value) {
            addCriterion("gzt_rs <", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsLessThanOrEqualTo(String value) {
            addCriterion("gzt_rs <=", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsLike(String value) {
            addCriterion("gzt_rs like", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsNotLike(String value) {
            addCriterion("gzt_rs not like", value, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsIn(List<String> values) {
            addCriterion("gzt_rs in", values, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsNotIn(List<String> values) {
            addCriterion("gzt_rs not in", values, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsBetween(String value1, String value2) {
            addCriterion("gzt_rs between", value1, value2, "gztRs");
            return (Criteria) this;
        }

        public Criteria andGztRsNotBetween(String value1, String value2) {
            addCriterion("gzt_rs not between", value1, value2, "gztRs");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andAuditByIsNull() {
            addCriterion("audit_by is null");
            return (Criteria) this;
        }

        public Criteria andAuditByIsNotNull() {
            addCriterion("audit_by is not null");
            return (Criteria) this;
        }

        public Criteria andAuditByEqualTo(String value) {
            addCriterion("audit_by =", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByNotEqualTo(String value) {
            addCriterion("audit_by <>", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByGreaterThan(String value) {
            addCriterion("audit_by >", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByGreaterThanOrEqualTo(String value) {
            addCriterion("audit_by >=", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByLessThan(String value) {
            addCriterion("audit_by <", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByLessThanOrEqualTo(String value) {
            addCriterion("audit_by <=", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByLike(String value) {
            addCriterion("audit_by like", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByNotLike(String value) {
            addCriterion("audit_by not like", value, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByIn(List<String> values) {
            addCriterion("audit_by in", values, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByNotIn(List<String> values) {
            addCriterion("audit_by not in", values, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByBetween(String value1, String value2) {
            addCriterion("audit_by between", value1, value2, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditByNotBetween(String value1, String value2) {
            addCriterion("audit_by not between", value1, value2, "auditBy");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNull() {
            addCriterion("audit_time is null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNotNull() {
            addCriterion("audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeEqualTo(Date value) {
            addCriterion("audit_time =", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotEqualTo(Date value) {
            addCriterion("audit_time <>", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThan(Date value) {
            addCriterion("audit_time >", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("audit_time >=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThan(Date value) {
            addCriterion("audit_time <", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThanOrEqualTo(Date value) {
            addCriterion("audit_time <=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIn(List<Date> values) {
            addCriterion("audit_time in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotIn(List<Date> values) {
            addCriterion("audit_time not in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeBetween(Date value1, Date value2) {
            addCriterion("audit_time between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotBetween(Date value1, Date value2) {
            addCriterion("audit_time not between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonIsNull() {
            addCriterion("audit_refuse_reson is null");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonIsNotNull() {
            addCriterion("audit_refuse_reson is not null");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonEqualTo(String value) {
            addCriterion("audit_refuse_reson =", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonNotEqualTo(String value) {
            addCriterion("audit_refuse_reson <>", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonGreaterThan(String value) {
            addCriterion("audit_refuse_reson >", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonGreaterThanOrEqualTo(String value) {
            addCriterion("audit_refuse_reson >=", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonLessThan(String value) {
            addCriterion("audit_refuse_reson <", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonLessThanOrEqualTo(String value) {
            addCriterion("audit_refuse_reson <=", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonLike(String value) {
            addCriterion("audit_refuse_reson like", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonNotLike(String value) {
            addCriterion("audit_refuse_reson not like", value, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonIn(List<String> values) {
            addCriterion("audit_refuse_reson in", values, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonNotIn(List<String> values) {
            addCriterion("audit_refuse_reson not in", values, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonBetween(String value1, String value2) {
            addCriterion("audit_refuse_reson between", value1, value2, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRefuseResonNotBetween(String value1, String value2) {
            addCriterion("audit_refuse_reson not between", value1, value2, "auditRefuseReson");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkIsNull() {
            addCriterion("audit_remark is null");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkIsNotNull() {
            addCriterion("audit_remark is not null");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkEqualTo(String value) {
            addCriterion("audit_remark =", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkNotEqualTo(String value) {
            addCriterion("audit_remark <>", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkGreaterThan(String value) {
            addCriterion("audit_remark >", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("audit_remark >=", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkLessThan(String value) {
            addCriterion("audit_remark <", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkLessThanOrEqualTo(String value) {
            addCriterion("audit_remark <=", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkLike(String value) {
            addCriterion("audit_remark like", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkNotLike(String value) {
            addCriterion("audit_remark not like", value, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkIn(List<String> values) {
            addCriterion("audit_remark in", values, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkNotIn(List<String> values) {
            addCriterion("audit_remark not in", values, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkBetween(String value1, String value2) {
            addCriterion("audit_remark between", value1, value2, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andAuditRemarkNotBetween(String value1, String value2) {
            addCriterion("audit_remark not between", value1, value2, "auditRemark");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNull() {
            addCriterion("reserve1 is null");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNotNull() {
            addCriterion("reserve1 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve1EqualTo(String value) {
            addCriterion("reserve1 =", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotEqualTo(String value) {
            addCriterion("reserve1 <>", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThan(String value) {
            addCriterion("reserve1 >", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThanOrEqualTo(String value) {
            addCriterion("reserve1 >=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThan(String value) {
            addCriterion("reserve1 <", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThanOrEqualTo(String value) {
            addCriterion("reserve1 <=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Like(String value) {
            addCriterion("reserve1 like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotLike(String value) {
            addCriterion("reserve1 not like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1In(List<String> values) {
            addCriterion("reserve1 in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotIn(List<String> values) {
            addCriterion("reserve1 not in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Between(String value1, String value2) {
            addCriterion("reserve1 between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotBetween(String value1, String value2) {
            addCriterion("reserve1 not between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNull() {
            addCriterion("reserve2 is null");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNotNull() {
            addCriterion("reserve2 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve2EqualTo(String value) {
            addCriterion("reserve2 =", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotEqualTo(String value) {
            addCriterion("reserve2 <>", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThan(String value) {
            addCriterion("reserve2 >", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThanOrEqualTo(String value) {
            addCriterion("reserve2 >=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThan(String value) {
            addCriterion("reserve2 <", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThanOrEqualTo(String value) {
            addCriterion("reserve2 <=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Like(String value) {
            addCriterion("reserve2 like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotLike(String value) {
            addCriterion("reserve2 not like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2In(List<String> values) {
            addCriterion("reserve2 in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotIn(List<String> values) {
            addCriterion("reserve2 not in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Between(String value1, String value2) {
            addCriterion("reserve2 between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotBetween(String value1, String value2) {
            addCriterion("reserve2 not between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNull() {
            addCriterion("reserve3 is null");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNotNull() {
            addCriterion("reserve3 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve3EqualTo(String value) {
            addCriterion("reserve3 =", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotEqualTo(String value) {
            addCriterion("reserve3 <>", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThan(String value) {
            addCriterion("reserve3 >", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThanOrEqualTo(String value) {
            addCriterion("reserve3 >=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThan(String value) {
            addCriterion("reserve3 <", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThanOrEqualTo(String value) {
            addCriterion("reserve3 <=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Like(String value) {
            addCriterion("reserve3 like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotLike(String value) {
            addCriterion("reserve3 not like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3In(List<String> values) {
            addCriterion("reserve3 in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotIn(List<String> values) {
            addCriterion("reserve3 not in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Between(String value1, String value2) {
            addCriterion("reserve3 between", value1, value2, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotBetween(String value1, String value2) {
            addCriterion("reserve3 not between", value1, value2, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve4IsNull() {
            addCriterion("reserve4 is null");
            return (Criteria) this;
        }

        public Criteria andReserve4IsNotNull() {
            addCriterion("reserve4 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve4EqualTo(String value) {
            addCriterion("reserve4 =", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4NotEqualTo(String value) {
            addCriterion("reserve4 <>", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4GreaterThan(String value) {
            addCriterion("reserve4 >", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4GreaterThanOrEqualTo(String value) {
            addCriterion("reserve4 >=", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4LessThan(String value) {
            addCriterion("reserve4 <", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4LessThanOrEqualTo(String value) {
            addCriterion("reserve4 <=", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4Like(String value) {
            addCriterion("reserve4 like", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4NotLike(String value) {
            addCriterion("reserve4 not like", value, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4In(List<String> values) {
            addCriterion("reserve4 in", values, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4NotIn(List<String> values) {
            addCriterion("reserve4 not in", values, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4Between(String value1, String value2) {
            addCriterion("reserve4 between", value1, value2, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve4NotBetween(String value1, String value2) {
            addCriterion("reserve4 not between", value1, value2, "reserve4");
            return (Criteria) this;
        }

        public Criteria andReserve5IsNull() {
            addCriterion("reserve5 is null");
            return (Criteria) this;
        }

        public Criteria andReserve5IsNotNull() {
            addCriterion("reserve5 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve5EqualTo(String value) {
            addCriterion("reserve5 =", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5NotEqualTo(String value) {
            addCriterion("reserve5 <>", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5GreaterThan(String value) {
            addCriterion("reserve5 >", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5GreaterThanOrEqualTo(String value) {
            addCriterion("reserve5 >=", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5LessThan(String value) {
            addCriterion("reserve5 <", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5LessThanOrEqualTo(String value) {
            addCriterion("reserve5 <=", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5Like(String value) {
            addCriterion("reserve5 like", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5NotLike(String value) {
            addCriterion("reserve5 not like", value, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5In(List<String> values) {
            addCriterion("reserve5 in", values, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5NotIn(List<String> values) {
            addCriterion("reserve5 not in", values, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5Between(String value1, String value2) {
            addCriterion("reserve5 between", value1, value2, "reserve5");
            return (Criteria) this;
        }

        public Criteria andReserve5NotBetween(String value1, String value2) {
            addCriterion("reserve5 not between", value1, value2, "reserve5");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}