package com.jsunicom.api.common.utils;

import org.joda.time.DateTime;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> by <PERSON><PERSON><PERSON><PERSON> on 2017/12/7.
 * @Date 2017/12/7 17:53
 * 功能描述：时间工具类
 */
public class TimeUtils {

    public enum DateFormat {
        df1("yyyy-MM-dd"),
        df2("yyyy-MM-dd HH:mm:ss"),
        df3("yyyyMMdd"),
        df4("yyyyMMddHHmmss");

        private String code;

        private DateFormat(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    // 获取年份
    public static String getYear(Date date) {
        DateTime dt = new DateTime(date);
        return dt.toString("yyyy");
    }

    // 获取月份
    public static String getMonth(Date date) {
        DateTime dt = new DateTime(date);
        return dt.toString("MM");
    }

    // 获取日
    public static String getDay(Date date) {
        DateTime dt = new DateTime(date);
        return dt.toString("dd");
    }

    // 获取完整日期，默认格式：yyyy-MM-dd
    public static String getDateStr(Date date, DateFormat dateFormat) {
        DateTime dt = new DateTime(date);
        return dt.toString(dateFormat.getCode());
    }

    // 获取完整日期，默认格式：yyyy-MM-dd
    public static String getDateStr(Date date) {
        return getDateStr(date, DateFormat.df1);
    }
    
 // 获取缩写日期，默认格式：yyyyMMdd
    public static String getSimpleDateStr(Date date) {
        return getDateStr(date, DateFormat.df3);
    }

    // 获取完整日期时间，默认格式：yyyy-MM-dd HH:mm:ss
    public static String getNowStr() {
        return getNowStr(DateFormat.df2);
    }

    // 获取指定格式的时间
    public static String getNowStr(DateFormat dateFormat) {
        return getDateStr(new Date(), dateFormat);
    }
    
    /**
     * 
     * @Title: isSameDate
     * @Description: 判断2个日期是否同一天
     * @param date1 日期1
     * @param date2 日期2
     * @return true/false
     * @throws
     */
	public static boolean isSameDate(Date date1, Date date2) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
		boolean isSameMonth = isSameYear
				&& cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		boolean isSameDate = isSameMonth && cal1
				.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
		return isSameDate;
	}
    /**
     *
     * @Title: isSameDate
     * @Description: 判断当前日期是否在开始和结束时间内
     * @param now 当前日期
     * @param beginTime 开始时间
     * @param endTime 开始时间
     * @return true/false
     * @throws
     */
    public static boolean isValidDate(Date now, Date beginTime, Date endTime ) {
        boolean result = true;
        //奖项未生效
        if(now.compareTo(beginTime) < 0){
            result = false;
        }
        //奖项已失效
        if(now.compareTo(endTime) > 0){
            result = false;
        }
        return result;
    }

}
