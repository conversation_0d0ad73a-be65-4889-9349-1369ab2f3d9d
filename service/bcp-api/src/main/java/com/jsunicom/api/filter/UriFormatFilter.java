package com.jsunicom.api.filter;

import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.utils.SM4Utils;
import com.jsunicom.api.entity.whiteList.FilterUrl;
import com.jsunicom.api.service.impl.whiteList.WhitelistManagerService;
import com.jsunicom.api.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @BelongsProject: 20230926
 * @BelongsPackage: com.jsunicom.api.filter
 * @Author: zhaowang
 * @CreateTime: 2024-05-08  21:18
 * @Description: TODO
 * @Version: 1.0
 */
@WebFilter(value = "/*", filterName = "uriFormatFilter")
public class UriFormatFilter extends OncePerRequestFilter {
    @Autowired
    private WhitelistManagerService whitelistManagerService;
    @Value("${sha.sm4key}")
    private String sm4key;

    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws IOException, ServletException {

        String uri = httpServletRequest.getRequestURI();
        String newUri = uri.replace("//","/");
        httpServletRequest = new HttpServletRequestWrapper(httpServletRequest){
            @Override
            public String getRequestURI() {
                return newUri;
            }
        };
        ServletRequest requestWrapper = new RequestWrapper(httpServletRequest);
        String path = httpServletRequest.getRequestURI();
        if (whitelistPatch(path, whitelistManagerService.getEncryptWhitelist())) {
            logger.info("In EncryptWhitelist: " + path);
            filterChain.doFilter(requestWrapper, httpServletResponse);
            return;
        }
        if (requestWrapper != null) {
            httpServletResponse.setCharacterEncoding("UTF-8");
            ModifyResponseBodyWrapper modifyResponseBodyWrapper = new ModifyResponseBodyWrapper(httpServletResponse);
            filterChain.doFilter(requestWrapper, modifyResponseBodyWrapper);
            dataEncrypt(httpServletResponse, httpServletRequest, modifyResponseBodyWrapper);
        } else {
            httpServletResponse.setCharacterEncoding("UTF-8");
            ModifyResponseBodyWrapper modifyResponseBodyWrapper = new ModifyResponseBodyWrapper(httpServletResponse);
            filterChain.doFilter(httpServletRequest, modifyResponseBodyWrapper);
            dataEncrypt(httpServletResponse, httpServletRequest, modifyResponseBodyWrapper);
        }

    }
    private boolean whitelistPatch(String uri, List<FilterUrl> filterUrls) {
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        return filterUrls.stream().anyMatch(url -> antPathMatcher.match(url.getUrl(), uri));// && url.getMethod()== method
    }
    public void dataEncrypt(HttpServletResponse httpServletResponse,HttpServletRequest httpServletRequest,ModifyResponseBodyWrapper modifyResponseBodyWrapper){
        Object handler = httpServletRequest.getAttribute(HandlerMapping.BEST_MATCHING_HANDLER_ATTRIBUTE);
        if (handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            // RequireLogin annotation = method.getMethodAnnotation(RequireLogin.class);
            if (method.hasMethodAnnotation(EncryptApi.class)) {
                try {
                    String oldResponseBody = modifyResponseBodyWrapper.getResponseBody();
                    if (!StringUtil.isEmpty(oldResponseBody)) {
                        /*String encryptedContent = SM4Utils.encryptEcb(sm4key, oldResponseBody);*/
                        //加密
                        Integer index=21;
                        String k1 = SM4Utils.generateKey();
                        String i1 = SM4Utils.generateKey();
                        String data=SM4Utils.encryptCBC(oldResponseBody, k1, i1);
                        String last=k1.substring(index);
                        String dataLast=k1.replace(last,i1+last);
                        String encryptedContent=dataLast+data;
                        httpServletResponse.setContentType(httpServletRequest.getContentType());
                        byte[] responseBodyData = encryptedContent.getBytes(StandardCharsets.UTF_8);
                        httpServletResponse.setHeader("Content-Length", String.valueOf(responseBodyData.length));//解决数据过长导致截断问题
                        ServletOutputStream out = httpServletResponse.getOutputStream();
                        out.write(responseBodyData);
                    }
                } catch (Exception e) {
                    logger.info("响应数据加密异常:"+e.getMessage());
                }
            }else{
                try {
                    String oldResponseBody = modifyResponseBodyWrapper.getResponseBody();
                    if (!StringUtil.isEmpty(oldResponseBody)) {
                        httpServletResponse.setContentType(httpServletRequest.getContentType());
                        byte[] responseBodyData = oldResponseBody.getBytes(StandardCharsets.UTF_8);
                        httpServletResponse.setHeader("Content-Length", String.valueOf(responseBodyData.length));//解决数据过长导致截断问题
                        ServletOutputStream out = httpServletResponse.getOutputStream();
                        out.write(responseBodyData);
                    }
                } catch (IOException e) {
                    logger.info("响应数据没有加密但是出现异常:"+e.getMessage());
                }
            }
        }
    }
}
