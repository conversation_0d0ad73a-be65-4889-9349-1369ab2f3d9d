package com.jsunicom.api.service;

import com.jsunicom.api.po.WoScYiMember;

import java.util.List;

/**
 * @BelongsProject: jspop_backend
 * @BelongsPackage: com.jsunicom.api.service
 * @ClassName: InnovateMemberService
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2023-07-17  11:56
 * @Description: TODO
 * @Version: 1.0
 */
public interface InnovateMemberService {
    WoScYiMember findById(Long id);
    List<WoScYiMember> findByAcctNo(String acctNo);
}
