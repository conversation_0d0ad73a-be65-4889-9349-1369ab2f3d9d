package com.jsunicom.api.service.impl;

import com.jsunicom.api.mapper.CommisIntoAcctDao;
import com.jsunicom.api.model.CommisIntoAcct;
import com.jsunicom.api.service.CommisIntoAcctFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: CommisIntoAcctServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-12  14:32
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class CommisIntoAcctServiceImpl implements CommisIntoAcctFacade {
    @Autowired
    private CommisIntoAcctDao commisIntoAcctDao;

    @Override
    public List<CommisIntoAcct> findMerchantCommisIntoAccts(Long merchantId, String state) {
        return commisIntoAcctDao.findByMercntIdAndState(merchantId,state);
    }
}
