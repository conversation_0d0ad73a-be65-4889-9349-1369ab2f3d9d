package com.jsunicom.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;

@Slf4j
public class Base64PhotoUtil {
	static Logger logger = LoggerFactory.getLogger(Base64PhotoUtil.class);

	/**
	 * 将base64字符串转为输入流
	 * @param base64string
	 * @return
	 */
	public static InputStream BaseToInputStream(String base64string) {
		ByteArrayInputStream stream = null;
		try {
			Base64.Decoder decoder = Base64.getDecoder();
			byte[] bytes1 = decoder.decode(base64string);
			stream = new ByteArrayInputStream(bytes1);
		} catch (Exception e) {
			logger.error("Base64转流异常:",e);
			stream = null;
		}
		return stream;
	}

}
