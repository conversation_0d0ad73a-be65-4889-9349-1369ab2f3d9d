package com.jsunicom.api.entity.user;

import com.alibaba.fastjson.JSON;
import com.jsunicom.api.model.base.BasicModel;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

public class UserInfo extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;

    @Pattern(regexp = "^\\d+$", message = "orgCode必须为数字")
    private String orgCode;
    @NotEmpty(message = "账户名不能为空")
    @Size(min = 1, max = 30, message = "账号名为1-30个字符")
    private String acctNo;
    @Size(min = 1, max = 30, message = "账号名为1-30个字符")
    private String acctName;

//    @JSONField(serialize = false)
    @NotNull(message = "密码不能为空")
    private String password;
    @Pattern(regexp = "^1([358][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8}$", message = "手机号码不符合格式")
    private String mblNbr;
    @Email(message = "电子邮箱不符合规则")
    private String email;
    private String wxAcct;
    private String state;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    //用户添加条线
    private String busLine;

    private String smsCode;

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMblNbr() {
        return mblNbr;
    }

    public void setMblNbr(String mblNbr) {
        this.mblNbr = mblNbr;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWxAcct() {
        return wxAcct;
    }

    public void setWxAcct(String wxAcct) {
        this.wxAcct = wxAcct;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBusLine() {
        return busLine;
    }

    public void setBusLine(String busLine) {
        this.busLine = busLine;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

/*List columns as follows:
"id", "org_code", "acct_no", "acct_name", "password", "mbl_nbr", "email",
"wx_acct", "state", "create_by", "create_time", "update_by", "update_time"
*/
