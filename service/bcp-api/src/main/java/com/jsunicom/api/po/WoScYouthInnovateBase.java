package com.jsunicom.api.po;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class WoScYouthInnovateBase implements Serializable {
    private Long societyId;

    private String societyName;

    private Long campusId;

    private String orgCode;

    private String societyLogoUrl;

    private String societyIntroduction;

    private String societyRemark;

    private String state;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private String reserve1;

    private String reserve2;

    private String reserve3;

    private List<Map> list;

    public List<Map> getList() {
        return list;
    }

    public void setList(List<Map> list) {
        this.list = list;
    }

    private static final long serialVersionUID = 1L;

    public Long getSocietyId() {
        return societyId;
    }

    public void setSocietyId(Long societyId) {
        this.societyId = societyId;
    }

    public String getSocietyName() {
        return societyName;
    }

    public void setSocietyName(String societyName) {
        this.societyName = societyName == null ? null : societyName.trim();
    }

    public Long getCampusId() {
        return campusId;
    }

    public void setCampusId(Long campusId) {
        this.campusId = campusId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }


    public String getSocietyLogoUrl() {
        return societyLogoUrl;
    }

    public void setSocietyLogoUrl(String societyLogoUrl) {
        this.societyLogoUrl = societyLogoUrl == null ? null : societyLogoUrl.trim();
    }

    public String getSocietyIntroduction() {
        return societyIntroduction;
    }

    public void setSocietyIntroduction(String societyIntroduction) {
        this.societyIntroduction = societyIntroduction == null ? null : societyIntroduction.trim();
    }

    public String getSocietyRemark() {
        return societyRemark;
    }

    public void setSocietyRemark(String societyRemark) {
        this.societyRemark = societyRemark == null ? null : societyRemark.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getReserve1() {
        return reserve1;
    }

    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1 == null ? null : reserve1.trim();
    }

    public String getReserve2() {
        return reserve2;
    }

    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2 == null ? null : reserve2.trim();
    }

    public String getReserve3() {
        return reserve3;
    }

    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3 == null ? null : reserve3.trim();
    }
}
