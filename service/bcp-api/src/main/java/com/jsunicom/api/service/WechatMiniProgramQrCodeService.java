package com.jsunicom.api.service;

import com.jsunicom.api.po.WechatMiniProgramQrCode;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-13-11:19
 */
public interface WechatMiniProgramQrCodeService {
    /**
     * 创建小程序码，如果有直接返回，没有就新建一个
     *
     * @param schoolId  学校ID
     * @param partnerId 合伙人ID
     *
     * @return 生成的小程序码
     */
    WechatMiniProgramQrCode createQrCode(Long schoolId, Long partnerId);
    /**
     * 创建小程序码，如果有直接返回，没有就新建一个
     *
     * @param scene     场景ID，最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
     * @param schoolId  学校ID
     * @param partnerId 合伙人ID
     *
     * @return 生成的小程序码
     */
    WechatMiniProgramQrCode createQrCode(String scene, long schoolId, long partnerId);
}
