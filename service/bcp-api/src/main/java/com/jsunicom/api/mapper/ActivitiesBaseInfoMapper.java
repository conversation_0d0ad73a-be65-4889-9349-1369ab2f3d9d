package com.jsunicom.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsunicom.api.entity.Campus.ActivitiesBaseInfo;
import com.jsunicom.api.vo.campus.ActivitiesDetailsVO;
import com.jsunicom.api.vo.campus.ClockUserInfo;
import com.jsunicom.api.vo.campus.TeamBaseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【activities_base_info】的数据库操作Mapper
* @createDate 2025-04-02 16:17:13
* @Entity generator.domain.ActivitiesBaseInfo
*/
public interface ActivitiesBaseInfoMapper extends BaseMapper<ActivitiesBaseInfo> {

    List<TeamBaseInfo> getTeamInfo(@Param("campusId") String campusId,@Param("isMerAdminList") List<String> isMerAdminList,@Param("memberIds") List<String> memberIds);

    int  insertActivity(@Param("activitiesBaseInfo") ActivitiesBaseInfo activitiesBaseInfo);

    String getCenterPoint(@Param("monthId") String monthId, @Param("dayId") String dayId, @Param("activitiesId") String activitiesId);

    ActivitiesDetailsVO getActivitiesDetailsByActivitiesId( @Param("activitiesId") String activitiesId);

    ClockUserInfo getUserInfoByMblNbr(@Param("mblNbr") String mblNbr);
}




