package com.jsunicom.api.po;


import com.jsunicom.api.model.base.BasicModel;

import java.util.Date;

/**
 * Created by xuc40 on 20180712 for ActionCollect
 */
public class UserActionDict extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private int actionId;
    private short actionType;
    private String actionDesc;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String vue;
    private String route;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionId() {
        return actionId + "";
    }

    public void setActionId(String actionId) {
        this.actionId = Integer.parseInt(actionId);
    }

    public short getActionType() {
        return actionType;
    }

    public void setActionType(short actionType) {
        this.actionType = actionType;
    }

    public String getActionDesc() {
        return actionDesc;
    }

    public void setActionDesc(String actionDesc) {
        this.actionDesc = actionDesc;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getVue() {
        return vue;
    }

    public void setVue(String vue) {
        this.vue = vue;
    }

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }
}
