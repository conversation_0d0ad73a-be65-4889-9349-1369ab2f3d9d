package com.jsunicom.api.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.api.entity.Campus.Campus;
import com.jsunicom.api.entity.Campus.CampusBind;
import com.jsunicom.api.entity.school.SchoolListInfo;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * .
 *
 * @ClassName: CampusFacade
 * @Auther: LJ.Huang
 * @Date: 2022/3/19 15:33
 * @Version: bcpV1.0
 * @Description: campus facade
 **/
public interface CampusFacade {

  /**
   *
   * @param campusId
   * @return
   */
  Campus query(Long campusId);
  /**
   * 获取校区团队
   * @param campusId
   * @return
   */
  PageInfo getSchoolInfoList(String campusId, Integer pageStar, Integer pageNum);
  /**
   * 获取所属校区
   * @param campusBind
   * @return
   */
  List<Campus> getCampusList(CampusBind campusBind);

  List<Campus> getCampusListFromProvince(String orgCode);

  // 社长查询校区列表
  public List<Campus> getCampusListBySz(long societyId);
}
