package com.jsunicom.api.mapper;


import com.jsunicom.api.entity.dataMonitor.DataMonitor;
import com.jsunicom.api.mapper.basic.BasicDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xuc40 on 20180725 for OperationLogForData
 */
public interface DataMonitorDao extends BasicDao {

    ArrayList<DataMonitor> findByPage(Map<String, Object> paramMap);

    int saveBatchPromotActvMonitor(HashMap map);

    int saveBatchGoodsMonitor(HashMap map);
}
