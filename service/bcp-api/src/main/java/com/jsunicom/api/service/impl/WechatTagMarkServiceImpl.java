package com.jsunicom.api.service.impl;

import com.jsunicom.api.common.Constants;
import com.jsunicom.api.mapper.WechatTagMarkDao;
import com.jsunicom.api.model.WechatTagMark;
import com.jsunicom.api.service.WechatTagMarkFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Calendar;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: WechatTagMarkServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-14  10:43
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class WechatTagMarkServiceImpl implements WechatTagMarkFacade {
//    @Autowired
//    private QyWXUserService qyWXUserService;

    @Autowired
    private WechatTagMarkDao wechatTagMarkDao;

    @Value("${qywx.merchant.bd.tag.id}")
    private Integer bdTagId;

    private final String bdTagName = "BD助手";

    @Override
    public void deleteBdAssistant(String userId) {
        //TODO 先注释 20230321
//        qyWXUserService.deleteTagUsers(userId, bdTagId);
        wechatTagMarkDao.deleteWechatTagMark(userId, bdTagId);
    }
    @Override
    public void addBdAssistant(String userId) {
        WechatTagMark tagMark = new WechatTagMark(userId, Constants.Wechat.TAG_TARGET_MEMBER, bdTagId, bdTagName,
                new Timestamp(Calendar.getInstance().getTimeInMillis()));
        wechatTagMarkDao.save(tagMark);
        //TODO 先注释 20230321
//        qyWXUserService.addTag(userId, bdTagId);
    }
}
