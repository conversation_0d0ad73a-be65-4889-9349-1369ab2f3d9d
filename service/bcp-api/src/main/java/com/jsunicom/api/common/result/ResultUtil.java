package com.jsunicom.api.common.result;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * <AUTHOR> z<PERSON>wang
 * @ClassName ResultUtil
 * @Description TODO 通用返回类Result工具类
 * @date : 2021-08-24 20:12
 * @Version 1.0
 **/
public class ResultUtil {
    /** 成功且带数据---Object **/
    public static CustomResult success(Object object){
        CustomResult result = new CustomResult();
        result.setrespCode(ResultEnum.SUCCESS.getrespCode());
        result.setrespDesc(ResultEnum.SUCCESS.getrespDesc());
        result.setrespData(object);
        return result;
    }

    /** 成功且带数据---JSONObject **/
    public static CustomResult successByJSONObject(JSONObject object){
        CustomResult result = new CustomResult();
        result.setrespCode(ResultEnum.SUCCESS.getrespCode());
        result.setrespDesc(ResultEnum.SUCCESS.getrespDesc());
        result.setrespData(object);
        return result;
    }

    /** 成功且带数据---JSONArray **/
    public static CustomResult successByJSONArray(JSONArray array){
        CustomResult result = new CustomResult();
        result.setrespCode(ResultEnum.SUCCESS.getrespCode());
        result.setrespDesc(ResultEnum.SUCCESS.getrespDesc());
        result.setrespData(array);
        return result;
    }

    /** 成功且带数据---JSONArray **/
    public static CustomResult successByMap(Map map){
        CustomResult result = new CustomResult();
        result.setrespCode(ResultEnum.SUCCESS.getrespCode());
        result.setrespDesc(ResultEnum.SUCCESS.getrespDesc());
        result.setrespData(map);
        return result;
    }


    /**成功但不带数据**/
    public static CustomResult success(){
        return success("");
    }

    /**成功带自定义消息**/
    public static CustomResult success(String code, String msg){
        CustomResult result = new CustomResult();
        result.setrespCode(code);
        result.setrespDesc(msg);
        result.setrespData("");
        return result;
    }

    /**失败**/
    public static CustomResult error(String code, String msg){
        CustomResult result = new CustomResult();
        result.setrespCode(code);
        result.setrespDesc(msg);
        result.setrespData("");
        return result;
    }

    /**失败
     *传入枚举
     * **/
    public static CustomResult error(ResultEnum resultEnum){
        CustomResult result = new CustomResult();
        result.setrespCode(resultEnum.getrespCode());
        result.setrespDesc(resultEnum.getrespDesc());
        result.setrespData("");
        return result;
    }



}
