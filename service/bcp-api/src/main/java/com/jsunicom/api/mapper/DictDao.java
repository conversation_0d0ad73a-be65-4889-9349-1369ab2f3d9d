package com.jsunicom.api.mapper;

import com.jsunicom.api.mapper.basic.BasicDao;
import com.jsunicom.api.po.Dict;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

public interface DictDao extends BasicDao {

    Dict findByUK(@Param("kind") String kind, @Param("code") String code);

    ArrayList<Dict> selectBykind(String kind);

    Integer selectByMenu(@Param("menu") String menu);
}
