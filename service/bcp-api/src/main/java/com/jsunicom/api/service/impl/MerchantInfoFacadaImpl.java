package com.jsunicom.api.service.impl;

import com.jsunicom.api.mapper.MerchantInfoMapper;
import com.jsunicom.api.po.MerchantInfo;
import com.jsunicom.api.po.MerchantInfoExample;
import com.jsunicom.api.po.MerchantListInfo;
import com.jsunicom.api.service.MerchantInfoFacada;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MerchantInfoFacadaImpl implements MerchantInfoFacada {

    @Autowired
    private MerchantInfoMapper merchantInfoMapper;


    @Override
    public long countByExample(Long societyId)
    {
        MerchantInfoExample example = new MerchantInfoExample();
        MerchantInfoExample.Criteria criteria = example.createCriteria();
        // 是否需要判断状态
        criteria.andSocietyIdEqualTo(societyId);
        return merchantInfoMapper.countByExample(example);
    }

    @Override
    public List<MerchantListInfo> selectMerchantList(Long societyId)
    {
        return  merchantInfoMapper.selectMerchantList(societyId);

    }
    @Override
    public List<MerchantListInfo> selectMerchantListInfo(Long merchantId){

        return  merchantInfoMapper.selectMerchantInfoList(merchantId);

    }
    @Override
    public MerchantInfo selectByPrimaryKey(Long id){

        return  merchantInfoMapper.selectByPrimaryKey(id);

    }
    @Override
    public int updateMerchant(MerchantInfo record)
    {
        return merchantInfoMapper.updateByPrimaryKeySelective(record);

    }

    @Override
    public Long createMerchant(MerchantInfo record)
    {

        int i = merchantInfoMapper.insertSelective(record);
        if(i==0){
            throw new RuntimeException("插入失败");
        }

        return record.getId();
    }

}
