package com.jsunicom.api.service;

import com.jsunicom.api.service.impl.PartnerServiceImpl;
import com.lz.lsf.framework.spring.SpringContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/30.
 */
public class PartnerThread implements Runnable {

    private final static Logger log = LoggerFactory.getLogger(PartnerThread.class);

    private String id;
    private String partnerName;
    private String partnerCertNo;

    public PartnerThread(String id, String partnerName, String partnerCertNo) {
        this.id = id;
        this.partnerName = partnerName;
        this.partnerCertNo = partnerCertNo;
    }

    @Override
    public void run() {
        try {
            log.info("国政通查询线程启动 入参：{}", id,partnerName,partnerCertNo);
            ApplicationContext context = SpringContextHolder.getApplicationContext();
            PartnerServiceImpl partnerService = (PartnerServiceImpl) context.getBean("partnerServiceImpl");
            partnerService.updateGzt(id, partnerName, partnerCertNo);
            log.info("国政通查询线程结束：{}",id,partnerName,partnerCertNo);
        } catch (Exception e) {
            log.error("国政通查询线程内执行调用异常", e);
        }
    }
}
