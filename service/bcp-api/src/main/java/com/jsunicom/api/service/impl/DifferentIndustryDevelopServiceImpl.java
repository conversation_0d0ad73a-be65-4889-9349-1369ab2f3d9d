package com.jsunicom.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.api.model.dto.PartnerDto;
import com.jsunicom.api.service.DifferentIndustryDevelopFacade;
import com.jsunicom.api.service.IdentityDifferentIndustryDevelopFacade;
import com.jsunicom.api.service.PartnerFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: DifferentIndustryDevelopServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-12  16:06
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class DifferentIndustryDevelopServiceImpl implements DifferentIndustryDevelopFacade {
    @Autowired
    private PartnerFacade partnerService;

    @Autowired
    private IdentityDifferentIndustryDevelopFacade identityDifferentIndustryDevelopService;

    //从总部返回消息请求code
    private static final String RESP_CODE_SUCCESS = "00000"; //成功
    //从总部返回消息对应的，业务侧返回成功
    private static final String RETURN_STATE_SUCCESS = "01"; //成功
    @Override
    public JSONObject getDevelopmenterInfoNew(PartnerDto partner) {
        JSONObject result = new JSONObject();
        try{
            log.info("------请求江苏联通能力平台异业发展人导入服务接口开始，partner：{}",partner);
            String responseStr = identityDifferentIndustryDevelopService.getDevelopmenterInfoByIdentity(partner);
            log.info("------江苏联通能力平台异业发展人导入服务接口返回，responseStr：{}",responseStr);
            JSONObject responseJson = JSONObject.parseObject(responseStr);
            JSONObject uniBssHead = responseJson.getJSONObject("UNI_BSS_HEAD");
            String respCode = uniBssHead.getString("RESP_CODE");
            if(RESP_CODE_SUCCESS.equals(respCode)){//能力平台侧服务调用成功
                JSONObject UNI_BSS_BODY_RSP = responseJson.getJSONObject("UNI_BSS_BODY");
                JSONObject developmenterSyncRsp = UNI_BSS_BODY_RSP.getJSONObject("DEVELOPMENTER_SYNC_RSP");
                String returnState = developmenterSyncRsp.getString("RETURN_STATE");
                if(RETURN_STATE_SUCCESS.equals(returnState)) { //业务侧返回成功
                    String devCode1=developmenterSyncRsp.getString("DEV_CODE");//发展人编码
                    result.put("ret","0");
                    result.put("develId",devCode1);
                    result.put("respCode",RESP_CODE_SUCCESS);
                    result.put("code",returnState);
                    result.put("errorMsg","");
                    partner.setDevelId(devCode1);
                    //partner.setMappingPointCode(partner.getMappingPointCode());
                    partnerService.updateIgnoreNull(partner);
                }
                else{
                    String failCause = developmenterSyncRsp.getString("FAIL_CAUSE");
                    result.put("ret","1");
                    result.put("respCode",RESP_CODE_SUCCESS);
                    result.put("code",returnState);
                    result.put("errorMsg",failCause);
                    //02——集中渠道系统存在未完成订单
                    //03——传入的发展人编码和证件号不匹配，无法修改或删除。
                    //04——渠道不存在或渠道为非正常状态
                    //05——根据发展人编码【******】和渠道编码【*****】没有找到发展人信息，无法修改或删除。
                    //06——业务代理人账号【******】已存在,无法重复添加。
                    //07——新增发展人或修改发展人，但身份证已在该渠道下有生效发展人。
                    //08——发展人修改归属的渠道。
                    //09——发展人归属的渠道类型不能挂异业发展人,不能进行沃行销发展人同步。
                    //10——根据业务代理人账号ID【******】没有找到发展人信息，无法修改或删除。
                    //11——授权码生成失败
                    //99——失败（其他原因）
                }
            }else{
                String respDesc=uniBssHead.getString("RESP_DESC");
                result.put("ret","2");
                result.put("respCode",respCode);
                result.put("code",respCode);
                result.put("errorMsg",respDesc);
            }
        }catch(Exception e){
            log.error("------调用江苏联通异业发展人导入服务接口出现异常，异常信息为：{}",e);
        }
        return result;
    }
}
