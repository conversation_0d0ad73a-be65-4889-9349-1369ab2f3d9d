package com.jsunicom.api.service.impl;

import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.model.MerchantPartner;
import com.jsunicom.api.service.PartnerClerkFacade;
import com.jsunicom.api.service.PartnerFacade;
import com.lz.lsf.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: PartnerClerkServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-13  16:43
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class PartnerClerkServiceImpl implements PartnerClerkFacade {
    @Autowired
    private PartnerFacade partnerService;

    /**
     * 添加店员
     * @param info
     * @param employerPartner
     * @return
     */
    @Override
    public Partner addClerk(MerchantPartner info, Partner employerPartner) {
        if(employerPartner == null){
            throw new BusinessException("CLERK001","请重新登录");
        }
        Long merchantId = employerPartner.getMerchantId();
        if(merchantId == null || merchantId == 0){
            throw new BusinessException("CLERK002","非商户用户");
        }
        info.setMerchantId(String.valueOf(merchantId));
        //添加店员商户名称和营业执照设为空
        info.setMerchantName("");
        info.setBusLicense("");
        //info.setIndustry(employerPartner.getIndustry());
        info.setProvCode(employerPartner.getProvCode());
        info.setProvName(employerPartner.getProvName());
        info.setCityCode(employerPartner.getCityCode());
        info.setCityName(employerPartner.getCityName());
        info.setAreaCode(employerPartner.getAreaCode());
        info.setAreaName(employerPartner.getAreaName());
        info.setAddress(employerPartner.getAddress());
        info.setOrgCode(employerPartner.getOrgCode());
        //info.setIntentBusCode(employerPartner.getIntentBusCode());
        //info.setIntentBusName(employerPartner.getIntentBusName());
        info.setSaleMgrPhone(employerPartner.getSaleMgrPhone());
        info.setPartnerType(employerPartner.getPartnerType());
        //added by xuc40 on 20180706 for PartnerIdentity begin
        info.setDevelChannel(employerPartner.getDevelChannel());
        //info.setRegisterPath(employerPartner.getRegisterPath());
        /*if(StringUtils.isEmpty(employerPartner.getBusinessClassification())){
            info.setBusinessClassification("13");
        }else{
            info.setBusinessClassification(employerPartner.getBusinessClassification());
        }*/
        //added by xuc40 on 20180706 for PartnerIdentity end
        if(!StringUtils.isNotEmpty(info.getRegisterPath())){
            info.setRegisterPath("1");
        }
        return partnerService.addPartner(info);
    }

}
