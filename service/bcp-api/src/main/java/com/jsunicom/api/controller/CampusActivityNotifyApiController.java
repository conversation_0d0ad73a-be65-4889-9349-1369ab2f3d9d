package com.jsunicom.api.controller;



import com.github.pagehelper.PageInfo;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.common.result.CustomResult;
import com.jsunicom.api.entity.Campus.ActivitiesNotifyBaseInfo;
import com.jsunicom.api.entity.Campus.Campus;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.service.ActivitiesNotifyBaseInfoService;
import com.jsunicom.api.service.WoSchoolFacade;
import com.jsunicom.api.vo.campus.ActivityNotifyDetailsViewVO;
import com.jsunicom.api.vo.campus.ActivityNotifyViewVO;
import com.jsunicom.api.vo.campus.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.controller
 * @ClassName: CampusActivityController
 * @Author: lidehua
 * @CreateTime: 2025-03-18  10:14
 * @Description:
 * @Version: 1.0
 */

@Slf4j
@RestController
@RequestMapping(value = "/campus/activity/notify/api")
public class CampusActivityNotifyApiController extends BaseController {

    @Resource
    private ActivitiesNotifyBaseInfoService activitiesNotifyBaseInfoService;

    @Resource
    private WoSchoolFacade woSchoolFacade;


    @PostMapping(value = "/getactivityNotifyList", name = "校园活动通知列表查询")
    @DecryptApi
    @EncryptApi
    public CustomResult getactivityNotifyList(HttpServletRequest request, @RequestBody  PageVo pageVo) {
          log.info("接收参数:{}", pageVo.toString());
//        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        PageInfo<ActivityNotifyViewVO> activityNotifyViewVOPageInfo =  activitiesNotifyBaseInfoService.getactivityNotifyList(null,pageVo);
        if (CollectionUtils.isEmpty(activityNotifyViewVOPageInfo.getList())) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", activityNotifyViewVOPageInfo);
    }


    @GetMapping(value = "/getactivityNotifyDetailById", name = "校园活动详情查询")
    @DecryptApi
    @EncryptApi
    public CustomResult getactivityNotifyDetailById(@RequestParam String activitiesNotifyId, @RequestParam String campusId, HttpServletRequest request) {
        Partner partner = getCurrentUser(request);
//        String mblNbr = partner.getMblNbr();
//        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        ActivityNotifyDetailsViewVO activityNotifyDetailsViewVO =  activitiesNotifyBaseInfoService.getactivityNotifyDetailById(activitiesNotifyId,campusId);
        if (activityNotifyDetailsViewVO == null) {
            return new CustomResult("9999", "暂无数据", null);
        }
        return new CustomResult("0000", "查询成功", activityNotifyDetailsViewVO);
    }


}
