package com.jsunicom.api.service;

import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.model.MerchantPartner;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service
 * @ClassName: PartnerClerkFacade
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2023-04-13  16:42
 * @Description: TODO
 * @Version: 1.0
 */
public interface PartnerClerkFacade {
    /**
     * 添加店员
     * @param info
     * @param employerPartner
     * @return
     */
    public Partner addClerk(MerchantPartner info, Partner employerPartner);
}
