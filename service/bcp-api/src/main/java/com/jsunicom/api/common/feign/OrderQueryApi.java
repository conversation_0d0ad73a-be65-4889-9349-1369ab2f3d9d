package com.jsunicom.api.common.feign;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.util.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.tbp.api
 * @ClassName: DictApiService
 * @Author: zhaowang
 * @CreateTime: 2023-05-30  10:30
 * @Description: TODO
 * @Version: 1.0
 */
@Component
@FeignClient(value = "bcp-oms")
public interface OrderQueryApi {
    @PostMapping(value = "/oms-bcp/orderQuery/qryStaffList",produces = "application/json;charset=UTF-8")
    public Result qryStaffList(@RequestBody JSONObject jsonObject);
}
