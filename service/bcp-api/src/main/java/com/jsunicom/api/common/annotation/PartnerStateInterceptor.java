package com.jsunicom.api.common.annotation;

import com.jsunicom.api.common.enums.PartnerStateCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-13-20:15
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PartnerStateInterceptor {
    PartnerStateCode[] stateCode() default PartnerStateCode.PASSED;
}
