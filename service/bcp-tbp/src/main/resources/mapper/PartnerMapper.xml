<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.tbp.mapper.PartnerMapper">
    <select id="checkPhoneExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
            SELECT mbl_nbr as phone FROM partner WHERE mbl_nbr = #{phone} and state = '1'
            UNION ALL
            SELECT tourist_phone as phone FROM tourist_waiting WHERE tourist_phone = #{phone}
        ) t
    </select>
</mapper> 