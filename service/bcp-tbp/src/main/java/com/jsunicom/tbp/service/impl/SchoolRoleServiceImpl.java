package com.jsunicom.tbp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.tbp.common.constant.AppletRoleType;
import com.jsunicom.tbp.entity.*;
import com.jsunicom.tbp.mapper.TbpCheckRoleInfoMapper;
import com.jsunicom.tbp.mapper.UserLoginAppletLogMapper;
import com.jsunicom.tbp.po.Dict;
import com.jsunicom.tbp.po.WeChatRoleInfo;
import com.jsunicom.tbp.result.CustomResult;
import com.jsunicom.tbp.result.ResultEnum;
import com.jsunicom.tbp.result.ResultUtil;
import com.jsunicom.tbp.service.SchoolRoleService;
import com.jsunicom.tbp.service.feign.DictApiService;
import com.jsunicom.tbp.utils.SM4Utils;
import com.jsunicom.tbp.utils.jwt.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * @BelongsProject: hb_school_backend
 * @BelongsPackage: com.jsunicom.tbp.service.impl
 * @ClassName: SchoolRoleServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-04-24  17:01
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class SchoolRoleServiceImpl implements SchoolRoleService {
    @Autowired
    TbpCheckRoleInfoMapper tbpCheckRoleInfoMapper;
    @Autowired
    UserLoginAppletLogMapper userLoginAppletLogMapper;
    @Autowired
    DictApiService dictApiService;
    @Value("${sha.keyLogin}")
    private String keyLogin;

    @Autowired
    private RedisTemplate redisTemplate;

    public static final String SCHOOL_BUS_LINE = "20";

    @Override
    public CustomResult getRoleInfo(String ecbPhoneNumber,String shakey) {
        log.info("进入登录用户角色判断方法，手机号：{}",ecbPhoneNumber);
        String resultMsg = "";
        WeChatRoleInfo dataInfo = new WeChatRoleInfo();
        try {
            //手机号解密
            log.info("手机号解密：开始... ...");
            String phoneNumber = SM4Utils.decryptEcb(shakey, ecbPhoneNumber).replaceAll("\"","");
            if (phoneNumber.length()>13){
                phoneNumber = SM4Utils.decryptEcb(shakey, phoneNumber).replaceAll("\"","");
            }
            log.info("手机号解密：结束  {}",phoneNumber);

            String roleType = "";
            StringBuffer roleTypeName = new StringBuffer();
            String name = "";
            String orgCode = "";
//            String phoneNumber = dataInfo.getPhoneNumber();
            //首先排除是省、地市管理员 sales_manager存校园经理信息
            List<SalesManager> salesMgr = tbpCheckRoleInfoMapper.findSalesManagerByPhone(phoneNumber);
            if (salesMgr.size() >0 && salesMgr.get(0).getBusLine().contains(SCHOOL_BUS_LINE)){//校区经理
                roleType = AppletRoleType.ROLE_TYPE_SCHOOL;
                name = salesMgr.get(0).getName();
                orgCode = salesMgr.get(0).getOrgCode();
                addMenuList(dataInfo,roleType);
                roleTypeName.append("校区经理").append(",");
                log.info("登录用户{},所属角色{}为校区经理",phoneNumber,roleType);
            }else{
                //判断是否是团队成员
                List<Partner> partnerList = tbpCheckRoleInfoMapper.findPartnerByPhone(phoneNumber);
                if (partnerList.size()>0){
                    Partner partner = partnerList.get(0);
                    dataInfo.setPartnerId(Long.toString(partner.getId()));
                    String isMerAdmin = partner.getIsMerAdmin();
                    if (!isMerAdmin.isEmpty() && isMerAdmin.equals("1")){//团队长
                        roleType = AppletRoleType.ROLE_TYPE_CAPTAIN;
                        name = partner.getPartnerName();
                        orgCode = partner.getOrgCode();
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("团队长").append(",");
                        log.info("登录用户{},所属角色{}为校团队长",phoneNumber,roleType);
                    }else if(!isMerAdmin.isEmpty() && isMerAdmin.equals("0")){//团队成员
                        roleType = AppletRoleType.ROLE_TYPE_MEMBER;
                        name = partner.getPartnerName();
                        orgCode = partner.getOrgCode();
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("团队成员").append(",");
                        log.info("登录用户{},所属角色{}为团队成员",phoneNumber,roleType);
                    }
                    Long schoolId=tbpCheckRoleInfoMapper.selectSchoolIdByMerchantId(partner.getMerchantId());
                    dataInfo.setSchoolId(Long.toString(schoolId));
                }
                //判断是不是青创社成员。只查主席、部长角色
                List<WoScYiMember> memberList = tbpCheckRoleInfoMapper.findMemberByPhone(phoneNumber);
                if (memberList.size()>0){
                    WoScYiMember member = memberList.get(0);
                    Long societyId = member.getSocietyId();//根据青创社ID查询青创社当前状态，如果是停用状态，不允许青创社成员操作
                    String state = tbpCheckRoleInfoMapper.getYouthInnovateInfoBySocietyId(societyId);//启用标识：0 失效、1 生效
                    if (state.equals("1")){
                        dataInfo.setMemberId(Long.toString(member.getMemberId()));
                        String memberRoleType = member.getRoleType();
                        name = member.getMemberName();
                        orgCode = member.getOrgCode();
                        if (!memberRoleType.isEmpty() && memberRoleType.equals("1")){//主席
                            roleType = AppletRoleType.ROLE_TYPE_PRESIDENT;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("青创社院系长").append(",");
                            log.info("登录用户{},所属角色{}为青创社主席",phoneNumber,roleType);
                        }else if(!memberRoleType.isEmpty() && memberRoleType.equals("2")){//部长
                            roleType = AppletRoleType.ROLE_TYPE_MINISTER;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("青创社团长").append(",");
                            log.info("登录用户{},所属角色{}为青创社部长",phoneNumber,roleType);
                        }else if(!memberRoleType.isEmpty() && memberRoleType.equals("3")){//骨干
                            roleType = AppletRoleType.ROLE_TYPE_BACKBONE;
                            addMenuList(dataInfo,roleType);
                            log.info("登录用户{},所属角色{}为青创社团员",phoneNumber,roleType);
                        }
                    }
                }

               if(partnerList.size() == 0 && memberList.size() == 0){ //判断是不是省管理员
                    List<UserRoleInfo> userRoleInfos = tbpCheckRoleInfoMapper.findUserRoleInfos(phoneNumber);
                    if (userRoleInfos.size()>0){
                        UserRoleInfo userRoleInfo = userRoleInfos.get(0);
                        String areaLevel = userRoleInfo.getAreaLevel();
                        if (!areaLevel.isEmpty() && areaLevel.equals("1")){
                            roleType = AppletRoleType.ROLE_TYPE_PROVINCE;
                            name = userRoleInfo.getStaffName();
                            orgCode = userRoleInfo.getAreaCode();
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("省份管理员").append(",");
                            log.info("登录用户{},所属角色{}为省份管理员",phoneNumber,roleType);
                        }else if(!areaLevel.isEmpty() && areaLevel.equals("2")){
                            roleType = AppletRoleType.ROLE_TYPE_CITY;
                            name = userRoleInfo.getStaffName();
                            orgCode = userRoleInfo.getAreaCode();
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("地市管理员").append(",");
                            log.info("登录用户{},所属角色{}为地市管理员",phoneNumber,roleType);
                        }else{
                            roleType = AppletRoleType.ROLE_TYPE_GUEST;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("访客").append(",");
                            log.info("登录用户{},所属角色{}为访客",phoneNumber,roleType);
                        }
                    }else{
                        roleType = AppletRoleType.ROLE_TYPE_GUEST;
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("访客").append(",");
                        log.info("登录用户{},所属角色{}为访客",phoneNumber,roleType);
                    }
                }
            }
            //返回待办任务、已办任务数量
            int pendingProcessCount = 0;
            int processedProcessCount = 0;
            if (roleType != null && !roleType.isEmpty() && !roleType.equals(AppletRoleType.ROLE_TYPE_GUEST)){
                try {
                    pendingProcessCount = tbpCheckRoleInfoMapper.getPendingProcessCount(phoneNumber);//待办
                    processedProcessCount = tbpCheckRoleInfoMapper.getProcessedProcessCount(phoneNumber);//已办
                }catch (Exception e){
                    log.error("查询待办任务、已办任务失败：{}",e.getMessage());
                }
            }
            dataInfo.setPendingProcessCount(pendingProcessCount);
            dataInfo.setProcessedProcessCount(processedProcessCount);

            //是否限制
            dataInfo.setRoleType(roleType);
            dataInfo.setName(name);

            //小程序端地市权限开关 true 限制地市访问，false 对所有地市开放
//            String areaControlFlag =
//            ArrayList<Dict> list = dictApiService.getDictsByKind("areaControlFlag");
//            String areaControlFlag = "";
//            if (list.size() > 0){
//                Dict dict = list.get(0);
//                areaControlFlag = dict.getCode();
//                if (!areaControlFlag.isEmpty() && areaControlFlag.equals("1")){
//                    ArrayList<Dict> list1 = dictApiService.getDictsByKind("areaControlInfo");
//                    if (list1.size() > 0){
//                        String areaControlInfo = list1.get(0).getCode();
//                        String[] split = areaControlInfo.split(",");
//                        if (!roleType.equals(AppletRoleType.ROLE_TYPE_GUEST) && !Arrays.asList(split).contains(orgCode)){
//                            dataInfo.setMenuList(new ArrayList<>());
//                        }
//                    }
//                }
//            }
            dataInfo.setOrgCode(orgCode);
            dataInfo.setPhoneNumber(SM4Utils.encryptEcb(shakey,phoneNumber));
            dataInfo.setPurePhoneNumber(SM4Utils.encryptEcb(shakey,phoneNumber));
            String jwt = JwtUtil.createJWT(UUID.randomUUID().toString(), JSON.toJSONString(dataInfo), null);
            String redisKey = "bcp.applet_login_" + phoneNumber;
            redisTemplate.opsForValue().set(redisKey, jwt, Duration.ofHours(24*7));//存到redis 2小时
            dataInfo.setToken(jwt);

            //记录登录日志
            UserLoginAppletLog loginAppletLog = new UserLoginAppletLog();
            loginAppletLog.setAcctNo(phoneNumber);
            loginAppletLog.setRoleType(roleType);
            loginAppletLog.setRoleTypeName(roleTypeName.deleteCharAt(roleTypeName.length() - 1).toString());
            loginAppletLog.setLoginTime(new Date());
            userLoginAppletLogMapper.insert(loginAppletLog);

            //返回内容加密

            if (dataInfo!=null){
                resultMsg = SM4Utils.encryptEcb(shakey, JSON.toJSONString(dataInfo));
            }
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),e.getMessage());
        }
        return ResultUtil.success(resultMsg);
    }

    @Override
    public CustomResult getRoleInfoNew(String ecbPhoneNumber,String shakey) {
        log.info("进入登录用户角色判断方法，手机号：{}",ecbPhoneNumber);
        String resultMsg = "";
        WeChatRoleInfo dataInfo = new WeChatRoleInfo();
        try {
            //手机号解密
            log.info("手机号解密：开始... ...");
            String phoneNumber = SM4Utils.decryptEcb(shakey, ecbPhoneNumber).replaceAll("\"","");
            if (phoneNumber.length()>13){
                phoneNumber = SM4Utils.decryptEcb(shakey, phoneNumber).replaceAll("\"","");
            }
            log.info("手机号解密：结束  {}",phoneNumber);

            String roleType = "";
            StringBuffer roleTypeName = new StringBuffer();
            String name = "";
            String orgCode = "";
//            String phoneNumber = dataInfo.getPhoneNumber();
            //首先排除是省、地市管理员 sales_manager存校园经理信息
            List<SalesManager> salesMgr = tbpCheckRoleInfoMapper.findSalesManagerByPhone(phoneNumber);
            if (salesMgr.size() >0 && salesMgr.get(0).getBusLine().contains(SCHOOL_BUS_LINE)){//校区经理
                roleType = AppletRoleType.ROLE_TYPE_SCHOOL;
                name = salesMgr.get(0).getName();
                orgCode = salesMgr.get(0).getOrgCode();
                addMenuList(dataInfo,roleType);
                roleTypeName.append("校区经理").append(",");
                log.info("登录用户{},所属角色{}为校区经理",phoneNumber,roleType);
            }else{
                //判断是否是团队成员
                List<Partner> partnerList = tbpCheckRoleInfoMapper.findPartnerByPhone(phoneNumber);
                if (partnerList.size()>0){
                    Partner partner = partnerList.get(0);
                    dataInfo.setPartnerId(Long.toString(partner.getId()));
                    String isMerAdmin = partner.getIsMerAdmin();
                    if (!isMerAdmin.isEmpty() && isMerAdmin.equals("1")){//团队长
                        roleType = AppletRoleType.ROLE_TYPE_CAPTAIN;
                        name = partner.getPartnerName();
                        orgCode = partner.getOrgCode();
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("团队长").append(",");
                        log.info("登录用户{},所属角色{}为校团队长",phoneNumber,roleType);
                    }else if(!isMerAdmin.isEmpty() && isMerAdmin.equals("0")){//团队成员
                        roleType = AppletRoleType.ROLE_TYPE_MEMBER;
                        name = partner.getPartnerName();
                        orgCode = partner.getOrgCode();
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("团队成员").append(",");
                        log.info("登录用户{},所属角色{}为团队成员",phoneNumber,roleType);
                    }
                    Long schoolId=tbpCheckRoleInfoMapper.selectSchoolIdByMerchantId(partner.getMerchantId());
                    dataInfo.setSchoolId(Long.toString(schoolId));
                }
                //判断是不是青创社成员。只查主席、部长角色
                List<WoScYiMember> memberList = tbpCheckRoleInfoMapper.findMemberByPhone(phoneNumber);
                if (memberList.size()>0){
                    WoScYiMember member = memberList.get(0);
                    Long societyId = member.getSocietyId();//根据青创社ID查询青创社当前状态，如果是停用状态，不允许青创社成员操作
                    String state = tbpCheckRoleInfoMapper.getYouthInnovateInfoBySocietyId(societyId);//启用标识：0 失效、1 生效
                    if (state.equals("1")){
                        dataInfo.setMemberId(Long.toString(member.getMemberId()));
                        String memberRoleType = member.getRoleType();
                        name = member.getMemberName();
                        orgCode = member.getOrgCode();
                        if (!memberRoleType.isEmpty() && memberRoleType.equals("1")){//主席
                            roleType = AppletRoleType.ROLE_TYPE_PRESIDENT;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("青创社院系长").append(",");
                            log.info("登录用户{},所属角色{}为青创社主席",phoneNumber,roleType);
                        }else if(!memberRoleType.isEmpty() && memberRoleType.equals("2")){//部长
                            roleType = AppletRoleType.ROLE_TYPE_MINISTER;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("青创社团长").append(",");
                            log.info("登录用户{},所属角色{}为青创社部长",phoneNumber,roleType);
                        }else if(!memberRoleType.isEmpty() && memberRoleType.equals("3")){//骨干
                            roleType = AppletRoleType.ROLE_TYPE_BACKBONE;
                            addMenuList(dataInfo,roleType);
                            log.info("登录用户{},所属角色{}为青创社团员",phoneNumber,roleType);
                        }
                    }
                }

                if(partnerList.size() == 0 && memberList.size() == 0){ //判断是不是省管理员
                    List<UserRoleInfo> userRoleInfos = tbpCheckRoleInfoMapper.findUserRoleInfos(phoneNumber);
                    if (userRoleInfos.size()>0){
                        UserRoleInfo userRoleInfo = userRoleInfos.get(0);
                        String areaLevel = userRoleInfo.getAreaLevel();
                        if (!areaLevel.isEmpty() && areaLevel.equals("1")){
                            roleType = AppletRoleType.ROLE_TYPE_PROVINCE;
                            name = userRoleInfo.getStaffName();
                            orgCode = userRoleInfo.getAreaCode();
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("省份管理员").append(",");
                            log.info("登录用户{},所属角色{}为省份管理员",phoneNumber,roleType);
                        }else if(!areaLevel.isEmpty() && areaLevel.equals("2")){
                            roleType = AppletRoleType.ROLE_TYPE_CITY;
                            name = userRoleInfo.getStaffName();
                            orgCode = userRoleInfo.getAreaCode();
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("地市管理员").append(",");
                            log.info("登录用户{},所属角色{}为地市管理员",phoneNumber,roleType);
                        }else{
                            roleType = AppletRoleType.ROLE_TYPE_GUEST;
                            addMenuList(dataInfo,roleType);
                            roleTypeName.append("访客").append(",");
                            log.info("登录用户{},所属角色{}为访客",phoneNumber,roleType);
                        }
                    }else{
                        roleType = AppletRoleType.ROLE_TYPE_GUEST;
                        addMenuList(dataInfo,roleType);
                        roleTypeName.append("访客").append(",");
                        log.info("登录用户{},所属角色{}为访客",phoneNumber,roleType);
                    }
                }
            }
            //返回待办任务、已办任务数量
            int pendingProcessCount = 0;
            int processedProcessCount = 0;
            if (roleType != null && !roleType.isEmpty() && !roleType.equals(AppletRoleType.ROLE_TYPE_GUEST)){
                try {
                    pendingProcessCount = tbpCheckRoleInfoMapper.getPendingProcessCount(phoneNumber);//待办
                    processedProcessCount = tbpCheckRoleInfoMapper.getProcessedProcessCount(phoneNumber);//已办
                }catch (Exception e){
                    log.error("查询待办任务、已办任务失败：{}",e.getMessage());
                }
            }
            dataInfo.setPendingProcessCount(pendingProcessCount);
            dataInfo.setProcessedProcessCount(processedProcessCount);

            //是否限制
            dataInfo.setRoleType(roleType);
            dataInfo.setName(name);

            //小程序端地市权限开关 true 限制地市访问，false 对所有地市开放
//            String areaControlFlag =
//            ArrayList<Dict> list = dictApiService.getDictsByKind("areaControlFlag");
//            String areaControlFlag = "";
//            if (list.size() > 0){
//                Dict dict = list.get(0);
//                areaControlFlag = dict.getCode();
//                if (!areaControlFlag.isEmpty() && areaControlFlag.equals("1")){
//                    ArrayList<Dict> list1 = dictApiService.getDictsByKind("areaControlInfo");
//                    if (list1.size() > 0){
//                        String areaControlInfo = list1.get(0).getCode();
//                        String[] split = areaControlInfo.split(",");
//                        if (!roleType.equals(AppletRoleType.ROLE_TYPE_GUEST) && !Arrays.asList(split).contains(orgCode)){
//                            dataInfo.setMenuList(new ArrayList<>());
//                        }
//                    }
//                }
//            }
            dataInfo.setOrgCode(orgCode);
            dataInfo.setPhoneNumber(SM4Utils.encryptEcb(shakey,phoneNumber));
            dataInfo.setPurePhoneNumber(SM4Utils.encryptEcb(shakey,phoneNumber));
            String jwt = JwtUtil.createJWT(UUID.randomUUID().toString(), JSON.toJSONString(dataInfo), null);
            String redisKey = "bcp.applet_login_" + phoneNumber;
            redisTemplate.opsForValue().set(redisKey, jwt, Duration.ofHours(24*7));//存到redis 2小时
            dataInfo.setToken(jwt);

            //记录登录日志
            UserLoginAppletLog loginAppletLog = new UserLoginAppletLog();
            loginAppletLog.setAcctNo(phoneNumber);
            loginAppletLog.setRoleType(roleType);
            loginAppletLog.setRoleTypeName(roleTypeName.deleteCharAt(roleTypeName.length() - 1).toString());
            loginAppletLog.setLoginTime(new Date());
            userLoginAppletLogMapper.insert(loginAppletLog);

            //返回内容加密

            if (dataInfo!=null){
                resultMsg = SM4Utils.encryptEcb(keyLogin, JSON.toJSONString(dataInfo));
            }
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),e.getMessage());
        }
        return ResultUtil.success(resultMsg);
    }

    @Override
    public CustomResult getInnovateRoleInfo(String roleType) {
        log.info("进入登录青创社成员角色判断方法，角色编码：{}",roleType);
        WeChatRoleInfo dataInfo = new WeChatRoleInfo();
        try {
            addClildMenuList(dataInfo,roleType);
        }catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),e.getMessage());
        }
        return ResultUtil.success(dataInfo);
    }


    private void addMenuList(WeChatRoleInfo dataInfo, String code) {
        String menuCodes=tbpCheckRoleInfoMapper.selectDictByRole(code,"menuRole");
        String[] split = menuCodes.split(",");
        List<String> list = Arrays.asList(split);
        List<String> menus=tbpCheckRoleInfoMapper.selectMenuList(list);
        List<Map> menuList=new ArrayList<>();
        if (dataInfo.getMenuList() != null){
            menuList = dataInfo.getMenuList();
        }
        for (String menu : menus) {
            JSONObject jsonObject = JSON.parseObject(menu);
            menuList.add(jsonObject);
        }
        dataInfo.setMenuList(menuList);
    }

    private void addClildMenuList(WeChatRoleInfo dataInfo, String code) {
        String menuCodes=tbpCheckRoleInfoMapper.selectDictByRole(code,"childMenuRole");
        String[] split = menuCodes.split(",");
        List<String> list = Arrays.asList(split);
        List<String> menus=tbpCheckRoleInfoMapper.selectChildMenuList(list);
        List<Map> menuList=new ArrayList<>();
        for (String menu : menus) {
            JSONObject jsonObject = JSON.parseObject(menu);
            menuList.add(jsonObject);
        }
        dataInfo.setMenuList(menuList);
    }

    @Override
    public List<Map> getRoleInfoTest(String code){
        List<Map> menuList=new ArrayList<>();
        String menuCodes=tbpCheckRoleInfoMapper.selectDictByRole(code,"menuRole");
        String[] split = menuCodes.split(",");
        List<String> list = Arrays.asList(split);
        List<String> menus=tbpCheckRoleInfoMapper.selectMenuList(list);
        for (String menu : menus) {
            JSONObject jsonObject = JSON.parseObject(menu);
            menuList.add(jsonObject);
        }
        return menuList;
    }

    public static void main(String[] args) {
        String menu="{ name: '激活', key: 'Activation', path: '../../pages/h5/index', redirectPath: 'https://zyxt.newbuy.chinaunicom.cn/spa-h5/campusAssistant/#/school/homePage', img: '../../image/nine-grids/xiaochengxu.svg' }";
        JSONObject jsonObject = JSON.parseObject(menu);

    }
}
