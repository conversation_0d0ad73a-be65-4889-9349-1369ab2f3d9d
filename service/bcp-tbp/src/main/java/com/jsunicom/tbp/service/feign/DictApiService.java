package com.jsunicom.tbp.service.feign;

import com.jsunicom.tbp.po.Dict;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.tbp.api
 * @ClassName: DictApiService
 * @Author: z<PERSON><PERSON>
 * @CreateTime: 2023-05-30  10:30
 * @Description: TODO
 * @Version: 1.0
 */
@Component
@FeignClient(value = "bcp-api")
public interface DictApiService {
    @GetMapping("/dict/getDictsByKind")
    public ArrayList<Dict> getDictsByKind(@RequestParam(value = "kind") String kind);
}
