package com.jsunicom.tbp.service;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.tbp.common.Result;
import com.jsunicom.tbp.po.WeChatRoleInfo;
import com.jsunicom.tbp.result.CustomResult;

import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.tbp.service.impl
 * @ClassName: WxService
 * @Author: zhaowang
 * @CreateTime: 2023-04-23  18:51
 * @Description: TODO
 * @Version: 1.0
 */
public interface SchoolRoleService {
    CustomResult getRoleInfo(String ecbPhoneNumber,String shakey);

    CustomResult getRoleInfoNew(String ecbPhoneNumber, String shakey);

    CustomResult getInnovateRoleInfo(String roleType);

    List<Map> getRoleInfoTest(String s);
}
