package com.jsunicom.tbp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.tbp.common.Result;
import com.jsunicom.tbp.config.LocalDateTimeSerializerConfig;
import com.jsunicom.tbp.entity.TbpLoginTime;
import com.jsunicom.tbp.entity.TbpWeixinInfo;
import com.jsunicom.tbp.entity.TbpWeixinInfoExample;
import com.jsunicom.tbp.mapper.TbpLoginTimeMapper;
import com.jsunicom.tbp.mapper.TbpWeixinInfoMapper;
import com.jsunicom.tbp.service.WxService;
import com.jsunicom.tbp.utils.WeChatUtil;
import com.jsunicom.tbp.utils.jwt.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class WxServiceImpl implements WxService {
    LocalDateTimeSerializerConfig config = new LocalDateTimeSerializerConfig();

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    WeChatUtil weChatUtil;

    @Autowired
    private TbpLoginTimeMapper loginTimeMapper;
    @Autowired
    private TbpWeixinInfoMapper weixinInfoMapper;


    String redisKey = "";
    Date date = new Date();
    JwtUtil jwtUtil = new JwtUtil();

    @Override
    public Result<Object> getLoginResultInfo(String jsonData, JSONObject jsonObject, String encryptedData, String iv) throws Exception{
        Result<Object> result = new Result();

        String md5key = DigestUtils.md5Hex(jsonData);
        //校验通过 规则:WX_LOGIN_(MD5值)
        redisKey = "WX_LOGIN" + md5key;
        redisTemplate.opsForValue().set(redisKey, jsonData, Duration.ofHours(1));//存到redis 1小时
        //每个用户有唯一的reidskey所以在每一次进行登录判断时，将rediskey发送出去
        String openid = jsonObject.getString("openid");
        String sessionKey = jsonObject.getString("session_key");

        TbpWeixinInfoExample weixinInfoExample = new TbpWeixinInfoExample();
        TbpWeixinInfoExample.Criteria weixinCriteria = weixinInfoExample.createCriteria();
        weixinCriteria.andOpenIdEqualTo(openid);
        List<TbpWeixinInfo> weixinInfos = weixinInfoMapper.selectByExample(weixinInfoExample);


        TbpWeixinInfo weixin = new TbpWeixinInfo();
        String s = weChatUtil.wxDecrypt(encryptedData, jsonObject, iv);
        JSONObject jsonObject1 = JSON.parseObject(s);
        Object watermark = jsonObject1.get("watermark");
        JSONObject jsonObject2 = JSON.parseObject(String.valueOf(watermark));
        if (weixinInfos.size() == 0) {
            weixin.setOpenId(openid);
            weixin.setSessionkey(sessionKey);
            jsonObject2.get("timestamp");
            jsonObject2.get("appid");
            weixin.setNickName(String.valueOf(jsonObject1.get("nickName")));
            weixin.setAvatarUrl(String.valueOf(jsonObject1.get("avatarUrl")));
            String timestamp = String.valueOf(jsonObject2.get("timestamp"));
            Date localDateTime = config.localDateTime(timestamp);
            weixin.setCreateTime(localDateTime);
            weixin.setAppId(String.valueOf(jsonObject2.get("appid")));
            weixinInfoMapper.insert(weixin);
            String token = JwtUtil.createJWT(UUID.randomUUID().toString(), redisKey, null);
            result.setData(token);
            result.setMessage(weixin.getAvatarUrl());
            result.setData2(weixin.getNickName());
            TbpLoginTime logintime = new TbpLoginTime();
            logintime.setLogintime(date);
            logintime.setOpenid(openid);
            loginTimeMapper.insert(logintime);
            return result;
        } else {
            TbpWeixinInfo weixinInfo = weixinInfos.get(0);
            String token = JwtUtil.createJWT(UUID.randomUUID().toString(), redisKey, null);
            result.setData(token);
            result.setData2(weixinInfo.getNickName());
            result.setMessage(weixinInfo.getAvatarUrl());
            return result;
        }
    }
}
