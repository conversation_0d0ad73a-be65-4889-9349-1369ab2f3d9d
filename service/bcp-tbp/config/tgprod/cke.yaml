apiVersion: apps/v1
kind: Deployment
metadata:
  name: bcp-tbp
  namespace: sxxyprod
  labels:
    app: bcp-tbp
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: bcp-tbp
  template:
    metadata:
      labels:
        app: bcp-tbp
    spec:
      containers:
      - name: bcp-tbp
        image: harbor.dcos.xixian.unicom.local/sxgzzt-xy/bcp-tbp:latest
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        ports:
        - containerPort: 8199
          name: bcp-tbp
          protocol: TCP
