package com.test;

import com.alibaba.fastjson.JSONArray;
import com.jsunicom.securitycas.SecurityCasApplication;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.concurrent.TimeUnit;


/**
 * 测试类
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-05 10:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SecurityCasApplication.class)
public class Test {

    @Autowired
    private RedisTemplate redisTemplate;

    @org.junit.Test
    public void test(){
//        redisTemplate.opsForValue().set("123", "456",600, TimeUnit.SECONDS);
//        redisTemplate.opsForValue().set("abc", "efg",60, TimeUnit.SECONDS);
    }

}
