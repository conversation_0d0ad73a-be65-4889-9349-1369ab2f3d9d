package com.jsunicom.securitycas.util;

import java.util.Calendar;

/**
 * time工具类
 * <AUTHOR>
 * @version 1.0
 * @date 2021-02-05 10:18
 */
public class TimeUtil {

    /**
     * 获取特定时间的 毫秒数
     * @param timeString 12:20:00
     */
    public static Long getMiol(String timeString) {
        Calendar ca = Calendar.getInstance();
        //失效的时间
        try {
            ca.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeString.split(":")[0]));
            ca.set(Calendar.MINUTE, Integer.parseInt(timeString.split(":")[1]));
            ca.set(Calendar.SECOND, Integer.parseInt(timeString.split(":")[2]));
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        long fl = ca.getTimeInMillis();
        return fl;
    }


}
