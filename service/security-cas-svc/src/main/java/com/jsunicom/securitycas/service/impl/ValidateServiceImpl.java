package com.jsunicom.securitycas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.securitycas.config.CasServerProperties;
import com.jsunicom.securitycas.eneity.Result;
import com.jsunicom.securitycas.eneity.req.PermInfosReq;
import com.jsunicom.securitycas.eneity.req.UrlVisitValidateReq;
import com.jsunicom.securitycas.eneity.rsp.PermInfosRsp;
import com.jsunicom.securitycas.eneity.rsp.UrlVisitValidateRsp;
import com.jsunicom.securitycas.service.IUserService;
import com.jsunicom.securitycas.service.IValidateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: ValidateServiceImpl
 * @ProjectName jspop_backend
 * @Description: TODO
 * @date 2021/2/315:10
 */
@Service
public class ValidateServiceImpl implements IValidateService {

    @Autowired
    private CasServerProperties casServerProperties;
    @Autowired
    private IUserService userService;

    private static Logger logger = LoggerFactory.getLogger(ValidateServiceImpl.class);

    @Override
    public Result getLoginUserId(HttpSession session) {
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(session.getAttribute("SPRING_SECURITY_CONTEXT"));
        if(jsonObject == null){
            return Result.fail("9999","获取SECURITY上下文失败");
        }
        JSONObject authentication = (JSONObject) jsonObject.get("authentication");
        if(authentication == null){
            return Result.fail("9999","获取authentication失败");
        }
        JSONObject user = authentication.getJSONObject("principal");
        if(user == null){
            return Result.fail("9999","获取用户信息失败");
        }
        String credentials = authentication.getString("credentials");
        String userName = user.getString("username");
        JSONObject data = new JSONObject();
        data.put("credentials",credentials);
        data.put("userName",userName);
        return Result.success(data);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Result<UrlVisitValidateRsp> urlVisitValidate(HttpSession session, UrlVisitValidateReq urlVisitValidateReq) {
        logger.info("进入URL验证接口session :{}",JSONObject.toJSONString(session));
        logger.info("进入URL验证接口urlVisitValidate :{}",JSONObject.toJSONString(urlVisitValidateReq));
        if(session == null){
            return Result.fail("9999","session不能为空");
        }
        if(urlVisitValidateReq == null){
            return Result.fail("9999","请求参数不能为空");
        }
        String targetUrl = urlVisitValidateReq.getTargetUrl();
        if("".equals(targetUrl)){
            return Result.fail("9999","目标URL不能为空");
        }
        String casServerLoginUrl = casServerProperties.getCasServerLoginUrl();
        UrlVisitValidateRsp urlVisitValidateRsp = new UrlVisitValidateRsp();
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(session.getAttribute("SPRING_SECURITY_CONTEXT"));

        logger.info("SPRING_SECURITY_CONTEXT :{}",JSONObject.toJSONString(jsonObject));

        urlVisitValidateRsp.setReturnType("1");
        urlVisitValidateRsp.setRedirectUrl(casServerLoginUrl+"?service="+targetUrl);

        if(jsonObject == null){
            return Result.fail("9999","用户信息已失效请重新登录",urlVisitValidateRsp);
        }
        JSONObject authentication = (JSONObject) jsonObject.get("authentication");
        if(authentication == null){
            return Result.fail("9999","用户信息已失效请重新登录",urlVisitValidateRsp);
        }
        JSONObject user = authentication.getJSONObject("principal");
        if(user == null){
            return Result.fail("9999","用户信息已失效请重新登录",urlVisitValidateRsp);
        }
        //判断是否当前用户有目标菜单的权限
        //总部username格式为：省份编码_员工工号 如34_Z0000TEST
        String userName = user.getString("username");
        String staffId = "";
        if(userName.indexOf("_") >= 0){
//            staffId = userName.split("_")[1];
            staffId = userName.replace("84_","");
        }else{
            staffId = userName;
        }

        PermInfosReq permInfosReq = new PermInfosReq();
        permInfosReq.setStaffId(staffId);
        Result<List<PermInfosRsp>> permInfosResult = userService.qryPermInfos(permInfosReq);
        //是否有权限
        boolean authenFlag = false;
        if(permInfosResult.isSuccess()){
            List<PermInfosRsp> permInfosRsps = permInfosResult.getData();
            for(PermInfosRsp permInfosRsp:permInfosRsps){
//                String fullUrlPath = permInfosRsp.getDomainName().replace("http://","");
//                fullUrlPath += ("/"+filterFirstSeparator(permInfosRsp.getSysPath()));
//                fullUrlPath += ("/"+filterFirstSeparator(permInfosRsp.getMenuUrl()));
                String fullUrlPath = permInfosRsp.getMenuUrl();
                if(fullUrlPath.equals(targetUrl)){
                    authenFlag = true;
                    break;
                }
            }
            logger.info("targetUrl :{}，authenFlag{}",targetUrl,authenFlag);
            //有权限无影响
            if(authenFlag){
                urlVisitValidateRsp.setReturnType("0");
                urlVisitValidateRsp.setRedirectUrl("");
                return Result.success(urlVisitValidateRsp);
            }else{
               // 无权限判断是否是页面请求，如果是页面请求则返回状态3，返回状态3是，无权限页面无法通过白名单校验
                    urlVisitValidateRsp.setReturnType("2");
                urlVisitValidateRsp.setRedirectUrl("");
                return Result.fail("9999","无目标地址权限",urlVisitValidateRsp);
            }
        }else{
            return Result.fail("9999",permInfosResult.getResultMsg());
        }
    }

    /**
     * 过滤url第一个/
     * @param targetStr
     * @return
     */
    public String filterFirstSeparator(String targetStr){
        String separator = "/";
        if(targetStr.length() < 1){
            return "";
        }else{
            String firstChar = targetStr.substring(0,1);
            if(separator.equals(firstChar)){
                return targetStr.substring(1);
            }else{
                return targetStr;
            }
        }
    }
}
