package com.jsunicom.securitycas.filter;

import org.jasig.cas.client.util.AbstractConfigurationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Title: TestFilter
 * @ProjectName jspop_backend
 * @Description: TODO
 * @date 2021/3/2614:42
 */
public class TestFilter extends AbstractConfigurationFilter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        System.out.println("TestFilter------");
    }

    @Override
    public void destroy() {
        System.out.println("destroy------");
    }
}
