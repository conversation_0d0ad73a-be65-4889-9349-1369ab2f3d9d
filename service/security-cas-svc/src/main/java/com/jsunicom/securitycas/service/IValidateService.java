package com.jsunicom.securitycas.service;

import com.jsunicom.securitycas.eneity.Result;
import com.jsunicom.securitycas.eneity.req.UrlVisitValidateReq;
import com.jsunicom.securitycas.eneity.rsp.UrlVisitValidateRsp;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpSession;
/**
 * <AUTHOR>
 * @Title: IValidateService
 * @ProjectName jspop_backend
 * @Description: TODO
 * @date 2021/2/315:10
 */
public interface IValidateService {

    Result getLoginUserId(HttpSession session);

    Result<UrlVisitValidateRsp> urlVisitValidate(HttpSession session, @RequestBody UrlVisitValidateReq urlVisitValidateReq);
}
