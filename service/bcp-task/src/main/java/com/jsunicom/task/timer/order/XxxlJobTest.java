package com.jsunicom.task.timer.order;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class XxxlJobTest {

    @XxlJob("testXxlJobTask")
    public ReturnT<String> testXxlJobTask(String param) {
        log.info("xxlTestTask excutor is starting !!!");
        return ReturnT.SUCCESS;
    }

}
