package com.jsunicom.task.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WoScYouthInnovateBaseExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYouthInnovateBaseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSocietyIdIsNull() {
            addCriterion("SOCIETY_ID is null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIsNotNull() {
            addCriterion("SOCIETY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdEqualTo(Long value) {
            addCriterion("SOCIETY_ID =", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotEqualTo(Long value) {
            addCriterion("SOCIETY_ID <>", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThan(Long value) {
            addCriterion("SOCIETY_ID >", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SOCIETY_ID >=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThan(Long value) {
            addCriterion("SOCIETY_ID <", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThanOrEqualTo(Long value) {
            addCriterion("SOCIETY_ID <=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIn(List<Long> values) {
            addCriterion("SOCIETY_ID in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotIn(List<Long> values) {
            addCriterion("SOCIETY_ID not in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdBetween(Long value1, Long value2) {
            addCriterion("SOCIETY_ID between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotBetween(Long value1, Long value2) {
            addCriterion("SOCIETY_ID not between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyNameIsNull() {
            addCriterion("SOCIETY_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSocietyNameIsNotNull() {
            addCriterion("SOCIETY_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyNameEqualTo(String value) {
            addCriterion("SOCIETY_NAME =", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameNotEqualTo(String value) {
            addCriterion("SOCIETY_NAME <>", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameGreaterThan(String value) {
            addCriterion("SOCIETY_NAME >", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameGreaterThanOrEqualTo(String value) {
            addCriterion("SOCIETY_NAME >=", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameLessThan(String value) {
            addCriterion("SOCIETY_NAME <", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameLessThanOrEqualTo(String value) {
            addCriterion("SOCIETY_NAME <=", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameLike(String value) {
            addCriterion("SOCIETY_NAME like", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameNotLike(String value) {
            addCriterion("SOCIETY_NAME not like", value, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameIn(List<String> values) {
            addCriterion("SOCIETY_NAME in", values, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameNotIn(List<String> values) {
            addCriterion("SOCIETY_NAME not in", values, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameBetween(String value1, String value2) {
            addCriterion("SOCIETY_NAME between", value1, value2, "societyName");
            return (Criteria) this;
        }

        public Criteria andSocietyNameNotBetween(String value1, String value2) {
            addCriterion("SOCIETY_NAME not between", value1, value2, "societyName");
            return (Criteria) this;
        }

        public Criteria andCampusIdIsNull() {
            addCriterion("CAMPUS_ID is null");
            return (Criteria) this;
        }

        public Criteria andCampusIdIsNotNull() {
            addCriterion("CAMPUS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCampusIdEqualTo(Long value) {
            addCriterion("CAMPUS_ID =", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotEqualTo(Long value) {
            addCriterion("CAMPUS_ID <>", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThan(Long value) {
            addCriterion("CAMPUS_ID >", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID >=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThan(Long value) {
            addCriterion("CAMPUS_ID <", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID <=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdIn(List<Long> values) {
            addCriterion("CAMPUS_ID in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotIn(List<Long> values) {
            addCriterion("CAMPUS_ID not in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID not between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNull() {
            addCriterion("ORG_CODE is null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNotNull() {
            addCriterion("ORG_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeEqualTo(String value) {
            addCriterion("ORG_CODE =", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotEqualTo(String value) {
            addCriterion("ORG_CODE <>", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThan(String value) {
            addCriterion("ORG_CODE >", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_CODE >=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThan(String value) {
            addCriterion("ORG_CODE <", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThanOrEqualTo(String value) {
            addCriterion("ORG_CODE <=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLike(String value) {
            addCriterion("ORG_CODE like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotLike(String value) {
            addCriterion("ORG_CODE not like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIn(List<String> values) {
            addCriterion("ORG_CODE in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotIn(List<String> values) {
            addCriterion("ORG_CODE not in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeBetween(String value1, String value2) {
            addCriterion("ORG_CODE between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotBetween(String value1, String value2) {
            addCriterion("ORG_CODE not between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagIsNull() {
            addCriterion("OFFICIAL_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagIsNotNull() {
            addCriterion("OFFICIAL_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagEqualTo(String value) {
            addCriterion("OFFICIAL_FLAG =", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagNotEqualTo(String value) {
            addCriterion("OFFICIAL_FLAG <>", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagGreaterThan(String value) {
            addCriterion("OFFICIAL_FLAG >", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagGreaterThanOrEqualTo(String value) {
            addCriterion("OFFICIAL_FLAG >=", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagLessThan(String value) {
            addCriterion("OFFICIAL_FLAG <", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagLessThanOrEqualTo(String value) {
            addCriterion("OFFICIAL_FLAG <=", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagLike(String value) {
            addCriterion("OFFICIAL_FLAG like", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagNotLike(String value) {
            addCriterion("OFFICIAL_FLAG not like", value, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagIn(List<String> values) {
            addCriterion("OFFICIAL_FLAG in", values, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagNotIn(List<String> values) {
            addCriterion("OFFICIAL_FLAG not in", values, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagBetween(String value1, String value2) {
            addCriterion("OFFICIAL_FLAG between", value1, value2, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialFlagNotBetween(String value1, String value2) {
            addCriterion("OFFICIAL_FLAG not between", value1, value2, "officialFlag");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlIsNull() {
            addCriterion("OFFICIAL_VOUCHER_URL is null");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlIsNotNull() {
            addCriterion("OFFICIAL_VOUCHER_URL is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlEqualTo(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL =", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlNotEqualTo(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL <>", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlGreaterThan(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL >", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlGreaterThanOrEqualTo(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL >=", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlLessThan(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL <", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlLessThanOrEqualTo(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL <=", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlLike(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL like", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlNotLike(String value) {
            addCriterion("OFFICIAL_VOUCHER_URL not like", value, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlIn(List<String> values) {
            addCriterion("OFFICIAL_VOUCHER_URL in", values, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlNotIn(List<String> values) {
            addCriterion("OFFICIAL_VOUCHER_URL not in", values, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlBetween(String value1, String value2) {
            addCriterion("OFFICIAL_VOUCHER_URL between", value1, value2, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andOfficialVoucherUrlNotBetween(String value1, String value2) {
            addCriterion("OFFICIAL_VOUCHER_URL not between", value1, value2, "officialVoucherUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlIsNull() {
            addCriterion("SOCIETY_LOGO_URL is null");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlIsNotNull() {
            addCriterion("SOCIETY_LOGO_URL is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlEqualTo(String value) {
            addCriterion("SOCIETY_LOGO_URL =", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlNotEqualTo(String value) {
            addCriterion("SOCIETY_LOGO_URL <>", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlGreaterThan(String value) {
            addCriterion("SOCIETY_LOGO_URL >", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("SOCIETY_LOGO_URL >=", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlLessThan(String value) {
            addCriterion("SOCIETY_LOGO_URL <", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlLessThanOrEqualTo(String value) {
            addCriterion("SOCIETY_LOGO_URL <=", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlLike(String value) {
            addCriterion("SOCIETY_LOGO_URL like", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlNotLike(String value) {
            addCriterion("SOCIETY_LOGO_URL not like", value, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlIn(List<String> values) {
            addCriterion("SOCIETY_LOGO_URL in", values, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlNotIn(List<String> values) {
            addCriterion("SOCIETY_LOGO_URL not in", values, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlBetween(String value1, String value2) {
            addCriterion("SOCIETY_LOGO_URL between", value1, value2, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyLogoUrlNotBetween(String value1, String value2) {
            addCriterion("SOCIETY_LOGO_URL not between", value1, value2, "societyLogoUrl");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionIsNull() {
            addCriterion("SOCIETY_INTRODUCTION is null");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionIsNotNull() {
            addCriterion("SOCIETY_INTRODUCTION is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionEqualTo(String value) {
            addCriterion("SOCIETY_INTRODUCTION =", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionNotEqualTo(String value) {
            addCriterion("SOCIETY_INTRODUCTION <>", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionGreaterThan(String value) {
            addCriterion("SOCIETY_INTRODUCTION >", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionGreaterThanOrEqualTo(String value) {
            addCriterion("SOCIETY_INTRODUCTION >=", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionLessThan(String value) {
            addCriterion("SOCIETY_INTRODUCTION <", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionLessThanOrEqualTo(String value) {
            addCriterion("SOCIETY_INTRODUCTION <=", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionLike(String value) {
            addCriterion("SOCIETY_INTRODUCTION like", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionNotLike(String value) {
            addCriterion("SOCIETY_INTRODUCTION not like", value, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionIn(List<String> values) {
            addCriterion("SOCIETY_INTRODUCTION in", values, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionNotIn(List<String> values) {
            addCriterion("SOCIETY_INTRODUCTION not in", values, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionBetween(String value1, String value2) {
            addCriterion("SOCIETY_INTRODUCTION between", value1, value2, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyIntroductionNotBetween(String value1, String value2) {
            addCriterion("SOCIETY_INTRODUCTION not between", value1, value2, "societyIntroduction");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkIsNull() {
            addCriterion("SOCIETY_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkIsNotNull() {
            addCriterion("SOCIETY_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkEqualTo(String value) {
            addCriterion("SOCIETY_REMARK =", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkNotEqualTo(String value) {
            addCriterion("SOCIETY_REMARK <>", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkGreaterThan(String value) {
            addCriterion("SOCIETY_REMARK >", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("SOCIETY_REMARK >=", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkLessThan(String value) {
            addCriterion("SOCIETY_REMARK <", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkLessThanOrEqualTo(String value) {
            addCriterion("SOCIETY_REMARK <=", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkLike(String value) {
            addCriterion("SOCIETY_REMARK like", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkNotLike(String value) {
            addCriterion("SOCIETY_REMARK not like", value, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkIn(List<String> values) {
            addCriterion("SOCIETY_REMARK in", values, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkNotIn(List<String> values) {
            addCriterion("SOCIETY_REMARK not in", values, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkBetween(String value1, String value2) {
            addCriterion("SOCIETY_REMARK between", value1, value2, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andSocietyRemarkNotBetween(String value1, String value2) {
            addCriterion("SOCIETY_REMARK not between", value1, value2, "societyRemark");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("STATE is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("STATE is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("STATE =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("STATE <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("STATE >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("STATE >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("STATE <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("STATE <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("STATE like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("STATE not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("STATE in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("STATE not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("STATE between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("STATE not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("CREATED_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("CREATED_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Date value) {
            addCriterion("CREATED_TIME =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Date value) {
            addCriterion("CREATED_TIME <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Date value) {
            addCriterion("CREATED_TIME >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_TIME >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Date value) {
            addCriterion("CREATED_TIME <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_TIME <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Date> values) {
            addCriterion("CREATED_TIME in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Date> values) {
            addCriterion("CREATED_TIME not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Date value1, Date value2) {
            addCriterion("CREATED_TIME between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_TIME not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeIsNull() {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME is null");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeIsNotNull() {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME =", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeNotEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME <>", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeGreaterThan(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME >", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME >=", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeLessThan(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME <", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME <=", value, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeIn(List<Date> values) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME in", values, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeNotIn(List<Date> values) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME not in", values, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeBetween(Date value1, Date value2) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME between", value1, value2, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("OFFICIAL_EFFECTIVE_START_TIME not between", value1, value2, "officialEffectiveStartTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeIsNull() {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME is null");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeIsNotNull() {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME =", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeNotEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME <>", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeGreaterThan(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME >", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME >=", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeLessThan(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME <", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME <=", value, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeIn(List<Date> values) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME in", values, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeNotIn(List<Date> values) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME not in", values, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeBetween(Date value1, Date value2) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME between", value1, value2, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andOfficialEffectiveEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("OFFICIAL_EFFECTIVE_END_TIME not between", value1, value2, "officialEffectiveEndTime");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIsNull() {
            addCriterion("APPROVAL_STATE is null");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIsNotNull() {
            addCriterion("APPROVAL_STATE is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalStateEqualTo(String value) {
            addCriterion("APPROVAL_STATE =", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotEqualTo(String value) {
            addCriterion("APPROVAL_STATE <>", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateGreaterThan(String value) {
            addCriterion("APPROVAL_STATE >", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateGreaterThanOrEqualTo(String value) {
            addCriterion("APPROVAL_STATE >=", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLessThan(String value) {
            addCriterion("APPROVAL_STATE <", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLessThanOrEqualTo(String value) {
            addCriterion("APPROVAL_STATE <=", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLike(String value) {
            addCriterion("APPROVAL_STATE like", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotLike(String value) {
            addCriterion("APPROVAL_STATE not like", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIn(List<String> values) {
            addCriterion("APPROVAL_STATE in", values, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotIn(List<String> values) {
            addCriterion("APPROVAL_STATE not in", values, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateBetween(String value1, String value2) {
            addCriterion("APPROVAL_STATE between", value1, value2, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotBetween(String value1, String value2) {
            addCriterion("APPROVAL_STATE not between", value1, value2, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIsNull() {
            addCriterion("APPROVAL_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIsNotNull() {
            addCriterion("APPROVAL_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkEqualTo(String value) {
            addCriterion("APPROVAL_REMARK =", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotEqualTo(String value) {
            addCriterion("APPROVAL_REMARK <>", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkGreaterThan(String value) {
            addCriterion("APPROVAL_REMARK >", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("APPROVAL_REMARK >=", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLessThan(String value) {
            addCriterion("APPROVAL_REMARK <", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLessThanOrEqualTo(String value) {
            addCriterion("APPROVAL_REMARK <=", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLike(String value) {
            addCriterion("APPROVAL_REMARK like", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotLike(String value) {
            addCriterion("APPROVAL_REMARK not like", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIn(List<String> values) {
            addCriterion("APPROVAL_REMARK in", values, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotIn(List<String> values) {
            addCriterion("APPROVAL_REMARK not in", values, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkBetween(String value1, String value2) {
            addCriterion("APPROVAL_REMARK between", value1, value2, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotBetween(String value1, String value2) {
            addCriterion("APPROVAL_REMARK not between", value1, value2, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("UPDATED_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("UPDATED_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Date value) {
            addCriterion("UPDATED_TIME =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Date value) {
            addCriterion("UPDATED_TIME <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Date value) {
            addCriterion("UPDATED_TIME >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_TIME >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Date value) {
            addCriterion("UPDATED_TIME <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_TIME <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Date> values) {
            addCriterion("UPDATED_TIME in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Date> values) {
            addCriterion("UPDATED_TIME not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATED_TIME between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_TIME not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNull() {
            addCriterion("RESERVE1 is null");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNotNull() {
            addCriterion("RESERVE1 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve1EqualTo(String value) {
            addCriterion("RESERVE1 =", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotEqualTo(String value) {
            addCriterion("RESERVE1 <>", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThan(String value) {
            addCriterion("RESERVE1 >", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE1 >=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThan(String value) {
            addCriterion("RESERVE1 <", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThanOrEqualTo(String value) {
            addCriterion("RESERVE1 <=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Like(String value) {
            addCriterion("RESERVE1 like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotLike(String value) {
            addCriterion("RESERVE1 not like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1In(List<String> values) {
            addCriterion("RESERVE1 in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotIn(List<String> values) {
            addCriterion("RESERVE1 not in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Between(String value1, String value2) {
            addCriterion("RESERVE1 between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotBetween(String value1, String value2) {
            addCriterion("RESERVE1 not between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNull() {
            addCriterion("RESERVE2 is null");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNotNull() {
            addCriterion("RESERVE2 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve2EqualTo(String value) {
            addCriterion("RESERVE2 =", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotEqualTo(String value) {
            addCriterion("RESERVE2 <>", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThan(String value) {
            addCriterion("RESERVE2 >", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE2 >=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThan(String value) {
            addCriterion("RESERVE2 <", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThanOrEqualTo(String value) {
            addCriterion("RESERVE2 <=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Like(String value) {
            addCriterion("RESERVE2 like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotLike(String value) {
            addCriterion("RESERVE2 not like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2In(List<String> values) {
            addCriterion("RESERVE2 in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotIn(List<String> values) {
            addCriterion("RESERVE2 not in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Between(String value1, String value2) {
            addCriterion("RESERVE2 between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotBetween(String value1, String value2) {
            addCriterion("RESERVE2 not between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNull() {
            addCriterion("RESERVE3 is null");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNotNull() {
            addCriterion("RESERVE3 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve3EqualTo(String value) {
            addCriterion("RESERVE3 =", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotEqualTo(String value) {
            addCriterion("RESERVE3 <>", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThan(String value) {
            addCriterion("RESERVE3 >", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE3 >=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThan(String value) {
            addCriterion("RESERVE3 <", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThanOrEqualTo(String value) {
            addCriterion("RESERVE3 <=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Like(String value) {
            addCriterion("RESERVE3 like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotLike(String value) {
            addCriterion("RESERVE3 not like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3In(List<String> values) {
            addCriterion("RESERVE3 in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotIn(List<String> values) {
            addCriterion("RESERVE3 not in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Between(String value1, String value2) {
            addCriterion("RESERVE3 between", value1, value2, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotBetween(String value1, String value2) {
            addCriterion("RESERVE3 not between", value1, value2, "reserve3");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
