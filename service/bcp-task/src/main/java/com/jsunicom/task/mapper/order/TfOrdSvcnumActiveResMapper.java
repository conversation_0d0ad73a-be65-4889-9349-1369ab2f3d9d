package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tf.TfOrdSvcnumActiveRes;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TfOrdSvcnumActiveResMapper {
    int deleteByPrimaryKey(String id);

    int insert(TfOrdSvcnumActiveRes record);

    int insertSelective(TfOrdSvcnumActiveRes record);

    TfOrdSvcnumActiveRes selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TfOrdSvcnumActiveRes record);

    int updateByPrimaryKey(TfOrdSvcnumActiveRes record);

    List<TfOrdSvcnumActiveRes> getAllByActivieId(String activieId);
}
