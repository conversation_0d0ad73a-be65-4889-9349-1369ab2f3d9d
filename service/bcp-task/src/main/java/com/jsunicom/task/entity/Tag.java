/**
 * Copyright © 2015 - 2017 mucfc. All Rights Reserved
 */
package com.jsunicom.task.entity;

import com.jsunicom.task.common.annotation.ApiField;
import com.lz.lsf.entity.BaseEntity;

/**
 *
 * <AUTHOR>
 * @version
 */
public class Tag extends BaseEntity {

    private static final long serialVersionUID = 2616116065107601601L;

    /**
     * 标签ID
     */
    @ApiField("tagid")
    private Long tagId;

    /**
     * 标签名称
     */
    @ApiField("tagname")
    private String tagName;

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

}
