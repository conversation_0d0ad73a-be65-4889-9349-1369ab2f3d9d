package com.jsunicom.task.po.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 搬运自老校园orderreceive
 * <AUTHOR>
 * */

@ApiModel(description = "订单列表查询返回报文入网信息")
@Data
public class NetworkNumberRsp implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "开户号码", required = true, position = 1)
	private String serialNumber;

//	@ApiModelProperty(value = "新老号码标记", required = true, position = 2)
//	private String serialNumberTag;

	@ApiModelProperty(value = "开户号码类型", required = true, position = 3)
	private String serialNumberType;

}
