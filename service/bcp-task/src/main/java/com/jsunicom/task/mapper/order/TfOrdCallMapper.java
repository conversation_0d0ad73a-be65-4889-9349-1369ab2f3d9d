package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tf.TfOrdCall;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface TfOrdCallMapper{

    List<TfOrdCall> qryTfOrdCallByOrderId(String orderId);

    int deleteByPrimaryKey(String messageId);

    int insert(TfOrdCall record);

    int insertSelective(TfOrdCall record);

    TfOrdCall selectByPrimaryKey(String messageId);

    int updateByPrimaryKeySelective(TfOrdCall record);

    int updateByPrimaryKey(TfOrdCall record);

    List<TfOrdCall> qryCallRecord(@Param("orderId") String orderId, @Param("callerId") String callerId);
}
