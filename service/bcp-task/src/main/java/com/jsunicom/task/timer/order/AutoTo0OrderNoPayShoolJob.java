package com.jsunicom.task.timer.order;

import com.jsunicom.task.mapper.order.OrderMonitorJobMapper;
import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.mapper.order.TlOrdOperatorLogMapper;
import com.jsunicom.task.po.order.tf.TfOrdMain;
import com.jsunicom.task.po.order.tl.TlOrdOperatorLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
@EnableScheduling
public class AutoTo0OrderNoPayShoolJob {
    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private OrderMonitorJobMapper orderMonitorJobMapper;

    @Autowired
    private TlOrdOperatorLogMapper tlOrdOperatorLogMapper;

    public void autoTo0OrderNoPayShoolJob() {
        log.info("=====autoTo0OrderNoPayShoolJob====");
        try {
            List<Map<String, Object>> orderList = orderMonitorJobMapper.queryNoPaySchoolOrder();
            log.info("=====autoTo0OrderNoPayShoolJob==待处理订单数=="+orderList.size());
            if (orderList != null && !orderList.isEmpty()) {
                log.info("=====autoTo0OrderNoPayShoolJob==待处理订单数=="+orderList.size());
                for (int i = 0; i < orderList.size(); i++) {
                    Map<String, Object> monitorOrder = orderList.get(i);
                    String orderId = (String) monitorOrder.get("ORDER_ID");
                    String goodsOrderId = (String) monitorOrder.get("GOODS_ORDER_ID");
                    //是否需要增加校验
                    log.info("=====autoTo0OrderNoPayShoolJob==自动转0元单开始=="+orderId);
                    TfOrdMain tfOrdMain = new TfOrdMain();
                    tfOrdMain.setOrderId(orderId);
                    tfOrdMain.setOrderState("0040");//待调度
                    tfOrdMain.setOrderAmount("0");
                    tfOrdMain.setRealAmount("0");
                    tfOrdMain.setPayResult("");
                    tfOrdMainMapper.updateOrdMainInfoByorderId(tfOrdMain);

                    //插入操作日志
                    TlOrdOperatorLog tlOrdOperatorLog=new TlOrdOperatorLog();
                    //    tlOrdOperatorLog.setLogId(generateId.getId("tl_ord_operchange"));
                    tlOrdOperatorLog.setOrderId(orderId);
                    tlOrdOperatorLog.setBusiType("");
                    tlOrdOperatorLog.setNodeCode("orderPay");
                    tlOrdOperatorLog.setNodeName("支付");
                    tlOrdOperatorLog.setReasonCode("success");
                    tlOrdOperatorLog.setReasonName("自动转0元单");
                    tlOrdOperatorLog.setReasonDesc("超时未支付自动转0元单"  );
                    tlOrdOperatorLog.setOperatorId("system");
                    tlOrdOperatorLog.setOperatorName("");
                    tlOrdOperatorLog.setOperatorPhone("");
                    tlOrdOperatorLog.setUpdateTime(new Date());
                    tlOrdOperatorLog.setOrderLineId("");
                    tlOrdOperatorLogMapper.insertTlOrdOperatorLog(tlOrdOperatorLog);

                    log.info("=====autoTo0OrderNoPayShoolJob==自动转0元单结束=="+orderId);

                }
            }


        } catch (Exception e) {
            e.printStackTrace();

        }

    }
}
