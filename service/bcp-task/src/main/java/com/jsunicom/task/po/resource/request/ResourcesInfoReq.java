package com.jsunicom.task.po.resource.request;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ResourcesInfoReq{

	private static final long serialVersionUID = 1L;

	//能人
	private String ablePerson;

	//操作人
	private String operator;

	//是否修改
	private String keyChangeTag;

	//原资源预占关键字
	private String oldKey;

	//资源预占关键字类型
	private String proKeyMode;

	//资源预占关键字
	private String proKey;

	//组别编码
	private String groupKey;

	//资源类型
	private String resourcesType;

	//套包销售标记
	private String packageTag;

	//电话号码
	private String resourcesCode;

	//原资源唯一标识
	private String oldResourcesCode;

	//号码状态标识
	private String occupiedFlag;

	//号码是否变更
	private String snChangeTag;

	//是否延时
	private String delayOccupiedFlag;

	//占用时间
	private String occupiedTime;

	//客户名称
	private String custName;

	//证件类型
	private String certType;

	//证件号码
	private String certNum;

	//联系电话
	private String contactNum;

	//校验类型
	private String preOrderTag;

	//套餐编码
	private String productId;

	//受理渠道标识
	private String acceptChannelTag;

	//发展人判断标识
	private String developPersonTag;

	//发展人标识，在号码预定和付费预定时需要必传以上两个节点
	private String recomPersonId;

	//发展人渠道标识，在号码预定和付费预定时需要必传以上两个节点
	private String recomDeparId;

	//备注信息
	private String remark;

}
