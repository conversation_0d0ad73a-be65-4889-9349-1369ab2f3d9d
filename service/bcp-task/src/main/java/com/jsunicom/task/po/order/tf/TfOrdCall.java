package com.jsunicom.task.po.order.tf;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单外呼记录表
 * @TableName tf_ord_call
 */
@Data
public class TfOrdCall implements Serializable {
    /**
     * 呼叫申请流水号
     */
    private String messageId;

    /**
     * 订单编码
     */
    private String orderId;

    /**
     * 主叫号码
     */
    private String callerId;

    /**
     * 被叫号码
     */
    private String calledId;

    /**
     * 呼叫流水号
     */
    private String callId;

    /**
     * 创建工号
     */
    private String createStaffId;

    /**
     * 呼叫开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callStartTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    private static final long serialVersionUID = 1L;
}
