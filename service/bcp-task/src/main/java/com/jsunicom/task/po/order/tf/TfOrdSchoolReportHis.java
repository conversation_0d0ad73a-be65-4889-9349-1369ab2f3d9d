package com.jsunicom.task.po.order.tf;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TfOrdSchoolReportHis implements Serializable {
    private String orderId;

    private String deliveryType;

    private String orderStatus;

    private String createDate;

    private String goodsState;

    private String birthday;

    private String goodsId;

    private String goodsName;

    private String mainNumber;

    private String developerCode;

    private String developerName;

    private String channlId;

    private String custQq;

    private String custPhone;

    private String custContact;

    private String developerRemark;

    private String staffName;

    private String merchantName;

    private String storeMangerName;

    private String custName;

    private String city;

    private String psptId;

    private String address;

    private String postStatus;

    private String fee;

    private String iccid;
    private String staffId;

    private Date activationdate;

    private Date updateTime;

    private String schoolId;

    private String schoolName;

    private String installAddress;

    private String numStatus;

    private static final long serialVersionUID = 1L;

}
