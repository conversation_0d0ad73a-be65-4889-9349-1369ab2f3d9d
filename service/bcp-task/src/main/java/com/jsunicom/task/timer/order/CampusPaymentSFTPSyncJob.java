package com.jsunicom.task.timer.order;


import com.alibaba.fastjson.JSON;
import com.jcraft.jsch.*;
import com.jsunicom.task.entity.PaymentOrderDTO;
import com.jsunicom.task.entity.SftpProperties;
import com.jsunicom.task.mapper.TfOrdPayDetailSchoolDao;
import com.jsunicom.task.mapper.order.PaymentOrderSyncSFTPMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Title: CampusPaymentSFTPSyncJob
 * <AUTHOR>
 * @Package com.jsunicom.sync.job
 * @Date 2024/3/11 9:02
 * @description: 校园支付信息每日定时生成txt文件，同步到网格
 */
@Slf4j
@Component
public class CampusPaymentSFTPSyncJob {

    @Resource
    private SftpProperties sftpProperties;

    @Autowired
    private PaymentOrderSyncSFTPMapper paymentOrderSyncSFTPMapper;

    @Resource
    private TfOrdPayDetailSchoolDao tfOrdPayDetailSchoolDao;

    private static final String suffixStr = "jspop_payInfo_0002_";

    @XxlJob("paymentCampusSyncSFTPTask")
    public ReturnT<String> paymentCampusSyncSFTPTask(String param) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String path = sftpProperties.getPath() + "/" + suffixStr + sdf.format(new Date()) + ".txt";
        PrintWriter paymentOrderOut = null;
        try {
            SimpleDateFormat temp = new SimpleDateFormat("yyyy-MM-dd");
            String endTime = temp.format(new Date());
            Calendar instance = Calendar.getInstance();
            instance.add(Calendar.DAY_OF_MONTH, -1);
            String startTime = temp.format(instance.getTime());
            String fileName = suffixStr + sdf.format(instance.getTime()) + ".txt";
            log.info("paymentCampusSyncSFTPTaskw文件名：{}",fileName);
            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("startTime", startTime);
            paramMap.put("endTime", endTime);
            log.info("paymentCampusSyncSFTPTask paramMap:{}", JSON.toJSONString(paramMap));
            List<PaymentOrderDTO> paymentOrderDTOS = paymentOrderSyncSFTPMapper.getUpPayInfoList(paramMap);
            log.info("paymentCampusSyncSFTPTask size:{}", paymentOrderDTOS.size());
            File file = new File(fileName);
            if (paymentOrderDTOS != null && paymentOrderDTOS.size() > 0) {
                paymentOrderOut = new PrintWriter(file, "UTF-8");
                String uploadErrMsg = null;
                getRefundDetailFile(paymentOrderOut, paymentOrderDTOS);
                uploadFile(file);
            } else {
                file.createNewFile();
                uploadFile(file);
            }
            log.info("生成校园支付明细给网格：文件上传成功，文件位置:" + path);

        } catch (Exception e) {
            log.info("paymentCampusSyncSFTPTask甩单每日支付信息同步网格异常:{}", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    public String uploadFile(File uploadFile) throws Exception {
        String directory = sftpProperties.getPath();
        ChannelSftp sftp = sftpConnect();
        try {
            if (uploadFile == null) {
                throw new Exception("上传文件为空");
            }

            try {
                sftp.cd(directory);
            } catch (SftpException var8) {
                sftp.mkdir(directory);
            }

            sftp.cd(directory);
            sftp.put(new FileInputStream(uploadFile), uploadFile.getName());
        } catch (Exception var9) {
            throw new Exception("上传文件失败!" + var9.getMessage());
        } finally {
            if (sftp != null) {
                sftp.disconnect();
            }
        }

        return directory + File.separator + uploadFile.getName();
    }

    private void getRefundDetailFile(PrintWriter paymentOrderOut, List<PaymentOrderDTO> paymentOrderDTOS) {
        for (PaymentOrderDTO orderDTO : paymentOrderDTOS) {
            StringBuilder buffer = new StringBuilder();
            buffer.append(orderDTO.getClctDay()).append("|");
            buffer.append(orderDTO.getEparchyCode()).append("|");
            buffer.append(orderDTO.getOrderId()).append("|");
            buffer.append(orderDTO.getBusiType()).append("|");
            buffer.append(orderDTO.getOrderTime()).append("|");
            buffer.append(orderDTO.getOutOrderId()).append("|");
            buffer.append(orderDTO.getPayTime()).append("|");
            buffer.append(orderDTO.getGoodsName()).append("|");
            buffer.append(orderDTO.getPayTransactionId()).append("|");
            buffer.append(orderDTO.getPayStatus()).append("|");
            buffer.append(orderDTO.getRealFee()).append("|");
            buffer.append(orderDTO.getTradeId()).append("|");
            buffer.append(orderDTO.getSerialNumber());
            String eachLine = buffer.toString();
            paymentOrderOut.println(eachLine);
        }
        paymentOrderOut.flush();
    }

    /**
     * 连接sftp服务器
     */
    public ChannelSftp sftpConnect() throws Exception {
        ChannelSftp sftp = null;
        JSch jsch = new JSch();
        //用户名，主机，端口号
        Session sshSession = jsch.getSession(sftpProperties.getUsername(), sftpProperties.getIp(), sftpProperties.getPort());
        log.info("sftp username:{} ip:{} port:{}", sftpProperties.getUsername(), sftpProperties.getIp(), sftpProperties.getPort());
        sshSession.setPassword(sftpProperties.getPassword());//密码
        Properties sshConfig = new Properties();
        sshConfig.put("StrictHostKeyChecking", "no");
        sshSession.setConfig(sshConfig);
        sshSession.setTimeout(30000); // 设置timeout时间
        sshSession.connect();
        Channel channel = sshSession.openChannel("sftp");
        channel.connect();
        sftp = (ChannelSftp) channel;
        log.info("sftp connected success");
        return sftp;
    }
}
