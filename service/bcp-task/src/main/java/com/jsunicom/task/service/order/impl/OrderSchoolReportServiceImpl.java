package com.jsunicom.task.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.task.mapper.order.OrderQueryMapper;
import com.jsunicom.task.mapper.order.OrderSchoolReportMapper;
import com.jsunicom.task.mapper.order.TfOrdSchoolReportHisMapper;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.po.order.tf.TfOrdSchoolReportHis;
import com.jsunicom.task.service.order.OrderSchoolReportService;
import com.jsunicom.task.utils.DateUtils;
import com.jsunicom.task.utils.DesensitizeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class OrderSchoolReportServiceImpl implements OrderSchoolReportService {

    @Autowired
    private OrderSchoolReportMapper orderSchoolReportMapper;

    @Autowired
    private TfOrdSchoolReportHisMapper tfOrdSchoolReportHisMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private OrderQueryMapper orderQueryMapper;

    @Override
    public int SynSchoolReportHistory() {
        int res = tfOrdSchoolReportHisMapper.deleteAllRecord();
        Map<String, Object> param = new HashMap<>();
        List<Map<String, Object>> maps = orderSchoolReportMapper.querySchoolComplete(param);
        List<Map<String, Object>> resultMaps = convertIntentionList(maps,false);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("SynSchoolReportHistory size:{} data:{}",resultMaps.size(), JSON.toJSONString(resultMaps));
        List<TfOrdSchoolReportHis> schoolReportHis = resultMaps.stream().map(report -> {
            TfOrdSchoolReportHis reportHis = new TfOrdSchoolReportHis();
            reportHis.setOrderId((String) report.get("orderId"));
            reportHis.setCustName((String) report.get("custName"));
            reportHis.setCustPhone((String) report.get("custPhone"));
            reportHis.setCustContact((String) report.get("custContact"));
            reportHis.setPsptId((String) report.get("psptId"));
            reportHis.setCreateDate((String) report.get("createDate"));
            reportHis.setChannlId((String) report.get("channelId"));
            reportHis.setOrderStatus((String) report.get("orderState"));
            reportHis.setMainNumber((String) report.get("mainNumber"));
            reportHis.setGoodsId((String) report.get("goodsId"));
            reportHis.setGoodsName((String) report.get("goodsName"));
            reportHis.setAddress((String) report.get("address"));
            reportHis.setDeliveryType((String) report.get("deliveryType"));
            reportHis.setMerchantName((String) report.get("schoolName"));
            String activationDate = (String) report.get("activationDate");
            log.info("TfOrdSchoolReportHisactivationDate:{}",activationDate);
            try{
                reportHis.setActivationdate(format.parse(activationDate));
            }catch (Exception e){
                log.info("TfOrdSchoolReportHisactivationDate转换异常！");
            }
            reportHis.setIccid((String) report.get("ICCID"));
            reportHis.setCustQq((String) report.get("QQ"));
            reportHis.setDeveloperCode((String) report.get("developerCode"));
            reportHis.setDeveloperName((String) report.get("developerName"));
            reportHis.setPostStatus((String) report.get("postStatus"));
            reportHis.setCity((String) report.get("cityCode"));
            reportHis.setFee(String.valueOf(report.get("fee")) );
            reportHis.setStoreMangerName((String) report.get("storeMangerName"));
            reportHis.setStaffId((String) report.get("staffId"));
            String storeMangerName = "";
            if (StringUtils.isNotBlank((String) report.get("developerCode"))) {
                storeMangerName = orderSchoolReportMapper.getStoreManagerName((String) report.get("developerCode"));
            }
            reportHis.setStoreMangerName(storeMangerName == null ? "" : storeMangerName);
            reportHis.setSchoolId((String) report.get("campusId"));
            reportHis.setSchoolName((String) report.get("campusName"));
            reportHis.setStaffName((String) report.get("staffName"));
            reportHis.setBirthday((String) report.get("birthday"));
            reportHis.setUpdateTime(new Date());
            reportHis.setNumStatus((String) report.get("numStatus"));
            return reportHis;
        }).collect(Collectors.toList());
        log.info("TfOrdSchoolReportHis:{}",schoolReportHis);
        if (schoolReportHis != null && schoolReportHis.size() > 0){
            int applySize = 10000;
            int limit = (schoolReportHis.size() + applySize - 1) / applySize;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
                List<TfOrdSchoolReportHis> result = schoolReportHis.stream().skip(a * applySize).limit(applySize).collect(Collectors.toList());
                tfOrdSchoolReportHisMapper.insertBatchInfo(result);
            });
        }

        return schoolReportHis.size();
    }

    private List<Map<String, Object>> convertIntentionList(List<Map<String, Object>> orderTotalList,boolean auth) {
        TdMSysDict tdMSysDict = new TdMSysDict();
        tdMSysDict.setParamType("orderState");
        List<TdMSysDict> orderStateList = tdMSysDictMapper.queryAll(tdMSysDict);
        tdMSysDict.setParamType("deliveryType");
        List<TdMSysDict> deliveryTypeList = tdMSysDictMapper.queryAll(tdMSysDict);
        List<Map<String, Object>> collect = orderTotalList.stream().map(report -> {
            Map<String, Object> result = new HashMap<>();
            String orderState = (String) report.get("orderState");
            String orderStateDesc = orderState;
            if (StringUtils.isNotBlank(orderState)) {
                for (TdMSysDict mSysDict : orderStateList) {
                    if (mSysDict.getParamKey().equals(orderState)) {
                        orderStateDesc = mSysDict.getParamValue();
                        break;
                    }
                }
                if (StringUtils.isNotBlank(orderStateDesc)) {
                    result.put("orderState", orderStateDesc);
                }
            }
            String psptId = (String) report.get("psptId");
            String address = (String) report.get("address");
            String custName = (String)report.get("custName");
            if (auth){
                psptId = DesensitizeUtil.identificationNum(psptId);
                address = StringUtils.rightPad(StringUtils.left(address, 10), StringUtils.length(address), "*");
                custName = StringUtils.rightPad(StringUtils.left(custName, 1), StringUtils.length(custName), "*");
            }
            String birthday = (String) report.get("birthday");
            if(StringUtils.isNotBlank(birthday)){
                String year = birthday.substring(0, 4);
                String curYear = DateUtils.getYear();
                result.put("age", Integer.valueOf(curYear) - Integer.valueOf(year));
            }else{
                result.put("age", "");
            }
            result.put("postStatus", "0".equals((String) report.get("postStatus")) ? "未寄送" : "已寄送");
            result.put("orderId", report.get("orderId"));
            result.put("custName", custName);
            result.put("custPhone", report.get("custPhone"));
            result.put("custContact", report.get("custContact"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String createDate = sdf.format(report.get("createDate"));
            result.put("createDate", createDate);
            result.put("channelId", report.get("channelId"));
            result.put("mainNumber", report.get("mainNumber"));
            result.put("goodsId", report.get("goodsId"));
            result.put("goodsName", report.get("goodsName"));
            result.put("schoolName", report.get("schoolName"));
            result.put("staffId", report.get("staffId"));
            String activationDate = "";
            if (!ObjectUtils.isEmpty((report.get("activationDate")))) {
                activationDate = sdf.format(report.get("activationDate"));
            }
            result.put("activationDate", activationDate);
            result.put("ICCID", report.get("ICCID"));
            String deliveryType = (String) report.get("deliveryType");
            String deliveryTypeDesc = deliveryType;
            if (StringUtils.isNotBlank(deliveryType)) {
                for (int j = 0; j < deliveryTypeList.size(); j++) {
                    if (deliveryType.equals(deliveryTypeList.get(j).getParamKey())) {
                        deliveryTypeDesc = deliveryTypeList.get(j).getParamValue();
                        break;
                    }
                }
                if (StringUtils.isNotBlank(deliveryTypeDesc)) {
                    result.put("deliveryType",deliveryTypeDesc);
                }
            }
            // result.put("deliveryType", report.get("deliveryType"));
            result.put("QQ", report.get("QQ"));
            result.put("cityCode", report.get("cityCode"));
            result.put("fee", report.get("fee"));
            String developerCode = (String) report.get("developerCode");
            result.put("developerCode", developerCode);
            result.put("developerName", report.get("developerName"));
            String storeMangerName = "";
            if (StringUtils.isNotBlank(developerCode)) {
                storeMangerName = orderSchoolReportMapper.getStoreManagerName((String) report.get("developerCode"));
            }
            result.put("storeMangerName", storeMangerName == null ? "" : storeMangerName);
            result.put("psptId",psptId);
            result.put("eparchyName",report.get("eparchyName"));
            result.put("acctNumber",report.get("acctNumber"));
            result.put("studentIdOrderId",report.get("studentId_orderId"));
            result.put("studentId",report.get("studentId"));
            String provinceCodeStr = "";
            String countyCodeStr = "";
            String cityCodeStr = "";
            String province_code = StringUtils.defaultIfBlank((String) report.get("PROVINCE_CODE"),"");
            String city_code = StringUtils.defaultIfBlank((String) report.get("CITY_CODE"),"");
            String county_code = StringUtils.defaultIfBlank((String) report.get("COUNTY_CODE"),"");
            if (StringUtils.isNotBlank(province_code)){
                provinceCodeStr = orderQueryMapper.getCodeName(province_code);
            }
            if (StringUtils.isNotBlank(county_code)){
                countyCodeStr = orderQueryMapper.getCodeName(county_code);
            }
            if (StringUtils.isNotBlank(city_code)){
                cityCodeStr= orderQueryMapper.getCodeName(city_code);
            }
            StringBuilder deliverAddress = new StringBuilder();
            deliverAddress.append(StringUtils.isBlank(provinceCodeStr) ? province_code : provinceCodeStr);
            deliverAddress.append(StringUtils.isBlank(cityCodeStr) ? city_code : cityCodeStr);
            deliverAddress.append(StringUtils.isBlank(countyCodeStr) ? county_code : countyCodeStr);
            deliverAddress.append(address);
            result.put("address",deliverAddress.toString());
            return result;
        }).collect(Collectors.toList());
        return collect;
    }

}
