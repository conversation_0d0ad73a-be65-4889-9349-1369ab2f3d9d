package com.jsunicom.task.common.result;

/**
 * <AUTHOR> z<PERSON><PERSON>
 * @ClassName Result
 * @Description TODO 类描述
 * @date : 2021-08-24 20:04
 * @Version 1.0
 **/
public class CustomResult<T> {
    private String respCode;
    private String respDesc;
    private T respData;

    public CustomResult() {
        super();
    }

    public CustomResult(String respCode, String respDesc, T respData) {
        this.respCode = respCode;
        this.respDesc = respDesc;
        this.respData = respData;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setrespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespDesc() {
        return respDesc;
    }

    public void setrespDesc(String respDesc) {
        this.respDesc = respDesc;
    }

    public T getRespData() {
        return respData;
    }

    public void setrespData(T respData) {
        this.respData = respData;
    }

    @Override
    public String toString() {
        return "Result{" +
                "respCode=" + respCode +
                ", respDesc='" + respDesc + '\'' +
                ", respData=" + respData +
                '}';
    }
}
