package com.jsunicom.task.utils;

import com.jcraft.jsch.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

public class SFTPKit {

    private static final Logger log = LogManager.getLogger(SFTPKit.class);

    private ChannelSftp sftp;

    private boolean connected = false;

    public SFTPKit(String host, int port, String username, String password) {
        log.info("SFTPKit开始连接sftp");
        sftp = connect(host, port, username, password);
    }

    /**
     * 上传文件
     *
     * @param directory  上传的目录
     * @param uploadFile 要上传的文件
     */
    public boolean upload(String directory, String uploadFile) {
        try {
            sftp.cd(directory);
            File file = new File(uploadFile);
            FileInputStream fileInputStream = new FileInputStream(file);
            sftp.put(fileInputStream, file.getName());
            fileInputStream.close();
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    /**
     * 上传文件
     *
     * @param directory  上传的目录
     * @param uploadFile 要上传的文件
     */
    public boolean upload(String directory, File uploadFile) {
        try {
            sftp.cd(directory);
            FileInputStream fileInputStream = new FileInputStream(uploadFile);
            sftp.put(fileInputStream, uploadFile.getName());
            fileInputStream.close();
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    /**
     * 下载文件
     *
     * @param directory    下载目录
     * @param downloadFile 下载的文件
     * @param saveFile     存在本地的路径
     */
    public File download(String directory, String downloadFile, String saveFile) {
        try {
            sftp.cd(directory);
            File file = new File(saveFile);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            sftp.get(downloadFile, fileOutputStream);
            fileOutputStream.close();
            return file;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * 批量下载文件
     *
     * @param remotePath：远程下载目录(以路径符号结束,可以为相对路径eg:/assess/sftp/jiesuan_2/2014/)
     * @param localPath：本地保存目录(以路径符号结束,D:\Duansha\sftp\)
     * @param fileFormat：下载文件格式(以特定字符开头,为空不做检验)
     * @param
     * @param
     * @return
     */
    public static List<String> batchDownLoadFile(String host, int port, String username, String password, String remotePath, String localPath, String fileFormat) {
        JSch jsch = new JSch();
        ChannelSftp sftp = null;
        Session sshSession = null;
        List<String> filenames = new ArrayList<String>();
        try {
            // 采用指定的端口连接服务器
            sshSession = jsch.getSession(username, host, port);

            // 如果服务器连接不上，则抛出异常
            if (sshSession == null) {
                throw new Exception("sftp服务器异常");
            }
            // 设置登陆主机的密码
            sshSession.setPassword(password);

            // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
            sshSession.setConfig("StrictHostKeyChecking", "no");

            // 设置登陆超时时间
            sshSession.connect(300000);
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;

            Vector v = sftp.ls(remotePath);
            if (v.size() > 0) {
                log.info("batchDownLoadFile 本次处理文件个数不为零,开始下载...fileSize=" + v.size());
                Iterator it = v.iterator();
                while (it.hasNext()) {
                    ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) it.next();
                    String filename = entry.getFilename();
                    SftpATTRS attrs = entry.getAttrs();
                    if (!attrs.isDir()) {
                        boolean flag = false;
                        if (filename.contains(fileFormat)) {
                            log.info("file matches success!");
                            flag = downloadFile(remotePath, filename, localPath, filename, sftp);
                            if (flag) {
                                log.info("file download success! Then remove file and rename files!");
                                filenames.add(filename);
                            }
                        }
                    }
                }
            }
            log.info("Download file is success:remotePath="+ remotePath +" and localPath="+ localPath +",file size is "+ filenames.size());
        }catch (Exception e){
            log.error("Batch download Failed:",e);
        }finally {
            if (sftp != null) {
                if (sftp.isConnected()){
                    sftp.disconnect();
                    log.info("已关闭sftp");
                }
            }
            if (sshSession != null) {
                if (sshSession.isConnected()) {
                    sshSession.disconnect();
                    log.info("已关闭sshSession");
                }
            }
        }
        return filenames;
    }

    public Vector listFiles(String directory) throws SftpException {
        return sftp.ls(directory);
    }

    private static boolean downloadFile(String remotePath, String remoteFileName, String localPath, String localFileName, ChannelSftp sftp) {
        FileOutputStream fieloutput = null;
        try {
            sftp.cd(remotePath);
            log.info("downloadFile localPath = {}, localFileName = {}",localPath,localFileName);
            File localFile = new File(localPath + "/" + localFileName);
            if(localFile.exists()){
                log.info("downloadFile：{}文件已存在,请勿重复拉取文件!",localFileName);
                return false;
            }
//            File localFile = new File(localPath);
//            if (!localFile.exists()) {
//                log.info("ztLgtsState/文件夹不存在，开始创建文件夹");
//                localFile.mkdirs();
//                log.info("ztLgtsState/文件夹创建结束");
//            } else {
//                log.info("ztLgtsState/文件夹已存在");
//            }
            log.info("localFile.getPath():" + localFile.getPath());
            log.info("localFile.getAbsolutePath():" + localFile.getAbsolutePath());
            String file = localPath +"/"+ localFileName;
            fieloutput = new FileOutputStream(file);
            sftp.get(remoteFileName, fieloutput);
            log.info("DownloadFile:" + remoteFileName + " success from sftp.");
            return true;
        } catch (Exception e) {
            log.error("File({}) download Failed:", remoteFileName, e);
        } finally {
            if (null != fieloutput) {
                try {
                    fieloutput.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    /**
     * 删除stfp文件
     *
     * @param directory：要删除文件所在目录
     * @param deleteFile：要删除的文件
     */
    public void deleteSFTP(String directory, String deleteFile) {
        try {
            // sftp.cd(directory);
            sftp.rm(directory + deleteFile);
            if (log.isInfoEnabled()) {
                log.info("delete file success from sftp.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 连接sftp服务器
     *
     * @param host     主机
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     * @return
     */
    public ChannelSftp connect(String host, int port, String username, String password) {
        log.info("ChannelSftp开始连接sftp");
        ChannelSftp sftp = null;
        try {
            JSch jsch = new JSch();
            jsch.getSession(username, host, port);
            Session sshSession = jsch.getSession(username, host, port);
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            sshSession.connect();
            log.info("SFTP Session connected.");
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;
            log.info("Connected to " + host);
            connected = true;
        } catch (Exception e) {
            connected = false;
        }
        return sftp;
    }

    public boolean isConnected() {
        return connected;
    }

    public void disconnect() {
        try {
            sftp.getSession().disconnect();
        } catch (JSchException e) {
            log.error(e.getMessage());
        }
        sftp.quit();
        sftp.disconnect();
    }
}
