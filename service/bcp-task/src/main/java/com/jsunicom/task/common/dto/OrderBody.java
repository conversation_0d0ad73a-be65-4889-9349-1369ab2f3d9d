package com.jsunicom.task.common.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class OrderBody implements Serializable {

    private String sourceSystemId;
    private String extOrderId;
    private String orderCenterId;
    private String areaCode;
    private String eparchyCode;

    private String psptId;
    private String custName;

    private String veriFlag;
    private String receivePhone;
    private String busiType;

    private String networkNumber;

    private String goodsIds;
    private String startDate;

    private String orderState;
    private String goodsState;
    private String goodsStateTag;

    private String currPage;
    private String pageSize;
    private String phone;

    public String getNetworkNumber() {
        return networkNumber;
    }

    public void setNetworkNumber(String networkNumber) {
        this.networkNumber = networkNumber;
    }

    public String getReceivePhone() {
        return receivePhone;
    }

    public void setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getSourceSystemId() {
        return sourceSystemId;
    }

    public void setSourceSystemId(String sourceSystemId) {
        this.sourceSystemId = sourceSystemId;
    }

    public String getExtOrderId() {
        return extOrderId;
    }

    public void setExtOrderId(String extOrderId) {
        this.extOrderId = extOrderId;
    }

    public String getOrderCenterId() {
        return orderCenterId;
    }

    public void setOrderCenterId(String orderCenterId) {
        this.orderCenterId = orderCenterId;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getEparchyCode() {
        return eparchyCode;
    }

    public void setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
    }

    public String getPsptId() {
        return psptId;
    }

    public void setPsptId(String psptId) {
        this.psptId = psptId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getVeriFlag() {
        return veriFlag;
    }

    public void setVeriFlag(String veriFlag) {
        this.veriFlag = veriFlag;
    }

    public String getGoodsIds() {
        return this.goodsIds;
    }

    public void setGoodsIds(String goodsIds) {
        this.goodsIds = goodsIds;
    }

    public String getStartDate() {
        return this.startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getGoodsState() {
        return goodsState;
    }

    public void setGoodsState(String goodsState) {
        this.goodsState = goodsState;
    }

    public String getGoodsStateTag() {
        return goodsStateTag;
    }

    public void setGoodsStateTag(String goodsStateTag) {
        this.goodsStateTag = goodsStateTag;
    }

    public String getCurrPage() {
        return currPage;
    }

    public void setCurrPage(String currPage) {
        this.currPage = currPage;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
