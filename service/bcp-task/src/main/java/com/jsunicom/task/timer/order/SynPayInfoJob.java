package com.jsunicom.task.timer.order;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.task.mapper.order.SynPayInfoMapper;
import com.jsunicom.task.service.order.SendSmsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 同步与对账文件状态不一致的支付记录
 * <AUTHOR>
 * */

@Component
@Slf4j
public class SynPayInfoJob {

    @Resource
    private SynPayInfoMapper synPayInfoMapper;

    @Resource
    private SendSmsService sendSmsService;

    @XxlJob("synPayInfoJob")
    public ReturnT<String> synPayInfoJob(String param) {
        log.info("synPayInfoJobStart...");
        try {
            int ePayNum = synPayInfoMapper.checkPayInfoNum(getTimeStr());
            if (ePayNum > 0) {
                //填充发送短信的报文
                JSONObject smsParams = new JSONObject();
                smsParams.put("orderId", "");
                smsParams.put("sendTo", "18602508968");
                //替换参数
                smsParams.put("sendParams", ePayNum);
                //发送短信
                sendSmsService.sendSms("20003", smsParams);
                smsParams.put("sendTo", "13073490954");
                //发送短信
                sendSmsService.sendSms("20003", smsParams);
            }
            List<String> busiOrderIdList = synPayInfoMapper.qryNoSynPayInfo(getTimeStr());
            for (String busiOrderId : busiOrderIdList) {
                log.info("处理未同步支付状态的支付记录,busiOrderId:{}", busiOrderId);
                synPayInfoMapper.updatePayCheckTag(busiOrderId);
            }
            int eRefundNum = synPayInfoMapper.checkRefundInfoNum(getTimeStr());
            if (eRefundNum > 0) {
                //填充发送短信的报文
                JSONObject smsParams = new JSONObject();
                smsParams.put("orderId", "");
                smsParams.put("sendTo", "18602508968");
                //替换参数
                smsParams.put("sendParams", eRefundNum);
                //发送短信
                sendSmsService.sendSms("20004", smsParams);
                smsParams.put("sendTo", "13073490954");
                //发送短信
                sendSmsService.sendSms("20004", smsParams);
            }
            int ePaySchoolNum = synPayInfoMapper.checkPayInfoNumForSchool(getTimeStr());
            if (ePaySchoolNum > 0) {
                //填充发送短信的报文
                JSONObject smsParams = new JSONObject();
                smsParams.put("orderId", "");
                smsParams.put("sendTo", "18602508968");
                //替换参数
                smsParams.put("sendParams", ePaySchoolNum);
                //发送短信
                sendSmsService.sendSms("20003", smsParams);
                smsParams.put("sendTo", "13073490954");
                //发送短信
                sendSmsService.sendSms("20003", smsParams);
            }
            //休眠5分钟，等待其他定时任务处理
            Thread.sleep(5 * 60 * 1000);
            busiOrderIdList = synPayInfoMapper.qryNoSynPayInfo(getTimeStr());
            if (!ObjectUtils.isEmpty(busiOrderIdList))  {
                //填充发送短信的报文
                JSONObject smsParams = new JSONObject();
                smsParams.put("orderId", "");
                smsParams.put("sendTo", "18602508968");
                //替换参数
                smsParams.put("sendParams", busiOrderIdList.size());
                //发送短信
                sendSmsService.sendSms("20005", smsParams);
                smsParams.put("sendTo", "13073490954");
                //发送短信
                sendSmsService.sendSms("20005", smsParams);
            }
        }catch (Exception e) {
            log.error("synPayInfoJobException...", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
        log.info("synPayInfoJobEnd...");
        return ReturnT.SUCCESS;
    }

    private String getTimeStr() {
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DATE, -1);
        return f.format(c.getTime());
    }

}
