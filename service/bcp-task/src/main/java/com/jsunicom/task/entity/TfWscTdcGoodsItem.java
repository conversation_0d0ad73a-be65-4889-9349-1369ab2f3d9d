package com.jsunicom.task.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/11 10:59
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TfWscTdcGoodsItem implements Serializable {
    private static final long serialVersionUID = 1L;
    private String propId;
    private String goodsId;
    private String proptCode;
    private String propName;
    private String propDesc;
    private String propValue;
    public static final String PROP_ID = "PROP_ID";
    public static final String GOODS_ID = "GOODS_ID";
    public static final String PROPT_CODE = "PROPT_CODE";
    public static final String PROP_NAME = "PROP_NAME";
    public static final String PROP_DESC = "PROP_DESC";
    public static final String PROP_VALUE = "PROP_VALUE";
}
