package com.jsunicom.task.po.order;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "订单详情查询返回报文实名认证多媒体资料")
@Data
public class ActiveInfoResRsp implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "多媒体资料类型（1：图片，2：视频）", required = true, position = 1)
	private String resType;

	@ApiModelProperty(value = "多媒体资料名称", required = true, position = 2)
	private String resName;

	@ApiModelProperty(value = "多媒体资料路径", required = true, position = 3)
	private String url;

	@ApiModelProperty(value = "多媒体资料描述", required = true, position = 4)
	private String resDesc;

}
