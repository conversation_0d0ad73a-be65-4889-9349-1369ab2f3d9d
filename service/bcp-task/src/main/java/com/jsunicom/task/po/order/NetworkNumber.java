package com.jsunicom.task.po.order;

import lombok.Data;

import java.io.Serializable;

@SuppressWarnings("all")
@Data
public class NetworkNumber implements Serializable {

	private static final long serialVersionUID = 1L;

	private String serialNumber;
	private String serialNumberTag;
	private String serialNumberType;

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((serialNumber == null) ? 0 : serialNumber.hashCode());
		result = prime * result + ((serialNumberTag == null) ? 0 : serialNumberTag.hashCode());
		result = prime * result + ((serialNumberType == null) ? 0 : serialNumberType.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		NetworkNumber other = (NetworkNumber) obj;
		if (serialNumber == null) {
			if (other.serialNumber != null)
				return false;
		} else if (!serialNumber.equals(other.serialNumber))
			return false;
		if (serialNumberTag == null) {
			if (other.serialNumberTag != null)
				return false;
		} else if (!serialNumberTag.equals(other.serialNumberTag))
			return false;
		if (serialNumberType == null) {
			if (other.serialNumberType != null)
				return false;
		} else if (!serialNumberType.equals(other.serialNumberType))
			return false;
		return true;
	}

}
