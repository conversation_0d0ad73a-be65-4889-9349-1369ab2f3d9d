package com.jsunicom.task.po.resource.request;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class SerialOperateReq{

	private static final long serialVersionUID = 1L;

	//操作员ID
	private String operatorId;

	//省分
	private String province;

	//地市
	private String city;

	//区县
	private String district;

	//渠道编码
	private String channelId;

	//渠道类型
	private String channelType;

	//保留字段
	private String para;

	//资源信息
	private List<ResourcesInfoReq>  resourcesInfo;

	//备用信息
	private List<ParaInfoReq> paraInfo;


}
