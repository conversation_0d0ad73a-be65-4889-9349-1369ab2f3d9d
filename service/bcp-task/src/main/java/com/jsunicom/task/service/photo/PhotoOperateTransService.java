package com.jsunicom.task.service.photo;

import java.util.HashMap;
import java.util.Map;

/**
 * @PackageName: com.jsunicom.task.service
 * @ClassName PhotoOperateTransService
 * @Description 照片相关操作事务处理
 * @Date 2023/6/5 14:07
 * @Created by yangyj3
 */
public interface PhotoOperateTransService {
    String PHOTO_DES_A = "身份证正面";
    String PHOTO_DES_A1 = "idcardAUrl";
    final String PHOTO_DES_B = "身份证背面";
    String PHOTO_DES_B1 = "idcardBUrl";
    String PHOTO_DES_CJ1 = "photoUrl";
    String PHOTO_DES_CJ = "现场照片";
    String PHOTO_DES_SITE = "现场照片";
    String RES_TYPE_3 = "3";
    String RES_TYPE_1 = "1";
    String SYN_PHOTO_STATE_FAIL = "2";
    String SYN_PHOTO_STATE_DEAL = "3";

    String SYN_PHOTO_STATE_SUCCESS = "1";

    void wslOrderPhotoSyn(Map<String, String> record);

    void updateSynSuccess(HashMap<String, String> param);
}
