package com.jsunicom.task.mapper.order;

import com.jsunicom.task.po.order.tf.TfOrdCustinfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TfOrdCustinfoMapper {

    TfOrdCustinfo qryTfOrdCustinfoByOrderId(@Param("orderId") String orderId);

    int updateTfOrdCustinfoByOrderId(TfOrdCustinfo tfOrdCustinfo);

    int deleteByPrimaryKey(String custinfoId);

    int insert(TfOrdCustinfo record);

    int insertSelective(TfOrdCustinfo record);

    TfOrdCustinfo selectByPrimaryKey(String custinfoId);

    int updateByPrimaryKeySelective(TfOrdCustinfo record);

    int updateByPrimaryKey(TfOrdCustinfo record);

    int updateByOrderId(@Param("orderId") String orderId,@Param("numStatus") String numStatus);
}
