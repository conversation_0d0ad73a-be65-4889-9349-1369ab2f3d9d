package com.jsunicom.task.mapper;

import com.jsunicom.task.po.SchoolCcpShopTable;
import com.jsunicom.task.po.SchoolCcpShopTableExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SchoolCcpShopTableMapper {
    long countByExample(SchoolCcpShopTableExample example);

    int deleteByExample(SchoolCcpShopTableExample example);

    int insert(SchoolCcpShopTable record);

    int insertSelective(SchoolCcpShopTable record);

    List<SchoolCcpShopTable> selectByExample(SchoolCcpShopTableExample example);

    int updateByExampleSelective(@Param("record") SchoolCcpShopTable record, @Param("example") SchoolCcpShopTableExample example);

    int updateByExample(@Param("record") SchoolCcpShopTable record, @Param("example") SchoolCcpShopTableExample example);
}
