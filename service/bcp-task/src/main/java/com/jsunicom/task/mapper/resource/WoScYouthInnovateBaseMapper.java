package com.jsunicom.task.mapper.resource;

import com.jsunicom.task.po.WoScYouthInnovateBase;
import com.jsunicom.task.po.WoScYouthInnovateBaseExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface WoScYouthInnovateBaseMapper {

    List<WoScYouthInnovateBase> selectByExample(WoScYouthInnovateBaseExample example);

    int updateByExampleSelective(@Param("record") WoScYouthInnovateBase record, @Param("example") WoScYouthInnovateBaseExample example);

}