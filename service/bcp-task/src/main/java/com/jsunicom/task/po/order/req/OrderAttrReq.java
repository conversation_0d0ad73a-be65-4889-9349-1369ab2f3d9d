package com.jsunicom.task.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "订单请求报文属性信息")
@Data
public class OrderAttrReq {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "属性编码", required = true, position = 1)
    private String attrCode;

    @ApiModelProperty(value = "属性名称", required = true, position = 2)
    private String attrName;

    @ApiModelProperty(value = "属性值", required = true, position = 3)
    private String attrValue;

    @ApiModelProperty(value = "属性类型", required = true, position = 4)
    private String attrType;

}
