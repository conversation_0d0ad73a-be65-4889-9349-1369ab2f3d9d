package com.jsunicom.task.controller;

import com.alibaba.fastjson.JSON;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.task.feign.BcpOmsFeign;
import com.jsunicom.task.mapper.TestMapper;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.po.order.req.SendSmsReq;
import com.jsunicom.task.timer.order.SmsAndFaceAbilityJob;
import com.jsunicom.task.timer.order.SyncPayDetaiSchoolJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.InetAddress;
import java.net.URI;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Title: TestController
 * <AUTHOR>
 * @Package com.jsunicom.task
 * @Date 2023/12/18 17:48
 * @description:
 */
@Slf4j
@RestController
public class TestController {
    @Autowired
    TestMapper testMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private BcpOmsFeign bcpOmsFeign;

    @GetMapping("test")
    public void test(){
        log.info("kongWarnJob is starting work");

        TdMSysDict sysDict = new TdMSysDict();
        sysDict.setParamType("kongWarn");
        //    sysDict.setParamKey("kongWarn");
        //    TdMSysDict tdMSysDict = tdMSysDictMapper.query(sysDict);
        List<TdMSysDict> list = tdMSysDictMapper.queryAll(sysDict);
        for (int j = 0; j < list.size(); j++) {
            String sendPhone=list.get(j).getParam1();
            String url=list.get(j).getParamValue();
            String msgId=list.get(j).getParam2();
            String key=list.get(j).getParamKey();
            try{
                int code=reqGet(url);
                if(code==0){
                    log.info("kongWarnJob ====error"+"==url="+url);
                    SendSmsReq sendSmsReq = new SendSmsReq();
                    sendSmsReq.setTemplateId(msgId);
                    sendSmsReq.setSendTo(sendPhone);
                    sendSmsReq.setSendParam(key);
                    log.info("kongWarnJob ======sendSms="+sendSmsReq.toString());
                    bcpOmsFeign.sendSms(sendSmsReq);
                };
            }catch (Exception e){
                log.info("kongWarnJob ====url="+url+"error=="+e.getMessage());
                e.printStackTrace();

            }

        }
    }

    public static int  reqGet(String url) throws Exception{
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        int code=0;
        try {
            URIBuilder builder = new URIBuilder(url);
            URI uri = builder.build();
            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            log.info("kongWarnJob ==reqGet===url="+url+"===Code="+response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() == 200) {
                //    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                code=1;
                return code;
            }
        }catch (Exception e){
            log.info("kongWarnJob ==reqGet===url="+url+"error=="+e.getMessage());
            e.printStackTrace();
        } finally {
            httpclient.close();
        }
        return code;

    }


}
