package com.jsunicom.task.response;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/11 14:13
 * @Since: V1.00
 */
public class ResponseResult implements Serializable {
    private int status;
    private boolean success;
    private Object data;
    private String msg;
    private int count;

    public ResponseResult() {
    }

    public int getStatus() {
        return this.status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Object getData() {
        return this.data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCount() {
        return this.count;
    }

    public void setCount(int count) {
        this.count = count;
    }

}
