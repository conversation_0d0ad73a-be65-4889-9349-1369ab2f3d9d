package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tf.TfOrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TfOrderItemMapper {

    List<TfOrderItem> qryTfOrderItemByOrderIdAndAttrType(@Param("orderId") String orderId, @Param("attrType") String attrType);

    String qryIccidByOrderId(@Param("orderId") String orderId, @Param("attrType") String attrType);

    int insert(TfOrderItem record);

    int insertSelective(TfOrderItem record);

    List<TfOrderItem> qryOrderItems(TfOrderItem item);

    void updateTfOrdItem(TfOrderItem item);

    int countByOrderIdAndAttrCode(@Param("orderId") String orderId, @Param("propCode") String propCode);

    String qryGoodsItemAttrValue(@Param("orderId") String orderId, @Param("attrCode") String attrCode);

    List<TfOrderItem> qryOrderItemList(@Param("orderId") String orderId, @Param("attrCodeList") String[] attrCodeList,
                                       @Param("attrType") String attrType, @Param("attrValue") String attrValue);

}
