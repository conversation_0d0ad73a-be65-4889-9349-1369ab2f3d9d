package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tf.TfOrdGoodsMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TfOrdGoodsMainMapper{

    int deleteByPrimaryKey(String goodsOrderId);

    int insert(TfOrdGoodsMain record);

    int insertSelective(TfOrdGoodsMain record);

    TfOrdGoodsMain selectByPrimaryKey(String goodsOrderId);

    int updateByPrimaryKeySelective(TfOrdGoodsMain record);

    int updateByPrimaryKey(TfOrdGoodsMain record);

    List<TfOrdGoodsMain> selectByOrderId(@Param("orderId")String orderId);
}
