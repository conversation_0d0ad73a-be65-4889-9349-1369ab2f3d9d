package com.jsunicom.task.entity;

import java.sql.Timestamp;

public class SchoolListInfoDto {
    /*
    主键
     */
    private Long id;

    /*
   学校名称
    */
    private String schoolName;

    /*
   学校简称
    */
    private String schoolNameShort;

    /*
   学校所属地市编码
    */
    private String orgCode;

    /*
   学校所属地市名称
    */
    private String orgName;

    /*
   学校经理手机号
    */
    private String managerPhone;

    /*
   学校经理姓名
    */
    private String managerName;

    /*
   学校商户id
    */
    private Long merchantId;

    /*
   学校经理id
    */
    private Long schoolManagerId;

    /*
       学校信息创建时间
        */
    private Timestamp createTime;

    /*
   学校信息更新时间
    */
    private Timestamp updateTime;

    //团队长姓名
    private String partnerName;
    //团队长手机号
    private String mblNbr;

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getMblNbr() {
        return mblNbr;
    }

    public void setMblNbr(String mblNbr) {
        this.mblNbr = mblNbr;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSchoolName() {
        return schoolName;
    }

    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName;
    }

    public String getSchoolNameShort() {
        return schoolNameShort;
    }

    public void setSchoolNameShort(String schoolNameShort) {
        this.schoolNameShort = schoolNameShort;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getManagerPhone() {
        return managerPhone;
    }

    public void setManagerPhone(String managerPhone) {
        this.managerPhone = managerPhone;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Long getSchoolManagerId() {
        return schoolManagerId;
    }

    public void setSchoolManagerId(Long schoolManagerId) {
        this.schoolManagerId = schoolManagerId;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
