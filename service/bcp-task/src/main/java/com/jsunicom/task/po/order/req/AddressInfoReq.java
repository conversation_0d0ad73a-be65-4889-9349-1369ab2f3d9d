package com.jsunicom.task.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel(description = "订单修改请求报文收货信息")
public class AddressInfoReq  {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "省份编码", required = true, position = 1)
	private String provinceCode;

	@ApiModelProperty(value = "地市编码", required = true, position = 2)
	private String cityCode;

	@ApiModelProperty(value = "区县编码", required = true, position = 3)
	private String countyCode;

	@ApiModelProperty(value = "收货人", required = true, position = 4)
	private String receiveName;

	@ApiModelProperty(value = "联系电话", required = true, position = 5)
	private String receivePhone;

	@ApiModelProperty(value = "收货地址", required = true, position = 6)
	private String receiveAddress;

	@ApiModelProperty(value = "配送方式", required = true, position = 7)
	private String deliveryType;

    @ApiModelProperty(value = "物流员工号", required = true, position = 8)
    private String packStaffId;

}
