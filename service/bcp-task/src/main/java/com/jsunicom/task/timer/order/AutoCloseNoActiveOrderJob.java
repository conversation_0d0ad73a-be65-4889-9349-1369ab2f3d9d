package com.jsunicom.task.timer.order;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.common.core.util.IdUtil;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.task.feign.BcpOmsFeign;
import com.jsunicom.task.mapper.order.TfOrdLineMapper;
import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.mapper.order.TlOrdOperatorLogMapper;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.po.order.tl.TlOrdOperatorLog;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: AutoCloseNoActiveOrderJob
 * <AUTHOR>
 * @Package com.jsunicom.task.timer.order
 * @Date 2023/6/12 9:46
 * @description: 生产成功后，30天未激活（待资料上传），系统自动进入退单流程
 */

@Slf4j
@Component
public class AutoCloseNoActiveOrderJob {

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private TfOrdLineMapper tfOrdLineMapper;

    @Autowired
    private BcpOmsFeign bcpOmsFeign;

    @Autowired
    private TlOrdOperatorLogMapper tlOrdOperatorLogMapper;

    //0 30 0 * * ?
    @XxlJob("autoCloseNoActiveOrderJob")
    public ReturnT<String> autoCloseNoActiveOrderJob(String param) {
        log.info("=====autoCloseNoActiveOrderJob  start====");
        try {
            //查询30天未激活的订单
            List<Map<String, String>> noActiveOrderList = tfOrdMainMapper.queryNoActiveOrder();
            //自当年6月1日起，"完工归档时间（YYYYMMDD） - 下单时间（YYYYMMDD）"大于等于 30天的号码数量
            //因延长未激活退单时限而激活的号码的数量
            int nowCount = tfOrdMainMapper.queryActiveOrderNumsOverLimit();
            //预处理地市配置参数
            TdMSysDict sysDict = new TdMSysDict();
            sysDict.setParamType("schoolOrderAutoCloseConf");
            List<TdMSysDict> sysDictList = tdMSysDictMapper.queryAll(sysDict);
            Map<String, String> confMap = new HashMap<>();
            sysDictList.forEach(v -> {
                String cityCode = v.getParamKey();
                String param2 = v.getParam2();
                String param3 = v.getParam3();
                String param4 = v.getParam4();
                confMap.put(cityCode, param2 + "," + param3 + "," + param4);
            });
            log.info("confMap:{}", confMap);

            if (noActiveOrderList != null && noActiveOrderList.size() > 0) {
                for (Map<String, String> orderMap : noActiveOrderList) {
                    String eparchyCode = orderMap.get("EPARCHY_CODE");
                    String inModeCode = orderMap.get("IN_MODE_CODE");
                    String busiType = orderMap.get("BUSI_TYPE");
                    String orderId = orderMap.get("ORDER_ID");
                    if (StringUtils.isNotBlank(confMap.get(eparchyCode))) {
                        String[] data = confMap.get(eparchyCode).split(",");
                        //地市清单生效时间
                        long startDate = DateUtil.parse(data[0], "yyyy-MM-dd").getTime();
                        long endDate = DateUtil.parse(data[1], "yyyy-MM-dd").getTime();

                        //限制超时激活而不退单的数量, 所有地市统一配置
                        int countLimit = Integer.parseInt(data[2]);
                        long now = System.currentTimeMillis();
                        boolean flag = (now >= startDate && now <= endDate);
                     /*
                            对原有30天自动退单逻辑进行改造，针对系统来源为展业(0601)、业务类型为移网标准(01)且下单角色为校园用户的（operRole为CT08）的订单，
                            根据地市、生失效时间以及全省超30天激活订单量（自当年6月1日起，"完工归档时间（YYYYMMDD） - 下单时间（YYYYMMDD）"
                            大于等于 30天的号码即认为是因延长未激活退单时限而激活的号码），如果符合条件则不进行自动退单。
                        */
                        if (nowCount < countLimit && flag && "0601".equals(inModeCode) && "01".equals(busiType)) {
                            continue;
                        }
                    }
                    log.info("【" + orderId + "】订单将进入退单流程");
                    try {
                        //记录订单轨迹
                        TlOrdOperatorLog tlOrdOperatorLog = new TlOrdOperatorLog();
                        tlOrdOperatorLog.setLogId(IdUtil.getOrderIdNew());
                        tlOrdOperatorLog.setOrderId(orderId);
                        tlOrdOperatorLog.setBusiType("chargeback");
                        tlOrdOperatorLog.setReasonCode("chargeback");
                        tlOrdOperatorLog.setReasonName("【" + orderId + "】超过三十天未激活，系统自动退单！");
                        tlOrdOperatorLog.setReasonDesc("【" + orderId + "】超过三十天未激活，系统自动退单！");
                        tlOrdOperatorLog.setOperatorId("admin");
                        tlOrdOperatorLog.setOperatorName("");
                        tlOrdOperatorLog.setOperatorPhone("");
                        tlOrdOperatorLog.setNodeCode("chargeback");
                        tlOrdOperatorLog.setNodeName("自动退单");
                        tlOrdOperatorLog.setUpdateTime(new Date());
                        tlOrdOperatorLog.setOrderLineId("");
                        tlOrdOperatorLogMapper.insertTlOrdOperatorLog(tlOrdOperatorLog);

                        JSONObject params = new JSONObject();
                        params.put("orderId", orderId);
                /*    params.put("nodeCode","checkPhoto");
                    params.put("nodeName","资料补录");*/
                        Result result = bcpOmsFeign.orderRefundFeign(params);
                        if (result.getSuccess()) {
                            log.info("【" + orderId + "】订单进入退单流程成功");
                        } else {
                            log.info("【" + orderId + "】订单进入退单流程异常，异常信息：" + result.getMsg());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("【" + orderId + "】订单进入退单流程异常，异常信息：" + e.getMessage());
                    }
                }
            }
            log.info("=====autoCloseNoActiveOrderJob  end====");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("autoCloseNoActiveOrderJobException:{}", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
