package com.jsunicom.task.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TdOrdPayOperInfoExt extends TdOrdPayOperInfo {

//    @Column(name = GOODS_TYPE)
    private String goodsType;
    private String staffId;
    private String eparchyCode;
    private String eparchyName;
    private String payPointName;
    private String staffName;
    private String bssPasswd;
    private String cbssPasswd;
    private String channelCode;
    private String staffType;
    private String payOrg;
    private String bssDepartCode;

}
