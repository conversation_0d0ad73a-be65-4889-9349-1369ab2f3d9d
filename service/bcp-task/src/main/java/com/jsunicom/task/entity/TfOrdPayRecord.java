package com.jsunicom.task.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

//TF_ORD_PAY_RECORD
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TfOrdPayRecord {
    private static final long serialVersionUID = 1L;

    private String payId;


    private String orderId;


    private BigDecimal fee;


    private Date payTime;


    private String serialNumber;

    /**
     *O:缴费,C:返销
     */
    private String operTag;

    /**
     *0:成功,1:失败,2:初始化
     */
    private String operResult;

    /**
     *失败原因
     */
    private String operMsg;

    /**
     *null
     */
    private String chargeId;

    /**
     *null
     */
    private Date respTime;

    /**
     *null
     */
    private Date createDate;

    /**
     *执行次数
     */
    private String payNum;

    /**
     *null
     */
    private String eparchyCode;

    /**
     *null
     */
    private String feeCode;

    private String goodsType;
    private String busOrderId;

}

