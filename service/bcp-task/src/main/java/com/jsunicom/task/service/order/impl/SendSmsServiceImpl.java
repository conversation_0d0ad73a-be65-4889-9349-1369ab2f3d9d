package com.jsunicom.task.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.entity.response.Rsp;
import com.jsunicom.common.core.util.IdUtil;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.task.common.constants.CommonProperties;
import com.jsunicom.task.entity.TdOrdSysDict;
import com.jsunicom.task.mapper.order.SmsMapper;
import com.jsunicom.task.mapper.order.TlOrdSmsLogMapper;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.mapper.wobuy.WoBuyMapper;
import com.jsunicom.task.po.order.TdOrdSmsTemplate;
import com.jsunicom.task.po.order.req.SendSmsReq;
import com.jsunicom.task.po.order.rsp.SendSmsRsp;
import com.jsunicom.task.po.order.tl.TlOrdSmsLog;
import com.jsunicom.task.service.order.SendSmsService;
import com.jsunicom.task.utils.DateUtils;
import com.jsunicom.task.utils.HttpUtil;
import com.jsunicom.task.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class SendSmsServiceImpl implements SendSmsService {

    @Autowired
    private CommonProperties commonProperties;

    @Autowired
    private SmsMapper smsMapper;

    @Autowired
    private TlOrdSmsLogMapper tlOrdSmsLogMapper;
    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;
    @Autowired
    private WoBuyMapper woBuyMapper;

    final String systemId = "10000067";
    final String moduleId = "20000146";
    final String busiCode = "30000111";
    final String appId = "WhdbEDRSZ6";
    final String appSecret = "YCSvVDYMzWyxnwyt";
    final String sourceFrom="25";

    @Override
    public String checkNumberNet(JSONObject param) throws Exception {
        log.info("checkNumberNet reqParam:{}", param);
        JSONObject uniBssBody = new JSONObject();
        JSONObject bobyParam = new JSONObject();
        bobyParam.put("SERIAL_NUMBER", param.getString("serialNumber"));
        uniBssBody.put("CHECK_NUMBER_NET_REQ", bobyParam);
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        String url = commonProperties.getCheckNumberNetUrl();
        log.info("checkNumberNetUrl :{}", url);
        String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached, "checkNumberNet", param.getString("orderId"));
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        log.info("checkNumberNet rep:{}", responseJson);
        JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
        if (!head.getString("RESP_CODE").equals("00000")) {
            throw new RuntimeException("号码校验失败！");
        }
        JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
        JSONObject checkNumberNetRsp = body.getJSONObject("CHECK_NUMBER_NET_RSP");
        String respCode = checkNumberNetRsp.getString("RESP_CODE");
        if (!"0000".equals(respCode)) {
            throw new RuntimeException(checkNumberNetRsp.getString("RESP_DESC"));
        }
        //号码网络归属 ：0 非本网号码1 是本网号码
        log.info("numStatus:{}", checkNumberNetRsp.getString("NUM_STATUS"));
        return checkNumberNetRsp.getString("NUM_STATUS");

    }

    @Override
    public Result noticeSmsindustry(JSONObject param) throws Exception {
        log.info("本网短信发送接口接收参数:{}", param);
        Result result = new Result();
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("SMSINDUSTRY_REQ", assembleIndustryParam(param));
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        String url = commonProperties.getSmsindustryUrl();
        log.info("noticeSmsindustrytUrl :{}", url);
        String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached, "noticeSmsindustry", param.getString("orderId"));
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        log.info("noticeSmsindustry rep:{}", responseJson);
        JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
        if (!head.getString("RESP_CODE").equals("00000")) {
            result.setMsg("短信发送失败！");
            result.setSuccess(false);
            return result;
        }
        JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
        JSONObject smsindustryRsp = body.getJSONObject("SMSINDUSTRY_RSP");
        JSONObject rsp = smsindustryRsp.getJSONObject("RSP");
        if (!"0000".equals(rsp.getString("RSP_CODE"))) {
            result.setMsg(rsp.getString("RSP_DESC"));
            result.setCode(Long.valueOf(rsp.getString("RSP_CODE")));
            result.setSuccess(false);
            return result;
        }
        result.setSuccess(true);
        result.setMsg(rsp.getString("RSP_DESC"));
        result.setCode(Long.valueOf(rsp.getString("RSP_CODE")));
        return result;
    }

    private JSONObject assembleIndustryParam(JSONObject param) {
        JSONObject jsonObject = new JSONObject();
        JSONObject object = new JSONObject();
        object.put("APP_ID", commonProperties.getSmsAppId());
        object.put("MOBILE_NUMBER", param.getString("phone"));
        object.put("APP_SECRET", commonProperties.getSmsAppSecret());
        object.put("CONTENT", param.getString("content"));
        object.put("MSG_TYPE", "9");//消息类型消息类型 ( 短信 9 | 随沃行日程提醒 32 | 钉钉工作通知 5 | 邮件６ )
        object.put("CIP", "0");//是否加密
        object.put("PROV_CODE", "34");
        jsonObject.put("INDUSTRY_SMS_REQ", object);
        return jsonObject;
    }

    @Override
    public Result<SendSmsRsp> sendSms(SendSmsReq sendSmsReq) {
        SendSmsRsp sendSmsRsp = new SendSmsRsp();
        Result result = new Result();
        try {
            Rsp smsRsp = sendMessage(sendSmsReq);
            //记录短信日志
            insertTlOrdSmsLog(smsRsp,sendSmsReq);
            sendSmsRsp.setCode(200);
            JSONObject datas = new JSONObject();
            datas.put("content", smsRsp.getMsg());
            sendSmsRsp.setDatas(datas);
            result.setSuccess(true);
            result.setData(sendSmsRsp);
        } catch (Exception e) {
            e.printStackTrace();
            sendSmsRsp.setCode(9999);
            sendSmsRsp.setError(e.getMessage());
            result.setSuccess(false);
            result.setMsg("短信发送失败");
        }

        return result;
    }

    private void insertTlOrdSmsLog(Rsp smsRsp,SendSmsReq sendSmsReq) {
        TlOrdSmsLog tlOrdSmsLog = new TlOrdSmsLog();
        String data = (String) smsRsp.getData();
        if (StringUtils.isNotBlank(data)){
            String[] split = data.split("\\|");
            tlOrdSmsLog.setRequestMsg(split[0]);
            tlOrdSmsLog.setResponseMsg(split[1]);
        }
        tlOrdSmsLog.setId(IdUtil.getOrderIdNew());
        tlOrdSmsLog.setLogTime(new Date());
        tlOrdSmsLog.setOrderId(sendSmsReq.getOrderId());
        tlOrdSmsLog.setMsgId(sendSmsReq.getTemplateId());
        tlOrdSmsLog.setMsgContent(smsRsp.getMsg());
        tlOrdSmsLog.setSmsFlag(smsRsp.getCode());
        tlOrdSmsLogMapper.insertSelective(tlOrdSmsLog);
    }

    /**
     * 发送消息
     *
     * @param sendSmsReq
     * @return
     */
   public Rsp sendMessage(SendSmsReq sendSmsReq) throws Exception {
        String provinceCode="";
        String numberNet="2";
        String operatorId="A0009194";
        String city="340";
        String channelId="34a1432";
        String channelType="1010600";
        Map<String,Object> map=new HashMap<>();
        map.put("OPERATOR_ID",operatorId);
        map.put("CITY",city);
        map.put("CHANNEL_ID",channelId);
        map.put("CHANNEL_TYPE",channelType);
        String[] phoneNumbers = sendSmsReq.getSendTo().split(",");
        TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
        String templateType = tdOrdSmsTemplate.getTemplateType();
        Rsp smsRsp = new Rsp();
        smsRsp.setSuccess(false);
        smsRsp.setCode("8888");
        if ("2".equals(templateType)) {
            for (String phone : phoneNumbers) {
                Rsp serialCheck = checkNumNet(phone, sendSmsReq);
                if (!serialCheck.isSuccess()) {
                    return serialCheck;
                }
                String checkNumNet = serialCheck.getMsg();
                if ("0".equals(checkNumNet)) {
                    smsRsp =send(sendSmsReq,phone,numberNet); ;//异网发送
                } else {
                    smsRsp = inNetSend(sendSmsReq, phone);//本网发送
                }
            }
            return smsRsp;
        }else if("1".equals(templateType)){
            for (String phone : phoneNumbers) {
                try {
                    JSONObject rspData = checkUserInfo(phone, sendSmsReq.getOrderId(), map);
                    if(rspData.getString("rspCode").equals("0000")){
                        if(rspData.get("data")!=null){
                            JSONObject data= rspData.getJSONObject("data");
                            if(data.containsKey("custInfo") && data.containsKey("userInfo")){
                                provinceCode=data.getJSONObject("userInfo").getString("provinceCode");
                                log.info("省份编码"+provinceCode);
                            }
                        }
                    }
                    if("34".equals(provinceCode)){
                        numberNet="1";//本网发送
                    }else {
                        numberNet="2";//异网发送
                    }
                    smsRsp=send(sendSmsReq,phone,numberNet);
                }catch (Exception e){
                    log.info(e.getMessage(),e);

                }

            }
            return smsRsp;
        }else{
            for(String  phone:phoneNumbers){
                smsRsp=sendCrossNetSms(sendSmsReq,phone);
            }
            return smsRsp;
        }
   }

  /*  public Rsp sendMessage(SendSmsReq sendSmsReq)throws Exception {
        Rsp smsRsp = new Rsp();
        smsRsp.setSuccess(false);
        smsRsp.setCode("9999");
        String[] phoneNumbers=sendSmsReq.getSendTo().split(",");
        TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
        String templateType = tdOrdSmsTemplate.getTemplateType();
        if ("2".equals(templateType)) {
            for(String  phone:phoneNumbers){
                Rsp serialCheck  = checkNumNet(phone, sendSmsReq);
                if (!serialCheck.isSuccess()) {
                    return serialCheck;
                }
                String checkNumNet = serialCheck.getMsg();
                if("0".equals(checkNumNet)){
                    smsRsp=sendCrossNetSms(sendSmsReq,phone);

                }else{
                    smsRsp = inNetSend(sendSmsReq, phone);//本网发送
                }
            }
            return smsRsp;

        }else{
            for(String  phone:phoneNumbers){
                smsRsp=sendCrossNetSms(sendSmsReq,phone);
            }
            return smsRsp;
        }
    }*/

    public Rsp sendCrossNetSms(SendSmsReq sendSmsReq, String phoneNum) throws Exception {
        Rsp result = new Rsp();
        result.setCode("9999");
        result.setSuccess(false);
        if (null != sendSmsReq) {
            TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
            if (null != tdOrdSmsTemplate) {
                JSONObject input = new JSONObject();
                input.put("SERIAL_NUMBER", phoneNum);
                input.put("SOURCE_FROM", sourceFrom);
                input.put("TEMPLATE_ID", tdOrdSmsTemplate.getMsgId());
                input.put("PARAM", assemblyParameters(tdOrdSmsTemplate, sendSmsReq));
                JSONObject uniBssAttached = new JSONObject();
                uniBssAttached.put("MEDIA_INFO", "");
                JSONObject uniBssBody = new JSONObject();
                uniBssBody.put("SMS_REQ", input);
                TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey("getApiInfo", "sendCrossNetSms");
                String url=dict.getParam1();
                String responseBody = HttpUtil.sendRequest2(dict.getParam2(),dict.getParam3(),url, uniBssBody, uniBssAttached,"sendCrossNetSms",sendSmsReq.getOrderId());
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                log.info("sendCrossNetSms TASK短信接口返回：{}",jsonObject.toJSONString());
                if (null != jsonObject) {
                    JSONObject UNI_BSS_HEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
                    String RESP_CODE = UNI_BSS_HEAD.getString("RESP_CODE");
                    if (!"00000".equals(RESP_CODE)) {
                        result.setCode(RESP_CODE);
                        result.setMsg(UNI_BSS_HEAD.getString("RESP_DESC"));
                        result.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                    } else {
                        JSONObject uniBssBody1 = jsonObject.getJSONObject("UNI_BSS_BODY");
                        JSONObject smsRsp = uniBssBody1.getJSONObject("SMS_RSP");
                        String status=smsRsp.getString("respCode");
                        if ("0000".equals(status)){
                            result.setCode(status);
                            result.setSuccess(true);
                            result.setMsg(getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContentYw()));
                        }else {
                            result.setCode(status);
                            result.setMsg(smsRsp.getString("respDesc"));
                        }
                        result.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                    }
                }
            } else {
                result.setMsg("从表里获取TdOrdSmsTemplate短信模板失败");
               return result;
            }
        } else {
            result.setMsg("sendSmsReq为空");
            return result;
        }
        return result;
    }

    private JSONObject assemblyParameters(TdOrdSmsTemplate tdOrdSmsTemplate, SendSmsReq sendSmsReq) {
        String sendParamsYw = tdOrdSmsTemplate.getSendParamsYw();
        List<String> sendParamsKey = Arrays.asList(sendParamsYw.split("\\|"));
        List<String> sendParamsValue = Arrays.asList(sendSmsReq.getSendParam().split("\\|"));
        JSONObject data=new JSONObject();
        for (int i = 0; i < sendParamsKey.size(); i++) {
            data.put(sendParamsKey.get(i),sendParamsValue.get(i));
        }
        return data;
    }




    /**
     * 校验本网异网
     *
     * @param phoneNum
     * @return
     */
    public Rsp checkNumNet(String phoneNum, SendSmsReq sendSmsReq) {
        Rsp rsp = new Rsp();
        JSONObject input = new JSONObject();
        input.put("SERIAL_NUMBER", phoneNum);
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("CHECK_NUMBER_NET_REQ", input);
        try {
            String responseBody = HttpUtil.sendRequest(commonProperties.getCheckNumberNetUrl(), uniBssBody, uniBssAttached, "checkNumberNet", sendSmsReq.getOrderId());
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            if (null != jsonObject) {
                JSONObject UNI_BSS_HEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
                String RESP_CODE = UNI_BSS_HEAD.getString("RESP_CODE");
                if (!"00000".equals(RESP_CODE)) {
                    rsp.setCode(RESP_CODE);
                    rsp.setSuccess(false);
                    rsp.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                    log.info("checkNumNetData:{}",JSON.toJSONString(uniBssBody)+"|"+responseBody);
                } else {
                    JSONObject uniBssBody1 = jsonObject.getJSONObject("UNI_BSS_BODY");
                    JSONObject checkNumberNetRsp = uniBssBody1.getJSONObject("CHECK_NUMBER_NET_RSP");
                    String respCode = checkNumberNetRsp.getString("RESP_CODE");
                    if ("0000".equals(respCode)){
                        String numStatus = checkNumberNetRsp.getString("NUM_STATUS");
                        rsp.setMsg(numStatus);
                        rsp.setSuccess(true);
                    }else {
                        rsp.setCode(respCode);
                        rsp.setSuccess(false);
                    }
                    rsp.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                }
            }
        } catch (Exception e) {
            log.error("checkNumNet号码本异网校验异常：{}，{}",e,e.getMessage());
            e.printStackTrace();
        }
        return rsp;
    }

    /**
     * 异网消息发送
     *
     * @param sendSmsReq
     * @return
     */
    public Rsp otherNetSend(SendSmsReq sendSmsReq, String phoneNum) throws Exception {
        Rsp result = new Rsp();
        TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
        if (null != tdOrdSmsTemplate) {
            JSONObject input = new JSONObject();
            input.put("SYSTEM_ID", systemId);
            input.put("MODULE_ID", moduleId);
            input.put("BUSI_CODE", busiCode);
            input.put("NOTICE_CONTENT", tdOrdSmsTemplate.getMsgId());

            input.put("DATA", getOtherNetSendContent(tdOrdSmsTemplate.getSendParamsYw(), sendSmsReq.getSendParam(), tdOrdSmsTemplate.getMsgId(), phoneNum));
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            JSONObject uniBssBody = new JSONObject();
            uniBssBody.put("NOTICEINFO", input);
            JSONObject uniBssBody2 = new JSONObject();
            uniBssBody2.put("SMSGATEWAY_REQ", uniBssBody);
            String responseBody = null;
            try {
                responseBody = HttpUtil.sendRequest(commonProperties.getSmsgatewayUrl(), uniBssBody2, uniBssAttached, "smsgateway", sendSmsReq.getOrderId());
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (null != jsonObject) {
                    JSONObject UNI_BSS_HEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
                    String RESP_CODE = UNI_BSS_HEAD.getString("RESP_CODE");
                    if (!"00000".equals(RESP_CODE)){
                        result.setCode(RESP_CODE);
                        result.setSuccess(false);
                        result.setData(JSON.toJSONString(uniBssBody2)+"|"+responseBody);
                    }else {
                        JSONObject uniBssBody1 = jsonObject.getJSONObject("UNI_BSS_BODY");
                        JSONObject smsindustryRsp = uniBssBody1.getJSONObject("SMSGATEWAY_RSP");
                        JSONObject rsp = smsindustryRsp.getJSONObject("RSP");
                        if (rsp != null) {
                            String code = rsp.getString("RSP_CODE");
                            result.setCode(code);
                            result.setSuccess("0000".equals(code));
                            result.setMsg(getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContentYw()));
                            result.setData(JSON.toJSONString(uniBssBody2)+"|"+responseBody);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("otherNetSend异网短信发送失败:{},{}", e.getMessage(), e);
            }
        } else {
            throw new Exception("未匹配到消息模板");
        }
        return result;
    }

	    public Rsp send(SendSmsReq sendSmsReq, String phoneNum,String numberNet) throws Exception {
        Rsp result = new Rsp();
        result.setCode("8888");
        result.setSuccess(false);
        TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
        if (null != tdOrdSmsTemplate) {
            JSONObject input = new JSONObject();
            TdMSysDict dict = new TdMSysDict();

            if("1".equals(numberNet)){
                try {
                    dict.setParamType("smsSpNumber");
                    dict.setParamKey("inNet");
                    TdMSysDict query = tdMSysDictMapper.query(dict);
                    input.put("SP_NUMBER",query.getParamValue());
                    input.put("CONTENT", getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContent()));
                    input.put("TEMPLATE_ID",tdOrdSmsTemplate.getMsgId());
                }catch (Exception e){
                    log.info("从字典表中获取信息失败:"+e.getMessage());
                }

            }else {
                try {
                    dict.setParamType("smsSpNumber");
                    dict.setParamKey("outNet");
                    TdMSysDict query = tdMSysDictMapper.query(dict);
                    input.put("SP_NUMBER",query.getParamValue());
                    input.put("CONTENT", getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContentYw()));
                    input.put("TEMPLATE_ID",query.getParam1());

                }catch (Exception e){
                    log.info("从字典表中获取信息失败:"+e.getMessage());
                }

            }

            input.put("SOURCE_FROM",sourceFrom);
            input.put("SERIAL_NUMBER", phoneNum);
            input.put("NUMBER_NET", numberNet);
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            JSONObject uniBssBody = new JSONObject();
            uniBssBody.put("SMS_REQ", input);

            String responseBody = null;
            try {
                TdOrdSysDict tdOrdSysDict = woBuyMapper.getSysDictByTypeAndKey("getApiInfo", "SmsPlatform_smsSend");
                String url = tdOrdSysDict.getParam1();
                responseBody = HttpUtil.sendRequest2(tdOrdSysDict.getParam2(), tdOrdSysDict.getParam3(),url,uniBssBody, uniBssAttached, "SmsPlatform_smsSend",sendSmsReq.getOrderId());
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (null != jsonObject) {
                    JSONObject UNI_BSS_HEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
                    String RESP_CODE = UNI_BSS_HEAD.getString("RESP_CODE");
                    if (!"00000".equals(RESP_CODE)){
                        result.setCode(RESP_CODE);
                        result.setSuccess(false);
                        result.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                    }else {
                        JSONObject uniBssBody1 = jsonObject.getJSONObject("UNI_BSS_BODY");
                        JSONObject smsRsp = uniBssBody1.getJSONObject("SMS_RSP");
                        String status=smsRsp.getString("STATUS");
                        String msg = smsRsp.getString("MSG");
                        result.setSuccess("00000".equals(status));
                        if("1".equals(numberNet)){
                            result.setMsg(getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContent()));
                        }else{
                            result.setMsg(getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContentYw()));
                        }
                        result.setData(JSON.toJSONString(uniBssBody)+"|"+responseBody);
                    }
                }
            } catch (Exception e) {
                log.info("send短信发送失败:"+e.getMessage());
            }
        } else {
            throw new Exception("未匹配到消息模板");
        }
        return result;
    }


    /**
     * 本网消息发送
     *
     * @param sendSmsReq
     * @return
     */
    public Rsp inNetSend(SendSmsReq sendSmsReq, String phoneNum) throws Exception {
        Rsp result = new Rsp();
        TdOrdSmsTemplate tdOrdSmsTemplate = selectOrderSmsTemplateByTemplateId(sendSmsReq.getTemplateId());
        String content = "";
        if (null != tdOrdSmsTemplate) {
            JSONObject input = new JSONObject();
            content = getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContent());
            input.put("MSG_TYPE", "9");
            input.put("CONTENT", getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContent()));
            input.put("CIP", "false");
            input.put("APP_ID", commonProperties.getSmsAppId());
            input.put("APP_SECRET", commonProperties.getSmsAppSecret());
            input.put("MOBILE_NUMBER", phoneNum);
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            JSONObject uniBssBody = new JSONObject();
            uniBssBody.put("INDUSTRY_SMS_REQ", input);
            JSONObject uniBssBody2 = new JSONObject();
            uniBssBody2.put("SMSINDUSTRY_REQ", uniBssBody);
            String responseBody = null;
            try {
                responseBody = HttpUtil.sendRequest(commonProperties.getSmsindustryUrl(), uniBssBody2, uniBssAttached, "smsindustry", sendSmsReq.getOrderId());
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (null != jsonObject) {
                    JSONObject UNI_BSS_HEAD = jsonObject.getJSONObject("UNI_BSS_HEAD");
                    String RESP_CODE = UNI_BSS_HEAD.getString("RESP_CODE");
                    if (!"00000".equals(RESP_CODE)){
                        result.setCode(RESP_CODE);
                        result.setSuccess(false);
                        result.setData(JSON.toJSONString(uniBssBody2)+"|"+responseBody);
                    }else {
                        JSONObject uniBssBody1 = jsonObject.getJSONObject("UNI_BSS_BODY");
                        JSONObject smsindustryRsp = uniBssBody1.getJSONObject("SMSINDUSTRY_RSP");
                        JSONObject rsp = smsindustryRsp.getJSONObject("RSP");
                        if (rsp != null) {
                            String code = rsp.getString("RSP_CODE");
                            result.setCode(code);
                            result.setSuccess("0000".equals(code));
                            result.setMsg(getInNetSendContent(sendSmsReq.getSendParam(), tdOrdSmsTemplate.getContent()));
                            result.setData(JSON.toJSONString(uniBssBody2)+"|"+responseBody);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("inNetSend本网短信发送失败:{},{}", e.getMessage(), e);
            }
        } else {
            throw new Exception("未匹配到消息模板");
        }
        return result;
    }

    /**
     * 获取本网发送内容
     *
     * @param sendParam 高浩宇|15651601820
     * @param content
     * @return
     */
    public String getInNetSendContent(String sendParam, String content) {
        String[] sendParams = sendParam.split("\\|");
        for (String param : sendParams) {
            //用入参替换字符串中的{}
            content = content.replaceFirst("\\{\\}", param);
        }
        return content;
    }

    /**
     * 获取异网发送内容 是一个json 的数组
     *
     * @param sendParam 高浩宇|15651601820
     * @param codeList
     * @return
     */
    public List<JSONObject> getOtherNetSendContent(String codeList, String sendParam, String msgId, String phone) {
        List<JSONObject> datas = new ArrayList<>();
        JSONObject data = new JSONObject();
        data.put("SMS_NOTICE_ID", msgId);
        data.put("RECV_CONTENT", msgId);
        data.put("RECV_OBJECT", phone);
        List<JSONObject> paramlists = new ArrayList();
        String[] sendParams = sendParam.split("\\|");
        String[] codes = codeList.split("\\|");
        for (int i = 0; i < codes.length; i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("PARA_ID", codes[i]);
            jsonObject.put("PARA_VALUE", sendParams[i]);
            paramlists.add(jsonObject);
        }
//        JSONObject jsonObject =new JSONObject();
//        jsonObject.put("PARA_ID","dealStaffPhone");
//        jsonObject.put("PARA_VALUE",phone);
//        paramlists.add(jsonObject);
        data.put("PARAM_LIST", paramlists);
        datas.add(data);
        return datas;
    }

    /**
     * 获取短信模板信息
     *
     * @param templateId
     * @return
     */
    public TdOrdSmsTemplate selectOrderSmsTemplateByTemplateId(String templateId) {
        return smsMapper.selectByTemplateId(templateId);
    }

    @Override
    public boolean sendSms(String msgId, JSONObject smsParams) {
        String params = "";
        String sendPhone = "";
        boolean isSend = true;
        String orderId = smsParams.getString("orderId");
        String orderNode = smsParams.getString("orderNode");
        String nowDate = DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");//当前日期
        String contactPhone = smsParams.getString("contactPhone");//用户联系电话
        String serialNumber = smsParams.getString("serialNumber");//业务号码
        String nodeState = StringUtil.ifNullGetDefualt(smsParams.getString("nodeState"), "01");
        String sendTo = smsParams.getString("sendTo");
        String sendParams = "";
        String busiDesc = "";
        String orderDate = smsParams.getString("orderDate");//订单日期
        String staffId = smsParams.getString("staffId");//转派人工号
        String checkPhotoUrl = smsParams.getString("checkPhotoUrl");//转派人工号

        SendSmsReq sendSmsReq = new SendSmsReq();
        sendSmsReq.setTemplateId(msgId);
        sendSmsReq.setSendTo(sendTo);
        sendSmsReq.setOrderId(orderId);
        if (msgId.equals("10001")) {
            sendParams = serialNumber;
        } else if (msgId.equals("10002")) {
            sendParams = orderId + "|" + busiDesc + "|" + orderDate;
        } else if ("10003".equals(msgId)) {
            sendParams = orderId + "|" + nowDate + "|" + staffId + "|" + serialNumber;
        } else if (msgId.equals("10004")) {
            sendParams = serialNumber;
        } else if ("10005".equals(msgId)) {
            sendParams = serialNumber;
        } else if ("10006".equals(msgId)) {
            sendParams = checkPhotoUrl;
        } else if ("10007".equals(msgId)) {
            sendParams = serialNumber;
        } else if ("10008".equals(msgId)) {
            sendParams = serialNumber;
        }else if ("20003".equals(msgId) || "20004".equals(msgId) || "20005".equals(msgId)) {
            sendParams = smsParams.getString("sendParams");
        }
        sendSmsReq.setSendParam(sendParams);
        Result<SendSmsRsp> result = this.sendSms(sendSmsReq);
        //未完，待续写

        return result.getSuccess();
    }

	    private JSONObject checkUserInfo(String serialNumber,String orderId,Map<String, Object> op)throws Exception{
        JSONObject dataUser =new JSONObject();
        String rspCode="8888";
        String rspDesc="";
        try{
            JSONObject uniBssBody = new JSONObject();
            JSONObject uniBssReq = new JSONObject();
            JSONObject msg = new JSONObject();
            String operatorId=op.get("OPERATOR_ID").toString();
            String city=op.get("CITY").toString();
            String channelId=op.get("CHANNEL_ID").toString();
            String channelType=op.get("CHANNEL_TYPE").toString();
            String url = commonProperties.getSpthreepartcheck();

            msg.put("serialNumber",serialNumber);
            msg.put("province", "34");
            msg.put("city",city);
            msg.put("district",city);
            msg.put("channelType", channelType);
            msg.put("infoList", "CUST");
            msg.put("operatorId", operatorId);
            msg.put("channelId", channelId);
            msg.put("tradeTypeCode", "9999");
            uniBssReq.put("MSG", msg);
            uniBssReq.put("APPKEY", "jsordcen");
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            uniBssReq.put("APPTX", df.format(new Date()));
            uniBssBody.put("SP_THREEPART_CHECK_REQ", uniBssReq);
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");

            String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached,"spthreepartcheck");
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
            if (StringUtils.isNotBlank(head.getString("RESP_CODE"))) {
                if (!head.getString("RESP_CODE").equals("00000")) {
                    rspDesc=head.getString("RESP_DESC");
                }else{
                    //调用成功
                    JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
                    JSONObject rsp = body.getJSONObject("SP_THREEPART_CHECK_RSP").getJSONObject("RSP");
                    String code=rsp.getString("RSP_CODE");
                    if(!"0000".equals(code)){
                    }else{
                        rspCode="0000";
                        JSONArray list=rsp.getJSONArray("DATA");
                        if(list.size()>0){
                            JSONObject rspData=(JSONObject)list.get(0);
                            dataUser.put("rspCode",rspCode);
                            dataUser.put("rspDesc",rspDesc);
                            dataUser.put("data",rspData);
                            return dataUser;

                           /* if(rspData.containsKey("custInfo") && rspData.containsKey("userInfo")){

                                dataUser.put("eparchyCode",rspData.getJSONObject("userInfo").getString("eparchyCode"));
                                dataUser.put("certCode",rspData.getJSONObject("custInfo").getString("custInfo"));
                                dataUser.put("custName",rspData.getJSONObject("custInfo").getString("custName"));
                            }*/

                        }

                    }
                }
            } else {
                rspDesc="三户资料查询，响应报文头中的RESP_CODE为空";
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception("orderId" + orderId + "三户校验失败:" + e.getMessage());
        }
        dataUser.put("rspCode",rspCode);
        dataUser.put("rspDesc",rspDesc);
        return dataUser;
    }


    @Override
    public void saveSmsWork(Map<String, String> param) {
        smsMapper.insertSmsWork(param);

    }
}
