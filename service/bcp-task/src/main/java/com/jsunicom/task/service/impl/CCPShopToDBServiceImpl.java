package com.jsunicom.task.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.jsunicom.task.entity.TdOrdSysDict;
import com.jsunicom.task.mapper.CCPShopToDBMapper;
import com.jsunicom.task.mapper.SchoolCcpShopTableMapper;
import com.jsunicom.task.mapper.wobuy.WoBuyMapper;
import com.jsunicom.task.po.SchoolCcpShopTable;
import com.jsunicom.task.po.SchoolCcpShopTableExample;
import com.jsunicom.task.service.CCPShopToDBService;
import com.jsunicom.task.utils.SFTPKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-09-14-11:08
 */
@Service
@Slf4j
public class CCPShopToDBServiceImpl implements CCPShopToDBService {
    @Autowired
    private SchoolCcpShopTableMapper schoolCcpShopTableMapper;
    @Resource
    private WoBuyMapper woBuyMapper;
    @Autowired
    private CCPShopToDBMapper ccpShopToDBMapper;

    @Override
    public void cCPShopToDBJob() {
        TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey("ftpDownload", "downloadToDB");
        String sftpIp = dict.getParam1();
        String sftpPort = dict.getParam2();
        String sftpUserName = dict.getParam3();
        String sftpPassword = dict.getParam4();
        String downLoadDire = dict.getParam5();
        String localPath = dict.getParamValue();
        String checkMsg = "DWD_D_MRT_CCP_SHOP_"+getTimeStr()+"";
        log.info("download file sftpIp:{},sftpPort:{},sftpUserName:{},sftpPassword:{},downLoadDire:{},localPath:{}",sftpIp,sftpPort,sftpUserName,sftpPassword,downLoadDire,localPath);
        List<String> fileList = SFTPKit.batchDownLoadFile(sftpIp, Integer.parseInt(sftpPort), sftpUserName, sftpPassword, downLoadDire, localPath, checkMsg);
        log.info("pullReconiliation fileList size is={}",fileList.size());
        for(String fileName:fileList){
            if (fileName.contains(".MD5")){
                continue;
            }
            List<SchoolCcpShopTable> arrayList = new ArrayList<>();
            String fileInfo = localPath +"/"+ fileName;
            log.info("pullSchoolCcpShop fileInfo==>"+fileInfo);
            // 判断文件是否处理过
            SchoolCcpShopTableExample example1=new SchoolCcpShopTableExample();
            example1.createCriteria().andFileNameEqualTo(fileName);
            long qryFileName = schoolCcpShopTableMapper.countByExample(example1);
            if(qryFileName>0){
                continue;
            }else{
                File file = new File(fileInfo);
                BufferedReader reader = null;
                try {
                    reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
                    String tempString = null;
                    while ((tempString = reader.readLine()) != null) {
                        tempString = tempString.trim();
                        if(tempString.isEmpty()){
                            continue;
                        }
                        String[] ary = tempString.split(new String(new byte[]{0x01}),-1);
                        SchoolCcpShopTable clearCenter = new SchoolCcpShopTable();
                        clearCenter.setMonthId(ary[0]);
                        clearCenter.setMerchantId(ary[1]);
                        clearCenter.setProvId(ary[2]);
                        clearCenter.setOrgProvCode(ary[3]);
                        clearCenter.setOrgProvName(ary[4]);
                        clearCenter.setOrgAreaCode(ary[5]);
                        clearCenter.setOrgAreaName(ary[6]);
                        clearCenter.setOrgCityCode(ary[7]);
                        clearCenter.setOrgCityName(ary[8]);
                        clearCenter.setOrgStreetCode(ary[9]);
                        clearCenter.setOrgStreetName(ary[10]);
                        clearCenter.setMerchantType(ary[11]);
                        clearCenter.setMerchantName(ary[12]);
                        clearCenter.setBusiLicense(ary[13]);
                        clearCenter.setPrincipalName(ary[14]);
                        clearCenter.setPrincipalPhone(ary[15]);
                        clearCenter.setRoadName(ary[16]);
                        clearCenter.setMerchantScope(ary[17]);
                        clearCenter.setMerchantAddress(ary[18]);
                        clearCenter.setCustomerRelation(ary[19]);
                        clearCenter.setIsHasWideband(ary[20]);
                        clearCenter.setWidebandType(ary[21]);
                        clearCenter.setPhoneType(ary[22]);
                        clearCenter.setPhoneSignal(ary[23]);
                        clearCenter.setStreetManagerPhone(ary[24]);
                        clearCenter.setStreetManagerName(ary[25]);
                        clearCenter.setBusiClassification(ary[26]);
                        clearCenter.setDevelopId(ary[27]);
                        clearCenter.setCreateName(ary[28]);
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (!StringUtils.isEmpty(ary[29])){
                            clearCenter.setCreateTime(sdf.parse(ary[29]));
                        }
                        clearCenter.setUpdateName(ary[30]);
                        if (!StringUtils.isEmpty(ary[31])){
                            clearCenter.setUpdateTime(sdf.parse(ary[31]));
                        }
                        clearCenter.setLongitude(ary[32]);
                        clearCenter.setLatitude(ary[33]);
                        clearCenter.setBaiduLongitude(ary[34]);
                        clearCenter.setBaiduLatitude(ary[35]);
                        clearCenter.setRemarkDesc(ary[36]);
                        clearCenter.setDeleteFlag(ary[37]);
                        clearCenter.setOrgStrtManagPhone(ary[38]);
                        clearCenter.setOrgStrtManagName(ary[39]);
                        clearCenter.setDistriTime(ary[40]);
                        clearCenter.setDistriName(ary[41]);
                        clearCenter.setOriginType(ary[42]);
                        clearCenter.setMerchantType(ary[43]);
                        clearCenter.setWidebandDeadline(ary[44]);
                        clearCenter.setIsConvert(ary[45]);
                        clearCenter.setMonthCost(ary[46]);
                        clearCenter.setTellPhone(ary[47]);
                        clearCenter.setSignId(ary[48]);
                        clearCenter.setSignType(ary[49]);
                        clearCenter.setIsFeeling(ary[50]);
                        clearCenter.setNetworkFlag(ary[51]);
                        clearCenter.setPartId(ary[52]);
                        clearCenter.setDayId(ary[53]);
                        clearCenter.setState("1");
                        clearCenter.setInfoInsertTime(new Date());
                        clearCenter.setFileName(fileName);
                        arrayList.add(clearCenter);
                    }
                } catch (Exception e){
                    log.error("pullSchoolCcpShop is erro = {}",e);
                }
                // 数据入库
                if(arrayList.size()>0) {
                    int applySize = 2000;
                    int limit = (arrayList.size() + applySize - 1)/applySize;
                    ccpShopToDBMapper.deleteCcpShopinfo();
                    Stream.iterate(0, n->n+1).limit(limit).forEach(a->{
                        List<SchoolCcpShopTable> result = arrayList.stream().skip(a * applySize).limit(applySize).collect(Collectors.toList());
                        log.info("pullSchoolccpShop result is={}",result.size());
                        ccpShopToDBMapper.addCcpShopListRemark(result);
                        ccpShopToDBMapper.addCcpShopList(result);

                    });
                    arrayList.clear();
                }else{
                    log.info("pullSchoolccpShop current file is null !!!");
                }
            }
        }
    }
    private String getTimeStr() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        Date today = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(today);
    }
}
