package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tf.TfOrdSvcnumActive;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TfOrdSvcnumActiveMapper {
    int deleteByPrimaryKey(String id);

    int insert(TfOrdSvcnumActive record);

    int insertSelective(TfOrdSvcnumActive record);

    TfOrdSvcnumActive selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TfOrdSvcnumActive record);

    int updateByPrimaryKey(TfOrdSvcnumActive record);

    List<TfOrdSvcnumActive> getIdByOrderId(String orderId);
}
