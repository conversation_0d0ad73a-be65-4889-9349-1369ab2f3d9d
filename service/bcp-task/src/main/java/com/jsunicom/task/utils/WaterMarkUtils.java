package com.jsunicom.task.utils;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class WaterMarkUtils {
    private static final BASE64Encoder base64en = new BASE64Encoder();

    /**
     * @param bytes       源图片
     * @param waterMark1 水印内容
     * @param waterMark2 水印内容
     */
    public static byte[] addWaterMark(byte[] bytes, String waterMark1, String waterMark2) {

        try {
            // 读取原图片信息
            InputStream is = new java.io.ByteArrayInputStream(bytes);
            BufferedImage srcImg = ImageIO.read(is);
            if (srcImg == null) return null;
            int srcImgWidth = srcImg.getWidth(null);//获取图片的宽
            int srcImgHeight = srcImg.getHeight(null);//获取图片的高
        //    Font font = new Font("微软雅黑", Font.BOLD, 20);//水印字体
            Color color = new Color(255, 0, 0, 128);//水印图片色彩以及透明度

            int fontSize = srcImg.getWidth(null) / 300 * 8;
            Font font = new Font("微软雅黑", Font.BOLD, fontSize);

            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);

            Graphics2D g = bufImg.createGraphics();
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            g.setColor(color); //根据图片的背景设置水印颜色
            g.setFont(font); //设置字体

            //设置水印的坐标
            int y = srcImgHeight - font.getSize();
            g.drawString(waterMark1, 0, y - 2 * font.getSize());  //画出水印
            g.drawString(waterMark2, 0, y);  //画出水印
            g.drawString(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), 0, y - font.getSize());  //画出水印
            g.dispose();

            /*把图片转换为字节*/
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(bufImg, "jpg", out);

            log.info("添加水印成功！");

           /* // 输出图片
            FileOutputStream outImgStream = new FileOutputStream("C:/temp/water"+new Date().getTime()+".jpg");
            ImageIO.write(bufImg, "jpg", outImgStream);
            System.out.println("添加水印完成");
            outImgStream.flush();
            outImgStream.close();*/
            return out.toByteArray();
        } catch (Exception e) {
            log.info("添加水印失败！"+ e);
            return bytes;
        }
    }
}
