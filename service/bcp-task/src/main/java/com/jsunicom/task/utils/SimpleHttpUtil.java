package com.jsunicom.task.utils;// package com.jsunicom.task.utils;
//
// import org.apache.http.HttpEntity;
// import org.apache.http.HttpResponse;
// import org.apache.http.NameValuePair;
// import org.apache.http.client.ClientProtocolException;
// import org.apache.http.client.ResponseHandler;
// import org.apache.http.client.config.RequestConfig;
// import org.apache.http.client.entity.UrlEncodedFormEntity;
// import org.apache.http.client.methods.HttpPost;
// import org.apache.http.impl.client.CloseableHttpClient;
// import org.apache.http.impl.client.HttpClients;
// import org.apache.http.message.BasicNameValuePair;
// import org.apache.http.util.EntityUtils;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// // import org.walkframework.base.tools.spring.SpringPropertyHolder;
//
// import java.io.IOException;
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// public class SimpleHttpUtil {
//
// 	private static final Logger log = LoggerFactory.getLogger(SimpleHttpUtil.class);
//
// 	private static int socketTimeout =   Integer.parseInt(SpringPropertyHolder.getContextProperty("timeOut.socketTimeout", "15000"));
// 	private static int connectTimeout =   Integer.parseInt(SpringPropertyHolder.getContextProperty("timeOut.connectTimeout", "3000"));
// 	private static RequestConfig config = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
//
// 	// 返回结果处理
// 	private static ResponseHandler<String> responseHandler = new ResponseHandler<String>() {
// 		public String handleResponse(final HttpResponse response) throws ClientProtocolException, IOException {
// 			HttpEntity entity = response.getEntity();
// 			String responseBody = entity != null ? EntityUtils.toString(entity, "GBK") : null;
// 			int status = response.getStatusLine().getStatusCode();
// 			if (status >= 200 && status <= 300) {
// 				return responseBody;
// 			} else if (status == 301 || status == 302) {
// 				return "错误：发生了重定向，登录状态失效！";
// 			} else {
// 				throw new ClientProtocolException("Unexpected response status: " + status + "\nresponseBody:"
// 						+ responseBody);
// 			}
// 		}
// 	};
//
// 	/**
// 	 * post方式请求url，返回结果
// 	 *
// 	 * @param
// 	 * @param url
// 	 * @return
// 	 * @throws Exception
// 	 */
// 	public static String post(String url, HashMap<String, Object> params) throws Exception {
// 		String responseBody = null;
// 		CloseableHttpClient httpclient = HttpClients.createDefault();
// 		HttpPost httppost = new HttpPost(url);
// 		httppost.setEntity(new UrlEncodedFormEntity(getPostParam(params), "UTF-8"));
// 		httppost.setConfig(config);
// 		try {
// 			responseBody = httpclient.execute(httppost, responseHandler);
// 			log.debug("post-response---------------------------------------------------------");
// 			log.debug(responseBody);
// 			log.debug("post-response---------------------------------------------------------");
// 		} finally {
// 			httpclient.close();
// 		}
// 		return responseBody;
// 	}
//
// 	/**
// 	 * 获取请求参数
// 	 *
// 	 * @param params
// 	 * @return
// 	 */
// 	public static List<NameValuePair> getPostParam(Map<String, Object> params) {
// 		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
// 		if (params != null) {
// 			for (String key : params.keySet()) {
// 				nvps.add(new BasicNameValuePair(key, params.get(key) == null ? "" : params.get(key)
// 						.toString()));
// 			}
// 		}
// 		return nvps;
// 	}
//
// }
