package com.jsunicom.task.mapper.order;


import com.jsunicom.task.po.order.tl.TlOrdSynphotoRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TlOrdSynphotoRecordMapper {
    int deleteByPrimaryKey(String orderId);

    int insert(TlOrdSynphotoRecord record);

    int insertSelective(TlOrdSynphotoRecord record);

    TlOrdSynphotoRecord selectByPrimaryKey(String orderId);

    int updateByPrimaryKeySelective(TlOrdSynphotoRecord record);

    int updateByPrimaryKey(TlOrdSynphotoRecord record);

}
