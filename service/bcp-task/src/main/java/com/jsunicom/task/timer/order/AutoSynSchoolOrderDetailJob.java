package com.jsunicom.task.timer.order;

import com.jsunicom.task.entity.TdOrdSysDict;
import com.jsunicom.task.mapper.order.OrderQueryMapper;
import com.jsunicom.task.mapper.wobuy.WoBuyMapper;
import com.jsunicom.task.utils.SftpClientUtil;
import com.jsunicom.task.utils.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 订单详情文件自动同步到经分
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class AutoSynSchoolOrderDetailJob {

    private static final String FTP_KEY = "schoolOrderDetails";

    public static final String FTP_CONFIG = "ftpConfig";

    @Resource
    private WoBuyMapper woBuyMapper;

    @Resource
    private OrderQueryMapper orderQueryMapper;

    @XxlJob("autoSynSchoolOrderDetailJob")
    public ReturnT<String> autoSynSchoolOrderDetailJob(String param) {
        log.info("###订单中心文件上传开始执行");
        PrintWriter out = null;

        try {
            //查询参数
            String fileName = "ORDER_INFO_"+getTimeStr()+ ".txt";

            TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey(FTP_CONFIG, FTP_KEY);
            String filePath = StringUtils.isBlank(dict.getParamValue()) ? "/home/<USER>/schoolOrderDetails" : dict.getParamValue();
            //String fileLastName=filePath + File.separator + fileName;
            File file = new File(filePath + File.separator + fileName);

            log.info("###待上传订单详情文件位置"+filePath + File.separator + fileName);
            out = new PrintWriter(file, "UTF-8");


            // 数据查询并构造数据文件
            getOrderDetailUploadFile(out);


            //文件上传
            String uploadErrMsg = null;

            boolean flag = upLoadFile(file, uploadErrMsg);
            if(!flag)
            {
                log.info("###上传订单详情文件失败..." + uploadErrMsg);
            }

        }catch(Exception e){
            e.printStackTrace();
            log.error("文件上传失败:" + e.getMessage());
            return ReturnT.FAIL;
        }finally {
            if (out != null) {
                out.close();
            }

        }
        return ReturnT.SUCCESS;
    }

    /**
     * 构造ftp服务参数
     *
     * @return
     */
    private Map<String, String> genFileUploadParam() {
        Map<String, String> paramMap = new HashMap<String, String>();
        TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey(FTP_CONFIG, FTP_KEY);
        String host = dict.getParam1();
        String port = dict.getParam2();
        String username = dict.getParam3();
        String password = dict.getParam4();
        String dir = dict.getParam5();
//		paramMap.put("username", username);
//		paramMap.put("password", password);
//		paramMap.put("host", host);
//		paramMap.put("port", port);
//		paramMap.put("dir", dir);
        paramMap.put("username", username);
        paramMap.put("password", password);
        paramMap.put("host", host);
        paramMap.put("port", port);
        paramMap.put("dir", dir);
        return paramMap;
    }


    /**
     * 数据文件上传到ftp
     *
     * @param file
     * @return
     */
    private boolean upLoadFile(File file, String uploadErrMsg) {
        int tryCnt = 3;
        String serverPath = null;
        boolean uploadSuccess;
        SftpClientUtil sftpClientUtil = new SftpClientUtil(true);
        // ftp参数
        Map<String, String> paramMap = genFileUploadParam();
        log.info("FTP:" + paramMap);
        do {
            try {
                tryCnt--;
                serverPath = sftpClientUtil.uploadFile(paramMap, file);
                log.info("上传文件while, serverPath:" + serverPath);
                uploadSuccess = true;
                //file.delete();
            } catch (Exception e) {
                log.info("上传文件失败while, error:" + e.getMessage());
                uploadSuccess = false;
                uploadErrMsg = e.getMessage();
            }
        } while (!uploadSuccess && tryCnt > 0);

        if (uploadSuccess && StringUtils.isNotEmpty(serverPath)) {
            log.debug("上传文件成功, 文件位置:" + serverPath);
            log.info("上传文件成功, 文件位置:" + serverPath);
        } else {
            log.error("上传文件失败: " + uploadErrMsg);
            log.info("上传文件失败: " + uploadErrMsg);
        }
        return uploadSuccess;
    }

    private String getTimeStr() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        Date today = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(today);
    }

    public void getOrderDetailUploadFile(PrintWriter out){
        //获取满足条件的订单
        long startTime = System.currentTimeMillis();
        List<Map<String, Object>> orderInfoList = orderQueryMapper.querySchoolOrderDetailsInfos();
        if(orderInfoList!=null && orderInfoList.size()>0){
        /*orderInfoList.forEach(orderInfo->{
                orderInfo.put("infos", orderInfo.get("order_id") + "|" + orderInfo.get("main_number") + "|" + orderInfo.get("cust_name") + "|"
                + orderInfo.get("eparchy_code") + "|" + orderInfo.get("county_code") + "|" + orderInfo.get("developer_id") + "|"
                + orderInfo.get("create_date") + "|" + orderInfo.get("update_time") + "|" + orderInfo.get("order_state") + "|"
                + orderInfo.get("campus_name") + "|" + orderInfo.get("school_id") + "|" + orderInfo.get("school_name") + "|"
                + orderInfo.get("member_name") + "|" + orderInfo.get("attr_value"));
            });*/
            for (Map<String, Object> orderInfo : orderInfoList) {
//				infos= infos.replace("\t", "");
//				infos= infos.replace("\n", "");
                String partnerInfo =(String)orderInfo.get("partnerInfo");
                if (StringUtil.isEmpty(partnerInfo)){
                    partnerInfo="||||";
                }
                String[] split = partnerInfo.split("\\|", -1);
                String infos=orderInfo.get("order_id") + "|" + orderInfo.get("main_number") + "|"  + "|"
                        + orderInfo.get("eparchy_code") + "|" + orderInfo.get("county_code") + "|" + split[0]+"|"+split[1]+"|"
                        + orderInfo.get("developer_id") + "|"
                        + orderInfo.get("in_time") + "|" + orderInfo.get("close_time") + "|" + orderInfo.get("order_state") + "|"
                        + orderInfo.get("campus_name") + "|" + orderInfo.get("school_manager_pid") + "|" +orderInfo.get("school_id") + "|" + orderInfo.get("school_name") + "|"
                        + orderInfo.get("member_name") + "|" + split[2]+"|" + split[3]+"|" + split[4];
                out.println(infos);
            }
            long endTime = System.currentTimeMillis();
            log.info("数据查询并构造数据文件所需时间："+(endTime-startTime));
            out.flush();
        }
    }




    public static void main(String[] args) {
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        try {
            String str = "";
            String str1 = "";
            //fis = new FileInputStream("D:\\360安全浏览器下载\\20180417.txt");// FileInputStream
            fis = new FileInputStream("D:\\work\\tool\\apache-tomcat-7.0.64\\orderDetail\\20180417.txt");// FileInputStream
            // 从文件系统中的某个文件中获取字节
            isr = new InputStreamReader(fis);// InputStreamReader 是字节流通向字符流的桥梁,
            br = new BufferedReader(isr);// 从字符输入流中读取文件中的内容,封装了一个new InputStreamReader的对象
            while ((str = br.readLine()) != null) {
                //str = br.readLine();
                System.out.println(str);
            }
        } catch (Exception e) {
            System.out.println("找不到指定文件");
        }
    }
}
