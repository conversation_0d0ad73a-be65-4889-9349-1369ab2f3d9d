package com.jsunicom.task.po.order;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@SuppressWarnings("all")
@Data
public class Goods implements Serializable {

	private static final long serialVersionUID = 1L;

	private String goodsOrderId;
	private String busiType;
	private String goodsType;
	private String goodsName;
	private String goodsPrice;
	private String goodsNum;
	private String goodsDesc;
	private String goodsMoney;
	private List<Product> products;
	private String custName;
	private String psptId;
	private String psptAddress;
	private String cardPhotoA;
	private String cardPhotoB;
	private String cardPhotoHand;
	private List<OperateInfo> operateInfo;
	private PostInfo postInfo;
	private String goodsUrl;
	private String extWebUrl;
	private String goodsId;
	private List<GoodsProps> goodsProps;
	private List<GoodsCustInfo> goodsCustInfo;

	private List<GoodsFees> goodsFees;

	private String goodsState;

	private List<GoodsSubs> goodsSubs;

	private List<NetworkNumber> networkNumber;

	//新增productionMain信息
	private List<Production> productions;


}
