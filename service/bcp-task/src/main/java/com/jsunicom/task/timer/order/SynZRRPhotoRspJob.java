package com.jsunicom.task.timer.order;

import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.task.entity.TdOrdSysDict;
import com.jsunicom.task.feign.BcpOmsFeign;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.po.order.req.SendSmsReq;
import com.jsunicom.task.service.photo.PhotoProcessService;
import com.jsunicom.task.utils.SftpClientUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @PackageName: com.jsunicom.oms.timer.order
 * @ClassName SynZRRPhotoRspJob
 * @Description 照片同步自然人文件回执处理
 * @Date 2023/6/12 9:27
 * @Created by yangyj3
 */
@Slf4j
@Configuration
@EnableScheduling
public class SynZRRPhotoRspJob {
    @Autowired
    private PhotoProcessService photoProcessService;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private BcpOmsFeign bcpOmsFeign;

    @XxlJob("synZRRPhotoRspJob")
    public ReturnT<String> synZRRPhotoRspJob(String param) {
        List<TdOrdSysDict> dists = photoProcessService.queryDictList("synZRRPhoto");
        if (dists.size() == 0) {
            log.error("##未查询到同步照片的字典表相关配置");
            return ReturnT.FAIL;
        }
        String timestamp = getTimeStr();
        for (TdOrdSysDict dict : dists) {
            String sourceSystem = dict.getParamKey();
            String prefixStr = timestamp + "_" + "A800" + "_SceneAndCertPhotosSync_" + sourceSystem + "_";
            try {
                TdOrdSysDict dictSftp = photoProcessService.queryDict("sftpConfigZRR", "sftpConfigZRR");
                if (dictSftp == null) {
                    log.error("paramType为sftpConfigZRR，paramKey为sftpConfigZRR的字典数据未查询到");

                    return ReturnT.FAIL;
                }
                String filePath = dictSftp.getOper();//回执文件路径
                //  filePath= FileUtils.getLocalFileDir(filePath);
                String fileDirectory = dictSftp.getOper();
                String host = dictSftp.getParam1();
                String port = dictSftp.getParam2();
                String username = dictSftp.getParam3();
                String password = dictSftp.getParam4();
                log.info("====SynZRRPhotoRspJob==filePath=" + filePath + "===prefixStr=" + prefixStr);
                SftpClientUtil sftpClientUtil = new SftpClientUtil(host, Integer.parseInt(port), username, password);
                List<String> fileNames = sftpClientUtil.getFileOfDir(filePath, prefixStr, ".RSP");

                for (String fileName : fileNames) {
                    log.info("====SynZRRPhotoRspJob==fileName=" + fileName);
                    //解析文件入库
                    try {
                        InputStream is = sftpClientUtil.downloadFile2Stream(fileDirectory, fileName);
                    /*    String path = "D:/logs";
                        File file = new File(path + File.separator + fileName);
                        InputStream is = new FileInputStream(file);*/

                        if (is != null) {
                            InputStreamReader inputStreamReader = new InputStreamReader(is, "GBK");
                            BufferedReader reader = new BufferedReader(inputStreamReader);
                            String line;

                            byte space[] = {0x01};
                            byte space1[] = {0x02};
                            byte space2[] = {0x03};
                            String strSpace = new String(space, "UTF-8");
                            String strSpace1 = new String(space1, "UTF-8");
                            // String strSpace2 = new String(space2, "UTF-8");
                            String strSplit = "\\^" + strSpace + strSpace1 + "\\^";
                            // String strSplitBr = "^" + strSpace1 + strSpace2 + "^";

                            log.info("====SynZRRPhotoRspJob回执文件----strSplit---->:" + strSplit);

                            while ((line = reader.readLine()) != null) {
                                log.info("====SynZRRPhotoRspJob回执文件" + fileName + "-----line---->:" + line);
                                String[] arr = line.split(strSplit);

                                log.info("====SynZRRPhotoRspJob回执文件" + fileName + "-----arr.length---->:" + arr.length);
                                log.info("====SynZRRPhotoRspJob回执文件" + fileName + "-----arr[0]---->:" + arr[0]);
                                log.info("====SynZRRPhotoRspJob回执文件" + fileName + "-----arr[1]---->:" + arr[1]);


                                String respCode = arr[0].equals("null") ? "" : arr[0]; //错误编码
                                String respDesc = arr[1].equals("null") ? "" : arr[1]; //错误描述
                                String sourceSystem1 = "";
                                if (arr.length > 2)
                                    sourceSystem1 = arr[2].equals("null") ? "" : arr[2];
                                String eparchyCode = "";
                                if (arr.length > 4)
                                    eparchyCode = arr[4].equals("null") ? "" : arr[4];
                                String tradeId = "";
                                if (arr.length > 5)
                                    tradeId = arr[5].equals("null") ? "" : arr[5];
                                String orderId = "";
                                if (arr.length > 6)
                                    orderId = arr[6].equals("null") ? "" : arr[6];

                                HashMap<String, String> rspInfoMap = new HashMap<>();
                                rspInfoMap.put("orderId", orderId);
                                rspInfoMap.put("tradeId", tradeId);
                                rspInfoMap.put("eparchyCode", eparchyCode);
                                rspInfoMap.put("respCode", respCode);
                                rspInfoMap.put("respDesc", respDesc);
                                rspInfoMap.put("sourceSystem", sourceSystem1);
                                rspInfoMap.put("fileName", fileName);
                                log.info("====SynZRRPhotoRspJob回执文件" + fileName + "-----orderId---->:" + orderId + "开始入库：");
                                try {
                                    photoProcessService.saveRspPhotoInfoZRR(rspInfoMap);
                                } catch (Exception e) {
                                    log.error("SynZRRPhotoRspJob回执文件" + fileName + "-----orderId---->:" + orderId + "处理异常：" + e.getMessage());

                                }

                            }
                            // 关闭资源
                            reader.close();
                        }
                    } catch (Exception e) {
                        log.error("SynZRRPhotoRspJob回执文件" + fileName + "处理异常：" + e.getMessage());

                    }

                }

                log.info("====SynZRRPhotoRspJob==filePath=" + filePath + "===prefixStr=" + prefixStr + "处理结束");

            } catch (Exception e) {
                //e.printStackTrace();
                log.error("回执处理异常",e);
                return ReturnT.FAIL;
            }

        }

        photoProcessService.updateRspRecordSucess();

        List<Map<String, String>> synPhotoIFail =photoProcessService.queryRspNoDealFail();
        log.info("SynZRRPhotoRspJob回执文件同步失败的记录数=" + synPhotoIFail.size());



        for (int i = 0; i < synPhotoIFail.size(); i++) {
            HashMap<String, String> paramMap = new HashMap<>();
            String fileName = synPhotoIFail.get(i).get("FILE_NAME");
            String tradeId = synPhotoIFail.get(i).get("TRADE_ID");
            // String fileNameRsp = fileName;
            String fileNameReq = "";
            if (StringUtils.isNotBlank(fileName)) {
                fileName.replace(".RSP", ".REQ");
                fileNameReq = fileName;
            }
            if (StringUtils.isBlank(tradeId)) {
                paramMap.put("fileName", synPhotoIFail.get(i).get("FILE_NAME"));
                paramMap.put("fileNameReq", fileNameReq);
                photoProcessService.updateRspFail(paramMap);
            } else {
                /*paramMap.put("tradeId", tradeId);
                paramMap.put("fileName", synPhotoIFail.get(i).get("FILE_NAME"));
                paramMap.put("fileNameReq", fileNameReq);
                updateRspFail(paramMap);*/
            }

        }

        log.info("SynZRRPhotoRspJob回执文件记录处理完毕");

        if(synPhotoIFail.size()>0){
            log.info("SynZRRPhotoRspJob回执文件告警");
            try{
                //告警提醒
                TdMSysDict sysDict = new TdMSysDict();
                sysDict.setParamType("synPhotoIFailWarn");

                List<TdMSysDict> list = tdMSysDictMapper.queryAll(sysDict);
                if(list.size()>0){
                    for (int j = 0; j < list.size(); j++) {
                        String sendPhone=list.get(j).getParam1();
                        String msgId=list.get(j).getParam2();
                        String key=list.get(j).getParamKey();

                        try{
                            log.info("synPhotoIFailWarn ====");
                            SendSmsReq sendSmsReq = new SendSmsReq();
                            sendSmsReq.setTemplateId(msgId);
                            sendSmsReq.setSendTo(sendPhone);
                            sendSmsReq.setSendParam(key);
                            log.info("synPhotoIFailWarn ======sendSms="+sendSmsReq.toString());
                            bcpOmsFeign.sendSms(sendSmsReq);
                        }catch (Exception e){
                            log.info("synPhotoIFailWarn ====error=="+e.getMessage());
                            e.printStackTrace();

                        }
                    }

                }
            }catch (Exception e){
                log.info("synPhotoIFailWarn ==11==error=="+e.getMessage());
                e.printStackTrace();

            }

        }
        return ReturnT.SUCCESS;
    }

    private String getTimeStr() {
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date y = c.getTime();
        return f.format(y);
    }

}
