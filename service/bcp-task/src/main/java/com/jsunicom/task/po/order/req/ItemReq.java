package com.jsunicom.task.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


@ApiModel(description = "订单修改请求报文产品构成")
public class ItemReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "产品构成类型", required = true, position = 1)
	private String itemType;

	@ApiModelProperty(value = "产品构成编码", required = true, position = 2)
	private String itemId;

	@ApiModelProperty(value = "产品构成名称", required = true, position = 3)
	private String itemName;

	@ApiModelProperty(value = "产品构成描述", required = true, position = 4)
	private String itemDesc;

	@ApiModelProperty(value = "产品构成属性", required = true, position = 5)
	private List<PropsReq> props;

	public String getItemType() {
		return itemType;
	}
	public void setItemType(String itemType) {
		this.itemType = itemType;
	}
	public String getItemId() {
		return itemId;
	}
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getItemDesc() {
		return itemDesc;
	}
	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}
	public List<PropsReq> getProps() {
		return props;
	}
	public void setProps(List<PropsReq> props) {
		this.props = props;
	}

}
