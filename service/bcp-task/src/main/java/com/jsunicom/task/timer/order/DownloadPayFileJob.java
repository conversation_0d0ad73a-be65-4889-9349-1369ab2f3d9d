package com.jsunicom.task.timer.order;

import com.jsunicom.task.entity.TdOrdSysDict;
import com.jsunicom.task.mapper.order.OrderMonitorJobMapper;
import com.jsunicom.task.mapper.wobuy.WoBuyMapper;
import com.jsunicom.task.po.constants.PayFileSftpConfig;
import com.jsunicom.task.po.order.TfPayFileDownload;
import com.jsunicom.task.po.order.tf.TfOrdPayRecord;
import com.jsunicom.task.utils.SFTPKit;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;

/**
 * 下载新校园对账文件回执
 * <AUTHOR>
 * @date 2024/03/14
 * */
@Component
@Slf4j
public class DownloadPayFileJob {

    @Resource
    private WoBuyMapper woBuyMapper;

    @Resource
    private OrderMonitorJobMapper orderMonitorJobMapper;

    @XxlJob("downloadPayFileJob")
    public ReturnT<String> downloadPayFileJob(String param){
        log.info("downloadPayFileJobStart...");
        try {
            //查询文件所在路径配置
            TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey(PayFileSftpConfig.FTP_CONFIG, PayFileSftpConfig.FTP_KEY);
            String sftpIp = dict.getParam1();
            String sftpPort = dict.getParam2();
            String sftpUserName = dict.getParam3();
            String sftpPassword = dict.getParam4();
            String downLoadDire = dict.getOper();
            String localPath = dict.getParamValue();
            File localFile = new File(localPath + "/*_9900_TYJFDZ_JSXYZS_34CT.RSP");
            if (!localFile.exists()){
                log.info("文件不存在，需创建...");
                File parentFile = new File(localFile.getParent());
                parentFile.mkdir();
            }
            String checkMsg = "_9900_TYJFDZ_JSXYZS_34CT.RSP";
            log.info("download file sftpIp:{},sftpPort:{},sftpUserName:{},sftpPassword:{},downLoadDire:{},localPath:{}",sftpIp,sftpPort,sftpUserName,sftpPassword,downLoadDire,localPath);
            List<String> fileList = SFTPKit.batchDownLoadFile(sftpIp, Integer.parseInt(sftpPort), sftpUserName, sftpPassword, downLoadDire, localPath, checkMsg);
            if (ObjectUtils.isEmpty(fileList)){
                log.info("未查询到需下载的文件");
            }else {
                for (String fileName : fileList) {
                    if(ObjectUtils.isEmpty(orderMonitorJobMapper.queryDownloadFile(fileName))){
                        File file = new File(localPath + "/" + fileName);
                        BufferedReader reader;
                        reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "GBK"));
                        String tempString;
                        TfPayFileDownload tfPayFileDownload = new TfPayFileDownload();
                        //获取第一行，即文件头，一般为文件中的记录数量，如果对账无误，为0
                        String fileHead = reader.readLine();
                        tfPayFileDownload.setFileHead(fileHead);
                        //对账回执文件名
                        tfPayFileDownload.setFileName(fileName);
                        boolean flag = (tempString = reader.readLine()) != null;
                        //如果对账回执文件只有一行，并且第一行值为0，本次对账为成功
                        tfPayFileDownload.setFinishStatus(!flag && Objects.equals(fileHead, "0") ? "0" : "1");
                        //如果对账成功，直接入库，并更新pay_record对账状态为成功
                        if ("0".equals(tfPayFileDownload.getFinishStatus())){
                            orderMonitorJobMapper.insertDownloadInfo(tfPayFileDownload);
                            String reqFileName = StringUtils.left(fileName, 32) + ".REQ";
                            List<TfOrdPayRecord> payLists = orderMonitorJobMapper.queryPayInfoByFileName(reqFileName);
                            for (TfOrdPayRecord payInfo : payLists) {
                                orderMonitorJobMapper.updatePayRecord(payInfo);
                            }
                        }
                        //如果对账失败，取关键值入库
                        while (flag) {
                            tempString = tempString.trim();
                            //文件行内容
                            tfPayFileDownload.setFileLineContent(tempString);
                            String[] ary = tempString.split(",",-1);
                            tfPayFileDownload.setResult(ary[0]);
                            tfPayFileDownload.setAcctId(ary[2]);
                            tfPayFileDownload.setOuterTradeId(ary[5]);
                            tfPayFileDownload.setOuterTradeTime(ary[6]);
                            tfPayFileDownload.setFailReason(ary[ary.length - 1]);
                            orderMonitorJobMapper.insertDownloadInfo(tfPayFileDownload);
                            //更新pay_record表的对账状态为失败
                            TfOrdPayRecord tfOrdPayRecord = new TfOrdPayRecord();
                            tfOrdPayRecord.setOuterTradeId(tfPayFileDownload.getOuterTradeId());
                            tfOrdPayRecord.setOuterTradeTime(tfPayFileDownload.getOuterTradeTime());
                            //ary[1]为该条记录对账结果，0为成功
                            tfOrdPayRecord.setStatus("0".equals(ary[1]) ? "1" : "2");
                            orderMonitorJobMapper.updatePayRecord(tfOrdPayRecord);
                            flag = (tempString = reader.readLine()) != null;
                        }
                    }else {
                        log.info("文件已存在,fileName,{}", fileName);
                    }
                }
            }
        }catch (Exception e){
            log.error("downloadPayFileJobException...", e);
            return ReturnT.FAIL;
        }
        log.info("downloadPayFileJobEnd...");
        return ReturnT.SUCCESS;
    }

}
