package com.jsunicom.task.timer.order;

import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.mapper.order.TfOrderItemMapper;
import com.jsunicom.task.mapper.order.WoActiveNoticeMapper;
import com.jsunicom.task.mapper.photo.PhotoOperateMapper;
import com.jsunicom.task.po.order.tf.TfOrdMain;
import com.jsunicom.task.po.order.tf.TfOrderItem;
import com.jsunicom.task.service.photo.PhotoProcessService;
import com.jsunicom.task.service.order.GenerateId;
import com.jsunicom.task.utils.S3Util;
import com.jsunicom.task.utils.StringUtil;
import com.jsunicom.task.utils.WaterMarkUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
@EnableScheduling
public class AutoAddWaterMarkJob {
    @Autowired
    private PhotoProcessService photoProcessService;

    @Resource
    private PhotoOperateMapper photoOperateMapper;

    @Autowired
    private GenerateId generateId;

    @Autowired
    private WoActiveNoticeMapper woActiveNoticeMapper;

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private TfOrderItemMapper tfOrderItemMapper;


    private final static String IMAGE_PREFIX = "https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/";

    //激活认证上传的照片文件，进行加水印；
    @XxlJob("autoAddWaterMarkJob")
    public ReturnT<String> autoAddWaterMarkJob(String param) {
        log.info("======autoAddWaterMarkJob is starting ====");

        List<Map<String, String>> synPhotoRecords = photoProcessService.querySynPhotoRecords();
        if (null != synPhotoRecords && !synPhotoRecords.isEmpty()) {
            for (int i = 0; i < synPhotoRecords.size(); i++) {
                Map<String, String> record= synPhotoRecords.get(i);
                String orderId = record.get("ORDER_ID");
                String synState = record.get("SYN_STATE");
                if(!synState.equals("0")){
                    continue;
                }
                HashMap<String, Object> activeRecord = photoOperateMapper.queryLatestActiveRecord(orderId);
                String activieId = (String) activeRecord.get("ID");
                List<Map<String, String>> photoInfoList = photoOperateMapper.qryPhotosByActivieIdAndType(activieId,"3");
                if (!org.springframework.util.CollectionUtils.isEmpty(photoInfoList)) {
                    for (Map<String, String> photoRes : photoInfoList) {
                        if("3".equals(photoRes.get("RES_TYPE"))){
                            log.info("======autoAddWaterMarkJob is 加水印开始 ==id=="+photoRes.get("ID"));
                            String imageUrl=photoRes.get("URL");
                            String id=photoRes.get("ID");
                            try{
                                TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(orderId);
                                if (ordMain == null) {
                                    throw new RuntimeException("根据订单号" + orderId+ "未查询到订单");
                                }
                                TfOrderItem item = new TfOrderItem();
                                item.setOrderId(orderId);
                                item.setAttrType("0");
                                item.setAttrCode("departCode");
                                List<TfOrderItem> itemList=tfOrderItemMapper.qryOrderItems(item);
                                if(itemList.size()>0){
                                    String departCode=itemList.get(0).getAttrValue();
                                    if(!StringUtil.isBlank(departCode)){
                                        InputStream in=queryActiveFile(imageUrl);
                                        byte[] resource = org.apache.commons.io.IOUtils.toByteArray(in);
                                        String urlNew=uploadNewFile(ordMain.getStaffId(),departCode,resource,imageUrl);
                                        if(!StringUtil.isBlank(urlNew)){
                                            HashMap<String, String> paramMap = new HashMap<>();

                                            paramMap.put("id", id);
                                            paramMap.put("activeId", activieId);
                                            paramMap.put("url", urlNew);
                                            paramMap.put("resType", "1");
                                            photoOperateMapper.updateActiveResource(paramMap);
                                        }

                                    }
                                }



                            }catch (Exception e)
                            {
                                log.error("###autoAddWaterMarkJob"+photoRes.get("ID")+"生成报错###"+e.getMessage());
                            }

                        }else{
                            continue;
                        }
                    }
                }

            }
        }

        log.info("======autoAddWaterMarkJob is ending ====");
        return ReturnT.SUCCESS;

    }
    public  String uploadNewFile(String operId,   String departCode, byte[] resource,String imageUrl) throws Exception {
        String path="";

        byte[] bytes1 = resource;
        //给照片加水印
        String waterMark1 = "仅限办理中国联通业务使用";
        String waterMark2 = operId;
        bytes1 = WaterMarkUtils.addWaterMark(bytes1, waterMark1, waterMark2);

        String suffix = imageUrl.substring(imageUrl.lastIndexOf(".") + 1);
        String fileName = System.currentTimeMillis() + "." + suffix;

        //    InputStream inputStream  = new ByteArrayInputStream(bytes1);

        String uploadFile = S3Util.uploadFile(bytes1,fileName);

        return uploadFile;
    }

    private InputStream queryActiveFile(String imageUrl) throws Exception {
        if (org.apache.commons.lang3.StringUtils.isEmpty(imageUrl)) {
            return null;
        }

        String fileName =imageUrl.replace("https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/","");
        //   String fileName = imageUrl.replace(s3Properties.getUrlSuffix(), "");
        log.info("doOCRFaceCompare fileName={}", fileName);
        InputStream photoInfo = S3Util.getFile(fileName);
        return photoInfo;
    }

}
