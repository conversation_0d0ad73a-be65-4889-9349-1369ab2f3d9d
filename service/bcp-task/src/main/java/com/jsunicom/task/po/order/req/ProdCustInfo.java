package com.jsunicom.task.po.order.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProdCustInfo implements Serializable {
    private String custType;//客户类型，参考SYS_000006

    private String custName;//客户名称

    private String psptType;//证件类型，参考SYS_000007

    private String psptId;//证件ID

    private String psptAddress;//证件地址

    private String cardPhotoA;//证件照正面

    private String cardPhotoB;//证件照背面

    private String cardPhotoHand;//手持证件照

    private String productCustId;//手持证件照

    private String orgCode;//企业机构编码

    private String businessLicensePhoto;//营业执照

    private String authorizeCertificatePhoto;//授权书

    private String resvPhoto1;//预留照1

    private String resvPhoto2;//预留照2

}
