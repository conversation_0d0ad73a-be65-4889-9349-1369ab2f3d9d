package com.jsunicom.task.service.order.impl;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.task.feign.BcpOmsFeign;
import com.jsunicom.task.mapper.order.TfOrdAddressMapper;
import com.jsunicom.task.mapper.order.TfOrdLineMapper;
import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.mapper.order.TlOrdOperatorLogMapper;
import com.jsunicom.task.mapper.resource.TdMSysDictMapper;
import com.jsunicom.task.po.order.tf.TfOrdAddress;
import com.jsunicom.task.po.order.tf.TfOrdLine;
import com.jsunicom.task.po.order.tf.TfOrdMain;
import com.jsunicom.task.po.order.tl.TlOrdOperatorLog;
import com.jsunicom.task.service.order.GenerateId;
import com.jsunicom.task.service.order.SendSmsService;
import com.jsunicom.task.service.order.TlOrdOperatorLogService;
import com.jsunicom.task.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class TlOrdOperatorLogServiceImpl implements TlOrdOperatorLogService {

    @Autowired
    private TlOrdOperatorLogMapper tlOrdOperatorLogMapper;

    @Autowired
    private GenerateId generateId;

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private TfOrdLineMapper tfOrdLineMapper;

    @Autowired
    private TfOrdAddressMapper tfOrdAddressMapper;

    @Autowired
    private BcpOmsFeign bcpOmsFeign;

    @Autowired
    private SendSmsService sendSmsService;

    @Override
    public String recordOrderOperInfo(TlOrdOperatorLog logT) {
        String logId = generateId.getId("TL_ORD_OPERCHANGE");
        logT.setLogId(logId);
        String staffId = logT.getOperatorId();
        String operType = logT.getOperType();
        if (logT.getUpdateTime() == null) {
            logT.setUpdateTime(new Date());
        }
        String reasonDesc = logT.getReasonDesc();
        if (StringUtil.isEmpty(reasonDesc)) {
            reasonDesc = "";
        }
        if (reasonDesc.length() > 200) {
            reasonDesc = reasonDesc.substring(0, 200);
        }
        logT.setReasonDesc(reasonDesc);
        if (StringUtils.isEmpty(staffId)) {
            try {
               /* TdOrdMStaff ordMStaff = CommonUtil.getStaff();
                if (ordMStaff != null) {
                    staffId = ordMStaff.getStaffId();
                }*/
            } catch (Exception var21) {
                log.error(var21.getMessage(), var21);
            }
        }
        if (StringUtils.isEmpty(staffId)) {
            staffId = "system";
        }
        logT.setOperatorId(staffId);
        if (StringUtils.isEmpty(operType)) {
            logT.setOperType(logT.getNodeCode());
        }

        tlOrdOperatorLogMapper.insertTlOrdOperatorLog(logT);
        if (logT.getReasonCode().equals("success")) {
            //更新订单状态
            TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(logT.getOrderId());
            if (ordMain == null) {
                throw new RuntimeException("根据订单号" + logT.getOrderId() + "未查询到订单");
            }
            String orderStateNew = "";
            /*TdMSysDict dictParam = new TdMSysDict();
            dictParam.setParamType("orderNode");
            dictParam.setParamKey(logT.getNodeCode());
            TdMSysDict dict=tdMSysDictMapper.query(dictParam);

            if(dict != null){
                orderStateNew=dict.getParam1();
            }*/
            orderStateNew = logT.getOrderState();
            if (StringUtil.isNotBlank(orderStateNew)) {
                ordMain.setOrderState(orderStateNew);
                tfOrdMainMapper.updateOrdMainInfoByorderId(ordMain);
            }
            //this.changeOrderState();    //TL_ORD_BUSICHANGE
        }
        return logId;
    }

    private void changeOrderState(Map<String, Object> param, String tLogId) {
        ;
    }

    @Override
    public void orderOperComplete(TlOrdOperatorLog logT) {
        String nodeCode = logT.getNodeCode();
        String reasonCode = logT.getReasonCode();
        TlOrdOperatorLog operatorLog = new TlOrdOperatorLog();
        operatorLog.setOrderId(logT.getOrderId());
        operatorLog.setNodeCode(logT.getNodeCode());
        operatorLog.setReasonName(logT.getNodeName());
        operatorLog.setReasonCode(logT.getReasonCode());
        operatorLog.setReasonDesc(logT.getReasonDesc());
        if (nodeCode.equals("file") && reasonCode.equals("success")) {
            operatorLog.setOrderState("0060");
            operatorLog.setReasonDesc("归档成功");
        }
        recordOrderOperInfo(operatorLog);

        TfOrdLine ordLine = tfOrdLineMapper.qryTfOrdLineByOrderId(logT.getOrderId());
        String serialNumber = "";
        if (ordLine != null) {
            serialNumber = ordLine.getMainNumber();
            ordLine.setOrderNodeCode(logT.getNodeCode());
            ordLine.setOrderNodeName(logT.getNodeName());
            ordLine.setOrderNodeState("1"); //0-未处理
            if (!reasonCode.equals("success")) {
                ordLine.setOrderNodeState("0");
            }
            tfOrdLineMapper.updateTfOrdLine(ordLine);
        }


        //发送短信提醒
        if (nodeCode.equals("file") && reasonCode.equals("success")) {
            Map<String, Object> params = new HashMap<>();
            params.put("selectNumber", serialNumber);
            params.put("operator", logT.getOperatorId());
            params.put("occupiedFlag", "05");
            bcpOmsFeign.numOccupy(params);
            //订单关闭后，资源占用_end

            TfOrdAddress address = tfOrdAddressMapper.qryOrdAddressByOrderId(logT.getOrderId());
            if (address != null && StringUtil.isNotBlank(address.getPhone())) {
                JSONObject smsParams = new JSONObject();
                smsParams.put("orderNode", "file");
                smsParams.put("orderId", logT.getOrderId());
                smsParams.put("sendTo", address.getPhone());
                smsParams.put("serialNumber", serialNumber);
                sendSmsService.sendSms("10007", smsParams);  //业务办理成功短信
            }
        }

    }
}
