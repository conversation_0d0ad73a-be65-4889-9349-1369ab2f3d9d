package com.jsunicom.task.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.util.IdUtil;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.task.common.constants.CommonProperties;
import com.jsunicom.task.entity.TfFPayLogSettleCenter;
import com.jsunicom.task.mapper.order.TfFPayLogSettleCenterMapper;
import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.mapper.order.TlOrdMsglogMapper;
import com.jsunicom.task.po.order.tf.TfOrdMain;
import com.jsunicom.task.po.order.tl.TlOrdMsglog;
import com.jsunicom.task.request.CallBackNoticeReq;
import com.jsunicom.task.request.CallBackNoticeRsp;
import com.jsunicom.task.service.order.PayOrderService;
import com.jsunicom.task.utils.HttpUtil;
import com.jsunicom.task.utils.UpcEncodeUtilForJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class PayOrderServiceImpl implements PayOrderService {

    @Resource
    private TfFPayLogSettleCenterMapper tfFPayLogSettleCenterMapper;

    @Resource
    private CommonProperties commonProperties;

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private TlOrdMsglogMapper tlOrdMsglogMapper;

    @Override
    public Result cashierPay(JSONObject param) {
        Result respInfo = new Result();
        try {
            String staffId = (String) param.get("staffId");
            String epachyCode = (String) param.get("cucEparchyCode");
            String operId = (String) param.get("operId");
            String operName = (String) param.get("operName");
            String channelId = (String) param.get("channelId");

            if (StringUtils.isBlank(staffId)) {
                respInfo.setCode(500L);
                respInfo.setMsg("staffId节点为空");
                return respInfo;
            }
            if (StringUtils.isBlank(epachyCode)) {
                respInfo.setCode(500L);
                respInfo.setMsg("cucEparchyCode节点为空");
                return respInfo;
            }
            if (StringUtils.isBlank(operId)) {
                respInfo.setCode(500L);
                respInfo.setMsg("operId节点为空");
                return respInfo;
            }
            if (StringUtils.isBlank(channelId)) {
                respInfo.setCode(500L);
                respInfo.setMsg("channelId节点为空");
                return respInfo;
            }
            String orderId = (String) param.get("orderId");
            if (StringUtils.isBlank(orderId)) {
                respInfo.setCode(500L);
                respInfo.setMsg("orderId节点为空");
                return respInfo;
            }
            String tradeId = (String) param.get("tradeId"); //cbss流水号
            if (StringUtils.isBlank(tradeId)) {
                respInfo.setCode(500L);
                respInfo.setMsg("tradeId节点为空");
                return respInfo;
            }
            String redirectUrl = (String) param.get("redirectUrl"); //支付成功跳转地址
            if (StringUtils.isBlank(redirectUrl)) {
                respInfo.setCode(500L);
                respInfo.setMsg("redirectUrl节点为空");
                return respInfo;
            }

            //判断订单是否重复发起支付
            /*int cnt=tfFPayLogSettleCenterMapper.countInPayById(orderId);
            if (cnt>0){
                respInfo = new RespInfo(Result.ERR2007);
                return respInfo;
            }*/
            //获取该订单有效的支付记录
            List<TfFPayLogSettleCenter> payLogs = tfFPayLogSettleCenterMapper.qryPayLogById(orderId);
            String urlStr = "";
            String payTag = "0";
            String payTradeId = "";
            String busiMercId = "";

            for (int i = 0; i < payLogs.size(); i++) {
                TfFPayLogSettleCenter log = payLogs.get(i);
                if (StringUtils.isNotBlank(log.getPayResult())) {
                    if (log.getPayResult().equals("SUCCESS")) {
                        payTag = "1";
                        payTradeId = log.getBusiOrderId();
                        busiMercId = log.getBusiMercId();
                        break;
                    }
                }
            }

//            if (payTag.equals("1")) {
//                //该订单已支付成功，直接返回
//                JSONObject data = new JSONObject();
//                data.put("PAY_RESULT", "SUCCESS");
//                data.put("OUT_ORDER_ID", payTradeId);
//                data.put("MERCHANT_ID", busiMercId);
//                respInfo.setCode(200L);
//                respInfo.setMsg("支付成功");
//                respInfo.setData(data);
//                log.info("该订单已支付成功，orderId=" + orderId);
//                return respInfo;
//            }

       /*     payTag = "0";
            payTradeId = "";
            busiMercId = "";*/

//            if (payLogs.size() > 0) {
//                for (int i = 0; i < payLogs.size(); i++) {
//                    TfFPayLogSettleCenter log = payLogs.get(i);
//                    //调用总部支付状态查询接口，成功的直接返回
//                    Map<String, Object> payInfo = new HashMap<>();
//                    payInfo.put("staffId", staffId);
//                    payInfo.put("operId", operId);
//                    payInfo.put("oriOrderId", log.getBusiOrderId());
//                    Result<JSONObject> rspInfo = payStatusQuery(JSONObject.parseObject(JSON.toJSONString(payInfo)));
//                    if (rspInfo.getSuccess()) {
//                        Map<String, Object> data = rspInfo.getData();
//                        String resultCode = (String) data.get("RESULT_CODE");
//                        if (resultCode.equals("SUCCESS")) {
//                            payTag = "1";
//                            payTradeId = log.getBusiOrderId();
//                            busiMercId = log.getBusiMercId();
//                            break;
//                        }
//                    }
//                }
//                if (payTag.equals("1")) {
//                    //该订单已支付成功，直接返回
//                    JSONObject data = new JSONObject();
//                    data.put("PAY_RESULT", "SUCCESS");
//                    data.put("OUT_ORDER_ID", payTradeId);
//                    data.put("MERCHANT_ID", busiMercId);
//                    respInfo.setCode(200L);
//                    respInfo.setMsg("已支付成功");
//                    respInfo.setData(data);
//                    log.info("该订单已支付成功，orderId=" + orderId);
//                    return respInfo;
//                }
//            }
        /*    payTag = "0";
            payTradeId = "";
            busiMercId = "";
            urlStr = "";*/
            //存在有效的二维码记录，则返回该url
        /*    if (payLogs.size() > 0) {
                for (int i = 0; i < payLogs.size(); i++) {
                    TfFPayLogSettleCenter log = payLogs.get(i);
                    if (StringUtils.isNotBlank(log.getUrl())) {
                        Date now = new Date();
                        long times = 30 * 60 * 1000; //30分钟
                        Date date1 = new Date(now.getTime() - times);
                        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                        String beginTime = df.format(date1);
                        String busiOrderDate = log.getBusiOrderDate();
                        int timeFlag = busiOrderDate.compareTo(beginTime);
                        if (timeFlag > 0) {
                            //有效的url
                            urlStr = log.getUrl();
                            payTradeId = log.getBusiOrderId();
                            busiMercId = log.getBusiMercId();
                            break;
                        }
                    }
                }
                if (StringUtils.isNotBlank(urlStr)) {
                    JSONObject data = new JSONObject();
                    data.put("URL", urlStr);
                    data.put("OUT_ORDER_ID", payTradeId);
                    data.put("MERCHANT_ID", busiMercId);
                    respInfo.setCode(500L);
                    respInfo.setMsg("该订单存在未失效支付二维码，orderId=" + orderId);
                    respInfo.setData(data);
                    log.info("该订单存在未失效支付二维码，orderId=" + orderId);
                    return respInfo;
                }
            }*/

            List<HashMap<String, Object>> list = tfFPayLogSettleCenterMapper.qryParamsCashierPay();
            if (list == null || list.size() == 0) {
                respInfo.setCode(500L);
                respInfo.setMsg("CashierPay参数未配置");
                return respInfo;
            }
            if (StringUtils.isBlank(list.get(0).get("param1").toString()) || StringUtils.isBlank(list.get(0).get("param3").toString()) || StringUtils.isBlank(list.get(0).get("param4").toString())) {
                respInfo.setCode(500L);
                respInfo.setMsg("CashierPay参数未配置");
                return respInfo;
            }
            param.put("merchantId", list.get(0).get("param1"));
            param.put("redirectUrl", redirectUrl);
            param.put("notifyUrl", list.get(0).get("param2"));
            param.put("payMethod", list.get(0).get("param3"));
            param.put("busiId", list.get(0).get("param4"));
            param.put("presentForm", list.get(0).get("param5"));
            String busiId = list.get(0).get("param4").toString();

            String publicKey="";
            String signKey="";
            if(busiId.equals("JSCJ")){
                publicKey=commonProperties.getPublicKeyQszx();
                signKey=commonProperties.getSignKeyQszx();
            }
            if(busiId.equals("QS24")){
                publicKey=commonProperties.getPublicKeyQszxQs24();
                signKey=commonProperties.getSignKeyQszxQs24();
            }

            JSONObject orderBody = genCashierPay(param, publicKey,signKey);
            JSONObject outputObject = null;
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            log.info("返回结果:{}", outputObject);
            String url1 = commonProperties.getCashierPay();
            log.info("CashierPay url:{}", url1);
            String responseBody = HttpUtil.sendRequest(url1, orderBody, uniBssAttached,"CashierPay");
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            // JSONObject jsonObject = responseJson.getJSONObject("DATA");
            JSONObject uniBssHead = responseJson.getJSONObject("UNI_BSS_HEAD");
            JSONObject uniBssBody = responseJson.getJSONObject("UNI_BSS_BODY");
            JSONObject rsp = uniBssBody.getJSONObject("CASHIER_PAY_RSP");

            if (StringUtils.equals("00000", uniBssHead.getString("RESP_CODE"))) {
                if (StringUtils.equals("0000", rsp.getString("RSP_CODE"))) {
                    String content = "";
                    String contentNew = "";
                    if (rsp.containsKey("CONTENT")) {
                        content = rsp.getString("CONTENT");
                        //解密
                        contentNew = UpcEncodeUtilForJson.publicDecode(content, publicKey);
                        log.info("解密后content :{}", contentNew);
                        JSONObject data = JSONObject.parseObject(contentNew);
                        String outTradeNo = "";
                        outTradeNo = data.getString("OUT_ORDER_ID");
                        String url = "";
                        url = data.getString("URL");
                        respInfo.setCode(200L);
                        respInfo.setData(data);

                        String fee = (String) param.get("realFee");
                        String merchantId = (String) param.get("merchantId");
                        TfFPayLogSettleCenter tfFAppPayLog = new TfFPayLogSettleCenter();
                        //    String logId = IDUtil.genLogId();
                        SimpleDateFormat dateFormat = new SimpleDateFormat("MM");
                        String format = dateFormat.format(new Date());
                        //入main表
                        // TfOrdMain tfOrdMain = new TfOrdMain();
                        // tfOrdMain.setOrderId(orderId);
                        // tfOrdMain.setOrderAmount(fee);
                        // tfOrdMain.setRealAmount(fee);
                        // tfOrdMain.setPayResult("SUCCESS");
                        // tfOrdMain.setPayMode((String) param.get("payMethod"));
                        // tfOrdMainMapper.updateOrdMainInfoByorderId(tfOrdMain);
                        tfFAppPayLog.setPartitionId(Long.parseLong(format));
                        tfFAppPayLog.setOrderId(orderId);
                        tfFAppPayLog.setBusiId(busiId);
                        tfFAppPayLog.setBusiOrderId(outTradeNo);
                        tfFAppPayLog.setOrderType("01");
                        tfFAppPayLog.setReqWay("3");
                        tfFAppPayLog.setProvinceCode("34");
                        tfFAppPayLog.setEparchyCode((String) param.get("cucEparchyCode"));
                        tfFAppPayLog.setOperId((String) param.get("operId"));
                        tfFAppPayLog.setChannelId((String) param.get("channelId"));
                        tfFAppPayLog.setTradeId("0000");
                        tfFAppPayLog.setBusiAmt(fee); //单位是元
                        tfFAppPayLog.setBusiCellno((String) param.get("serialNumber"));
                        // tfFAppPayLog.setEpayqrcodeTransIdo(reqId);
                        tfFAppPayLog.setBusiMercId(merchantId);
                        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddHHmmss");
                        String orderDate = dateFormat1.format(new Date());
                        tfFAppPayLog.setTradeId(tradeId);
                        tfFAppPayLog.setBusiOrderDate(orderDate);
                        tfFAppPayLog.setGoodsId((String) param.get("goodsId"));
                        tfFAppPayLog.setGoodsName((String) param.get("goodsName"));
                        tfFAppPayLog.setPayMethod((String) param.get("payMethod"));
                        tfFAppPayLog.setUrl(url);

                        tfFAppPayLog.setUpdateTime(new Date());
                        tfFAppPayLog.setCityCode("0");
                        tfFAppPayLog.setCancelTag("0");
                        tfFAppPayLog.setCheckTag("0");

                        tfFPayLogSettleCenterMapper.insertSelective(tfFAppPayLog);
                        log.info("支付记录入表结束" + outTradeNo);
                    } else {
                        respInfo.setCode(500L);
                        respInfo.setMsg("CONTENT节点为空");
                    }
                } else {
                    respInfo.setCode(500L);
                    respInfo.setMsg(rsp.getString("RSP_NAME"));
                }
            } else {
                respInfo.setCode(500L);
                respInfo.setMsg(uniBssHead.getString("RESP_DESC"));
            }
        } catch (Exception e) {
            log.error("cashierPay支付下单接口异常:{}", e);
            respInfo.setCode(500L);
            respInfo.setMsg(StringUtils.substring(e.getMessage(), 0, 500));
            return respInfo;
        }
        log.info("PayOrderService - cashierPay - respInfo：{} ", JSONObject.toJSONString(respInfo));
        return respInfo;
    }

    @Override
    public Result payStatusQuery(JSONObject param) {
        Result resultSuc = new Result(200L, "", true, "支付状态查询", "支付状态查询成功", 0);
        Result resultFail = new Result(500L, "", false, "支付状态查询", "支付状态查询失败", 0);
        try {
            String staffId = (String) param.get("staffId");
            String operId = (String) param.get("operId");
            String oriOrderId = (String) param.get("oriOrderId");

            if (StringUtils.isBlank(staffId)) {
                resultFail.setMsg("staffId节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(oriOrderId)) {
                resultFail.setMsg("oriOrderId节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(operId)) {
                resultFail.setMsg("operId节点为空");
                return resultFail;
            }
            TfFPayLogSettleCenter tfFPayLog = new TfFPayLogSettleCenter();
            tfFPayLog = tfFPayLogSettleCenterMapper.selectByPrimaryKey(oriOrderId);
            if (tfFPayLog == null) {
                log.error("===payStatusQuery======未查询到该支付订单:" + oriOrderId);
                resultFail.setMsg("未查询到该支付订单:"+ oriOrderId);
                return resultFail;
            }
            param.put("busiId",tfFPayLog.getBusiId());
            String busiId=tfFPayLog.getBusiId();
            String publicKey="";
            String signKey="";
            if(busiId.equals("JSCJ")){
                publicKey=commonProperties.getPublicKeyQszx();
                signKey=commonProperties.getSignKeyQszx();
            }
            if(busiId.equals("QS24")){
                publicKey=commonProperties.getPublicKeyQszxQs24();
                signKey=commonProperties.getSignKeyQszxQs24();
            }

            JSONObject orderBody = genPayStatusQueryParam(param, publicKey,signKey);

            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            String url1 = commonProperties.getPayStatusQuery();
            log.info("PayStatusQuery url:{}", url1);
            String responseBody = HttpUtil.sendRequest(url1, orderBody, uniBssAttached,"PayStatusQuery");
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            log.info("返回结果:{}", responseJson);
            // JSONObject jsonObject = responseJson.getJSONObject("DATA");
            JSONObject uniBssHead = responseJson.getJSONObject("UNI_BSS_HEAD");
            JSONObject uniBssBody = responseJson.getJSONObject("UNI_BSS_BODY");
            JSONObject rsp = uniBssBody.getJSONObject("PAY_STATUS_QUERY_RSP");

            if (StringUtils.equals("00000", uniBssHead.getString("RESP_CODE"))) {
                if (StringUtils.equals("0000", rsp.getString("RSP_CODE"))) {
                    String content = "";
                    String contentNew = "";
                    if (rsp.containsKey("CONTENT")) {
                        content = rsp.getString("CONTENT");
                        //解密
                        contentNew = UpcEncodeUtilForJson.publicDecode(content, publicKey);
                        log.info("解密后content :{}", contentNew);
                        JSONObject data = JSONObject.parseObject(contentNew);
                        resultSuc.setData(data);
                    } else {
                        resultFail.setMsg("CONTENT节点为空");
                        return resultFail;
                    }
                } else {
                    resultFail.setMsg(rsp.getString("RSP_NAME"));
                    return resultFail;
                }
            } else {
                resultFail.setMsg(uniBssHead.getString("RESP_DESC"));
                return resultFail;
            }
        } catch (Exception e) {
            log.error("payStatusQuery支付状态查询接口异常:{}", e);
            resultFail.setMsg(StringUtils.substring(e.getMessage(), 0, 500));
            return resultFail;
        }
        log.info("PayOrderService - payStatusQuery - respInfo：{} ", JSONObject.toJSONString(resultSuc));
        return resultSuc;
    }

    @Override
    public CallBackNoticeRsp<JSONObject> liquidationInfo(CallBackNoticeReq req) {
        CallBackNoticeRsp<JSONObject> respInfo = null;
        try {
            //获取公钥
            log.info("入参:{}", req.toString());
            String busiId = req.getBUSI_ID();
            String content = req.getCONTENT();
            String contentNew = "";
            String publicKey="";
            String signKey="";
            if(busiId.equals("JSCJ")){
                publicKey=commonProperties.getPublicKeyQszx();
                signKey=commonProperties.getSignKeyQszx();
            }
            if(busiId.equals("QS24")){
                publicKey=commonProperties.getPublicKeyQszxQs24();
                signKey=commonProperties.getSignKeyQszxQs24();
            }
            //解密
            contentNew = UpcEncodeUtilForJson.publicDecode(content, publicKey);
            log.info("解密后content :{}", contentNew);
            JSONObject data = JSONObject.parseObject(contentNew);
            String outTradeNo = "";
            outTradeNo = data.getString("OUT_ORDER_ID");
            if (outTradeNo != null) {
                TfFPayLogSettleCenter tfFPayLog = new TfFPayLogSettleCenter();
                tfFPayLog = tfFPayLogSettleCenterMapper.selectByPrimaryKey(outTradeNo);
                if (tfFPayLog == null) {
                    log.error("===liquidationInfo======未查询到该支付订单:" + outTradeNo);
                    respInfo = new CallBackNoticeRsp<>("FAIL");
                    return respInfo;
                }
                String orderId = "";
                orderId = tfFPayLog.getOrderId();
                //更新订单的支付状态
               /* TfOrdMain tfOrdMain = new TfOrdMain();
                tfOrdMain.setOrderId(orderId);
                tfOrdMain.setPayResult(data.getString("RESULT_CODE"));
                tfOrdMain.setRealAmount(data.getString("REAL_FEE"));
                tfOrdMainMapper.updateOrdMainInfoByorderId(tfOrdMain);
                log.info("==支付结果回调:更新订单主表结束" + orderId);*/

                updateOrderPayStatus(orderId);

                TfFPayLogSettleCenter tfFPayLogSettleCenter = new TfFPayLogSettleCenter();
                tfFPayLogSettleCenter.setBusiOrderId(outTradeNo);
                tfFPayLogSettleCenter.setPayResult(data.getString("RESULT_CODE"));  //SUCCESS支付成功  FAIL 支付失败   PAYING 未支付  WAIT_CONFIRM 冻结成功，等待确认放款
                tfFPayLogSettleCenter.setPayResultMsg(data.getString("RESULT_MSG"));
                tfFPayLogSettleCenter.setPayMethod(data.getString("PAY_METHOD"));  //支付方式
                tfFPayLogSettleCenter.setPayTotalAmt(data.getString("TOTAL_FEE"));  //总金额 单位：元
                tfFPayLogSettleCenter.setPayActAmt(data.getString("REAL_FEE"));  //实付款 单位：元
                tfFPayLogSettleCenter.setTransactionsId(data.getString("TRANSACTIONS_ID"));  //支付中心订单号
                tfFPayLogSettleCenter.setPayTime(data.getString("TRADE_TIME"));  //交易时间
                tfFPayLogSettleCenter.setBusiMercId(data.getString("MERCHANT_ID"));  //商户ID
                tfFPayLogSettleCenter.setOperId(data.getString("CREATE_OPER_ID")); //支付下单人编号
                tfFPayLogSettleCenter.setPayAgent(data.getString("PAY_AGENT")); //支付机构
                tfFPayLogSettleCenter.setPayTransactionId(data.getString("PAY_TRANSACTION_ID"));
                tfFPayLogSettleCenter.setPaymentSubMethod(data.getString("PAYMENT_SUB_METHOD")); //支付机构子支付方式
                tfFPayLogSettleCenter.setPayOrderId(data.getString("PAY_ORDER_ID"));  //支付请求流水号
                Date time = new Date();
                tfFPayLogSettleCenter.setUpdateTime(time);
                tfFPayLogSettleCenter.setCheckTag("1");
                tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(tfFPayLogSettleCenter);
                log.info("支付结果回调更新结束" + outTradeNo);
            }
            respInfo = new CallBackNoticeRsp<>("SUCCESS");
        } catch (Exception e) {
            log.error("liquidationInfo支付通知接口异常:{}", e);
            respInfo = new CallBackNoticeRsp<>("FAIL");
            return respInfo;
        }
        return respInfo;
    }

    @Override
    public Result payRefund(JSONObject param) {
        Result resultSuc = new Result(200L, "", true, "支付退款", "支付退款成功", 0);
        Result resultFail = new Result(500L, "", false, "支付退款", "支付退款失败", 0);
        try {
            String staffId = (String) param.get("staffId");
            String epachyCode = (String) param.get("cucEparchyCode");
            String merchantId = (String) param.get("merchantId");
            String operId = (String) param.get("operId");
            String operName = (String) param.get("operName");
            String outRefundId = (String) param.get("outRefundId"); //被退款流水号

            if (StringUtils.isBlank(staffId)) {
                resultFail.setMsg("staffId节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(epachyCode)) {
                resultFail.setMsg("cucEparchyCode节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(operId)) {
                resultFail.setMsg("operId节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(outRefundId)) {
                resultFail.setMsg("outRefundId节点为空");
                return resultFail;
            }
            String realFee = (String) param.get("realFee");  //单位元
            String totalFee = (String) param.get("totalFee");

            if (StringUtils.isBlank(merchantId)) {
                resultFail.setMsg("merchantId节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(totalFee)) {
                resultFail.setMsg("totalFee节点为空");
                return resultFail;
            }
            if (StringUtils.isBlank(realFee)) {
                resultFail.setMsg("realFee节点为空");
                return resultFail;
            }

            //判断是否可以退款
            TfFPayLogSettleCenter tfFPayLog1 = new TfFPayLogSettleCenter();
            tfFPayLog1 = tfFPayLogSettleCenterMapper.selectByPrimaryKey(outRefundId);
            if (tfFPayLog1 == null) {
                resultFail.setMsg("未查询到该支付订单");
                return resultFail;
            }
            String busiId=tfFPayLog1.getBusiId();
            String publicKey="";
            String signKey="";
            if(busiId.equals("JSCJ")){
                publicKey=commonProperties.getPublicKeyQszx();
                signKey=commonProperties.getSignKeyQszx();
            }
            if(busiId.equals("QS24")){
                publicKey=commonProperties.getPublicKeyQszxQs24();
                signKey=commonProperties.getSignKeyQszxQs24();
            }

            if (tfFPayLog1.getCancelTag().equals("1")) {
                resultFail.setMsg("该支付订单支付状态不正常，或者已退款，无法申请退款处理！");
                return resultFail;
            } else if (tfFPayLog1.getPayResult() == null) {
                //    respInfo.setMsg("该订单尚未支付或者还未到账，请耐心等待几分钟再提交");
                //到清算中心查询支付状态
                Map<String, Object> payInfo = new HashMap<>();
                payInfo.put("staffId", staffId);
                payInfo.put("operId", operId);
                payInfo.put("oriOrderId", outRefundId);
                Result<JSONObject> rspInfo = payStatusQuery(JSONObject.parseObject(JSON.toJSONString(payInfo)));
                if (rspInfo.getSuccess()) {
                    Map<String, Object> data = rspInfo.getData();
                    String resultCode = (String) data.get("RESULT_CODE");
                    if (resultCode.equals("SUCCESS")) {
                        ;
                    } else {
                        resultFail.setMsg("该支付订单支付状态不正常，或者已退款，无法申请退款处理！");
                        return resultFail;
                    }
                }
            } else if (!tfFPayLog1.getPayResult().equals("SUCCESS")) {
                resultFail.setMsg("该支付订单支付状态不正常，或者已退款，无法申请退款处理！");
                return resultFail;
            }
            param.put("busiId",busiId);
            JSONObject orderBody = genPayRefund(param,publicKey,signKey);
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            String url1 = commonProperties.getPayRefund();
            log.info("PayRefund url:{}", url1);
            String responseBody = HttpUtil.sendRequest(url1, orderBody, uniBssAttached,"PayRefund");
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            log.info("返回结果:{}", responseJson);
            // JSONObject jsonObject = responseJson.getJSONObject("DATA");
            JSONObject uniBssHead = responseJson.getJSONObject("UNI_BSS_HEAD");
            JSONObject uniBssBody = responseJson.getJSONObject("UNI_BSS_BODY");
            JSONObject rsp = uniBssBody.getJSONObject("PAY_REFUND_RSP");

            if (StringUtils.equals("00000", uniBssHead.getString("RESP_CODE"))) {
                if (StringUtils.equals("0000", rsp.getString("RSP_CODE"))) {
                    String content = "";
                    String contentNew = "";
                    if (rsp.containsKey("CONTENT")) {
                        content = rsp.getString("CONTENT");
                        //解密
                        contentNew = UpcEncodeUtilForJson.publicDecode(content, publicKey);
                        log.info("解密后content :{}", contentNew);

                        JSONObject data = JSONObject.parseObject(contentNew);
                        String outTradeNo = "";
                        outTradeNo = (String) param.get("OUT_ORDER_ID");
                        String url = "";
                        url = data.getString("URL");

                        TfFPayLogSettleCenter tfFPayLogSettleCenter = new TfFPayLogSettleCenter();
                        tfFPayLogSettleCenter = tfFPayLog1;
                        Date time = new Date();
                        tfFPayLogSettleCenter.setBusiOrderId(outTradeNo);
                        SimpleDateFormat dateFormat = new SimpleDateFormat("MM");
                        String format = dateFormat.format(new Date());
                        tfFPayLogSettleCenter.setPartitionId(Long.parseLong(format));
                        tfFPayLogSettleCenter.setBusiId(busiId);
                        tfFPayLogSettleCenter.setOrderType("02");
                        tfFPayLogSettleCenter.setEparchyCode(epachyCode);
                        tfFPayLogSettleCenter.setOperId(operId);

                        tfFPayLogSettleCenter.setPayResult(data.getString("RESULT_CODE"));  //SUCCESS退款成功 FAIL 退款失败 PAYING 未支付 REFUNDING退款中  RECHARGE退款充值
                        tfFPayLogSettleCenter.setPayResultMsg(data.getString("RESULT_MSG"));
                        tfFPayLogSettleCenter.setPayMethod(data.getString("PAY_METHOD"));  //支付方式
                        tfFPayLogSettleCenter.setTransactionsId(data.getString("TRANSACTIONS_ID"));  //支付中心订单号
                        tfFPayLogSettleCenter.setPayTime(data.getString("TRADE_TIME"));  //交易时间
                        tfFPayLogSettleCenter.setCancelTag("1");
                        tfFPayLogSettleCenter.setCancelTime(time);
                        tfFPayLogSettleCenter.setRefundOrderId(outRefundId);
                        tfFPayLogSettleCenter.setUpdateTime(time);
                        tfFPayLogSettleCenterMapper.insertSelective(tfFPayLogSettleCenter);
                        log.info("支付退款记录入表结束" + outTradeNo);

                        String resultCode = "";
                        resultCode = data.getString("RESULT_CODE");
                        if (resultCode.equals("SUCCESS")) {
                            TfFPayLogSettleCenter tfFPayLog = new TfFPayLogSettleCenter();
                            tfFPayLog.setBusiOrderId(outRefundId);
                            tfFPayLog.setCancelTag("1");
                            tfFPayLog.setUpdateTime(time);
                            tfFPayLog.setCancelTime(time);
                            tfFPayLog.setRefundOrderId(outTradeNo);
                            tfFPayLog.setPayResult("REFUND_SUCCESS");
                            tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(tfFPayLog);
                            log.info("原支付记录更新结束" + outRefundId);
                            resultSuc.setData(data);
                        }else{
                            TfFPayLogSettleCenter tfFPayLog = new TfFPayLogSettleCenter();
                            tfFPayLog.setBusiOrderId(outRefundId);
                            tfFPayLog.setCancelTag("1");
                            tfFPayLog.setUpdateTime(time);
                            tfFPayLog.setCancelTime(time);
                            tfFPayLog.setRefundOrderId(outTradeNo);
                            tfFPayLog.setCheckTag("0");
                            tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(tfFPayLog);
                            log.info("原支付记录更新结束" + outRefundId);
                            resultFail.setMsg(data.getString("RESULT_MSG"));
                            return resultFail;
                        }

                    } else {
                        resultFail.setMsg("CONTENT节点为空");
                        return resultFail;
                    }
                } else {
                    resultFail.setMsg(rsp.getString("RSP_NAME"));
                    return resultFail;
                }
            } else {
                resultFail.setMsg(uniBssHead.getString("RESP_DESC"));
                return resultFail;
            }
        } catch (Exception e) {
            log.error("payRefund支付退款接口异常:{}", e);
            resultFail.setMsg(StringUtils.substring(e.getMessage(), 0, 500));
            return resultFail;
        }
        log.info("PayOrderService - payRefund - respInfo：{} ", JSONObject.toJSONString(resultSuc));
        return resultSuc;
    }

    @Override
    public List<TfFPayLogSettleCenter> qryPaySuccessInfo(String orderId) {
        return tfFPayLogSettleCenterMapper.qryPaySuccess(orderId);
    }

    static public String geneOrderId(SimpleDateFormat df) {
        return df.format(new Date()) + String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

    public JSONObject genPayRefund(Map<String, Object> inParam, String publicKey,String signKey) throws Exception {
        Map<String, Object> reqMap = new HashMap<>();
        String staffId = (String) inParam.get("staffId");
        String epachyCode = (String) inParam.get("cucEparchyCode");
        String merchantId = (String) inParam.get("merchantId");
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        String operId = (String) inParam.get("operId");
        String operName = (String) inParam.get("operName");
        String busiId = (String) inParam.get("busiId");

        String outTradeNo = "34" + geneOrderId(df);
        String outRefundId = (String) inParam.get("outRefundId"); //被退款流水号
        String content = "";
        String realFee = (String) inParam.get("realFee");  //单位元

        //UNI_BSS_BODY==========================
        JSONObject requestMap = new JSONObject();
        JSONObject req = new JSONObject();
        JSONObject contentObject = new JSONObject();

        contentObject.put("OUT_ORDER_ID", outTradeNo);//外部流水号
        contentObject.put("MERCHANT_ID", merchantId);//商户号ID   303400710038980
        contentObject.put("TOTAL_FEE", (String) inParam.get("totalFee")); //支付金额  单位：元
        contentObject.put("REAL_FEE", (String) inParam.get("realFee")); //支付金额  单位：元
        contentObject.put("OUT_REFUND_ID", outRefundId);
        contentObject.put("ORDER_TYPE", "02");  //请求码 02整单退款 06部分退款
        contentObject.put("CREATE_OPER_ID", operId);
        contentObject.put("CREATE_OPER_ID_NAME", operName);

        //商品详细信息
        JSONArray goodsList = new JSONArray();
        JSONObject goodsInfo = new JSONObject();
        goodsInfo.put("GOODS_NAME", (String) inParam.get("goodsName")); //属性编码
        goodsInfo.put("GOODS_ID", (String) inParam.get("goodsId")); //属性值
        goodsInfo.put("GOODS_NUM", "1");
        goodsInfo.put("GOODS_PRICE", realFee);
        goodsList.add(goodsInfo);
        contentObject.put("GOODS_DETAIL", goodsList);

        log.info("加密前content :{}", contentObject.toString());

        //签名密钥  UJWXnySpxwemwJSSFO1mE608ny0Xn60B
        // CONTENT 字段内容加密
        content = UpcEncodeUtilForJson.publicEncode(contentObject.toString(), publicKey, signKey);

        req.put("STAFF_ID", staffId);//
        req.put("IN_MODE_CODE", "5834");  //订单接入渠道类型   江苏沃受理-5834
        req.put("BUSI_ID", busiId);  //业务系统ID
        req.put("CONTENT", content); //加密的支付请求参数：Content: 根据访问接口的不同封装不同的数据。需要进行rsa加密 编码格式：UTF-8 签名方法可参考提供的RsaEncodeUtil，UpcEncodeUtilForJson。
        requestMap.put("PAY_REFUND_REQ", req);

        inParam.put("OUT_ORDER_ID", outTradeNo);

        return requestMap;
    }

    public JSONObject genPayStatusQueryParam(Map<String, Object> inParam, String publicKey,String signKey) throws Exception {
        Map<String, Object> reqMap = new HashMap<>();
        String staffId = (String) inParam.get("staffId");
        String operId = (String) inParam.get("operId");
        String content = "";

        //UNI_BSS_BODY==========================
        JSONObject requestMap = new JSONObject();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        JSONObject req = new JSONObject();
        JSONObject contentObject = new JSONObject();

        contentObject.put("REQ_WAY", "3");//请求渠道  1 PC支付  2 APP  3 H5  4 微信公众号  5 微信小程序
        contentObject.put("OUT_ORDER_ID", "34" + geneOrderId(df));//外部流水号
        contentObject.put("ORI_ORDER_ID", (String) inParam.get("oriOrderId"));
        contentObject.put("CREATE_OPER_ID", operId);
        contentObject.put("ORDER_TYPE", "03");  //订单请求类型，03订单查询

        log.info("加密前content :{}", contentObject.toString());

        // CONTENT 字段内容加密
        content = UpcEncodeUtilForJson.publicEncode(contentObject.toString(), publicKey,signKey);

        req.put("STAFF_ID", staffId);//
        req.put("IN_MODE_CODE", "5834");  //订单接入渠道类型   江苏沃受理-5834
        req.put("BUSI_ID", (String) inParam.get("busiId"));  //业务系统ID
        req.put("CONTENT", content); //加密的支付请求参数：Content: 根据访问接口的不同封装不同的数据。需要进行rsa加密 编码格式：UTF-8 签名方法可参考提供的RsaEncodeUtil，UpcEncodeUtilForJson。
        requestMap.put("PAY_STATUS_QUERY_REQ", req);

        return requestMap;
    }

    public JSONObject genCashierPay(Map<String, Object> inParam, String publicKey,String signKey) throws Exception {
        Map<String, Object> reqMap = new HashMap<>();
        String staffId = (String) inParam.get("staffId");
        String epachyCode = (String) inParam.get("cucEparchyCode");
        String sceneType = (String) inParam.get("sceneType");
        String operId = (String) inParam.get("operId");
        String operName = (String) inParam.get("operName");
        String merchantId = (String) inParam.get("merchantId");
        String busiId = (String) inParam.get("busiId");

        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        String outTradeNo = "34" + geneOrderId(df);
        String content = "";

        String realFee = (String) inParam.get("realFee");  //单位元

        //UNI_BSS_BODY==========================
        JSONObject requestMap = new JSONObject();

        JSONObject req = new JSONObject();
        JSONObject contentObject = new JSONObject();

        contentObject.put("ORDER_TYPE", "01");//支付
        contentObject.put("REQ_WAY", "3");//请求渠道  1 PC支付  2 APP  3 H5  4 微信公众号  5 微信小程序
        contentObject.put("OUT_ORDER_ID", outTradeNo);//外部流水号
        contentObject.put("MERCHANT_ID", merchantId);//商户号ID   303400710038980
        contentObject.put("SCENE_TYPE", sceneType);
        contentObject.put("REAL_FEE", realFee); //支付金额  单位：元
        contentObject.put("DETAIL_NAME", "-1"); //支付描述（若没有，默认传-1）
        contentObject.put("REDIRECT_URL", (String) inParam.get("redirectUrl")); //支付完成跳转页面 （沃支付分期支付默认传-1）
        contentObject.put("NOTIFY_URL", (String) inParam.get("notifyUrl")); //支付结果异步通知
        contentObject.put("CREATE_OPER_ID", operId); // 下单人编码 mispos方式和沃支付分期（包括线下-招联分期支付）方式传营业员工号 异业轻触点商户传发展人ID 用户自助下单方式可填-1
        contentObject.put("CREATE_OPER_ID_NAME", operName);  //下单人姓名
        contentObject.put("PROVINCE", "34");
        contentObject.put("CITY", epachyCode);
        contentObject.put("CHANNEL_ID", (String) inParam.get("channelId"));
        contentObject.put("OPER_TYPE_CODE", (String) inParam.get("operTypeCode")); //操作类0-订单受理  1-业务返销  2-撤单 3-改单
        contentObject.put("PAY_METHOD", (String) inParam.get("payMethod")); //支付方式   106 沃支付移动支付
        contentObject.put("REDIRECT_FAIL_URL", "");  //失败交易前台跳转地址
        contentObject.put("FRONT_URL", "");  //前台返回商户结果时使用，前台类交易需上送
        contentObject.put("UNIONPAY_ACCESS_TYPE", "");  //接入类型 0：商户直连接入  1：收单机构接入  2：平台商户接入  银联企业网银支付选填  政企电商化需求填写0
        contentObject.put("PRESENT_FORM", (String) inParam.get("presentForm"));
        //   contentObject.put("ONLINE_OFFLINE","online");
        //商品详细信息
        JSONArray goodsList = new JSONArray();
        JSONObject goodsInfo = new JSONObject();
        goodsInfo.put("GOODS_NAME", inParam.get("goodsName")); //属性编码
        goodsInfo.put("GOODS_ID", inParam.get("goodsId")); //属性值
        goodsInfo.put("GOODS_NUM", "1");
        goodsInfo.put("GOODS_PRICE", realFee);
        goodsList.add(goodsInfo);
        contentObject.put("GOODS_DETAIL", goodsList);

        log.info("加密前content :{}", contentObject.toString());

        //签名密钥  UJWXnySpxwemwJSSFO1mE608ny0Xn60B
        //CONTENT 字段内容加密
        content = UpcEncodeUtilForJson.publicEncode(contentObject.toString(), publicKey, signKey);

        req.put("STAFF_ID", staffId);//
        req.put("IN_MODE_CODE", "5834");  //订单接入渠道类型   江苏沃受理-5834
        req.put("BUSI_ID", busiId);  //业务系统ID
        req.put("CONTENT", content); //加密的支付请求参数：Content: 根据访问接口的不同封装不同的数据。需要进行rsa加密 编码格式：UTF-8 签名方法可参考提供的RsaEncodeUtil，UpcEncodeUtilForJson。
        requestMap.put("CASHIER_PAY_REQ", req);

        return requestMap;
    }

    public String payStatusQuery(String orderId) {
        String payFlag = "";
        //获取该订单有效的支付记录
        List<TfFPayLogSettleCenter> payLogs = tfFPayLogSettleCenterMapper.qryPayLogById(orderId);
        String urlStr = "";
        String payTag = "0";
        String payTradeId = "";
        String busiMercId = "";
        for (int i = 0; i < payLogs.size(); i++) {
            TfFPayLogSettleCenter log = payLogs.get(i);
            if (StringUtils.isNotBlank(log.getPayResult())) {
                if (log.getPayResult().equals("SUCCESS")) {
                    payTag = "1";
                    payTradeId = log.getBusiOrderId();
                    busiMercId = log.getBusiMercId();
                    break;
                }
            }
        }
        //    log.info("PayOrderService - payStatusQuery - respInfo：{} ", JSONObject.toJSONString(respInfo));
        return payFlag;
    }
    @Override
    public void updateOrderPayStatus(String orderId) {

        TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(orderId);
        if (ordMain == null) {
            throw new RuntimeException("根据订单号" + orderId+ "未查询到订单");
        }
        float realAmount=0;
        DecimalFormat fnum = new DecimalFormat("##0.00");
        List<TfFPayLogSettleCenter> payList= tfFPayLogSettleCenterMapper.qryPayLogById(orderId);
        for (TfFPayLogSettleCenter entry : payList) {
            if (entry.getPayResult().equals("SUCCESS") || entry.getPayResult().equals("REFUND_SUCCESS") ){
                log.info("==updateOrderPayStatus="+entry.getPayActAmt());
                realAmount=realAmount+Float.valueOf(entry.getPayActAmt());
            }
        }
        if(realAmount>0){
            String  totalFee = fnum.format(realAmount);
            ordMain.setPayResult("SUCCESS");
            ordMain.setOrderAmount(totalFee);
            ordMain.setRealAmount(totalFee);
            tfOrdMainMapper.updateOrdMainInfoByorderId(ordMain);
            log.info("======updateOrderPayStatus orderId ===="+orderId+"更新支付总金额="+totalFee);
        }

    }
    @Override
    public void updateOrderRefundStatus(String orderId) {

        TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(orderId);
        if (ordMain == null) {
            throw new RuntimeException("根据订单号" + orderId+ "未查询到订单");
        }


    }

}
