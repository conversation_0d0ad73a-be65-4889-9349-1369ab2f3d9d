package com.jsunicom.task.common.constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/9.
 */
public enum PartnerCode {

    SUCC("Success", "成功"),
    CHECK_DEVELCHANNEL("develChannelNull","映射点编码不能为空，请填写映射点编码后编辑"),
    EDIT_FAIL("editFail","调用异业发展人接口修改失败"),
    NOT_EXIST("notExist", "该合伙人不存在"),
    CHECK1("mblNbrExist", "手机号已存在"),
    CHECK2("certNoExist", "身份证号已存在"),
    CHECK3("develIdExist", "发展人Id已存在"),
    CHECK4("busLicenseExist", "营业执照号已存在"),
    CHECK5("rightsBan", "权益商户不允许修改发展人编码"),
    CHECK6("orderExist", "已存在订单的合伙人不允许修改发展人编码"),
    CHECK7("develIdIfUpdate", "发展人Id不可修改"),
    CHECK8("merchantNameExist", "商户名称已存在"),
    CHECK_STATE("stateNull", "合伙人状态不能为空"),
    REPEATED("repeated","重复的操作"),
    ERROR_STATE4("notWaiting","合伙人不可补充资料"),
    ERROR_DELETE("WexinDeleteFailed","合伙人的企业微信删除有误，请记录手机号，以便后续确认企业微信删除情况。"),
    ;

    private PartnerCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;

    private String msg;

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }
}
