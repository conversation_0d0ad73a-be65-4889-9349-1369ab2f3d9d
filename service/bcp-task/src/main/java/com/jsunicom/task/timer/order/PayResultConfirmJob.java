package com.jsunicom.task.timer.order;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.task.entity.TfFPayLogSettleCenter;
import com.jsunicom.task.mapper.order.OrderMonitorJobMapper;
import com.jsunicom.task.mapper.order.TfFPayLogSettleCenterMapper;
import com.jsunicom.task.mapper.order.TfOrdGoodsMainMapper;
import com.jsunicom.task.mapper.order.TfOrdMainMapper;
import com.jsunicom.task.po.order.tf.TfOrdGoodsMain;
import com.jsunicom.task.po.order.tf.TfOrdMain;
import com.jsunicom.task.po.order.tf.TfOrdPayRecord;
import com.jsunicom.task.service.order.GenerateId;
import com.jsunicom.task.service.order.PayOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Configuration
@EnableScheduling
public class PayResultConfirmJob {

    @Resource
    private TfFPayLogSettleCenterMapper tfFPayLogSettleCenterMapper;

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private GenerateId generateId;

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private OrderMonitorJobMapper orderMonitorJobMapper;

    @Autowired
    private TfOrdGoodsMainMapper tfOrdGoodsMainMapper;

    //准实时获取清算中心支付结果，并将支付结果同步到订单侧：更新支付结果信息；更新订单金额；
    @XxlJob("payResultConfirmJob")
    public ReturnT<String> payResultConfirmJob(String param) {
        log.info("======PayResultConfirmJob is starting ====");

        //查询待更新的订单记录
        List<Map<String, String>> map = tfFPayLogSettleCenterMapper.qryUnCheckOrder();
        log.info("======PayResultConfirmJob is 待处理订单记录数 ====" + map.size());
        for (int m = 0; m < map.size(); m++) {
            String orderId = "";
            orderId = map.get(m).get("ORDER_ID").toString();
            List<TfFPayLogSettleCenter> list = tfFPayLogSettleCenterMapper.qryUnCheckById(orderId);
            log.info("======PayResultConfirmJob  orderId====" + orderId + "待同步记录数" + list.size());
            int payFlag = 0;
            int refund = 0;
            int lenth=list.size();
            //0 未处理 1-处理成功 2-处理失败  3-处理中
            for (int i = 0; i < list.size(); i++) {

                TfFPayLogSettleCenter payLog = list.get(i);
                JSONObject queryParam = new JSONObject();
                queryParam.put("staffId", payLog.getOperId());
                queryParam.put("operId", payLog.getOperId());
                queryParam.put("oriOrderId", payLog.getBusiOrderId());
                queryParam.put("orderId", orderId);
                String oriOrderId = payLog.getBusiOrderId();
                try {
                   /* String payResult ="";
                    if(StringUtils.isNotEmpty(payLog.getPayResult())){
                         payResult =payLog.getPayResult();
                    }
                    //当前支付状态：支付中，且20分钟内支付单，则跳过处理
                    if(payLog.getCheckTag().equals("2") && payResult.equals("PAYING")){

                        Date now = new Date();
                        long times = 20 * 60 * 1000; //30分钟  总部url30分钟过期
                        Date date1 = new Date(now.getTime() - times);
                        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                        String beginTime = df.format(date1);

                        long times2 = 5 * 60 * 1000; //30分钟  总部url30分钟过期
                        Date date2 = new Date(now.getTime() - times2);
                        String beginTime2 = df.format(date2);

                        String busiOrderDate = payLog.getBusiOrderDate();
                        int timeFlag = busiOrderDate.compareTo(beginTime);
                        int timeFlag1 = busiOrderDate.compareTo(beginTime2);
                        *//*if (timeFlag >0 && timeFlag1<0) {
                            log.info("====PayResultConfirmJob=====30分钟内支付中订单跳过处理BusiOrderId=" + oriOrderId);
                            continue;
                        }*//*
                    }*/

                    Result<JSONObject> rspInfo = payOrderService.payStatusQuery(queryParam);
                    payLog.setCheckTag("2");
                    if (rspInfo.getSuccess()) {
                        JSONObject data = rspInfo.getData();
                        String resultCode = (String) data.get("RESULT_CODE");
                        if (resultCode.equals("SUCCESS")) {
                            payFlag = 1;
                            payLog.setCheckTag("3");

                        }
                        if (resultCode.equals("REFUND_SUCCESS")) {
                            refund = 1;
                            payLog.setCheckTag("3");
                        }
                        if(resultCode.equals("PAYING")){
                            //未支付，判断url是否过期
                            if (StringUtils.isNotBlank(payLog.getUrl())) {
                                Date now = new Date();
                                long times = 40 * 60 * 1000; //40分钟  总部url30分钟过期
                                Date date1 = new Date(now.getTime() - times);
                                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                                String beginTime = df.format(date1);
                                String busiOrderDate = payLog.getBusiOrderDate();
                                int timeFlag = busiOrderDate.compareTo(beginTime);
                                if (timeFlag < 0) {
                                    //无效的url
                                    payLog.setCheckTag("1");
                                    payLog.setRemark("url过期");
                                }
                            }

                        }
                        if (resultCode.equals("FAIL")) {
                        //    payLog.setCheckTag("1");
                            payLog.setRemark("失败："+data.getString("RESULT_MSG"));
                            if (StringUtils.isNotBlank(payLog.getUrl())) {
                                Date now = new Date();
                                long times = 40 * 60 * 1000; //40分钟  总部url30分钟过期
                                Date date1 = new Date(now.getTime() - times);
                                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                                String beginTime = df.format(date1);
                                String busiOrderDate = payLog.getBusiOrderDate();
                                int timeFlag = busiOrderDate.compareTo(beginTime);
                                if (timeFlag < 0) {
                                    //无效的url
                                    payLog.setCheckTag("1");
                                }
                            }

                        }
                        payLog.setPayResult(data.getString("RESULT_CODE"));  //SUCCESS支付成功  FAIL 支付失败   PAYING 未支付  WAIT_CONFIRM 冻结成功，等待确认放款
                        payLog.setPayResultMsg(data.getString("RESULT_MSG"));
                        payLog.setPayMethod(data.getString("PAY_METHOD"));  //支付方式
                        payLog.setPayTotalAmt(data.getString("TOTAL_FEE"));  //总金额 单位：元
                        payLog.setPayActAmt(data.getString("REAL_FEE"));  //实付款 单位：元
                        payLog.setTransactionsId(data.getString("TRANSACTIONS_ID"));  //支付中心订单号
                        payLog.setPayTime(data.getString("TRADE_TIME"));  //交易时间
                        payLog.setBusiMercId(data.getString("MERCHANT_ID"));  //商户ID
                        payLog.setOperId(data.getString("CREATE_OPER_ID")); //支付下单人编号
                        payLog.setPayAgent(data.getString("PAY_AGENT")); //支付机构
                        payLog.setPayTransactionId(data.getString("PAY_TRANSACTION_ID"));
                        payLog.setPaymentSubMethod(data.getString("PAYMENT_SUB_METHOD")); //支付机构子支付方式
                        payLog.setPayOrderId(data.getString("PAY_ORDER_ID"));  //支付请求流水号
                        Date time = new Date();
                        payLog.setUpdateTime(time);
                        payLog.setCheckDate(time);

                        tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(payLog);
                        log.info("====PayResultConfirmJob=====支付结果更新结束BusiOrderId=" + oriOrderId);

                        if (resultCode.equals("SUCCESS")) {

                            //判断是否同步到充值工单表  订单状态为0050、0060
                            TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(orderId);
                            if (ordMain != null) {
                                if(ordMain.getOrderState().equals("0050") || ordMain.getOrderState().equals("0060")){

                                    //判断是否已经同步过
                                    String busiOrderId =payLog.getBusiOrderId();
                                    //根据orderId和busiOrderId查pay_record表是否存在数据
                                    int res = orderMonitorJobMapper.countPayRecord(orderId,oriOrderId);
                                    if (res == 0){
                                        log.info("=====PayResultConfirmJob==待生成充值工单==BusiOrderId=" + oriOrderId);
                                        com.jsunicom.task.po.order.tf.TfOrdPayRecord payRecord = new TfOrdPayRecord();
                                        payRecord.setPayId("order"+generateId.getId("TF_ORD_PAY_RECORD"));
                                        int fee = Double.valueOf(data.getString("REAL_FEE")).intValue()*100;//单位 元转分

                                        payRecord.setOrderId(orderId);
                                        payRecord.setBusiOrderId(busiOrderId);
                                        payRecord.setFee(String.valueOf(fee));
                                        String serialNumber = payLog.getBusiCellno();
                                        List<TfOrdGoodsMain> tfOrdGoodsMainList = tfOrdGoodsMainMapper.selectByOrderId(orderId);
                                        if (tfOrdGoodsMainList.size()>0) {
                                            serialNumber=tfOrdGoodsMainList.get(0).getMainNumber();
                                        }

                                        payRecord.setSerialNumber(serialNumber);
                                        payRecord.setOperTag("0");  //缴费
                                        payRecord.setOperResult("2"); //初始化
                                        payRecord.setCreateDate(new Date());
                                        payRecord.setPayNum("0");
                                        payRecord.setEparchyCode(ordMain.getEparchyCode());
                                        payRecord.setFeeCode("100006");  //待确认  现网校园用100006
                                        orderMonitorJobMapper.insertTfOrdPayRecordSelective(payRecord);

                                    }
                                }

                            }


                        }


                    } else {
                        log.info("=========支付结果查询失败更新==BusiOrderId==" + oriOrderId);
                        payLog.setCheckTag("2");
                        String desc = "";
                        desc = rspInfo.getMsg();
                        payLog.setRemark(desc);
                        Date time = new Date();
                        payLog.setUpdateTime(time);
                        payLog.setCheckDate(time);
                        tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(payLog);
                    }
                } catch (Exception e) {
                    log.error("======PayResultConfirmJob  ==BusiOrderId= " + oriOrderId + "error==" + e.getMessage());
                    payLog.setCheckTag("2");
                    payLog.setRemark(e.getMessage());
                    Date time = new Date();
                    payLog.setUpdateTime(time);
                    payLog.setCheckDate(time);
                    tfFPayLogSettleCenterMapper.updateByPrimaryKeySelective(payLog);
                    e.printStackTrace();
                }

                if (i==lenth-1) {
                    //最后一条，更新订单金额、支付状态、退款状态等
                    try{
                        if (payFlag==1 || refund==1) {
                            //存在新的支付记录，更新订单金额
                            payOrderService.updateOrderPayStatus(orderId);
                            tfFPayLogSettleCenterMapper.updateCheckById(orderId);
                            log.info("======PayResultConfirmJob  orderId====" + orderId + "====updateOrderPayStatus");
                        }
                        if (refund == 1) {
                            //存在新的退款记录，更新退款状态
                            //    payOrderService.updateOrderRefundStatus(orderId);
                        }
                    }catch (Exception e) {
                        log.error("======PayResultConfirmJob  ==orderId= " + orderId + "error==" + e.getMessage());
                    }

                }

            }

        }

        log.info("======PayResultConfirmJob is ending ====");
        return ReturnT.SUCCESS;
    }


}
