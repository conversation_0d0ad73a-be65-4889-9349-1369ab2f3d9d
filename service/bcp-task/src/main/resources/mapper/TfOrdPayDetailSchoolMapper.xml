<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.task.mapper.TfOrdPayDetailSchoolDao">
    <insert id="integrationPayDetaiSchoolCompensate">
        insert into tf_ord_pay_detai_school(busi_order_id, order_id, order_time, eparchy, serial_number, trade_id,
                                            trade_day, pay_transaction_id, pay_time, business_type, finish_order_id,
                                            trade_type, real_fee, merchant_code, PAY_METHOD, update_time, goods_name,out_busi_order_id)
        SELECT IFNULL(a.BUSI_ORDER_ID, '')                        busi_order_id,
               IFNULL(a.order_id, '')                             order_id,
               DATE_FORMAT(d.CREATE_DATE, '%Y-%m-%d %H:%i:%s')    order_time,
               IFNULL((SELECT sys.param1
                       from td_m_sys_dict sys
                       where sys.param_type = 'cityCode'
                         and sys.param_key = a.eparchy_code), '') eparchy,
               IFNULL(c.MAIN_number, '')                          serial_number,
               IFNULL(c.BUSI_SYS_ORDER_ID, '')                    trade_id,
               IFNULL(SUBSTR(A.Pay_Time, 1, 8), '')               trade_day,
               IFNULL(a.PAY_ORDER_ID, '')                   pay_transaction_id,
               DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d %H:%i:%s') pay_time,
               '新校园'                                     business_type,
               IFNULL(a.PAY_ORDER_ID, '')                   finish_order_id,
               case
                   when a.order_type = '01' THEN '支付'
                   when a.order_type = '02' THEN '退款'
                   else a.order_type end                    trade_type,
               IFNULL(a.PAY_ACT_AMT * 100, '')              real_fee,
               a.BUSI_MERC_ID                               merchant_code,
               a.PAY_METHOD                                 PAY_METHOD,
               now()                                        update_time,
               b.GOODS_NAME                                 GOODS_NAME,
               a.REFUND_ORDER_ID                            out_busi_order_id
        FROM tf_f_pay_log_settlecenter a
                 LEFT JOIN tf_ord_goods_main b ON a.ORDER_ID = b.ORDER_ID
                 LEFT JOIN tf_ord_line c ON a.ORDER_ID = c.ORDER_ID
                 LEFT JOIN tf_ord_main d ON a.ORDER_ID = d.ORDER_ID
        WHERE a.BUSI_ID = 'QS24'
          AND a.PAY_RESULT <![CDATA[ <> ]]> 'PAYING'
          AND DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}
          AND DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d') <![CDATA[ < ]]> #{endTime}
          AND a.BUSI_ORDER_ID not IN (select BUSI_ORDER_ID FROM tf_ord_pay_detai_school)
    </insert>
    <update id="updateSchool">
        update tf_ord_pay_detai_school a
        set trade_id=(select t.BUSI_SYS_ORDER_ID from tf_ord_line t where a.ORDER_ID = t.ORDER_ID limit 1),
            update_time=now()
        where (trade_id = '' or trade_id is null)
          and EXISTS (select 1 from tf_ord_main b where a.ORDER_ID = b.ORDER_ID and b.ORDER_STATE = '0060')
          and EXISTS (select 1 from tf_ord_line c where a.ORDER_ID = c.ORDER_ID and c.BUSI_SYS_ORDER_ID is not null)
    </update>

<!--    <update id="updateRefundOrderId">-->
<!--        UPDATE tf_ord_pay_detai_school a INNER JOIN tf_f_pay_log_settlecenter b-->
<!--        ON a.order_id=b.order_id set a.out_busi_order_id=b.REFUND_ORDER_ID;-->
<!--    </update>-->

    <!--    mappr.xml映射的位置-->
    <insert id="integrationPayDetaiSchool">
        insert into tf_ord_pay_detai_school(busi_order_id, order_id, order_time, eparchy, serial_number, trade_id,
                                            trade_day, pay_transaction_id, pay_time, business_type, finish_order_id,
                                            trade_type, real_fee, merchant_code, PAY_METHOD, update_time, goods_name,out_busi_order_id)
        SELECT IFNULL(a.BUSI_ORDER_ID, '')                        busi_order_id,
               IFNULL(a.order_id, '')                             order_id,
               DATE_FORMAT(d.CREATE_DATE, '%Y-%m-%d %H:%i:%s')    order_time,
               IFNULL((SELECT sys.param1
                       from td_m_sys_dict sys
                       where sys.param_type = 'cityCode'
                         and sys.param_key = a.eparchy_code), '') eparchy,
               IFNULL(c.MAIN_number, '')                          serial_number,
               IFNULL(c.BUSI_SYS_ORDER_ID, '')                    trade_id,
               IFNULL(SUBSTR(A.Pay_Time, 1, 8), '')               trade_day,
               IFNULL(a.PAY_ORDER_ID, '')                         pay_transaction_id,
               DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d %H:%i:%s')       pay_time,
               '新校园'                                            business_type,
               IFNULL(a.PAY_ORDER_ID, '')                         finish_order_id,
               case
                   when a.order_type = '01' THEN '支付'
                   when a.order_type = '02' THEN '退款'
                   else a.order_type end                          trade_type,
               IFNULL(a.PAY_ACT_AMT * 100, '')                    real_fee,
               a.BUSI_MERC_ID                                     merchant_code,
               a.PAY_METHOD                                       PAY_METHOD,
               now()                                              update_time,
               b.GOODS_NAME                                       GOODS_NAME,
               a.REFUND_ORDER_ID                                  out_busi_order_id
        FROM tf_f_pay_log_settlecenter a
                 LEFT JOIN tf_ord_goods_main b ON a.ORDER_ID = b.ORDER_ID
                 LEFT JOIN tf_ord_line c ON a.ORDER_ID = c.ORDER_ID
                 LEFT JOIN tf_ord_main d ON a.ORDER_ID = d.ORDER_ID
        WHERE a.BUSI_ID = 'QS24'
          AND a.PAY_RESULT <![CDATA[ <> ]]> 'PAYING'
          AND DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d') <![CDATA[ >= ]]> #{startTime}
          AND DATE_FORMAT(a.PAY_TIME, '%Y-%m-%d') <![CDATA[ < ]]> #{endTime}
    </insert>
</mapper>