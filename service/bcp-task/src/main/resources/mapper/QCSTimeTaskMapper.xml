<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.QCSTimeTaskMapper">
<update id="update1">
    update
    WO_SC_YI_WORK_ORDER
    set
    ORDER_STATE = '3',
    UPDATED_TIME = now()
    where
    ORDER_ID in (
    select
    *
    from
    (
    select
    t.ORDER_ID
    from
    WO_SC_YI_ACTIVITY t
    left join WO_SC_YI_WORK_ORDER o on
    t.ORDER_ID = o.ORDER_ID
    and o.FLOW_ID = '2'
    and o.ORDER_TYPE = '2'
    where
    o.ORDER_STATE = '1'
    and
    DATE_FORMAT(t.END_TIME , '%Y%m%d') &lt; DATE_FORMAT(now() , '%Y%m%d')) as tab);
</update>
    <update id="update2">
        update
        WO_SC_YI_WORK_ORDER
        set
        ORDER_STATE = '3',
        UPDATED_TIME = now()
        where
        ORDER_ID in (
        select
        *
        from
        (
        select
        t.ORDER_ID
        from
        WO_SC_YI_TASK t
        left join WO_SC_YI_WORK_ORDER o on
        t.ORDER_ID = o.ORDER_ID
        and o.FLOW_ID = '3'
        and o.ORDER_TYPE = '3'
        where
        o.ORDER_STATE = '1'
        and
        DATE_FORMAT(t.END_TIME , '%Y%m%d') &lt; DATE_FORMAT(now() , '%Y%m%d')) as tab);
    </update>
    <update id="update3">
        update
        WO_SC_YI_WORK_ORDER
        set
        ORDER_STATE = '3',
        UPDATED_TIME = now()
        where
        ORDER_ID in (
        select
        *
        from
        (
        select
        o.order_id
        from
        WO_SC_YI_TASK_BASE t
        left join WO_SC_YI_WORK_ORDER o on
        t.BATCH_ID = o.BATCH_ID
        and o.FLOW_ID = '4'
        and o.ORDER_TYPE = '3'
        where
        o.ORDER_STATE = '1'
        and
        DATE_FORMAT(t.END_TIME , '%Y%m%d') &lt; DATE_FORMAT(now() , '%Y%m%d')) as tab);
    </update>
</mapper>
