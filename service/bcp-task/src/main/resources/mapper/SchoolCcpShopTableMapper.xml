<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.SchoolCcpShopTableMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.task.po.SchoolCcpShopTable">
    <result column="month_id" jdbcType="VARCHAR" property="monthId" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchantId" />
    <result column="prov_id" jdbcType="VARCHAR" property="provId" />
    <result column="org_prov_code" jdbcType="VARCHAR" property="orgProvCode" />
    <result column="org_prov_name" jdbcType="VARCHAR" property="orgProvName" />
    <result column="org_area_code" jdbcType="VARCHAR" property="orgAreaCode" />
    <result column="org_area_name" jdbcType="VARCHAR" property="orgAreaName" />
    <result column="org_city_code" jdbcType="VARCHAR" property="orgCityCode" />
    <result column="org_city_name" jdbcType="VARCHAR" property="orgCityName" />
    <result column="org_street_code" jdbcType="VARCHAR" property="orgStreetCode" />
    <result column="org_street_name" jdbcType="VARCHAR" property="orgStreetName" />
    <result column="merchant_type" jdbcType="VARCHAR" property="merchantType" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="busi_license" jdbcType="VARCHAR" property="busiLicense" />
    <result column="principal_name" jdbcType="VARCHAR" property="principalName" />
    <result column="principal_phone" jdbcType="VARCHAR" property="principalPhone" />
    <result column="road_name" jdbcType="VARCHAR" property="roadName" />
    <result column="merchant_scope" jdbcType="VARCHAR" property="merchantScope" />
    <result column="merchant_address" jdbcType="VARCHAR" property="merchantAddress" />
    <result column="customer_relation" jdbcType="VARCHAR" property="customerRelation" />
    <result column="is_has_wideband" jdbcType="VARCHAR" property="isHasWideband" />
    <result column="wideband_type" jdbcType="VARCHAR" property="widebandType" />
    <result column="phone_type" jdbcType="VARCHAR" property="phoneType" />
    <result column="phone_signal" jdbcType="VARCHAR" property="phoneSignal" />
    <result column="street_manager_phone" jdbcType="VARCHAR" property="streetManagerPhone" />
    <result column="street_manager_name" jdbcType="VARCHAR" property="streetManagerName" />
    <result column="busi_classification" jdbcType="VARCHAR" property="busiClassification" />
    <result column="develop_id" jdbcType="VARCHAR" property="developId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
    <result column="baidu_longitude" jdbcType="VARCHAR" property="baiduLongitude" />
    <result column="baidu_latitude" jdbcType="VARCHAR" property="baiduLatitude" />
    <result column="remark_desc" jdbcType="VARCHAR" property="remarkDesc" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="org_strt_manag_phone" jdbcType="VARCHAR" property="orgStrtManagPhone" />
    <result column="org_strt_manag_name" jdbcType="VARCHAR" property="orgStrtManagName" />
    <result column="distri_time" jdbcType="VARCHAR" property="distriTime" />
    <result column="distri_name" jdbcType="VARCHAR" property="distriName" />
    <result column="origin_type" jdbcType="VARCHAR" property="originType" />
    <result column="merchant_state" jdbcType="VARCHAR" property="merchantState" />
    <result column="wideband_deadline" jdbcType="VARCHAR" property="widebandDeadline" />
    <result column="is_convert" jdbcType="VARCHAR" property="isConvert" />
    <result column="month_cost" jdbcType="VARCHAR" property="monthCost" />
    <result column="tell_phone" jdbcType="VARCHAR" property="tellPhone" />
    <result column="sign_id" jdbcType="VARCHAR" property="signId" />
    <result column="sign_type" jdbcType="VARCHAR" property="signType" />
    <result column="is_feeling" jdbcType="VARCHAR" property="isFeeling" />
    <result column="network_flag" jdbcType="VARCHAR" property="networkFlag" />
    <result column="part_id" jdbcType="VARCHAR" property="partId" />
    <result column="day_id" jdbcType="VARCHAR" property="dayId" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="info_insert_time" jdbcType="TIMESTAMP" property="infoInsertTime" />
    <result column="info_update_time" jdbcType="TIMESTAMP" property="infoUpdateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    month_id, merchant_id, prov_id, org_prov_code, org_prov_name, org_area_code, org_area_name,
    org_city_code, org_city_name, org_street_code, org_street_name, merchant_type, merchant_name,
    busi_license, principal_name, principal_phone, road_name, merchant_scope, merchant_address,
    customer_relation, is_has_wideband, wideband_type, phone_type, phone_signal, street_manager_phone,
    street_manager_name, busi_classification, develop_id, create_name, create_time, update_name,
    update_time, longitude, latitude, baidu_longitude, baidu_latitude, remark_desc, delete_flag,
    org_strt_manag_phone, org_strt_manag_name, distri_time, distri_name, origin_type,
    merchant_state, wideband_deadline, is_convert, month_cost, tell_phone, sign_id, sign_type,
    is_feeling, network_flag, part_id, day_id, state, file_name, info_insert_time, info_update_time
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.task.po.SchoolCcpShopTableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from school_ccp_shop_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.jsunicom.task.po.SchoolCcpShopTableExample">
    delete from school_ccp_shop_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.task.po.SchoolCcpShopTable">
    insert into school_ccp_shop_table (month_id, merchant_id, prov_id,
      org_prov_code, org_prov_name, org_area_code,
      org_area_name, org_city_code, org_city_name,
      org_street_code, org_street_name, merchant_type,
      merchant_name, busi_license, principal_name,
      principal_phone, road_name, merchant_scope,
      merchant_address, customer_relation, is_has_wideband,
      wideband_type, phone_type, phone_signal,
      street_manager_phone, street_manager_name,
      busi_classification, develop_id, create_name,
      create_time, update_name, update_time,
      longitude, latitude, baidu_longitude,
      baidu_latitude, remark_desc, delete_flag,
      org_strt_manag_phone, org_strt_manag_name, distri_time,
      distri_name, origin_type, merchant_state,
      wideband_deadline, is_convert, month_cost,
      tell_phone, sign_id, sign_type,
      is_feeling, network_flag, part_id,
      day_id, state, file_name,
      info_insert_time, info_update_time)
    values (#{monthId,jdbcType=VARCHAR}, #{merchantId,jdbcType=VARCHAR}, #{provId,jdbcType=VARCHAR},
      #{orgProvCode,jdbcType=VARCHAR}, #{orgProvName,jdbcType=VARCHAR}, #{orgAreaCode,jdbcType=VARCHAR},
      #{orgAreaName,jdbcType=VARCHAR}, #{orgCityCode,jdbcType=VARCHAR}, #{orgCityName,jdbcType=VARCHAR},
      #{orgStreetCode,jdbcType=VARCHAR}, #{orgStreetName,jdbcType=VARCHAR}, #{merchantType,jdbcType=VARCHAR},
      #{merchantName,jdbcType=VARCHAR}, #{busiLicense,jdbcType=VARCHAR}, #{principalName,jdbcType=VARCHAR},
      #{principalPhone,jdbcType=VARCHAR}, #{roadName,jdbcType=VARCHAR}, #{merchantScope,jdbcType=VARCHAR},
      #{merchantAddress,jdbcType=VARCHAR}, #{customerRelation,jdbcType=VARCHAR}, #{isHasWideband,jdbcType=VARCHAR},
      #{widebandType,jdbcType=VARCHAR}, #{phoneType,jdbcType=VARCHAR}, #{phoneSignal,jdbcType=VARCHAR},
      #{streetManagerPhone,jdbcType=VARCHAR}, #{streetManagerName,jdbcType=VARCHAR},
      #{busiClassification,jdbcType=VARCHAR}, #{developId,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, #{baiduLongitude,jdbcType=VARCHAR},
      #{baiduLatitude,jdbcType=VARCHAR}, #{remarkDesc,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR},
      #{orgStrtManagPhone,jdbcType=VARCHAR}, #{orgStrtManagName,jdbcType=VARCHAR}, #{distriTime,jdbcType=VARCHAR},
      #{distriName,jdbcType=VARCHAR}, #{originType,jdbcType=VARCHAR}, #{merchantState,jdbcType=VARCHAR},
      #{widebandDeadline,jdbcType=VARCHAR}, #{isConvert,jdbcType=VARCHAR}, #{monthCost,jdbcType=VARCHAR},
      #{tellPhone,jdbcType=VARCHAR}, #{signId,jdbcType=VARCHAR}, #{signType,jdbcType=VARCHAR},
      #{isFeeling,jdbcType=VARCHAR}, #{networkFlag,jdbcType=VARCHAR}, #{partId,jdbcType=VARCHAR},
      #{dayId,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
      #{infoInsertTime,jdbcType=TIMESTAMP}, #{infoUpdateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.task.po.SchoolCcpShopTable">
    insert into school_ccp_shop_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="monthId != null">
        month_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="provId != null">
        prov_id,
      </if>
      <if test="orgProvCode != null">
        org_prov_code,
      </if>
      <if test="orgProvName != null">
        org_prov_name,
      </if>
      <if test="orgAreaCode != null">
        org_area_code,
      </if>
      <if test="orgAreaName != null">
        org_area_name,
      </if>
      <if test="orgCityCode != null">
        org_city_code,
      </if>
      <if test="orgCityName != null">
        org_city_name,
      </if>
      <if test="orgStreetCode != null">
        org_street_code,
      </if>
      <if test="orgStreetName != null">
        org_street_name,
      </if>
      <if test="merchantType != null">
        merchant_type,
      </if>
      <if test="merchantName != null">
        merchant_name,
      </if>
      <if test="busiLicense != null">
        busi_license,
      </if>
      <if test="principalName != null">
        principal_name,
      </if>
      <if test="principalPhone != null">
        principal_phone,
      </if>
      <if test="roadName != null">
        road_name,
      </if>
      <if test="merchantScope != null">
        merchant_scope,
      </if>
      <if test="merchantAddress != null">
        merchant_address,
      </if>
      <if test="customerRelation != null">
        customer_relation,
      </if>
      <if test="isHasWideband != null">
        is_has_wideband,
      </if>
      <if test="widebandType != null">
        wideband_type,
      </if>
      <if test="phoneType != null">
        phone_type,
      </if>
      <if test="phoneSignal != null">
        phone_signal,
      </if>
      <if test="streetManagerPhone != null">
        street_manager_phone,
      </if>
      <if test="streetManagerName != null">
        street_manager_name,
      </if>
      <if test="busiClassification != null">
        busi_classification,
      </if>
      <if test="developId != null">
        develop_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="baiduLongitude != null">
        baidu_longitude,
      </if>
      <if test="baiduLatitude != null">
        baidu_latitude,
      </if>
      <if test="remarkDesc != null">
        remark_desc,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="orgStrtManagPhone != null">
        org_strt_manag_phone,
      </if>
      <if test="orgStrtManagName != null">
        org_strt_manag_name,
      </if>
      <if test="distriTime != null">
        distri_time,
      </if>
      <if test="distriName != null">
        distri_name,
      </if>
      <if test="originType != null">
        origin_type,
      </if>
      <if test="merchantState != null">
        merchant_state,
      </if>
      <if test="widebandDeadline != null">
        wideband_deadline,
      </if>
      <if test="isConvert != null">
        is_convert,
      </if>
      <if test="monthCost != null">
        month_cost,
      </if>
      <if test="tellPhone != null">
        tell_phone,
      </if>
      <if test="signId != null">
        sign_id,
      </if>
      <if test="signType != null">
        sign_type,
      </if>
      <if test="isFeeling != null">
        is_feeling,
      </if>
      <if test="networkFlag != null">
        network_flag,
      </if>
      <if test="partId != null">
        part_id,
      </if>
      <if test="dayId != null">
        day_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="infoInsertTime != null">
        info_insert_time,
      </if>
      <if test="infoUpdateTime != null">
        info_update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="monthId != null">
        #{monthId,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="provId != null">
        #{provId,jdbcType=VARCHAR},
      </if>
      <if test="orgProvCode != null">
        #{orgProvCode,jdbcType=VARCHAR},
      </if>
      <if test="orgProvName != null">
        #{orgProvName,jdbcType=VARCHAR},
      </if>
      <if test="orgAreaCode != null">
        #{orgAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="orgAreaName != null">
        #{orgAreaName,jdbcType=VARCHAR},
      </if>
      <if test="orgCityCode != null">
        #{orgCityCode,jdbcType=VARCHAR},
      </if>
      <if test="orgCityName != null">
        #{orgCityName,jdbcType=VARCHAR},
      </if>
      <if test="orgStreetCode != null">
        #{orgStreetCode,jdbcType=VARCHAR},
      </if>
      <if test="orgStreetName != null">
        #{orgStreetName,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        #{merchantType,jdbcType=VARCHAR},
      </if>
      <if test="merchantName != null">
        #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="busiLicense != null">
        #{busiLicense,jdbcType=VARCHAR},
      </if>
      <if test="principalName != null">
        #{principalName,jdbcType=VARCHAR},
      </if>
      <if test="principalPhone != null">
        #{principalPhone,jdbcType=VARCHAR},
      </if>
      <if test="roadName != null">
        #{roadName,jdbcType=VARCHAR},
      </if>
      <if test="merchantScope != null">
        #{merchantScope,jdbcType=VARCHAR},
      </if>
      <if test="merchantAddress != null">
        #{merchantAddress,jdbcType=VARCHAR},
      </if>
      <if test="customerRelation != null">
        #{customerRelation,jdbcType=VARCHAR},
      </if>
      <if test="isHasWideband != null">
        #{isHasWideband,jdbcType=VARCHAR},
      </if>
      <if test="widebandType != null">
        #{widebandType,jdbcType=VARCHAR},
      </if>
      <if test="phoneType != null">
        #{phoneType,jdbcType=VARCHAR},
      </if>
      <if test="phoneSignal != null">
        #{phoneSignal,jdbcType=VARCHAR},
      </if>
      <if test="streetManagerPhone != null">
        #{streetManagerPhone,jdbcType=VARCHAR},
      </if>
      <if test="streetManagerName != null">
        #{streetManagerName,jdbcType=VARCHAR},
      </if>
      <if test="busiClassification != null">
        #{busiClassification,jdbcType=VARCHAR},
      </if>
      <if test="developId != null">
        #{developId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="baiduLongitude != null">
        #{baiduLongitude,jdbcType=VARCHAR},
      </if>
      <if test="baiduLatitude != null">
        #{baiduLatitude,jdbcType=VARCHAR},
      </if>
      <if test="remarkDesc != null">
        #{remarkDesc,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="orgStrtManagPhone != null">
        #{orgStrtManagPhone,jdbcType=VARCHAR},
      </if>
      <if test="orgStrtManagName != null">
        #{orgStrtManagName,jdbcType=VARCHAR},
      </if>
      <if test="distriTime != null">
        #{distriTime,jdbcType=VARCHAR},
      </if>
      <if test="distriName != null">
        #{distriName,jdbcType=VARCHAR},
      </if>
      <if test="originType != null">
        #{originType,jdbcType=VARCHAR},
      </if>
      <if test="merchantState != null">
        #{merchantState,jdbcType=VARCHAR},
      </if>
      <if test="widebandDeadline != null">
        #{widebandDeadline,jdbcType=VARCHAR},
      </if>
      <if test="isConvert != null">
        #{isConvert,jdbcType=VARCHAR},
      </if>
      <if test="monthCost != null">
        #{monthCost,jdbcType=VARCHAR},
      </if>
      <if test="tellPhone != null">
        #{tellPhone,jdbcType=VARCHAR},
      </if>
      <if test="signId != null">
        #{signId,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        #{signType,jdbcType=VARCHAR},
      </if>
      <if test="isFeeling != null">
        #{isFeeling,jdbcType=VARCHAR},
      </if>
      <if test="networkFlag != null">
        #{networkFlag,jdbcType=VARCHAR},
      </if>
      <if test="partId != null">
        #{partId,jdbcType=VARCHAR},
      </if>
      <if test="dayId != null">
        #{dayId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="infoInsertTime != null">
        #{infoInsertTime,jdbcType=TIMESTAMP},
      </if>
      <if test="infoUpdateTime != null">
        #{infoUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.task.po.SchoolCcpShopTableExample" resultType="java.lang.Long">
    select count(*) from school_ccp_shop_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update school_ccp_shop_table
    <set>
      <if test="record.monthId != null">
        month_id = #{record.monthId,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.provId != null">
        prov_id = #{record.provId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgProvCode != null">
        org_prov_code = #{record.orgProvCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgProvName != null">
        org_prov_name = #{record.orgProvName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgAreaCode != null">
        org_area_code = #{record.orgAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgAreaName != null">
        org_area_name = #{record.orgAreaName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCityCode != null">
        org_city_code = #{record.orgCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCityName != null">
        org_city_name = #{record.orgCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgStreetCode != null">
        org_street_code = #{record.orgStreetCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgStreetName != null">
        org_street_name = #{record.orgStreetName,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantType != null">
        merchant_type = #{record.merchantType,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantName != null">
        merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.busiLicense != null">
        busi_license = #{record.busiLicense,jdbcType=VARCHAR},
      </if>
      <if test="record.principalName != null">
        principal_name = #{record.principalName,jdbcType=VARCHAR},
      </if>
      <if test="record.principalPhone != null">
        principal_phone = #{record.principalPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.roadName != null">
        road_name = #{record.roadName,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantScope != null">
        merchant_scope = #{record.merchantScope,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantAddress != null">
        merchant_address = #{record.merchantAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.customerRelation != null">
        customer_relation = #{record.customerRelation,jdbcType=VARCHAR},
      </if>
      <if test="record.isHasWideband != null">
        is_has_wideband = #{record.isHasWideband,jdbcType=VARCHAR},
      </if>
      <if test="record.widebandType != null">
        wideband_type = #{record.widebandType,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneType != null">
        phone_type = #{record.phoneType,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneSignal != null">
        phone_signal = #{record.phoneSignal,jdbcType=VARCHAR},
      </if>
      <if test="record.streetManagerPhone != null">
        street_manager_phone = #{record.streetManagerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.streetManagerName != null">
        street_manager_name = #{record.streetManagerName,jdbcType=VARCHAR},
      </if>
      <if test="record.busiClassification != null">
        busi_classification = #{record.busiClassification,jdbcType=VARCHAR},
      </if>
      <if test="record.developId != null">
        develop_id = #{record.developId,jdbcType=VARCHAR},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=VARCHAR},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=VARCHAR},
      </if>
      <if test="record.baiduLongitude != null">
        baidu_longitude = #{record.baiduLongitude,jdbcType=VARCHAR},
      </if>
      <if test="record.baiduLatitude != null">
        baidu_latitude = #{record.baiduLatitude,jdbcType=VARCHAR},
      </if>
      <if test="record.remarkDesc != null">
        remark_desc = #{record.remarkDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteFlag != null">
        delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.orgStrtManagPhone != null">
        org_strt_manag_phone = #{record.orgStrtManagPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.orgStrtManagName != null">
        org_strt_manag_name = #{record.orgStrtManagName,jdbcType=VARCHAR},
      </if>
      <if test="record.distriTime != null">
        distri_time = #{record.distriTime,jdbcType=VARCHAR},
      </if>
      <if test="record.distriName != null">
        distri_name = #{record.distriName,jdbcType=VARCHAR},
      </if>
      <if test="record.originType != null">
        origin_type = #{record.originType,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantState != null">
        merchant_state = #{record.merchantState,jdbcType=VARCHAR},
      </if>
      <if test="record.widebandDeadline != null">
        wideband_deadline = #{record.widebandDeadline,jdbcType=VARCHAR},
      </if>
      <if test="record.isConvert != null">
        is_convert = #{record.isConvert,jdbcType=VARCHAR},
      </if>
      <if test="record.monthCost != null">
        month_cost = #{record.monthCost,jdbcType=VARCHAR},
      </if>
      <if test="record.tellPhone != null">
        tell_phone = #{record.tellPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.signId != null">
        sign_id = #{record.signId,jdbcType=VARCHAR},
      </if>
      <if test="record.signType != null">
        sign_type = #{record.signType,jdbcType=VARCHAR},
      </if>
      <if test="record.isFeeling != null">
        is_feeling = #{record.isFeeling,jdbcType=VARCHAR},
      </if>
      <if test="record.networkFlag != null">
        network_flag = #{record.networkFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.partId != null">
        part_id = #{record.partId,jdbcType=VARCHAR},
      </if>
      <if test="record.dayId != null">
        day_id = #{record.dayId,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.infoInsertTime != null">
        info_insert_time = #{record.infoInsertTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.infoUpdateTime != null">
        info_update_time = #{record.infoUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update school_ccp_shop_table
    set month_id = #{record.monthId,jdbcType=VARCHAR},
      merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      prov_id = #{record.provId,jdbcType=VARCHAR},
      org_prov_code = #{record.orgProvCode,jdbcType=VARCHAR},
      org_prov_name = #{record.orgProvName,jdbcType=VARCHAR},
      org_area_code = #{record.orgAreaCode,jdbcType=VARCHAR},
      org_area_name = #{record.orgAreaName,jdbcType=VARCHAR},
      org_city_code = #{record.orgCityCode,jdbcType=VARCHAR},
      org_city_name = #{record.orgCityName,jdbcType=VARCHAR},
      org_street_code = #{record.orgStreetCode,jdbcType=VARCHAR},
      org_street_name = #{record.orgStreetName,jdbcType=VARCHAR},
      merchant_type = #{record.merchantType,jdbcType=VARCHAR},
      merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      busi_license = #{record.busiLicense,jdbcType=VARCHAR},
      principal_name = #{record.principalName,jdbcType=VARCHAR},
      principal_phone = #{record.principalPhone,jdbcType=VARCHAR},
      road_name = #{record.roadName,jdbcType=VARCHAR},
      merchant_scope = #{record.merchantScope,jdbcType=VARCHAR},
      merchant_address = #{record.merchantAddress,jdbcType=VARCHAR},
      customer_relation = #{record.customerRelation,jdbcType=VARCHAR},
      is_has_wideband = #{record.isHasWideband,jdbcType=VARCHAR},
      wideband_type = #{record.widebandType,jdbcType=VARCHAR},
      phone_type = #{record.phoneType,jdbcType=VARCHAR},
      phone_signal = #{record.phoneSignal,jdbcType=VARCHAR},
      street_manager_phone = #{record.streetManagerPhone,jdbcType=VARCHAR},
      street_manager_name = #{record.streetManagerName,jdbcType=VARCHAR},
      busi_classification = #{record.busiClassification,jdbcType=VARCHAR},
      develop_id = #{record.developId,jdbcType=VARCHAR},
      create_name = #{record.createName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_name = #{record.updateName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      longitude = #{record.longitude,jdbcType=VARCHAR},
      latitude = #{record.latitude,jdbcType=VARCHAR},
      baidu_longitude = #{record.baiduLongitude,jdbcType=VARCHAR},
      baidu_latitude = #{record.baiduLatitude,jdbcType=VARCHAR},
      remark_desc = #{record.remarkDesc,jdbcType=VARCHAR},
      delete_flag = #{record.deleteFlag,jdbcType=VARCHAR},
      org_strt_manag_phone = #{record.orgStrtManagPhone,jdbcType=VARCHAR},
      org_strt_manag_name = #{record.orgStrtManagName,jdbcType=VARCHAR},
      distri_time = #{record.distriTime,jdbcType=VARCHAR},
      distri_name = #{record.distriName,jdbcType=VARCHAR},
      origin_type = #{record.originType,jdbcType=VARCHAR},
      merchant_state = #{record.merchantState,jdbcType=VARCHAR},
      wideband_deadline = #{record.widebandDeadline,jdbcType=VARCHAR},
      is_convert = #{record.isConvert,jdbcType=VARCHAR},
      month_cost = #{record.monthCost,jdbcType=VARCHAR},
      tell_phone = #{record.tellPhone,jdbcType=VARCHAR},
      sign_id = #{record.signId,jdbcType=VARCHAR},
      sign_type = #{record.signType,jdbcType=VARCHAR},
      is_feeling = #{record.isFeeling,jdbcType=VARCHAR},
      network_flag = #{record.networkFlag,jdbcType=VARCHAR},
      part_id = #{record.partId,jdbcType=VARCHAR},
      day_id = #{record.dayId,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      info_insert_time = #{record.infoInsertTime,jdbcType=TIMESTAMP},
      info_update_time = #{record.infoUpdateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>
