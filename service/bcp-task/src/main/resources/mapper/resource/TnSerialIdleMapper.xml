<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jsunicom.task.mapper.resource.TnSerialIdleMapper">

    <sql id="BASE_COLUMN_LIST">
        si.ID
        ,si.POOL_CODE,si.SERIAL_NO,si.STATE,si.ICCID,si.PSPT_ID,si.PRE_STORE,si.SERIAL_TYPE,si.SUPER_TYPE,si.CITY_CODE,si.BATCH_ID,si.CREATE_STAFF_ID,si.CREATE_TIME,si.UPDATE_TIME,si.UPDATE_STAFF_ID
    </sql>

    <resultMap id="BASE_RESULT_MAP" type="com.jsunicom.task.po.resource.TnSerialIdle">
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="poolCode" column="POOL_CODE" jdbcType="VARCHAR"/>
        <result property="serialNo" column="SERIAL_NO" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="iccid" column="ICCID" jdbcType="VARCHAR"/>
        <result property="psptId" column="PSPT_ID" jdbcType="VARCHAR"/>
        <result property="preStore" column="PRE_STORE" jdbcType="DECIMAL"/>
        <result property="serialType" column="SERIAL_TYPE" jdbcType="VARCHAR"/>
        <result property="superType" column="SUPER_TYPE" jdbcType="VARCHAR"/>
        <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
        <result property="batchId" column="BATCH_ID" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createStaffId" column="CREATE_STAFF_ID" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateStaffId" column="UPDATE_STAFF_ID" jdbcType="VARCHAR"/>
        <result property="schoolName" column="SCHL_NAME" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="selectSerialForCurrentImportAndMobileNetwork" resultMap="BASE_RESULT_MAP">
        select distinct si.SERIAL_NO,si.CITY_CODE,si.ICCID,si.PSPT_ID,si.POOL_CODE
        from tn_serial_idle si
                 left join tn_serial_number_pool np on si.POOL_CODE = np.POOL_CODE
        where si.STATE = '01'
          and si.CREATE_TIME &gt;= #{currentTime,jdbcType=VARCHAR}
          and np.goods_id in (select sd.param_key  from td_ord_sys_dict sd where sd.param_type = 'goodsList' and sd.param1 = 'mobileProduct')
    </select>

    <select id="selectSerialForCurrentImportAndFusion" resultMap="BASE_RESULT_MAP">
        select distinct si.SERIAL_NO,si.CITY_CODE,si.ICCID,si.PSPT_ID,si.POOL_CODE
        from tn_serial_idle si
                 left join tn_serial_number_pool np on si.pool_code = np.pool_code
        where si.STATE = '01'
          and si.CREATE_TIME &gt;= #{currentTime,jdbcType=VARCHAR}
          and np.goods_id in (select sd.param_key  from td_ord_sys_dict sd where sd.param_type = 'goodsList' and sd.param1 = 'fusionProduct')
    </select>

    <select id="queryPersonInfoByCityNo" resultType="java.util.Map">
        SELECT
            ee.EMPLOYEE_ID as STAFFID,
            ee.CHL_TYPE_CODE as CHANNELTYPE,
            ee.CHL_CODE as CHANNELID,
            ee.CITY_NO as CITYNO,
            ee.CITY_CODE as CITYCODE,
            ee.IS_USE as INMODECODE,
            ee.REMARK as DEPARTID
        FROM
            t_s_ess_empno ee
        WHERE
            ee.CITY_ID = #{cityId,jdbcType=VARCHAR}
    </select>

    <update id="changeSerialState" parameterType="java.lang.String">
        update tn_serial_idle set STATE = '07',UPDATE_STAFF_ID = 'admin',UPDATE_TIME = now() where SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
    </update>

    <insert id="addOperateInfo" parameterType="com.jsunicom.task.po.resource.TnlSerialStateOperateInfo">
        insert into tnl_serial_state_operateinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                ID,
            </if>
            <if test="serialNo != null and serialNo != ''">
                SERIAL_NO,
            </if>
            <if test="cityCode != null and cityCode != ''">
                CITY_CODE,
            </if>
            <if test="state != null and state != ''">
                STATE,
            </if>
            <if test="operateType != null and operateType != ''">
                OPERATE_TYPE,
            </if>
            <if test="batchId != null and batchId != ''">
                BATCH_ID,
            </if>
            <if test="iccid != null and iccid != ''">
                ICCID,
            </if>
            <if test="psptId != null and psptId != ''">
                PSPT_ID,
            </if>
            <if test="poolId != null and poolId != ''">
                POOL_ID,
            </if>
            <if test="operateTime != null">
                OPERATE_TIME,
            </if>
            <if test="operateStaffId != null and operateStaffId != ''">
                OPERATE_STAFF_ID,
            </if>
            <if test="operateStaffName != null and operateStaffName != ''">
                OPERATE_STAFF_NAME,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null and serialNo != ''">
                #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null and cityCode != ''">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="state != null and state != ''">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null and operateType != ''">
                #{operateType,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null and batchId != ''">
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="iccid != null and iccid != ''">
                #{iccid,jdbcType=VARCHAR},
            </if>
            <if test="psptId != null and psptId != ''">
                #{psptId,jdbcType=VARCHAR},
            </if>
            <if test="poolId != null and poolId != ''">
                #{poolId,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operateStaffId != null and operateStaffId != ''">
                #{operateStaffId,jdbcType=VARCHAR},
            </if>
            <if test="operateStaffName != null and operateStaffName != ''">
                #{operateStaffName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <delete id="clearSerialNumber" parameterType="java.lang.Integer">
        delete
        from TN_SERIAL_IDLE
        where STATE = '05'
    </delete>

    <update id="resetOccupiedNumber">
        UPDATE TN_SERIAL_IDLE
        SET state = '01'
        WHERE state = '03'
          AND update_time &lt;= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    </update>

</mapper>

