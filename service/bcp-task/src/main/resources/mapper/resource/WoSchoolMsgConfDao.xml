<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsunicom.task.mapper.resource.WoSchoolMsgConfDao">

    <select id="getMsgConfList" resultType="com.jsunicom.task.po.WoSchoolMsgconf">
        SELECT id,sign_name,content,create_time,update_time,create_by,update_by,STATUS,remark FROM `wo_school_msgconf`
        where 1=1 and delete_flag = 'N'
        <if test="status != null and status != ''" >
            AND status = #{status}
        </if>
        <if test="id != null and id != 0" >
            AND id = #{id}
        </if>
    </select>

    <select id="selectMsgIdList" resultType="java.lang.String">
    select msg_id from wo_school_sendMsg where send_phone_state='1' and send_state='2' group by msg_id
  </select>

    <select id="selectSendIdList" resultType="java.util.HashMap">
        select id,send_phone sendPhone from wo_school_sendMsg where
        send_state in ('2','3')
        <if test="batchId!=null and batchId!=''">
            and batch_id=#{batchId}
        </if>
        <if test="sendPhoneState!=null and sendPhoneState!=''">
            and send_phone_state=#{sendPhoneState}
        </if>
        <if test="msgId!=null and msgId!=''">
            and msg_id=#{msgId}
        </if>
    </select>

    <select id="updateSendInfoById">
        update wo_school_sendMsg set send_state=#{sendState},send_time=#{sendTime}
        where id in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item.id}
        </foreach>
        )
    </select>

</mapper>
