<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.order.TfFPayLogSettleCenterMapper">

  <resultMap id="BaseResultMap" type="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    <id column="BUSI_ORDER_ID" jdbcType="VARCHAR" property="busiOrderId" />
    <result column="PARTITION_ID" jdbcType="BIGINT" property="partitionId" />
    <result column="BUSI_ID" jdbcType="VARCHAR" property="busiId" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="ORDER_TYPE" jdbcType="VARCHAR" property="orderType" />
    <result column="REQ_WAY" jdbcType="VARCHAR" property="reqWay" />
    <result column="OPER_ID" jdbcType="VARCHAR" property="operId" />
    <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode" />
    <result column="EPARCHY_CODE" jdbcType="VARCHAR" property="eparchyCode" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId" />
    <result column="CHANNEL_TYPE" jdbcType="VARCHAR" property="channelType" />
    <result column="EPAYQRCODE_TRANS_IDO" jdbcType="VARCHAR" property="epayqrcodeTransIdo" />
    <result column="PAYRESULT_TRANS_IDO" jdbcType="VARCHAR" property="payresultTransIdo" />
    <result column="BUSI_MERC_ID" jdbcType="VARCHAR" property="busiMercId" />
    <result column="BUSI_AMT" jdbcType="VARCHAR" property="busiAmt" />
    <result column="BUSI_CELLNO" jdbcType="VARCHAR" property="busiCellno" />
    <result column="BUSI_ORDER_DATE" jdbcType="VARCHAR" property="busiOrderDate" />
    <result column="GOODS_ID" jdbcType="VARCHAR" property="goodsId" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="PAY_METHOD" jdbcType="VARCHAR" property="payMethod" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="PAY_AGENT" jdbcType="VARCHAR" property="payAgent" />
    <result column="PAY_RESULT" jdbcType="VARCHAR" property="payResult" />
    <result column="PAY_RESULT_MSG" jdbcType="VARCHAR" property="payResultMsg" />
    <result column="TRANSACTIONS_ID" jdbcType="VARCHAR" property="transactionsId" />
    <result column="PAY_TRANSACTION_ID" jdbcType="VARCHAR" property="payTransactionId" />
    <result column="PAY_ORDER_ID" jdbcType="VARCHAR" property="payOrderId" />
    <result column="PAY_TOTAL_AMT" jdbcType="VARCHAR" property="payTotalAmt" />
    <result column="PAY_ACT_AMT" jdbcType="VARCHAR" property="payActAmt" />
    <result column="PAY_ACCOUNT_MSG" jdbcType="VARCHAR" property="payAccountMsg" />
    <result column="PAYMENT_SUB_METHOD" jdbcType="VARCHAR" property="paymentSubMethod" />
    <result column="PAY_TIME" jdbcType="VARCHAR" property="payTime" />
    <result column="PAY_CLEAR_DT" jdbcType="VARCHAR" property="payClearDt" />
    <result column="TRADE_ID" jdbcType="VARCHAR" property="tradeId" />
    <result column="CHECK_RESULT" jdbcType="VARCHAR" property="checkResult" />
    <result column="CHECK_DATE" jdbcType="TIMESTAMP" property="checkDate" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CANCEL_TAG" jdbcType="VARCHAR" property="cancelTag" />
    <result column="CANCEL_TIME" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="REFUND_ORDER_ID" jdbcType="VARCHAR" property="refundOrderId" />
      <result column="CHECK_TAG" jdbcType="VARCHAR" property="checkTag" />
  </resultMap>

  <sql id="Base_Column_List">
    BUSI_ORDER_ID, PARTITION_ID, BUSI_ID, ORDER_ID, ORDER_TYPE, REQ_WAY, OPER_ID, PROVINCE_CODE,
    EPARCHY_CODE, CITY_CODE, CHANNEL_ID, CHANNEL_TYPE, EPAYQRCODE_TRANS_IDO, PAYRESULT_TRANS_IDO,
    BUSI_MERC_ID, BUSI_AMT, BUSI_CELLNO, BUSI_ORDER_DATE, GOODS_ID, GOODS_NAME, PAY_METHOD,
    URL, PAY_AGENT, PAY_RESULT, PAY_RESULT_MSG, TRANSACTIONS_ID, PAY_TRANSACTION_ID,
    PAY_ORDER_ID, PAY_TOTAL_AMT, PAY_ACT_AMT, PAY_ACCOUNT_MSG, PAYMENT_SUB_METHOD, PAY_TIME,
    PAY_CLEAR_DT, TRADE_ID, CHECK_RESULT, CHECK_DATE, UPDATE_TIME, CANCEL_TAG, CANCEL_TIME,
    REFUND_ORDER_ID, CHECK_TAG
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tf_f_pay_log_settlecenter
    where BUSI_ORDER_ID = #{busiOrderId,jdbcType=VARCHAR}
  </select>

  <select id="qryPayLogById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from tf_f_pay_log_settlecenter
    where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    and ORDER_TYPE='01'
    order by BUSI_ORDER_DATE desc
  </select>

  <select id="qryParamsCashierPay" resultType="java.util.HashMap">
    select
      id, param_value paramValue, param_key paramKey, param_type paramType, param1, param2, param3, param4, param5, description, sort, parent_id, create_by, create_date, update_by, update_date, remarks
    from td_m_sys_dict
    where PARAM_TYPE = 'CashierPay' AND PARAM_KEY= 'CashierPay'
  </select>

  <select id="qryPayInfoList" resultType="java.util.Map">
    select <include refid="Base_Column_List" />
    from tf_f_pay_log_settlecenter
    where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    and PAY_RESULT in ('SUCCESS','PAYING') and CANCEL_TAG='0'
    order by BUSI_ORDER_DATE desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from tf_f_pay_log_settlecenter
    where BUSI_ORDER_ID = #{busiOrderId,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    insert into tf_f_pay_log_settlecenter (BUSI_ORDER_ID, PARTITION_ID, BUSI_ID,
      ORDER_ID, ORDER_TYPE, REQ_WAY,
      OPER_ID, PROVINCE_CODE, EPARCHY_CODE,
      CITY_CODE, CHANNEL_ID, CHANNEL_TYPE,
      EPAYQRCODE_TRANS_IDO, PAYRESULT_TRANS_IDO,
      BUSI_MERC_ID, BUSI_AMT, BUSI_CELLNO,
      BUSI_ORDER_DATE, GOODS_ID, GOODS_NAME,
      PAY_METHOD, URL, PAY_AGENT,
      PAY_RESULT, PAY_RESULT_MSG, TRANSACTIONS_ID,
      PAY_TRANSACTION_ID, PAY_ORDER_ID, PAY_TOTAL_AMT,
      PAY_ACT_AMT, PAY_ACCOUNT_MSG, PAYMENT_SUB_METHOD,
      PAY_TIME, PAY_CLEAR_DT, TRADE_ID,
      CHECK_RESULT, CHECK_DATE, UPDATE_TIME,
      CANCEL_TAG, CANCEL_TIME, REFUND_ORDER_ID
      )
    values (#{busiOrderId,jdbcType=VARCHAR}, #{partitionId,jdbcType=BIGINT}, #{busiId,jdbcType=VARCHAR},
      #{orderId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{reqWay,jdbcType=VARCHAR},
      #{operId,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{eparchyCode,jdbcType=VARCHAR},
      #{cityCode,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR}, #{channelType,jdbcType=VARCHAR},
      #{epayqrcodeTransIdo,jdbcType=VARCHAR}, #{payresultTransIdo,jdbcType=VARCHAR},
      #{busiMercId,jdbcType=VARCHAR}, #{busiAmt,jdbcType=VARCHAR}, #{busiCellno,jdbcType=VARCHAR},
      #{busiOrderDate,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{payMethod,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{payAgent,jdbcType=VARCHAR},
      #{payResult,jdbcType=VARCHAR}, #{payResultMsg,jdbcType=VARCHAR}, #{transactionsId,jdbcType=VARCHAR},
      #{payTransactionId,jdbcType=VARCHAR}, #{payOrderId,jdbcType=VARCHAR}, #{payTotalAmt,jdbcType=VARCHAR},
      #{payActAmt,jdbcType=VARCHAR}, #{payAccountMsg,jdbcType=VARCHAR}, #{paymentSubMethod,jdbcType=VARCHAR},
      #{payTime,jdbcType=VARCHAR}, #{payClearDt,jdbcType=VARCHAR}, #{tradeId,jdbcType=VARCHAR},
      #{checkResult,jdbcType=VARCHAR}, #{checkDate,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{cancelTag,jdbcType=VARCHAR}, #{cancelTime,jdbcType=TIMESTAMP}, #{refundOrderId,jdbcType=VARCHAR}
      )
  </insert>

  <insert id="insertSelective" parameterType="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    insert into tf_f_pay_log_settlecenter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="busiOrderId != null">
        BUSI_ORDER_ID,
      </if>
      <if test="partitionId != null">
        PARTITION_ID,
      </if>
      <if test="busiId != null">
        BUSI_ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="reqWay != null">
        REQ_WAY,
      </if>
      <if test="operId != null">
        OPER_ID,
      </if>
      <if test="provinceCode != null">
        PROVINCE_CODE,
      </if>
      <if test="eparchyCode != null">
        EPARCHY_CODE,
      </if>
      <if test="cityCode != null">
        CITY_CODE,
      </if>
      <if test="channelId != null">
        CHANNEL_ID,
      </if>
      <if test="channelType != null">
        CHANNEL_TYPE,
      </if>
      <if test="epayqrcodeTransIdo != null">
        EPAYQRCODE_TRANS_IDO,
      </if>
      <if test="payresultTransIdo != null">
        PAYRESULT_TRANS_IDO,
      </if>
      <if test="busiMercId != null">
        BUSI_MERC_ID,
      </if>
      <if test="busiAmt != null">
        BUSI_AMT,
      </if>
      <if test="busiCellno != null">
        BUSI_CELLNO,
      </if>
      <if test="busiOrderDate != null">
        BUSI_ORDER_DATE,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="payMethod != null">
        PAY_METHOD,
      </if>
      <if test="url != null">
        URL,
      </if>
      <if test="payAgent != null">
        PAY_AGENT,
      </if>
      <if test="payResult != null">
        PAY_RESULT,
      </if>
      <if test="payResultMsg != null">
        PAY_RESULT_MSG,
      </if>
      <if test="transactionsId != null">
        TRANSACTIONS_ID,
      </if>
      <if test="payTransactionId != null">
        PAY_TRANSACTION_ID,
      </if>
      <if test="payOrderId != null">
        PAY_ORDER_ID,
      </if>
      <if test="payTotalAmt != null">
        PAY_TOTAL_AMT,
      </if>
      <if test="payActAmt != null">
        PAY_ACT_AMT,
      </if>
      <if test="payAccountMsg != null">
        PAY_ACCOUNT_MSG,
      </if>
      <if test="paymentSubMethod != null">
        PAYMENT_SUB_METHOD,
      </if>
      <if test="payTime != null">
        PAY_TIME,
      </if>
      <if test="payClearDt != null">
        PAY_CLEAR_DT,
      </if>
      <if test="tradeId != null">
        TRADE_ID,
      </if>
      <if test="checkResult != null">
        CHECK_RESULT,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="cancelTag != null">
        CANCEL_TAG,
      </if>
      <if test="cancelTime != null">
        CANCEL_TIME,
      </if>
      <if test="refundOrderId != null">
        REFUND_ORDER_ID,
      </if>
        <if test="checkTag != null">
            CHECK_TAG ,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="busiOrderId != null">
        #{busiOrderId,jdbcType=VARCHAR},
      </if>
      <if test="partitionId != null">
        #{partitionId,jdbcType=BIGINT},
      </if>
      <if test="busiId != null">
        #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="reqWay != null">
        #{reqWay,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        #{operId,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="eparchyCode != null">
        #{eparchyCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="epayqrcodeTransIdo != null">
        #{epayqrcodeTransIdo,jdbcType=VARCHAR},
      </if>
      <if test="payresultTransIdo != null">
        #{payresultTransIdo,jdbcType=VARCHAR},
      </if>
      <if test="busiMercId != null">
        #{busiMercId,jdbcType=VARCHAR},
      </if>
      <if test="busiAmt != null">
        #{busiAmt,jdbcType=VARCHAR},
      </if>
      <if test="busiCellno != null">
        #{busiCellno,jdbcType=VARCHAR},
      </if>
      <if test="busiOrderDate != null">
        #{busiOrderDate,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="payAgent != null">
        #{payAgent,jdbcType=VARCHAR},
      </if>
      <if test="payResult != null">
        #{payResult,jdbcType=VARCHAR},
      </if>
      <if test="payResultMsg != null">
        #{payResultMsg,jdbcType=VARCHAR},
      </if>
      <if test="transactionsId != null">
        #{transactionsId,jdbcType=VARCHAR},
      </if>
      <if test="payTransactionId != null">
        #{payTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="payOrderId != null">
        #{payOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payTotalAmt != null">
        #{payTotalAmt,jdbcType=VARCHAR},
      </if>
      <if test="payActAmt != null">
        #{payActAmt,jdbcType=VARCHAR},
      </if>
      <if test="payAccountMsg != null">
        #{payAccountMsg,jdbcType=VARCHAR},
      </if>
      <if test="paymentSubMethod != null">
        #{paymentSubMethod,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="payClearDt != null">
        #{payClearDt,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTag != null">
        #{cancelTag,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundOrderId != null">
        #{refundOrderId,jdbcType=VARCHAR},
      </if>
        <if test="checkTag != null">
            #{checkTag,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    update tf_f_pay_log_settlecenter
    <set>
      <if test="partitionId != null">
        PARTITION_ID = #{partitionId,jdbcType=BIGINT},
      </if>
      <if test="busiId != null">
        BUSI_ID = #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="reqWay != null">
        REQ_WAY = #{reqWay,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        OPER_ID = #{operId,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="eparchyCode != null">
        EPARCHY_CODE = #{eparchyCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="epayqrcodeTransIdo != null">
        EPAYQRCODE_TRANS_IDO = #{epayqrcodeTransIdo,jdbcType=VARCHAR},
      </if>
      <if test="payresultTransIdo != null">
        PAYRESULT_TRANS_IDO = #{payresultTransIdo,jdbcType=VARCHAR},
      </if>
      <if test="busiMercId != null">
        BUSI_MERC_ID = #{busiMercId,jdbcType=VARCHAR},
      </if>
      <if test="busiAmt != null">
        BUSI_AMT = #{busiAmt,jdbcType=VARCHAR},
      </if>
      <if test="busiCellno != null">
        BUSI_CELLNO = #{busiCellno,jdbcType=VARCHAR},
      </if>
      <if test="busiOrderDate != null">
        BUSI_ORDER_DATE = #{busiOrderDate,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="payAgent != null">
        PAY_AGENT = #{payAgent,jdbcType=VARCHAR},
      </if>
      <if test="payResult != null">
        PAY_RESULT = #{payResult,jdbcType=VARCHAR},
      </if>
      <if test="payResultMsg != null">
        PAY_RESULT_MSG = #{payResultMsg,jdbcType=VARCHAR},
      </if>
      <if test="transactionsId != null">
        TRANSACTIONS_ID = #{transactionsId,jdbcType=VARCHAR},
      </if>
      <if test="payTransactionId != null">
        PAY_TRANSACTION_ID = #{payTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="payOrderId != null">
        PAY_ORDER_ID = #{payOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payTotalAmt != null">
        PAY_TOTAL_AMT = #{payTotalAmt,jdbcType=VARCHAR},
      </if>
      <if test="payActAmt != null">
        PAY_ACT_AMT = #{payActAmt,jdbcType=VARCHAR},
      </if>
      <if test="payAccountMsg != null">
        PAY_ACCOUNT_MSG = #{payAccountMsg,jdbcType=VARCHAR},
      </if>
      <if test="paymentSubMethod != null">
        PAYMENT_SUB_METHOD = #{paymentSubMethod,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        PAY_TIME = #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="payClearDt != null">
        PAY_CLEAR_DT = #{payClearDt,jdbcType=VARCHAR},
      </if>
      <if test="tradeId != null">
        TRADE_ID = #{tradeId,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        CHECK_RESULT = #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTag != null">
        CANCEL_TAG = #{cancelTag,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        CANCEL_TIME = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundOrderId != null">
        REFUND_ORDER_ID = #{refundOrderId,jdbcType=VARCHAR},
      </if>
        <if test="checkTag != null">
            CHECK_TAG = #{checkTag,jdbcType=VARCHAR},
        </if>
        <if test="remark != null">
            remark = #{remark,jdbcType=VARCHAR},
        </if>
    </set>
    where BUSI_ORDER_ID = #{busiOrderId,jdbcType=VARCHAR}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    update tf_f_pay_log_settlecenter
    set PARTITION_ID = #{partitionId,jdbcType=BIGINT},
      BUSI_ID = #{busiId,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      REQ_WAY = #{reqWay,jdbcType=VARCHAR},
      OPER_ID = #{operId,jdbcType=VARCHAR},
      PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
      EPARCHY_CODE = #{eparchyCode,jdbcType=VARCHAR},
      CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      CHANNEL_TYPE = #{channelType,jdbcType=VARCHAR},
      EPAYQRCODE_TRANS_IDO = #{epayqrcodeTransIdo,jdbcType=VARCHAR},
      PAYRESULT_TRANS_IDO = #{payresultTransIdo,jdbcType=VARCHAR},
      BUSI_MERC_ID = #{busiMercId,jdbcType=VARCHAR},
      BUSI_AMT = #{busiAmt,jdbcType=VARCHAR},
      BUSI_CELLNO = #{busiCellno,jdbcType=VARCHAR},
      BUSI_ORDER_DATE = #{busiOrderDate,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      PAY_METHOD = #{payMethod,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      PAY_AGENT = #{payAgent,jdbcType=VARCHAR},
      PAY_RESULT = #{payResult,jdbcType=VARCHAR},
      PAY_RESULT_MSG = #{payResultMsg,jdbcType=VARCHAR},
      TRANSACTIONS_ID = #{transactionsId,jdbcType=VARCHAR},
      PAY_TRANSACTION_ID = #{payTransactionId,jdbcType=VARCHAR},
      PAY_ORDER_ID = #{payOrderId,jdbcType=VARCHAR},
      PAY_TOTAL_AMT = #{payTotalAmt,jdbcType=VARCHAR},
      PAY_ACT_AMT = #{payActAmt,jdbcType=VARCHAR},
      PAY_ACCOUNT_MSG = #{payAccountMsg,jdbcType=VARCHAR},
      PAYMENT_SUB_METHOD = #{paymentSubMethod,jdbcType=VARCHAR},
      PAY_TIME = #{payTime,jdbcType=VARCHAR},
      PAY_CLEAR_DT = #{payClearDt,jdbcType=VARCHAR},
      TRADE_ID = #{tradeId,jdbcType=VARCHAR},
      CHECK_RESULT = #{checkResult,jdbcType=VARCHAR},
      CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CANCEL_TAG = #{cancelTag,jdbcType=VARCHAR},
      CANCEL_TIME = #{cancelTime,jdbcType=TIMESTAMP},
      REFUND_ORDER_ID = #{refundOrderId,jdbcType=VARCHAR}
    where BUSI_ORDER_ID = #{busiOrderId,jdbcType=VARCHAR}
  </update>

  <select id="qryPaySuccess" resultType="com.jsunicom.task.entity.TfFPayLogSettleCenter">
    select <include refid="Base_Column_List" />
    from tf_f_pay_log_settlecenter
    where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    and PAY_RESULT in ('SUCCESS')  and ORDER_TYPE='01'
      order by BUSI_ORDER_DATE desc
  </select>

    <select id="qryUnCheckOrder" parameterType="java.lang.String" resultType="java.util.Map">
        select DISTINCT ORDER_ID
        from tf_f_pay_log_settlecenter
        where
        ORDER_TYPE='01' and check_tag in ('0','2')
        order by BUSI_ORDER_DATE
    </select>

    <select id="qryUnCheckById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tf_f_pay_log_settlecenter
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
        and ORDER_TYPE='01' and check_tag in ('0','2')
        order by ORDER_ID,BUSI_ORDER_DATE
    </select>
    <update id="updateCheckById" parameterType="java.lang.String" >
    update tf_f_pay_log_settlecenter
    set
      check_tag ='1',CHECK_DATE=now()
    where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    and check_tag ='3'
  </update>

</mapper>
