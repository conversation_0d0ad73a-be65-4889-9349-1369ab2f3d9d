<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.order.OrderReceiveMapper">

    <select id="queryDuplicateNumberCount" parameterType="hashmap" resultType="hashmap">
		select count(1) as NUMBERCOUNTS from tf_ord_goods_main gm, TF_ORD_MAIN t
		left join TF_ORD_ADDRESS a on a.order_id = t.order_id
		where a.phone = #{phone}
		  and gm.order_id = t.order_id
		  and gm.busi_type = #{busiType}
		  and t.ORDER_STATE in ('0030','0031', '0040', '0050')
		  and DATEDIFF(t.CREATE_DATE,now())>=-30
	</select>

    <select id="queryDuplicateAddressCount" parameterType="hashmap" resultType="hashmap">
		select count(1) as ADDRESSCOUNTS from tf_ord_goods_main gm,TF_ORD_MAIN t
		left join TF_ORD_ADDRESS a on a.order_id = t.order_id
		where a.address = #{address}
		  and a.county_code = #{countyCode}
		  and a.city_code = #{cityCode}
		  and a.province_code = #{provinceCode}
		  and gm.order_id = t.order_id
		  and gm.busi_type = #{busiType}
		  and t.ORDER_STATE in ('0030', '0040', '0050')
		  and DATEDIFF(t.CREATE_DATE,now())>=-30
	</select>

    <insert id="insertTfOrdOrignal" parameterType="hashmap">
		INSERT INTO TF_ORD_ORGINAL(ORDER_ID,ORDER_MSG,CREATE_DATE,SOURCE_SYSTEM_ID)
			VALUES(#{orderId},#{receiveJson},now(),#{sourceSystemId})
	</insert>

    <select id="hasMainNumber" parameterType="hashmap" resultType="java.lang.Integer">
		      SELECT  count(1) FROM TF_ORD_MAIN ORD  LEFT JOIN TF_ORD_GOODS_MAIN GOODS
		      ON (ORD.ORDER_ID = GOODS.ORDER_ID)
		      where   ORD.ORDER_STATE not in ('0060','0090')
  			  and goods.MAIN_NUMBER=#{mainNumber}
	</select>



</mapper>
