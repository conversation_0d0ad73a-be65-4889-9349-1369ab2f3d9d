<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.order.RefundMoneyMapper">

    <select id="qryDateConfig" resultType="java.lang.String">
        select param_value from td_m_sys_dict where param_type = 'refundMoneyJob' and param_key = 'qryDate'
    </select>

    <select id="qryNoRefundOrder" parameterType="java.lang.String" resultType="com.alibaba.fastjson.JSONObject">
        SELECT ls.OPER_ID AS 'staffId',
               ls.OPER_ID AS 'operId',
               ls.BUSI_ORDER_ID AS 'outRefundId',
               ls.EPARCHY_CODE AS 'cucEparchyCode',
               ls.BUSI_MERC_ID AS 'merchantId',
               ls.BUSI_AMT AS 'realFee',
               ls.BUSI_AMT AS 'totalFee',
               ls.ORDER_ID AS 'orderId'
        FROM tf_f_pay_log_settlecenter ls
        WHERE EXISTS (
                SELECT 1
                FROM tf_ord_main om
                WHERE om.ORDER_ID = ls.ORDER_ID
                  AND om.ORDER_STATE = '0090'
                  <if test="qryDate != null and qryDate != ''">
                    AND LEFT(om.CREATE_DATE, 10) = #{qryDate,jdbcType=VARCHAR}
                  </if>
                  <if test="qryDate == ''">
                    AND LEFT(om.CREATE_DATE, 10) &gt; DATE_SUB(CURDATE(), INTERVAL 1 WEEK)
                    AND LEFT(om.CREATE_DATE, 10) &lt;= CURDATE()
                  </if>
        )
          AND ls.PAY_RESULT = 'SUCCESS'
          AND ls.ORDER_TYPE = '01'
    </select>

    <insert id="insertRefundLog" parameterType="java.lang.String">
        INSERT INTO `tl_ord_operator_log`
            (`log_id`,`order_id`,`busi_type`,`NODE_CODE`,`NODE_NAME`,`REASON_CODE`,`REASON_NAME`,`REASON_DESC`,`operator_name`,`operator_id`,`operator_phone`,`update_time`,`remark`,`ORDER_LINE_ID`,`oper_type`)
        VALUES
            (#{logId,jdbcType=VARCHAR},#{orderId,jdbcType=VARCHAR},'','chargeBack','退款','success','退款申请','申请退款成功','system','system','',now(),null,'','chargeBack');
    </insert>

</mapper>
