server:
  port: 8396
spring:
  cloud:
    nacos:
      config:
        server-addr: ************:31100
        username: nacos
        password:
        namespace:
        group: DEFAULT_GROUP
        file-extension: yaml
        context-path: /nacos
      discovery:
        server-addr:
        username: nacos
        password:
        namespace:
xxl:
  job:
    accessToken:
    admin:
      addresses:
    executor:
      appname: oms-svc-task
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30