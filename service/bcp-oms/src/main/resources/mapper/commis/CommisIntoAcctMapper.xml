<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.commis.CommisIntoAcctDao">

	<resultMap id="CommisIntoAcctMapper"
		type="com.jsunicom.oms.model.commis.CommisIntoAcct">
		<id column="id" property="id" jdbcType="BIGINT" />
		<id column="partner_id" property="partnerId" jdbcType="BIGINT" />
		<id column="order_no" property="orderNo" jdbcType="VARCHAR" />
		<id column="commis_rule_id" property="commisRuleId" jdbcType="BIGINT" />
		<id column="commis_amt" property="commisAmt" jdbcType="DECIMAL" />
		<id column="remark" property="remark" jdbcType="VARCHAR" />
		<id column="symbol" property="symbol" jdbcType="CHAR" />
		<id column="state" property="state" jdbcType="CHAR" />
		<id column="create_by" property="createBy" jdbcType="VARCHAR" />
		<id column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<id column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<id column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	    <id column="type" property="type" jdbcType="VARCHAR" />
	    <id column="goods_code" property="goodsCode" jdbcType="VARCHAR" />
		<id column="commis_rule_merchant_id" property="commisRuleMerchantId" jdbcType="BIGINT" />
		<id column="merchant_id" property="merchantId" jdbcType="BIGINT" />
		<id column="order_goods_no" property="orderGoodsNo" jdbcType="VARCHAR" />
		<id column="level" property="level" jdbcType="VARCHAR" />
		<id column="org_code" property="orgCode" jdbcType="VARCHAR" />
		<id column="plan_pay_date" property="planPayDate" jdbcType="TIMESTAMP" />
	</resultMap>


	<!-- ============================= INSERT ============================= -->
	<insert id="save" useGeneratedKeys="true" keyProperty="id">
		INSERT
		INTO commis_into_acct(
		id,partner_id,org_code,order_no,commis_rule_id,commis_amt,
		remark,symbol,state,create_by,create_time,update_by,update_time,type,goods_code,order_goods_no,merchant_id,commis_rule_merchant_id, level,plan_pay_date)
		VALUES (
		#{id},#{partnerId},#{orgCode},#{orderNo},#{commisRuleId},#{commisAmt},
		#{remark},#{symbol},#{state},#{createBy},#{createTime},#{updateBy},#{updateTime},#{type},#{goodsCode},#{orderGoodsNo},#{merchantId},#{commisRuleMerchantId},#{level},#{planPayDate})
	</insert>


	<!-- batch insert for mysql -->
	<insert id="saveBatch">
		INSERT INTO commis_into_acct(
		id,partner_id,order_no,commis_rule_id,commis_amt,
		remark,symbol,state,create_by,create_time,update_by,update_time,type,goods_code,order_goods_no,merchant_id,commis_rule_merchant_id, level,plan_pay_date)
		VALUES
		<foreach collection="list" item="item" index="index"
			separator=",">
			(
			#{item.id},#{item.partnerId},#{item.orderNo},#{item.commisRuleId},#{item.commisAmt},
			#{item.remark},#{item.symbol},#{item.state},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},
			#{item.type},#{item.goodsCode},#{item.orgCode},#{item.orderGoodsNo},#{item.merchantId},#{item.commisRuleMerchantId},#{item.level},#{item.planPayDate})
		</foreach>
	</insert>


	<!-- batch insert for oracle -->
	<!-- <insert id="saveBatch"> INSERT INTO commis_into_acct( id,partner_id,order_no,commis_rule_id,commis_amt,
		remark,symbol,state,create_by,create_time,update_by,update_time ) <foreach 
		collection="list" item="item" index="index" separator="UNION ALL"> SELECT 
		#{item.id},#{item.orgCode},#{item.partnerId},#{item.orderId},#{item.busiId},#{item.commisRuleId},#{item.commisAmt}, 
		#{item.remark},#{item.symbol},#{item.state},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime} 
		FROM DUAL </foreach> </insert> -->

	<!-- ============================= UPDATE ============================= -->
	<update id="update">
		UPDATE commis_into_acct
		<set>
			partner_id=#{partnerId},
			order_no=#{orderNo},
			commis_rule_id=#{commisRuleId},
			commis_amt=#{commisAmt},
			remark=#{remark},
			symbol=#{symbol},
			state=#{state},
			create_by=#{createBy},
			create_time=#{createTime},
			update_by=#{updateBy},
			update_time=#{updateTime},
			type=#{type},
			goods_code=#{goodsCode},
			order_goods_no=#{orderGoodsNo},
			merchant_id=#{merchantId},
			commis_rule_merchant_id = #{commisRuleMerchantId},
			level = #{level},
			plan_pay_date=#{planPayDate},
		</set>
		WHERE id=#{id}
	</update>

	<update id="updatePayState">
		UPDATE commis_into_acct
		<set>
			state=#{state},
			update_by=#{updateBy},
			update_time=#{updateTime},
		</set>
		WHERE id=#{id}
	</update>

	<update id="updatePayStateAndPayType">
		UPDATE commis_into_acct
		<set>
			state=#{state},
			pay_type = #{payType},
			update_by=#{updateBy},
			update_time=#{updateTime},
		</set>
		WHERE id=#{id}
	</update>

	<update id="updateIgnoreNull">
		UPDATE commis_into_acct
		<set>
			<if test="partnerId!= null">partner_id=#{partnerId},</if>
			<if test="orderNo!= null">order_no=#{orderNo},</if>
			<if test="commisRuleId!= null">commis_rule_id=#{commisRuleId},</if>
			<if test="commisAmt!= null">commis_amt=#{commisAmt},</if>
			<if test="remark!= null">remark=#{remark},</if>
			<if test="symbol!= null">symbol=#{symbol},</if>
			<if test="state!= null">state=#{state},</if>
			<if test="createBy!= null">create_by=#{createBy},</if>
			<if test="createTime!= null">create_time=#{createTime},</if>
			<if test="updateBy!= null">update_by=#{updateBy},</if>
			<if test="updateTime!= null">update_time=#{updateTime},</if>
			<if test="type!= null">type=#{type},</if>
			<if test="goodsCode!= null">goods_code=#{goodsCode},</if>
			<if test="orderGoodsNo!=null">order_goods_no=#{orderGoodsNo},</if>
			<if test="merchantId != null">merchant_id=#{merchantId},</if>
			<if test="commisRuleMerchantId != null">commis_rule_merchant_id = #{commisRuleMerchantId},</if>
			<if test="level != null">level = #{level},</if>
			<if test="planPayDate != null">plan_pay_date = #{planPayDate},</if>
		</set>
		WHERE id=#{id}
	</update>

	<update id="updateBatch" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index"
			separator=";">
			UPDATE commis_into_acct
			<set>
				partner_id=#{item.partnerId},
				order_no=#{item.orderNo},
				commis_rule_id=#{item.commisRuleId},
				commis_amt=#{item.commisAmt},
				remark=#{item.remark},
				symbol=#{item.symbol},
				state=#{item.state},
				create_by=#{item.createBy},
				create_time=#{item.createTime},
				update_by=#{item.updateBy},
				update_time=#{item.updateTime},
				type=#{item.type},
				goods_code=#{item.goodsCode},
				order_goods_no=#{orderGoodsNo},
				merchant_id=#{merchantId},
				commis_rule_merchant_id = #{commisRuleMerchantId},
				level = #{level},
				plan_pay_date=#{planPayDate},
			</set>
			WHERE id=#{item.id}
		</foreach>
	</update>

	<update id="updateByOrderNo">
		UPDATE commis_into_acct
		<set>
			<if test="partnerId!= null">partner_id=#{partnerId},</if>
			<if test="orderNo!= null">order_no=#{orderNo},</if>
			<if test="commisRuleId!= null">commis_rule_id=#{commisRuleId},</if>
			<if test="commisAmt!= null">commis_amt=#{commisAmt},</if>
			<if test="remark!= null">remark=#{remark},</if>
			<if test="symbol!= null">symbol=#{symbol},</if>
			<if test="state!= null">state=#{state},</if>
			<if test="createBy!= null">create_by=#{createBy},</if>
			<if test="createTime!= null">create_time=#{createTime},</if>
			<if test="updateBy!= null">update_by=#{updateBy},</if>
			<if test="updateTime!= null">update_time=#{updateTime},</if>
			<if test="type!= null">type=#{type},</if>
			<if test="goodsCode!= null">goods_code=#{goodsCode},</if>
			<if test="orderGoodsNo!=null">order_goods_no=#{orderGoodsNo},</if>
			<if test="merchantId != null">merchant_id=#{merchantId},</if>
			<if test="commisRuleMerchantId != null">commis_rule_merchant_id = #{commisRuleMerchantId},</if>
			<if test="level != null">level = #{level},</if>
			<if test="planPayDate != null">plan_pay_date = #{planPayDate},</if>

		</set>
		WHERE order_no=#{orderNo}
	</update>

	<update id="updateStateByBatchId">
		UPDATE commis_into_acct
		set state = #{state}, update_time = #{updateTime}
		WHERE id in (select commis_into_acct_id from commis_merchant_batch where batch_id = #{batchId})
	</update>


	<!-- ============================= DELETE ============================= -->
	<delete id="delete">
		DELETE FROM commis_into_acct
		WHERE id=#{id}
	</delete>

	<delete id="deleteBatch">
		DELETE FROM commis_into_acct
		WHERE
		<foreach collection="list" item="item" index="index" open="("
			separator="OR" close=")">
			id=#{item.id}
		</foreach>
	</delete>

	<delete id="deleteByPK">
		DELETE FROM commis_into_acct
		WHERE id=#{id}
	</delete>

	<delete id="deleteAll">
		DELETE FROM commis_into_acct
	</delete>

	<delete id="deleteByOrderNo">
		DELETE FROM commis_into_acct
		WHERE order_no=#{orderNo}
	</delete>

	<!-- ============================= SELECT ============================= -->
	<select id="count" resultType="java.lang.Long">
		SELECT COUNT(*) FROM
		commis_into_acct
	</select>

	<select id="findByPK" resultType="com.jsunicom.oms.model.commis.CommisIntoAcct">
		SELECT * FROM commis_into_acct
		WHERE id=#{id}
	</select>

	<select id="find" resultType="com.jsunicom.oms.model.commis.CommisIntoAcct">
        SELECT id,partner_id,order_no,commis_rule_id,commis_amt,remark,org_code
               ,symbol,state,create_by,create_time,update_by,update_time,type,goods_code,plan_pay_date
         FROM commis_into_acct
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="partnerId!= null">
               AND partner_id = #{partnerId}
            </if>
            <if test="orderNo!= null">
               AND order_no = #{orderNo}
            </if>
            <if test="commisRuleId!= null">
               AND commis_rule_id = #{commisRuleId}
            </if>
            <if test="commisAmt!= null">
               AND commis_amt = #{commisAmt}
            </if>
            <if test="remark!= null">
               AND remark = #{remark}
            </if>
            <if test="symbol!= null">
               AND symbol = #{symbol}
            </if>
            <if test="state!= null">
               AND state = #{state}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
            <if test="dateRangeBegin!= null">
               AND DATE_FORMAT(create_time, '%Y%m%d')&gt;=#{dateRangeBegin}
            </if>
            <if test="dateRangeEnd!= null">
               AND DATE_FORMAT(create_time, '%Y%m%d')&lt;=#{dateRangeEnd}
            </if>
            <if test="type!= null">
               AND type = #{type}
            </if>
            <if test="goodsCode!= null">
               AND goods_code = #{goodsCode}
            </if>
			<if test="orgCode!= null">
				AND org_code = #{orgCode}
			</if>
			<if test="orderGoodsNo!=null">and order_goods_no=#{orderGoodsNo}</if>
			<if test="merchantId != null">and merchant_id=#{merchantId}</if>
			<if test="commisRuleMerchantId != null">and commis_rule_merchant_id = #{commisRuleMerchantId}</if>
			<if test="level != null">and level = #{level}</if>
        </where>
    </select>

	<select id="findByOrderNo" resultType="com.jsunicom.oms.model.commis.CommisIntoAcct">
		SELECT * FROM
		commis_into_acct where order_no = #{orderNo}
	</select>

	<select id="findByDay" resultMap="CommisIntoAcctMapper">
		<!--SELECT a.*
		FROM commis_into_acct a , partner b
		where a.partner_id = b.id
		and (DATEDIFF(CURDATE(),a.create_time)=#{day} OR DATEDIFF(CURDATE(),a.update_time)=#{day})
		and b.wo_acct is not null and a.state='W'-->
		SELECT a.*,c.busi_type
		FROM commis_into_acct a LEFT JOIN commis_rule c ON a.commis_rule_id = c.id, partner_bank_account b
		where a.state='V'
		and a.partner_id > 0
		AND a.partner_id = b.partner_id
		and b.card_no is not NULL and b.card_no !=''
		and CURDATE() >= date(a.plan_pay_date)
	</select>

	<select id="findByDayNew" resultMap="CommisIntoAcctMapper">
		SELECT a.*,c.busi_type
		FROM commis_into_acct a LEFT JOIN commis_rule c ON a.commis_rule_id = c.id, partner_bank_account b
		where a.state='V'
		and a.partner_id > 0
		AND a.partner_id = b.partner_id
		and b.card_no is not NULL and b.card_no !=''
		and CURDATE() >= date(a.plan_pay_date)
		and b.check_flag in ('Y', 'A')
	</select>


	<select id="findWowCommisByDay" resultMap="CommisIntoAcctMapper">
		SELECT a.*,c.busi_type, d.partner_id as orderPartnerId FROM commis_into_acct a
		LEFT JOIN commis_rule c ON a.commis_rule_id = c.id
		LEFT JOIN order_info d ON a.order_no = d.order_no, partner_bank_account b
		where a.state='V'
		and a.partner_id = -1
		and d.partner_id = b.partner_id
		and b.card_no is not NULL and b.card_no !=''
		and CURDATE() >= date(a.plan_pay_date)
		and a.merchant_id in(select d.name from dict d where d.kind = 'wow_advanced_merchants')
	</select>

	<select id="findByMonthForMerchant" resultMap="CommisIntoAcctMapper">
		SELECT a.*,c.busi_type
		FROM commis_into_acct a LEFT JOIN commis_rule c ON a.commis_rule_id = c.id left join partner_bank_account b on a.merchant_id = b.merchant_id
		LEFT join merchant d on a.merchant_id =d.id
		where a.state='V'
		and a.partner_id = -1
		and (b.partner_id = 0 or b.partner_id is null)
		and CURDATE() >= date(a.plan_pay_date)
		AND a.plan_pay_date BETWEEN #{beginTime} AND  #{endTime}
		and (d.head_office not in (select d.name from dict d where d.kind = 'wow_advanced_merchants') or d.head_office is null)
	</select>

	<select id="findByPartnerId" resultMap="CommisIntoAcctMapper">
		SELECT *
		FROM commis_into_acct where 
		partner_id = #{partnerId}
		<if test="createTime!= null">
		    AND DATE_FORMAT(create_time, '%Y%m%d') = #{createTime}
		</if>
	</select>
	<select id="findByPartnerIdAndState" resultMap="CommisIntoAcctMapper">
		SELECT *
		FROM commis_into_acct
		where partner_id = #{partnerId}
		AND state = #{state}
	</select>

	<select id="findByMercntIdAndState" resultMap="CommisIntoAcctMapper">
		SELECT *
		FROM commis_into_acct
		where merchant_id = #{merchantId}
		<if test="state!= null">
			AND state = #{state}
		</if>
	</select>

	<select id="findByPartnerIdAndTimeAndState" resultMap="CommisIntoAcctMapper">
		SELECT *
		FROM commis_into_acct a
		where a.partner_id = #{partnerId}
		<if test="state!= null">
			AND a.state = #{state}
		</if>
		<if test="createTime!= null">
		    AND DATE_FORMAT(a.create_time, '%Y%m%d') = #{createTime}
		</if>

		order by create_time DESC  LIMIT #{offset}, #{limit}
	</select>

	<select id="getCommisIntoAcctsCount" resultType="int">
		SELECT count(*)
		FROM commis_into_acct a
		where a.partner_id = #{partnerId}
		<if test="state!= null">
			AND a.state = #{state}
		</if>
		<if test="createTime!= null">
			AND DATE_FORMAT(a.create_time, '%Y%m%d') = #{createTime}
		</if>
	</select>


	<select id="findListByOrderNo" resultMap="CommisIntoAcctMapper">
		SELECT *
		FROM commis_into_acct a
		where  order_no = #{orderNo}
	</select>

	<select id="commisAlarm" resultType="float">
		select IFNULL((今日-昨日)/昨日,0) 环比 from (
		select IFNULL(sum(case when datediff(a.create_time, now())=0 then a.commis_amt else 0 end),0) 今日,
		IFNULL(sum(case when datediff(a.create_time,now())=-1 then a.commis_amt else 0 end),0) 昨日
		from commis_into_acct a where datediff(a.create_time, now())>=-1
		)b
	</select>

        <!--根据合伙人查询佣金账号汇总（总佣金和本月佣金）信息-->
	<select id="findCommisAccountCount" resultType="com.jsunicom.oms.model.commis.CommisInfoCount">
		SELECT
		ct.partner_id,
		FORMAT(SUM(CASE WHEN ct.state='P' THEN ct.commis_amt ELSE 0.0000 END),2) commis_amt_count,
		FORMAT(SUM(CASE WHEN DATE_FORMAT(ct.update_time , '%Y%m')    = DATE_FORMAT(CURDATE() , '%Y%m') AND DATE_FORMAT(ct.update_time , '%Y%m%d') &lt;= DATE_FORMAT(CURDATE(), '%Y%m%d') AND ct.state='P' THEN ct.commis_amt ELSE 0.0000 END),2) commis_amt_month
		FROM commis_into_acct ct
	    where ct.partner_id = #{partnerId}
	</select>

	<!--根据合伙人查询分类佣金汇总(备注：除1:通信类商品；2:卡券类商品，之外的佣金商品类型（3:终端类商品；4:奖励佣金类商品; 5:其他以及岁末充值、抽奖、微信绑定和世界杯奖励佣金）查询时统一划分到：3 终端及其他类商品)-->
	<select id="findCommisClassifyCount" resultType="com.jsunicom.oms.model.commis.CommisInfoClassifyCount">

		SELECT
		gtp.goods_type,
		CASE WHEN cc.commis_amt_payed IS NULL THEN 0.00 ELSE cc.commis_amt_payed END commis_amt_payed,
		CASE WHEN cc.commis_amt_wait IS NULL THEN 0.00 ELSE cc.commis_amt_wait END commis_amt_wait
		FROM
		(SELECT g.goods_type FROM goods g GROUP BY g.goods_type) gtp
		LEFT JOIN (
		SELECT
		FORMAT(CASE WHEN SUM(t.commis_amt_payed) IS NULL THEN 0.0000  ELSE  SUM(t.commis_amt_payed) END,2)  commis_amt_payed,
		FORMAT(CASE WHEN SUM(t.commis_amt_wait)  IS NULL THEN 0.0000  ELSE  SUM(t.commis_amt_wait) END,2)  commis_amt_wait,
		t.goods_type
		FROM (
		SELECT ct.partner_id,
		SUM(CASE WHEN ct.state='P' THEN ct.commis_amt ELSE NULL END)    commis_amt_payed,
		SUM(CASE WHEN ct.state='V' THEN ct.commis_amt ELSE NULL END)    commis_amt_wait,

		CASE WHEN (gs.goods_type='1' OR gs.goods_type='2') THEN gs.goods_type ELSE  '3' END goods_type
		FROM commis_into_acct ct
		LEFT JOIN
		(
		SELECT g.code,g.goods_type FROM  goods  g
		WHERE  g.goods_type IN ( '1','2','3','4','5')
		)gs
		ON ct.goods_code = gs.code
		WHERE ct.partner_id = #{partnerId}
		GROUP BY ct.partner_id,gs.goods_type ) t
		GROUP BY t.partner_id,t.goods_type ) cc
		ON cc.goods_type=gtp.goods_type
		WHERE gtp.goods_type IN ('1','2','3')

		<!--select * FROM (
		SELECT t.partner_id,
		FORMAT(CASE WHEN SUM(t.commis_amt_payed) IS NULL THEN 0.0000  ELSE  SUM(t.commis_amt_payed) END,2)  commis_amt_payed,
		FORMAT(CASE WHEN SUM(t.commis_amt_wait)  IS NULL THEN 0.0000  ELSE  SUM(t.commis_amt_wait) END,2)  commis_amt_wait,
		t.goods_type
		FROM (
		SELECT ct.partner_id,
		SUM(CASE WHEN ct.state='P' THEN ct.commis_amt ELSE NULL END)    commis_amt_payed,
		SUM(CASE WHEN ct.state='V' THEN ct.commis_amt ELSE NULL END)    commis_amt_wait,

		CASE WHEN gs.goods_type IS NULL THEN '4' ELSE  gs.goods_type END goods_type
		FROM commis_into_acct ct
		LEFT JOIN goods gs
		ON ct.goods_code = gs.code
		WHERE ct.partner_id = #{partnerId}
		GROUP BY ct.partner_id,gs.goods_type ) t
		GROUP BY t.partner_id,t.goods_type ) cc-->

	</select>

	<!--=====================================根据合伙人查询分类订单佣金明细列表==============================-->
	<select id="findCommisAndOrderList" resultType="com.jsunicom.oms.model.commis.CommisAndOrderDetail">

		SELECT
		 -- c.partner_id,
		  FORMAT(c.commis_amt,2) commis_amt,
		  c.state commis_state,
		 -- DATE_FORMAT(c.create_time , '%Y-%m-%d %H:%i:%s')       commis_time,
		  c.create_time    commis_time,
		  c.order_no,
		 -- c.order_goods_no,
		 -- og.goods_type       order_goods_type,
		  CASE WHEN og.goods_name IS NULL THEN c.remark ELSE og.goods_name END goods_name,
		  -- CASE WHEN og.create_time IS NULL THEN c.create_time ELSE  og.create_time END    order_time,
		  DATE_FORMAT(CASE WHEN og.create_time IS NULL THEN c.create_time ELSE  og.create_time END, '%Y-%m-%d %H:%i:%s')    order_time,
		  -- CONCAT(LEFT(og.cust_phone,3), '****' ,RIGHT(og.cust_phone,4))  cust_phone,
		  gs.goods_type,
		  gs.pic_url1 goods_pic_url1,
		  gs.pic_url2 goods_pic_url2,
		  c.goods_code
		  FROM commis_into_acct c
		  LEFT JOIN order_goods og
			ON c.order_no = og.order_no
		  LEFT JOIN
		  (
			SELECT
			g.code,
			g.pic_url1,
			g.pic_url2,
			g.goods_type
			FROM  goods  g
			WHERE
			g.goods_type IN ( '1','2','3','4','5')
		  )gs
			ON og.goods_code = gs.code
		WHERE c.partner_id =#{partnerId}
		<if test="goodsType!='3'.toString()">
			AND gs.goods_type = #{goodsType}
		</if>
		<if test="goodsType=='3'.toString()">
			AND (gs.goods_type IN ('3','4','5') OR gs.goods_type is NULL )
		</if>

		<!--<if test="goodsType!='4'.toString()">
			AND gs.goods_type = #{goodsType}
		</if>
		<if test="goodsType=='4'.toString()">
			AND (gs.goods_type is NULL OR gs.goods_type='4')
		</if>
		<if test="goodsName!= null">
			AND IFNULL(og.goods_name,c.remark) like #{goodsName}
		</if>-->
		<if test="startDate!= null">
			<![CDATA[ AND DATE_FORMAT(IFNULL(og.create_time,c.create_time), '%Y/%m/%d') >= #{startDate} ]]>
		</if>
		<if test="endDate!= null">
			<![CDATA[ AND DATE_FORMAT(IFNULL(og.create_time,c.create_time), '%Y/%m/%d')  <= #{endDate} ]]>
		</if>

	</select>

	<select id="queryPartnerCommisSummary" resultType="java.math.BigDecimal">
			SELECT IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				SELECT <![CDATA[cast(r.order_no as signed)]]> FROM commis_pay_record r WHERE r.state = '0' and r.order_no in
					<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
						 #{item}
					</foreach> )
			AND  <![CDATA[ a.partner_id <> -1 ]]> and a.org_code = #{orgCode}
			union all
			SELECT IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				  SELECT <![CDATA[cast(r.order_no as signed)]]> FROM commis_pay_record r WHERE r.state = '1' and r.order_no in
					<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
						#{item}
					</foreach> )
			AND  <![CDATA[ a.partner_id <> -1 ]]>  and a.org_code = #{orgCode}
			union all
			SELECT IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				  SELECT <![CDATA[cast(r.order_no as signed)]]> FROM commis_pay_record r WHERE  r.state = '2' and  r.order_no in
					<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
						#{item}
					</foreach>)
			AND  <![CDATA[ a.partner_id <> -1 ]]>  and a.org_code = #{orgCode}
	</select>

	<select id="queryMerchantCommisSummary" resultType="java.math.BigDecimal">
			SELECT  IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				SELECT b.commis_into_acct_id FROM commis_merchant_batch b WHERE b.batch_id IN(
					SELECT r.batch_id FROM commis_merchant_pay_record r WHERE r.state = '0' AND r.batch_id in
						<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
							#{item}
						</foreach>)
				) AND  a.partner_id = -1 and a.org_code = #{orgCode}
			UNION ALL
			SELECT  IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				SELECT b.commis_into_acct_id FROM commis_merchant_batch b WHERE b.batch_id IN(
					SELECT r.batch_id FROM commis_merchant_pay_record r WHERE r.state IN ('1', '2') AND r.batch_id in
						<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
							#{item}
						</foreach>)
				) AND  a.partner_id = -1 and a.org_code = #{orgCode}
	</select>

	<select id="queryWowCommisSummary" resultType="java.math.BigDecimal">
			SELECT IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				SELECT <![CDATA[cast(r.order_no as signed)]]> FROM commis_pay_record r WHERE r.state = '0' AND r.order_no in
					<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
						#{item}
					</foreach>)
		    AND a.partner_id = -1 AND a.merchant_id IN (SELECT d.name FROM dict d WHERE d.kind = 'wow_advanced_merchants') and a.org_code = #{orgCode}
			UNION ALL
			SELECT IFNULL(SUM(a.commis_amt), 0) AS amt FROM commis_into_acct a WHERE a.id IN(
				SELECT <![CDATA[cast(r.order_no as signed)]]> FROM commis_pay_record r WHERE r.state IN ('1', '2') AND r.order_no in
					<foreach collection="list" item="item" index="index" open="(" separator="," close=")">
						#{item}
					</foreach>)
			AND a.partner_id = -1 AND a.merchant_id IN (SELECT d.name FROM dict d WHERE d.kind = 'wow_advanced_merchants') and a.org_code = #{orgCode}
	</select>

	<select id="findTodayCommisData" resultType="com.jsunicom.oms.model.commis.CommisIntoAcct" parameterType="java.lang.String">
		SELECT id,partner_id,order_no,commis_rule_id,commis_amt,remark,org_code ,symbol,merchant_id,order_goods_no
				state,create_by,create_time,update_by,update_time,type,goods_code,plan_pay_date
		FROM commis_into_acct
		where DATE_FORMAT(create_time, '%Y%m%d') = #{dateStr}
	</select>
</mapper>
