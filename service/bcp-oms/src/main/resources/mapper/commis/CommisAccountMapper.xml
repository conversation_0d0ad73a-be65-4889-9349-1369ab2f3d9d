<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.commis.CommisAccountDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO commis_account( id,partner_id,balance,valid_commis,wait_commis,payed_commis,create_by,
                         create_time,update_by,update_time,wo_acct )
        VALUES ( #{id},#{partnerId},#{balance},#{validCommis},#{waitCommis},#{payedCommis},#{createBy},
                 #{createTime},#{updateBy},#{updateTime},#{woAcct})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO commis_account( id,partner_id,balance,valid_commis,wait_commis,payed_commis,create_by,
                          create_time,update_by,update_time,wo_acct )
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.partnerId},#{item.balance},#{item.validCommis},#{item.waitCommis},#{item.payedCommis},#{item.createBy},
              #{item.createTime},#{item.updateBy},#{item.updateTime},#{item.woAcct} )
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO commis_account( id,partner_id,balance,valid_commis,wait_commis,payed_commis,create_by,
                          create_time,update_by,update_time,wo_acct )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id},#{item.partnerId},#{item.balance},#{item.validCommis},#{item.waitCommis},#{item.payedCommis},#{item.createBy},
              #{item.createTime},#{item.updateBy},#{item.updateTime},#{item.woAcct} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE commis_account
        <set>
            partner_id=#{partnerId},
            balance=#{balance},
            valid_commis=#{validCommis},
            wait_commis=#{waitCommis},
            payed_commis=#{payedCommis},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
            wo_acct=#{woAcct},
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE commis_account
        <set>
            <if test="partnerId!= null">partner_id=#{partnerId},</if>
            <if test="balance!= null">balance=#{balance},</if>
            <if test="validCommis!= null">valid_commis=#{validCommis},</if>
            <if test="waitCommis!= null">wait_commis=#{waitCommis},</if>
            <if test="payedCommis!= null">payed_commis=#{payedCommis},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
            <if test="woAcct!= null">wo_acct=#{woAcct},</if>
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE commis_account
            <set>
                partner_id=#{item.partnerId},
                balance=#{item.balance},
                valid_commis=#{item.validCommis},
                wait_commis=#{item.waitCommis},
                payed_commis=#{item.payedCommis},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
                wo_acct=#{item.woAcct},
            </set>
            WHERE id=#{item.id} 
        </foreach>
    </update>

    <update id="updateCommisAccountByPartnerId">
        UPDATE commis_account
        <set>
            <if test="partnerId!= null">partner_id=#{partnerId},</if>
            <if test="prepareCommis!= null">prepare_commis=#{prepareCommis},</if>
            <if test="balanceCommis!= null">balance_commis=#{balanceCommis},</if>
            <if test="totalCommis!= null">total_commis=#{totalCommis},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE partner_id=#{partnerId}
    </update>

    <update id="updateWOAccountByPartnerId">
        UPDATE commis_account
        SET wo_acct = #{wo_acct}
        WHERE partner_id=#{partnerId}
    </update>
    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM commis_account
        WHERE id=#{id} 
    </delete>

    <delete id="deleteBatch">
        DELETE FROM commis_account
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM commis_account
        WHERE id=#{id} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM commis_account
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM commis_account
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.commis.CommisAccount">
        SELECT * FROM commis_account
        WHERE id=#{id} 
    </select>

    <select id="find" resultType="com.jsunicom.oms.model.commis.CommisAccount">
        SELECT id,partner_id,balance,valid_commis,wait_commis,payed_commis,create_by,create_time
               ,update_by,update_time,wo_acct
         FROM commis_account
        <where>
            <if test="partnerId!= null">
               AND partner_id = #{partnerId}
            </if>
        </where>
    </select>

    <select id="findByPartnerId" resultType="com.jsunicom.oms.model.commis.CommisAccount">
        SELECT * FROM commis_account where partner_id = #{partnerId};
    </select>


</mapper>
