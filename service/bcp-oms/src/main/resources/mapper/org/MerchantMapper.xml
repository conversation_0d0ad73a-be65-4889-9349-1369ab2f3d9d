<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.org.MerchantDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO merchant( id,org_code,campus_id,society_id,merchant_type,merchant_name,
                             state,create_by,create_time,update_by,update_time)
        VALUES ( #{id},#{orgCode},#{campusId},#{societyId},#{merchantType},#{merchantName},
                #{state},#{createBy},#{createTime},#{updateBy},#{updateTime})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO merchant( id,org_code,campus_id,society_id,merchant_type,merchant_name,
                            state,create_by,create_time,update_by,update_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{tem.orgCode},#{tem.campusId},#{tem.societyId},#{tem.merchantType},#{tem.merchantName},
            #{tem.state},#{tem.createBy},#{tem.createTime},#{tem.updateBy},#{tem.updateTime})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO merchant( id,org_code,org_name,merchant_type,merchant_name,bus_license,leg_rep_name,
                          leg_rep_phone,prov_code,prov_name,city_code,city_name,area_code,area_name,
                          address,leg_rep_cert_no,leg_rep_photo1,leg_rep_photo2,bus_license_photo,industry,intent_bus_code,
                          intent_bus_name,state,create_by,create_time,update_by,update_time,pid,
                          contact_phone,bus_permit_photo,head_office,wx_entp_did,wx_entp_pdid,commis_flag,rights_flag )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id},#{item.orgCode},#{item.orgName},#{item.merchantType},#{item.merchantName},#{item.busLicense},#{item.legRepName},
              #{item.legRepPhone},#{item.provCode},#{item.provName},#{item.cityCode},#{item.cityName},#{item.areaCode},#{item.areaName},
              #{item.address},#{item.legRepCertNo},#{item.legRepPhoto1},#{item.legRepPhoto2},#{item.busLicensePhoto},#{item.industry},#{item.intentBusCode},
              #{item.intentBusName},#{item.state},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.pid},
              #{item.contactPhone},#{item.busPermitPhoto},#{item.headOffice},#{item.wxEntpDid},#{item.wxEntpPdid},#{item.commisFlag},#{item.rightsFlag}
              FROM DUAL
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE merchant
        <set>
            org_code=#{orgCode},
            campus_id=#{campusId},
            society_id=#{societyId},
            merchant_type=#{merchantType},
            merchant_name=#{merchantName},
            state=#{state},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=now()
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateIgnoreNull">
        UPDATE merchant
        <set>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="orgName!= null">campus_id=#{campusId},</if>
            <if test="societyId!= null">society_id=#{societyId},</if>
            <if test="merchantType!= null">merchant_type=#{merchantType},</if>
            <if test="merchantName!= null">merchant_name=#{merchantName},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=now(),</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE merchant
            <set>
                org_code=#{item.orgCode},
                campus_id=#{item.campusId},
                society_id=#{item.societyId},
                merchant_type=#{item.merchantType},
                merchant_name=#{item.merchantName},
                state=#{item.state},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=now(),
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM merchant
        WHERE id=#{id}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM merchant
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id}
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM merchant
        WHERE id=#{id}
    </delete>

    <delete id="deleteAll">
        DELETE FROM merchant
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM merchant
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE id=#{id}
    </select>


    <select id="findByMbl" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE leg_rep_phone=#{legRepPhone}
    </select>

    <select id="findByMerchantName" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE Merchant_name=#{merchantName}
    </select>


    <select id="find" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="map">
        SELECT a.id,a.pid,a.org_code,a.org_name,a.merchant_type, a.merchant_name,a.bus_license,a.leg_rep_name,a.leg_rep_phone,a.prov_code,a.prov_name
        ,a.city_code,a.city_name,a.area_code,a.area_name,a.address,a.leg_rep_cert_no,a.leg_rep_photo1
        ,a.leg_rep_photo2,a.bus_license_photo,a.industry,a.intent_bus_code,a.intent_bus_name,a.state,a.create_by
        ,a.create_time,a.update_by,a.update_time,a.bus_permit_photo,a.contact_phone,a.head_office,a.wx_entp_did,a.wx_entp_pdid, a.commis_flag,
        a.rights_flag, b.bank_code, b.bank_name, b.card_no, b.card_name,a.qrcode_type,a.logo_img_url, d.merchant_name as belong_merchant_name
        FROM merchant a left join partner_bank_account b on a.id=b.merchant_id left join merchant d on d.id = a.head_office
        <where>
         (a.state = '1' OR  a.state = '2')
        <if test="orgCodes != null">
            and a.org_code in
            <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="id != null and id != 0">
            AND a.id = #{id}
        </if>
        <if test="merchantName != null and merchantName !=''">
            AND a.merchant_name like #{merchantName}
        </if>
        <if test="busLicense!= null">
            AND a.bus_license like #{busLicense}
        </if>
        <if test="legRepName!= null">
            AND a.leg_rep_name = #{legRepName}
        </if>
        <if test="legRepPhone!= null">
            AND a.leg_rep_phone = #{legRepPhone}
        </if>
        <if test="provCode!= null">
            AND a.prov_code = #{provCode}
        </if>
        <if test="provName!= null">
            AND a.prov_name = #{provName}
        </if>
        <if test="cityCode!= null">
            AND a.city_code = #{cityCode}
        </if>
        <if test="cityName!= null">
            AND a.city_name = #{cityName}
        </if>
        <if test="areaCode!= null">
            AND a.area_code = #{areaCode}
        </if>
        <if test="areaName!= null">
            AND a.area_name = #{areaName}
        </if>
        <if test="address!= null">
            AND a.address = #{address}
        </if>
        <if test="legRepCertNo!= null">
            AND a.leg_rep_cert_no like #{legRepCertNo}
        </if>
        <if test="legRepPhoto1!= null">
            AND a.leg_rep_photo1 = #{legRepPhoto1}
        </if>
        <if test="legRepPhoto2!= null">
            AND a.leg_rep_photo2 = #{legRepPhoto2}
        </if>
        <if test="busLicensePhoto!= null">
            AND a.bus_license_photo = #{busLicensePhoto}
        </if>
        <if test="industry!= null">
            AND a.industry = #{industry}
        </if>
        <if test="intentBusCode!= null">
            AND a.intent_bus_code = #{intentBusCode}
        </if>
        <if test="intentBusName!= null">
            AND a.intent_bus_name = #{intentBusName}
        </if>
        <if test="state!= null">
            AND a.state = #{state}
        </if>
        <if test="createBy!= null">
            AND a.create_by = #{createBy}
        </if>
        <if test="createTime!= null">
            AND a.create_time = #{createTime}
        </if>
        <if test="updateBy!= null">
            AND a.update_by = #{updateBy}
        </if>
        <if test="updateTime!= null">
            AND a.update_time = #{updateTime}
        </if>
        <if test="qrcodeType!= null">
            AND a.qrcode_type = #{qrcodeType}
        </if>
        <if test="headOffice != null and headOffice != ''">
            AND a.head_office = #{headOffice}
        </if>
        </where>
    </select>

    <select id="findWithManagerPhone" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="map">
        SELECT a.id,a.pid,a.org_code,a.org_name,a.merchant_type, a.merchant_name,a.bus_license,a.leg_rep_name,a.leg_rep_phone,a.prov_code,a.prov_name
        ,a.city_code,a.city_name,a.area_code,a.area_name,a.address,a.leg_rep_cert_no,a.leg_rep_photo1
        ,a.leg_rep_photo2,a.bus_license_photo,a.industry,a.intent_bus_code,a.intent_bus_name,a.state,a.create_by
        ,a.create_time,a.update_by,a.update_time,a.bus_permit_photo,a.contact_phone,a.head_office,a.wx_entp_did,a.wx_entp_pdid, a.commis_flag,
        a.rights_flag, b.bank_code, b.bank_name, b.card_no, b.card_name,a.qrcode_type,a.logo_img_url, d.merchant_name as belong_merchant_name
        FROM merchant a left join partner_bank_account b on a.id=b.merchant_id left join partner c on a.id = c.merchant_id left join merchant d on d.id = a.head_office
        <where>
            (a.state = '1' OR  a.state = '2') AND c.is_mer_admin = '1'
            <if test="orgCodes != null">
                and a.org_code in
                <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null and id != 0">
                AND a.id = #{id}
            </if>
            <if test="merchantName != null and merchantName !=''">
                AND a.merchant_name like #{merchantName}
            </if>
            <if test="busLicense!= null">
                AND a.bus_license like #{busLicense}
            </if>
            <if test="legRepName!= null">
                AND a.leg_rep_name = #{legRepName}
            </if>
            <if test="legRepPhone!= null">
                AND a.leg_rep_phone = #{legRepPhone}
            </if>
            <if test="provCode!= null">
                AND a.prov_code = #{provCode}
            </if>
            <if test="provName!= null">
                AND a.prov_name = #{provName}
            </if>
            <if test="cityCode!= null">
                AND a.city_code = #{cityCode}
            </if>
            <if test="cityName!= null">
                AND a.city_name = #{cityName}
            </if>
            <if test="areaCode!= null">
                AND a.area_code = #{areaCode}
            </if>
            <if test="areaName!= null">
                AND a.area_name = #{areaName}
            </if>
            <if test="address!= null">
                AND a.address = #{address}
            </if>
            <if test="legRepCertNo!= null">
                AND a.leg_rep_cert_no like #{legRepCertNo}
            </if>
            <if test="legRepPhoto1!= null">
                AND a.leg_rep_photo1 = #{legRepPhoto1}
            </if>
            <if test="legRepPhoto2!= null">
                AND a.leg_rep_photo2 = #{legRepPhoto2}
            </if>
            <if test="busLicensePhoto!= null">
                AND a.bus_license_photo = #{busLicensePhoto}
            </if>
            <if test="industry!= null">
                AND a.industry = #{industry}
            </if>
            <if test="intentBusCode!= null">
                AND a.intent_bus_code = #{intentBusCode}
            </if>
            <if test="intentBusName!= null">
                AND a.intent_bus_name = #{intentBusName}
            </if>
            <if test="state!= null">
                AND a.state = #{state}
            </if>
            <if test="createBy!= null">
                AND a.create_by = #{createBy}
            </if>
            <if test="createTime!= null">
                AND a.create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
                AND a.update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
                AND a.update_time = #{updateTime}
            </if>
            <if test="qrcodeType!= null">
                AND a.qrcode_type = #{qrcodeType}
            </if>
            <if test="headOffice != null and headOffice != ''">
                AND a.head_office = #{headOffice}
            </if>
            <if test="managerPhone != null">
                AND c.acct_no = #{managerPhone}
            </if>
            <if test="id != null">
                AND a.id = #{id}
            </if>
        </where>
    </select>


    <select id="findWoSchoolMerchant" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="map">
       SELECT * FROM  merchant a
        <where>
            a.state = '1' AND a.id NOT IN (SELECT w.merchant_Id FROM wo_school_info w )
            <if test="orgCodes != null">
                and a.org_code in
                <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null and id != 0">
                AND a.id = #{id}
            </if>
            <if test="merchantName != null and merchantName !=''">
                AND a.merchant_name like #{merchantName}
            </if>
            <if test="busLicense!= null">
                AND a.bus_license like #{busLicense}
            </if>
            <if test="legRepName!= null">
                AND a.leg_rep_name = #{legRepName}
            </if>
            <if test="legRepPhone!= null">
                AND a.leg_rep_phone = #{legRepPhone}
            </if>
            <if test="provCode!= null">
                AND a.prov_code = #{provCode}
            </if>
            <if test="provName!= null">
                AND a.prov_name = #{provName}
            </if>
            <if test="cityCode!= null">
                AND a.city_code = #{cityCode}
            </if>
            <if test="cityName!= null">
                AND a.city_name = #{cityName}
            </if>
            <if test="areaCode!= null">
                AND a.area_code = #{areaCode}
            </if>
            <if test="areaName!= null">
                AND a.area_name = #{areaName}
            </if>
            <if test="address!= null">
                AND a.address = #{address}
            </if>
            <if test="legRepCertNo!= null">
                AND a.leg_rep_cert_no like #{legRepCertNo}
            </if>
            <if test="legRepPhoto1!= null">
                AND a.leg_rep_photo1 = #{legRepPhoto1}
            </if>
            <if test="legRepPhoto2!= null">
                AND a.leg_rep_photo2 = #{legRepPhoto2}
            </if>
            <if test="busLicensePhoto!= null">
                AND a.bus_license_photo = #{busLicensePhoto}
            </if>
            <if test="industry!= null">
                AND a.industry = #{industry}
            </if>
            <if test="intentBusCode!= null">
                AND a.intent_bus_code = #{intentBusCode}
            </if>
            <if test="intentBusName!= null">
                AND a.intent_bus_name = #{intentBusName}
            </if>
            <if test="state!= null">
                AND a.state = #{state}
            </if>
            <if test="createBy!= null">
                AND a.create_by = #{createBy}
            </if>
            <if test="createTime!= null">
                AND a.create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
                AND a.update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
                AND a.update_time = #{updateTime}
            </if>
            <if test="qrcodeType!= null">
                AND a.qrcode_type = #{qrcodeType}
            </if>
            <if test="headOffice != null and headOffice != ''">
                AND a.head_office = #{headOffice}
            </if>
        </where>
    </select>

    <select id="findPromotMerchant" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="com.jsunicom.oms.model.org.Merchant">
        SELECT a.id,a.pid,a.org_code,a.org_name,a.merchant_type, a.merchant_name,a.bus_license,a.leg_rep_name,a.leg_rep_phone,a.prov_code,a.prov_name
        ,a.city_code,a.city_name,a.area_code,a.area_name,address,a.leg_rep_cert_no,a.leg_rep_photo1
        ,a.leg_rep_photo2,a.bus_license_photo,a.industry,a.intent_bus_code,a.intent_bus_name,a.state,a.create_by
        ,a.create_time,a.update_by,a.update_time,a.bus_permit_photo,a.contact_phone,a.head_office,a.wx_entp_did,a.wx_entp_pdid, a.commis_flag,
        a.rights_flag,a.qrcode_type,a.logo_img_url
        FROM merchant a join promot_actv b on a.id=b.merchant_id
      where b.merchant_id !=0
        AND   (a.state = '1' OR  a.state = '2')
        <if test="orgCode != null and orgCode != ''">
            and a.org_code =#{orgCode}
        </if>
        <if test="id != null and id != 0">
            AND a.id = #{id}
        </if>
        <if test="merchantName != null and merchantName !=''">
            AND a.merchant_name like #{merchantName}
        </if>
        <if test="qrcodeType!= null">
            AND a.qrcode_type = #{qrcodeType}
        </if>
    </select>



    <select id="findPromotMerchantBranch" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="com.jsunicom.oms.model.org.Merchant">
        SELECT a.id,a.pid,a.org_code,a.org_name,a.merchant_type, a.merchant_name,a.bus_license,a.leg_rep_name,a.leg_rep_phone,a.prov_code,a.prov_name
        ,a.city_code,a.city_name,a.area_code,a.area_name,address,a.leg_rep_cert_no,a.leg_rep_photo1
        ,a.leg_rep_photo2,a.bus_license_photo,a.industry,a.intent_bus_code,a.intent_bus_name,a.state,a.create_by
        ,a.create_time,a.update_by,a.update_time,a.bus_permit_photo,a.contact_phone,a.head_office,a.wx_entp_did,a.wx_entp_pdid, a.commis_flag,
        a.rights_flag,a.qrcode_type,a.logo_img_url
        FROM merchant a
        where (a.state = '1' OR  a.state = '2')
        <if test="orgCode != null and orgCode != ''">
            and a.org_code =#{orgCode}
        </if>
        <if test="merchantName != null and merchantName !=''">
            AND a.merchant_name like #{merchantName}
        </if>
        <if test="qrcodeType!= null">
            AND a.qrcode_type = #{qrcodeType}
        </if>
        <if test="headOffice != null and headOffice != ''">
            AND a.head_office = #{headOffice}
        </if>
    </select>

    <update id="updateStatBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE merchant
            <set>
                state=#{item.state},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>

    <select id="findUsing" resultType="com.jsunicom.oms.model.org.Merchant" parameterType="com.jsunicom.oms.model.org.Merchant">
        select * from merchant
        where state = '1' AND merchant_type = '1'
        <if test="orgCode!= null and orgCode !=''">
            and org_code = #{orgCode}
        </if>
    </select>

    <select id="getHeadOffice" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE state = '1' AND merchant_type = '1'
    </select>
    <select id="getHeadOfficeByOrgCode" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE state = '1' AND merchant_type = '1'
        and (org_code = #{orgCode} or org_code = '' or org_code = 'root')
        <if test="merchantName!= null">
            AND merchant_name LIKE #{merchantName}
        </if>
    </select>

    <select id="getMerchantByType" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        <where>
            <if test="merchantType != null">
                AND merchant_type = #{merchantType}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            AND state = '1'
        </where>
    </select>

    <select id="queryMerchant" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT m.org_code,m.id,p.`bus_line`,m.merchant_type,m.commis_flag,m.rights_flag,m.head_office, tpm.id AS tag_id
        FROM merchant m
        LEFT JOIN tag_partner_merchant tpm ON m.id =tpm.partner_merchant_id AND tpm.first_type='1'
        LEFT JOIN partner p ON p.merchant_id=m.id AND p.is_mer_admin=#{isMerAdmin} AND p.partner_type <![CDATA[!= ]]>#{partnerType}
        WHERE m.state='1' AND m.update_time &gt;= date_format(#{date},'%Y-%m-%d') AND m.update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>

    <select id="getMerchantByOrgCode" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        <where>
            <if test="merchantType != null">
                AND merchant_type = #{merchantType}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            <if test="merchantName!= null">
                AND merchant_name LIKE #{merchantName}
            </if>
            <if test="id!= null">
                AND id = #{id}
            </if>
            AND state = '1'
        </where>
        ORDER BY create_time DESC
         limit 10
    </select>

    <select id="getMerchantByOrgCodeNoLimit" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        <where>
            <if test="merchantType != null">
                AND merchant_type = #{merchantType}
            </if>
            <if test="orgCode!= null and orgCode != ''">
                AND org_code = #{orgCode}
            </if>
            <if test="merchantName!= null">
                AND merchant_name LIKE #{merchantName}
            </if>
            <if test="id!= null">
                AND id = #{id}
            </if>
            AND state = '1'
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="countBusLicense" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM merchant
        <where>
            <if test="busLicense != null and busLicense != ''">
                AND bus_license = #{busLicense}
            </if>
            <if test="id != null and id != ''">
                AND id != #{id}
            </if>
        </where>
    </select>

    <select id="countMerchant" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM merchant
        <where>
            <if test="merchantName != null and merchantName != ''">
                AND merchant_name = #{merchantName}
            </if>
            <if test="id != null and id != ''">
                AND id != #{id}
            </if>
        </where>
    </select>

    <select id="getMerchantByPid" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE pid = #{merchantId}
    </select>
    <select id="getMerchantBySocietyId" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE society_id = #{societyId}
    </select>
    <select id="getSubMerchant" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE head_office = #{headOffice} and state = '1'
    </select>
    <select id="findMerchantAll" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE state!='0'
    </select>

    <select id="queryAddModifyMerchant" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE state!='0' AND update_time &gt;= date_format(#{date},'%Y-%m-%d') AND update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>

    <select id="queryDeleteMerchant" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant
        WHERE state='0' AND update_time &gt;= date_format(#{date},'%Y-%m-%d') AND update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>

    <select id="queryDeleteBakMerchant" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant_del_bak
        WHERE   operate_time &gt;= date_format(#{date},'%Y-%m-%d') AND operate_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>



    <select id="getMerchantByMerchantName" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant where 1=1
            <if test="merchantName!= null">
                AND merchant_name LIKE #{merchantName}
            </if>
        ORDER BY create_time DESC
        limit 10
    </select>

    <select id="findAll" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT id,  merchant_name FROM merchant
    </select>

    <update id="updateMerchant2Qudao">
       update merchant
        set merchant_type='4' ,
            whitelist_ability ='0'
        where merchant_name = #{merchantName}
    </update>

    <select id="getMerchantByName" resultType="com.jsunicom.oms.model.org.Merchant">
        SELECT * FROM merchant where merchant_name=#{merchantName}
    </select>

    <select id="findDataSyn" resultType="com.jsunicom.oms.model.org.Merchant">
        select * from merchant p where p.update_time &gt;= DATE_FORMAT(#{synTime},'%Y-%m-%d %H:%i:%s') and p.update_time &lt;= DATE_FORMAT(#{nextTime},'%Y-%m-%d %H:%i:%s') order by p.update_time
    </select>

    <select id="findFirstUpdate" resultType="com.jsunicom.oms.model.org.Merchant">
        select * from merchant p order by p.update_time limit 0,1
    </select>


    <select id="getCfileImageUploadData" resultType="com.jsunicom.oms.model.org.Merchant">
      select a.* from (
       select * from merchant where (bus_license_photo like 'imageId=%' or bus_license_photo like CONCAT(#{param},'%')) and  bus_license_photo_state is null
         union
       select * from merchant where (bus_permit_photo like 'imageId=%' or bus_permit_photo like CONCAT(#{param},'%')) and  bus_permit_photo_state is null
      ) a order by a.id  limit #{offset},#{limit}

    </select>

    <select id="getCfileImageUploadDataCount" resultType="int">
      select count(*) from (
          select * from merchant where (bus_license_photo like 'imageId=%' or bus_license_photo like CONCAT(#{param},'%')) and  bus_license_photo_state is null
             union
          select * from merchant where (bus_permit_photo like 'imageId=%' or bus_permit_photo like CONCAT(#{param},'%')) and  bus_permit_photo_state is null

          ) a

    </select>

    <update id="updateBusLicensePhotoSuccess">
        UPDATE merchant set bus_license_photo=#{busLicensePhoto},update_time=sysdate(),bus_license_photo_state='1' WHERE id=#{id}
    </update>


    <update id="updateBusLicensePhotoFail">
        UPDATE merchant set update_time=sysdate(),bus_license_photo_state=#{busLicensePhotoState} WHERE id=#{id}
    </update>

    <update id="updateBusPermitPhotoSuccess">
        UPDATE merchant set bus_permit_photo=#{busPermitPhoto},update_time=sysdate(),bus_permit_photo_state='1' WHERE id=#{id}
    </update>


    <update id="updateBusPermitPhotoFail">
        UPDATE merchant set update_time=sysdate(),bus_permit_photo_state=#{busPermitPhotoState} WHERE id=#{id}
    </update>

    <select id="getImageUploadData" resultType="com.jsunicom.oms.model.org.Merchant">
      select a.* from (
       select * from merchant where
       (
           (bus_license_photo like 'imageId=%' or bus_license_photo like CONCAT(#{cfileparam},'%'))
           or bus_license_photo like CONCAT(#{shzxParam},'%')
       ) and  bus_license_photo_state is null
         union
       select * from merchant where
       (
          (bus_permit_photo like 'imageId=%' or bus_permit_photo like CONCAT(#{cfileparam},'%'))
          or bus_permit_photo like CONCAT(#{shzxParam},'%')
        ) and  bus_permit_photo_state is null
      ) a where DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') &lt;=#{uploadTime} order by a.id  limit #{offset},#{limit}
    </select>

    <select id="findMerchantByTime" resultType="com.jsunicom.oms.model.org.Merchant">
        select * from merchant where create_time &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s') and create_time &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s') order by create_time
    </select>
    <select id="selectSchoolIdByMerchantId" resultType="java.lang.String">
        select id from wo_school_info where merchant_id=#{merchantId}
    </select>
    <select id="selectMerchantNum" resultType="java.lang.Integer">
        select count(*) from merchant a
        where  a.state = '1'
    </select>
</mapper>

