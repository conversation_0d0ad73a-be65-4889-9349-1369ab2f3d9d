<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.org.OrgInfoDao">
    <sql id="allColumns">
        id id,
        parent_org_code parentOrgCode,
        org_code orgCode,
        full_org_code fullOrgCode,
        org_name orgName,
        wx_entp_code wxEntpCode,
        state state,
        create_by createBy,
        DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') createTime,
        update_by updateBy,
        DATE_FORMAT(update_time,'%Y-%m-%d %H:%i:%s') updateTime,
        order_by orderBy,
        is_city isCity
    </sql>

    <select id="findByPK" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>
        FROM org_info
        WHERE id = #{id}
    </select>

    <select id="findByCode" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>
        FROM org_info
        WHERE org_code = #{orgCode} limit 1
    </select>

    <select id="findList" parameterType="com.jsunicom.oms.model.org.OrgInfo" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>
        FROM org_info
        WHERE state = 'Y'
        <if test="orgName != null and orgName != ''">
            AND org_name LIKE CONCAT('%', #{orgName}, '%')
        </if>
        ORDER BY order_by asc
    </select>

    <select id="checkOrgCodeAndOrgName" resultType="java.lang.Boolean">
        SELECT
        CASE WHEN COUNT(1) > 0 THEN FALSE ELSE TRUE END
        AS result
        FROM org_info
        WHERE  state = 'Y' AND (org_code=#{orgCode} OR org_name=#{orgName})
    </select>

    <select id="checkOrgCodeAndOrgNameExcept" resultType="java.lang.Boolean">
        SELECT
        CASE WHEN COUNT(1) > 0 THEN FALSE ELSE TRUE END
        AS result
        FROM org_info
        WHERE id!=#{id} AND state = 'Y' AND (org_code=#{orgCode} OR org_name=#{orgName})
    </select>

    <select id="findAll" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>
        FROM org_info
    </select>
<!-- 嵌套查询机构形成树形结构 -->
    <resultMap id="SubResult" type="com.jsunicom.oms.model.org.OrgInfoTreeNode" >
        <id property="id" column="id"/>
        <result property="parentOrgCode" column="parentOrgCode"/>
        <result property="orgCode" column="orgCode"/>
        <result property="fullOrgCode" column="fullOrgCode"/>
        <result property="orgName" column="orgName"/>
        <result property="wxEntpCode" column="wxEntpCode"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="createBy"/>
        <result property="updateTime" column="updateTime"/>
        <result property="updateBy" column="updateBy"/>
        <result property="state" column="state"/>
        <result property="orderBy" column="orderBy"/>
        <result property="isCity" column="is_city"/>
        <collection property="children" javaType="java.util.ArrayList" column="orgCode"
                    ofType="com.jsunicom.oms.model.org.OrgInfoTreeNode" select="getSubNode">
        </collection>
    </resultMap>
    <!--查询子节点-->
    <select id="getSubNode" resultMap="SubResult">
        SELECT  <include refid="allColumns"/>  from org_info
        WHERE parent_org_code = #{orgCode} order by order_by
    </select>
    <!--查询子节点-->
    <select id="getOrgInfoTree" resultMap="SubResult">
        SELECT  <include refid="allColumns"/>  from org_info
        WHERE org_code = #{orgCode} order by order_by
    </select>

    <!--查询地市机构-->
    <select id="getCityDepartment" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE is_city='Y' order by order_by
    </select>
    <!--查询所有机构-->
    <select id="getCityDepartmentByOrgCode" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE state='Y'
        <if test="orgCode!= null and orgCode !='' and orgCode!='root' ">
            and org_code = #{orgCode}
        </if>
        order by order_by
    </select>

    <!--查询所有机构-->
    <select id="getCityDepartmentByOrgCodeWithRoot" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE state='Y'
        <if test="orgCode!= null and orgCode !=''">
            and org_code = #{orgCode}
        </if>
        order by order_by
    </select>

    <!--查询地市机构-->
    <select id="getTheCity" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE is_city='Y'
        and org_code = #{orgCode}
        order by order_by
    </select>

    <!--查询地市机构-->
    <select id="getTheCityCodeAndName" resultType="map" parameterType="String">
        SELECT org_code as areaCode ,org_name as areaName  FROM org_info
        WHERE is_city='Y'
        <if test="orgCode != null and orgCode != ''">
            and ORG_CODE = #{orgCode}
        </if>
        order by order_by
    </select>

<!--嵌套查询机构形成树形结构 -->
    <!-- 新增 -->
    <insert id="save" parameterType="com.jsunicom.oms.model.org.OrgInfo">
        INSERT INTO org_info(
        parent_org_code,
        org_code,
        full_org_code,
        org_name,
        wx_entp_code,
        state,
        create_by,
        create_time,
        update_by,
        update_time,
        order_by,
        is_city
        ) VALUES (
        #{parentOrgCode},
        #{orgCode},
        #{fullOrgCode},
        #{orgName},
        #{wxEntpCode},
        'Y',
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{orderBy},
        #{isCity}
        )
    </insert>

    <!-- 删除 -->
    <update id="delete" parameterType="com.jsunicom.oms.model.org.OrgInfo">
        UPDATE org_info
        SET state = 'N', update_by=#{curAcctNo}, update_time=#{updateTime}
        WHERE org_code = #{orgCode}
    </update>

    <!-- 修改 -->
    <update id="modOrgName" parameterType="com.jsunicom.oms.model.org.OrgInfo">
        UPDATE org_info
        SET org_name = #{orgName}, order_by=#{orderBy}, is_city=#{isCity}, update_by=#{curAcctNo}, update_time=#{updateTime}
        WHERE org_code = #{orgCode}
    </update>

    <!--查询地市机构数量-->
    <select id="queryTotalOrgNum" resultType="java.lang.Integer">
        SELECT count(1)  FROM org_info
        WHERE state='Y'
        AND  <![CDATA[ org_code <> 'root' ]]>
    </select>

    <select id="queryOrgCodeAndName" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT org_code, org_name FROM org_info
        WHERE is_city='Y'
        AND  <![CDATA[ org_code <> 'root' ]]> and (org_code is not null) and (org_name is not null)
        order by order_by
    </select>

    <select id="queryOfflineOrgInfo" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE parent_org_code='root'
        order by order_by
    </select>

    <select id="queryOfflineOrgInfoByOrgCode" resultType="com.jsunicom.oms.model.org.OrgInfo">
        SELECT <include refid="allColumns"/>  FROM org_info
        WHERE parent_org_code='root'
        <if test="orgCode!= null and orgCode !='' and orgCode!='root' ">
            and org_code = #{orgCode}
        </if>
        order by order_by
    </select>
</mapper>
