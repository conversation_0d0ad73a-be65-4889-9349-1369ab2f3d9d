<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.WoSchoolCampusMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.WoSchoolCampus">
    <id column="CAMPUS_ID" jdbcType="BIGINT" property="campusId" />
    <result column="CAMPUS_NAME" jdbcType="VARCHAR" property="campusName" />
    <result column="SCHOOL_ID" jdbcType="VARCHAR" property="schoolId" />
    <result column="SCHOOL_NAME" jdbcType="VARCHAR" property="schoolName" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="TEL" jdbcType="VARCHAR" property="tel" />
    <result column="POST_CODE" jdbcType="VARCHAR" property="postCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="ASCRIPTION_GRID_CODE" jdbcType="VARCHAR" property="ascriptionGridCode" />
    <result column="ASCRIPTION_GRID_NAME" jdbcType="VARCHAR" property="ascriptionGridName" />
    <result column="STATE" jdbcType="CHAR" property="state" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    CAMPUS_ID, CAMPUS_NAME, SCHOOL_ID, SCHOOL_NAME, ADDRESS, TEL, POST_CODE, CREATE_TIME, 
    UPDATE_TIME, ORG_CODE, CITY_CODE, ASCRIPTION_GRID_CODE, ASCRIPTION_GRID_NAME, STATE, 
    REMARK
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_school_campus
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wo_school_campus
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusExample">
    delete from wo_school_campus
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.WoSchoolCampus">
    insert into wo_school_campus (CAMPUS_ID, CAMPUS_NAME, SCHOOL_ID, 
      SCHOOL_NAME, ADDRESS, TEL, 
      POST_CODE, CREATE_TIME, UPDATE_TIME, 
      ORG_CODE, CITY_CODE, ASCRIPTION_GRID_CODE, 
      ASCRIPTION_GRID_NAME, STATE, REMARK
      )
    values (#{campusId,jdbcType=BIGINT}, #{campusName,jdbcType=VARCHAR}, #{schoolId,jdbcType=VARCHAR}, 
      #{schoolName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, 
      #{postCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{orgCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{ascriptionGridCode,jdbcType=VARCHAR}, 
      #{ascriptionGridName,jdbcType=VARCHAR}, #{state,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.WoSchoolCampus">
    insert into wo_school_campus
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        CAMPUS_ID,
      </if>
      <if test="campusName != null">
        CAMPUS_NAME,
      </if>
      <if test="schoolId != null">
        SCHOOL_ID,
      </if>
      <if test="schoolName != null">
        SCHOOL_NAME,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="tel != null">
        TEL,
      </if>
      <if test="postCode != null">
        POST_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="cityCode != null">
        CITY_CODE,
      </if>
      <if test="ascriptionGridCode != null">
        ASCRIPTION_GRID_CODE,
      </if>
      <if test="ascriptionGridName != null">
        ASCRIPTION_GRID_NAME,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        #{campusId,jdbcType=BIGINT},
      </if>
      <if test="campusName != null">
        #{campusName,jdbcType=VARCHAR},
      </if>
      <if test="schoolId != null">
        #{schoolId,jdbcType=VARCHAR},
      </if>
      <if test="schoolName != null">
        #{schoolName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null">
        #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="ascriptionGridCode != null">
        #{ascriptionGridCode,jdbcType=VARCHAR},
      </if>
      <if test="ascriptionGridName != null">
        #{ascriptionGridName,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusExample" resultType="java.lang.Long">
    select count(*) from wo_school_campus
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_school_campus
    <set>
      <if test="record.campusId != null">
        CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      </if>
      <if test="record.campusName != null">
        CAMPUS_NAME = #{record.campusName,jdbcType=VARCHAR},
      </if>
      <if test="record.schoolId != null">
        SCHOOL_ID = #{record.schoolId,jdbcType=VARCHAR},
      </if>
      <if test="record.schoolName != null">
        SCHOOL_NAME = #{record.schoolName,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        ADDRESS = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.tel != null">
        TEL = #{record.tel,jdbcType=VARCHAR},
      </if>
      <if test="record.postCode != null">
        POST_CODE = #{record.postCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orgCode != null">
        ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        CITY_CODE = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ascriptionGridCode != null">
        ASCRIPTION_GRID_CODE = #{record.ascriptionGridCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ascriptionGridName != null">
        ASCRIPTION_GRID_NAME = #{record.ascriptionGridName,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        STATE = #{record.state,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_school_campus
    set CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      CAMPUS_NAME = #{record.campusName,jdbcType=VARCHAR},
      SCHOOL_ID = #{record.schoolId,jdbcType=VARCHAR},
      SCHOOL_NAME = #{record.schoolName,jdbcType=VARCHAR},
      ADDRESS = #{record.address,jdbcType=VARCHAR},
      TEL = #{record.tel,jdbcType=VARCHAR},
      POST_CODE = #{record.postCode,jdbcType=VARCHAR},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
      CITY_CODE = #{record.cityCode,jdbcType=VARCHAR},
      ASCRIPTION_GRID_CODE = #{record.ascriptionGridCode,jdbcType=VARCHAR},
      ASCRIPTION_GRID_NAME = #{record.ascriptionGridName,jdbcType=VARCHAR},
      STATE = #{record.state,jdbcType=CHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.WoSchoolCampus">
    update wo_school_campus
    <set>
      <if test="campusName != null">
        CAMPUS_NAME = #{campusName,jdbcType=VARCHAR},
      </if>
      <if test="schoolId != null">
        SCHOOL_ID = #{schoolId,jdbcType=VARCHAR},
      </if>
      <if test="schoolName != null">
        SCHOOL_NAME = #{schoolName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        TEL = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null">
        POST_CODE = #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgCode != null">
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="ascriptionGridCode != null">
        ASCRIPTION_GRID_CODE = #{ascriptionGridCode,jdbcType=VARCHAR},
      </if>
      <if test="ascriptionGridName != null">
        ASCRIPTION_GRID_NAME = #{ascriptionGridName,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.WoSchoolCampus">
    update wo_school_campus
    set CAMPUS_NAME = #{campusName,jdbcType=VARCHAR},
      SCHOOL_ID = #{schoolId,jdbcType=VARCHAR},
      SCHOOL_NAME = #{schoolName,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      TEL = #{tel,jdbcType=VARCHAR},
      POST_CODE = #{postCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      ASCRIPTION_GRID_CODE = #{ascriptionGridCode,jdbcType=VARCHAR},
      ASCRIPTION_GRID_NAME = #{ascriptionGridName,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=CHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultType="com.jsunicom.oms.po.WoSchoolCampus">
    SELECT 
        CAMPUS_ID as campusId,
        CAMPUS_NAME as campusName,
        SCHOOL_ID as schoolId,
        SCHOOL_NAME as schoolName,
        ADDR_ID as addrId,
        ADDRESS as address,
        TEL as tel,
        POST_CODE as postCode,
        CREATE_TIME as createTime,
        UPDATE_TIME as updateTime,
        ORG_CODE as orgCode,
        CITY_CODE as cityCode,
        ASCRIPTION_GRID_CODE as ascriptionGridCode,
        ASCRIPTION_GRID_NAME as ascriptionGridName,
        STATE as state,
        REMARK as remark
    FROM wo_school_campus
    WHERE STATE = '1'
    ORDER BY CAMPUS_ID
  </select>
</mapper>