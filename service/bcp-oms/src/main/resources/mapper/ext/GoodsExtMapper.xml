<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.ext.GoodsExtMapper">
    <!-- 新增 -->
    <insert id="insert" parameterType="com.jsunicom.oms.po.Goods" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO goods(
        code,
        product_codes,
        name,
        product_amount,
        bus_line,
        bus_url,
        pic_url1,
        pic_url2,
        state,
        create_by,
        create_time,
        update_by,
        <if test="goodsType != null and goodsType != ''">
            goods_type,
        </if>
        <if test="secondType != null and secondType != ''">
            second_type,
        </if>
        <if test="goodsArea != null and goodsArea != ''">
            goods_area,
        </if>
        update_time,
        is_hot,
        end_time,
        pre_state,
        effective_method,
        package_content,
        notice_handling,
        reminder
        ) VALUES (
        #{code},
        #{productCodes},
        #{name},
        #{productAmount},
        #{busLine},
        #{busUrl},
        #{picUrl1},
        #{picUrl2},
        'Y',
        #{createBy},
        #{createTime},
        #{updateBy},
        <if test="goodsType != null and goodsType != ''">
            #{goodsType},
        </if>
        <if test="secondType != null and secondType != ''">
            #{secondType},
        </if>
        <if test="goodsArea != null and goodsArea != ''">
            #{goodsArea},
        </if>
        #{updateTime},
        #{isHot},
        #{endTime},
        #{preState},
        #{effectiveMethod},
        #{packageContent},
        #{noticeHandling},
        #{reminder}
        )
    </insert>
</mapper>
