<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.salesman.AreaInfoDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO area_info( id,city_no,city_name,area_no,area_name,state,create_by,
                         create_time,update_by,update_time )
        VALUES ( #{id},#{cityNo},#{cityName},#{areaNo},#{areaName},#{state},#{createBy},
                 #{createTime},#{updateBy},#{updateTime})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO area_info( id,city_no,city_name,area_no,area_name,state,create_by,
                          create_time,update_by,update_time )
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.cityNo},#{item.cityName},#{item.areaNo},#{item.areaName},#{item.state},#{item.createBy},
              #{item.createTime},#{item.updateBy},#{item.updateTime} )
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO area_info( id,city_no,city_name,area_no,area_name,state,create_by,
                          create_time,update_by,update_time )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id},#{item.cityNo},#{item.cityName},#{item.areaNo},#{item.areaName},#{item.state},#{item.createBy},
              #{item.createTime},#{item.updateBy},#{item.updateTime} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE area_info
        <set>
            city_no=#{cityNo},
            city_name=#{cityName},
            area_no=#{areaNo},
            area_name=#{areaName},
            state=#{state},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE area_info
        <set>
            <if test="cityNo!= null">city_no=#{cityNo},</if>
            <if test="cityName!= null">city_name=#{cityName},</if>
            <if test="areaNo!= null">area_no=#{areaNo},</if>
            <if test="areaName!= null">area_name=#{areaName},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE area_info
            <set>
                city_no=#{item.cityNo},
                city_name=#{item.cityName},
                area_no=#{item.areaNo},
                area_name=#{item.areaName},
                state=#{item.state},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
            </set>
            WHERE id=#{item.id} 
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM area_info
        WHERE id=#{id} 
    </delete>

    <delete id="deleteBatch">
        DELETE FROM area_info
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM area_info
        WHERE id=#{id} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM area_info
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM area_info
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.salesman.AreaInfo">
        SELECT * FROM area_info
        WHERE id=#{id} 
    </select>

    <select id="find" resultType="com.jsunicom.oms.model.salesman.AreaInfo">
        SELECT id,city_no,city_name,area_no,area_name,state,create_by,create_time
               ,update_by,update_time
         FROM area_info
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="cityNo!= null">
               AND city_no = #{cityNo}
            </if>
            <if test="cityName!= null">
               AND city_name = #{cityName}
            </if>
            <if test="areaNo!= null">
               AND area_no = #{areaNo}
            </if>
            <if test="areaName!= null">
               AND area_name = #{areaName}
            </if>
            <if test="state!= null">
               AND state = #{state}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="findByCityName" resultType="com.jsunicom.oms.model.salesman.AreaInfo">
        SELECT * FROM area_info
        WHERE city_no = #{cityNo}
    </select>

    <select id="findAll" resultType="com.jsunicom.oms.model.salesman.AreaInfo">
        SELECT * FROM area_info
    </select>
</mapper>
