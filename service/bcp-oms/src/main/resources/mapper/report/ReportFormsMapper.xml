<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.report.ReportFormsDao">
    <select id="getYouthInnovationReportData" resultType="java.util.Map">
        SELECT
            COALESCE(org.org_name, '') AS orgName,
            COALESCE(wsc.CAMPUS_NAME, '') AS campusName,
            COALESCE(manager_partner.partner_name, '') AS managerName,
            COALESCE(ysib.SOCIETY_NAME, '') AS societyName,
            COALESCE(leader_partner.partner_name, '') AS partnerName,
            IFNULL(COUNT(DISTINCT CASE WHEN member_partner.partner_type = '2' AND member_partner.is_mer_admin = '1' THEN member_partner.id ELSE NULL END), 0) AS captainsNumber,
            IFNULL(COUNT(DISTINCT CASE WHEN member_partner.partner_type = '2' AND member_partner.is_mer_admin = '0' THEN member_partner.id ELSE NULL END), 0) AS playersNumber
        FROM
            wo_school_campus wsc
        LEFT JOIN
            org_info org ON wsc.ORG_CODE = org.org_code
        LEFT JOIN
            wo_school_campus_bind wscb_manager ON wsc.CAMPUS_ID = wscb_manager.CAMPUS_ID AND wscb_manager.TYPE = '1'
        LEFT JOIN
            partner manager_partner ON wscb_manager.BIND_ID = manager_partner.id AND manager_partner.partner_type = '0'
        LEFT JOIN
            wo_sc_youth_innovate_base ysib ON wsc.CAMPUS_ID = ysib.CAMPUS_ID
        LEFT JOIN
            partner leader_partner ON ysib.SOCIETY_ID = leader_partner.society_id AND leader_partner.partner_type = '2' AND leader_partner.is_mer_admin = '2'
        LEFT JOIN
            partner member_partner ON ysib.SOCIETY_ID = member_partner.society_id AND member_partner.partner_type = '2'
        WHERE
            1=1
            <if test="orgCode != null and orgCode != ''">
                AND wsc.ORG_CODE = #{orgCode}
            </if>
            <if test="campusName != null and campusName != ''">
                AND wsc.CAMPUS_NAME LIKE CONCAT('%', #{campusName}, '%')
            </if>
        GROUP BY
            org.org_name,
            wsc.CAMPUS_NAME,
            manager_partner.partner_name,
            ysib.SOCIETY_NAME,
            leader_partner.partner_name
    </select>

    <select id="getActivityReportData" resultType="java.util.Map">
        SELECT
            COALESCE(org.org_name, '') AS orgName,   <!-- 地市 -->
            COALESCE(wsc.CAMPUS_NAME, '') AS campusName,   <!-- 校区 -->
            IFNULL(COUNT(DISTINCT anbi.activities_notify_id), 0) AS notifyCount,  <!-- 发起通知数量 -->
            IFNULL(COUNT(DISTINCT abi.activities_id), 0) AS activityCount,  <!-- 执行活动数量 -->
            IFNULL(SUM(aur.develop_nums), 0) AS developSum  <!-- 发展量总和 -->
        FROM
            wo_school_campus wsc
        LEFT JOIN
            org_info org ON wsc.ORG_CODE = org.org_code  <!-- 关联机构信息获取地市名称 -->
        LEFT JOIN
            activities_campus_relation acr ON wsc.CAMPUS_ID = acr.campus_id  <!-- 关联活动通知校区关系 -->
        LEFT JOIN
            activities_notify_base_info anbi ON acr.activities_notify_id = anbi.activities_notify_id  <!-- 关联活动通知基本信息 -->
        LEFT JOIN
            activities_base_info abi ON wsc.CAMPUS_ID = abi.campus_id  <!-- 关联活动基本信息 -->
        LEFT JOIN
            activities_user_relation aur ON abi.activities_id = aur.activities_id  <!-- 关联合动人员关系获取发展量 -->
        WHERE
            1=1
            <if test="orgCode != null and orgCode != ''">
                AND wsc.ORG_CODE = #{orgCode}
            </if>
            <if test="campusName != null and campusName != ''">
                AND wsc.CAMPUS_NAME LIKE CONCAT('%', #{campusName}, '%')
            </if>
        GROUP BY
            org.org_name,
            wsc.CAMPUS_NAME
    </select>
</mapper> 