<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.SchoolChannelMapper">

    <select id="queryNoSchoolChannel" resultType="com.jsunicom.oms.po.SchoolChannel">
        SELECT
            t1.CHNL_ID AS chnlId,t1.CHNL_CODE AS chnlCode,t1.CHNL_NAME as chnlName,t1.CHNL_KIND_ID as chnlKindId, t1.CHNL_KIND_NAME  as chnlKindName
            ,wsc.CAMPUS_ID as campusId,wsc.CAMPUS_NAME as campusName,if(wsc.CAMPUS_ID is not null, 1, 0) isRelation
        FROM
            school_channel t1 LEFT join wo_school_campus_channel t2 ON t1.CHNL_ID = t2.CHANNEL_ID
            left join wo_school_campus wsc on t2.CAMPUS_ID = wsc.CAMPUS_ID
        WHERE 1=1
            <if test="chnlCode != null and chnlCode != ''">
                AND t1.CHNL_CODE LIKE concat('%', #{chnlCode}, '%')
            </if>
            <if test="chnlName != null and chnlName != ''">
                AND t1.CHNL_NAME LIKE concat('%', #{chnlName}, '%')
            </if>
            <if test="areaCode != null and areaCode != ''">
                AND t1.AREA_CODE = #{areaCode}
            </if>
        order by wsc.CAMPUS_ID desc
    </select>

    <select id="querySchoolChannel" resultType="com.jsunicom.oms.po.SchoolChannel">
        SELECT
        t1.CHNL_ID AS chnlId,t1.CHNL_CODE AS chnlCode,t1.CHNL_NAME as chnlName,t1.CHNL_KIND_ID as chnlKindId, t1.CHNL_KIND_NAME  as chnlKindName
        FROM
        school_channel t1 LEFT join wo_school_campus_channel t2 ON t1.CHNL_ID = t2.CHANNEL_ID AND t2.CAMPUS_ID = #{campusId}
        WHERE t2.CHANNEL_ID is not null
        <if test="chnlCode != null and chnlCode != ''">
            AND t1.CHNL_CODE LIKE concat('%', #{chnlCode}, '%')
        </if>
        <if test="chnlName != null and chnlName != ''">
            AND t1.CHNL_NAME LIKE concat('%', #{chnlName}, '%')
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND t1.AREA_CODE = #{areaCode}
        </if>
    </select>
</mapper>
