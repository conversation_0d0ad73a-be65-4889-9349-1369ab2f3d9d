<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.WoScYouthInnovateBaseMapper">
    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.WoScYouthInnovateBase">
        <id column="SOCIETY_ID" jdbcType="BIGINT" property="societyId"/>
        <result column="SOCIETY_NAME" jdbcType="VARCHAR" property="societyName"/>
        <result column="CAMPUS_ID" jdbcType="BIGINT" property="campusId"/>
        <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode"/>
        <result column="SOCIETY_LOGO_URL" jdbcType="VARCHAR" property="societyLogoUrl"/>
        <result column="SOCIETY_INTRODUCTION" jdbcType="VARCHAR" property="societyIntroduction"/>
        <result column="SOCIETY_REMARK" jdbcType="VARCHAR" property="societyRemark"/>
        <result column="STATE" jdbcType="CHAR" property="state"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1"/>
        <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2"/>
        <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3"/>
    </resultMap>
    <resultMap id="InnovateListMap" type="com.jsunicom.oms.po.WoInnovateListInfo">
        <id column="SOCIETY_ID" jdbcType="BIGINT" property="societyId"/>
        <result column="SOCIETY_NAME" jdbcType="VARCHAR" property="societyName"/>
        <result column="CAMPUS_ID" jdbcType="BIGINT" property="campusId"/>
        <result column="SOCIETY_LOGO_URL" jdbcType="VARCHAR" property="societyLogoUrl"/>
        <result column="SOCIETY_INTRODUCTION" jdbcType="VARCHAR" property="societyIntroduction"/>
        <result column="SOCIETY_REMARK" jdbcType="VARCHAR" property="societyRemark"/>
        <result column="PARTNER_CERT_NO" jdbcType="VARCHAR" property="partnerCertNo"/>
        <result column="PARTNER_NAME" jdbcType="VARCHAR" property="partnerName"/>
        <result column="MBL_NBR" jdbcType="VARCHAR" property="mblNbr"/>
        <result column="ID" jdbcType="BIGINT" property="partnerId"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        SOCIETY_ID
        , SOCIETY_NAME, CAMPUS_ID, ORG_CODE,SOCIETY_LOGO_URL,
    SOCIETY_INTRODUCTION, SOCIETY_REMARK, STATE, CREATED_BY, CREATED_TIME,
    UPDATED_BY, UPDATED_TIME, RESERVE1, RESERVE2, RESERVE3
    </sql>
    <select id="selectByExample" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBaseExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from WO_SC_YOUTH_INNOVATE_BASE
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from WO_SC_YOUTH_INNOVATE_BASE
        where SOCIETY_ID = #{societyId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from WO_SC_YOUTH_INNOVATE_BASE
        where SOCIETY_ID = #{societyId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBaseExample">
        delete from WO_SC_YOUTH_INNOVATE_BASE
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBase" useGeneratedKeys="true"
            keyProperty="societyId">
        insert into WO_SC_YOUTH_INNOVATE_BASE (SOCIETY_ID, SOCIETY_NAME, CAMPUS_ID,
                                               ORG_CODE, SOCIETY_LOGO_URL, SOCIETY_INTRODUCTION, SOCIETY_REMARK,
                                               STATE, CREATED_BY, CREATED_TIME, UPDATED_BY, UPDATED_TIME, RESERVE1,
                                               RESERVE2,
                                               RESERVE3)
        values (#{societyId,jdbcType=BIGINT}, #{societyName,jdbcType=VARCHAR}, #{campusId,jdbcType=BIGINT},
                #{orgCode,jdbcType=VARCHAR}, #{societyLogoUrl,jdbcType=VARCHAR},
                #{societyIntroduction,jdbcType=VARCHAR}, #{societyRemark,jdbcType=VARCHAR}, #{state,jdbcType=CHAR},
                #{createdBy,jdbcType=VARCHAR},
                #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP},
                #{reserve1,jdbcType=VARCHAR}, #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBase">
        insert into WO_SC_YOUTH_INNOVATE_BASE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="societyId != null">
                SOCIETY_ID,
            </if>
            <if test="societyName != null">
                SOCIETY_NAME,
            </if>
            <if test="campusId != null">
                CAMPUS_ID,
            </if>
            <if test="orgCode != null">
                ORG_CODE,
            </if>
            <if test="societyLogoUrl != null">
                SOCIETY_LOGO_URL,
            </if>
            <if test="societyIntroduction != null">
                SOCIETY_INTRODUCTION,
            </if>
            <if test="societyRemark != null">
                SOCIETY_REMARK,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdTime != null">
                CREATED_TIME,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedTime != null">
                UPDATED_TIME,
            </if>
            <if test="reserve1 != null">
                RESERVE1,
            </if>
            <if test="reserve2 != null">
                RESERVE2,
            </if>
            <if test="reserve3 != null">
                RESERVE3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="societyId != null">
                #{societyId,jdbcType=BIGINT},
            </if>
            <if test="societyName != null">
                #{societyName,jdbcType=VARCHAR},
            </if>
            <if test="campusId != null">
                #{campusId,jdbcType=BIGINT},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="societyLogoUrl != null">
                #{societyLogoUrl,jdbcType=VARCHAR},
            </if>
            <if test="societyIntroduction != null">
                #{societyIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="societyRemark != null">
                #{societyRemark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=CHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reserve1 != null">
                #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null">
                #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="reserve3 != null">
                #{reserve3,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBaseExample"
            resultType="java.lang.Long">
        select count(*) from WO_SC_YOUTH_INNOVATE_BASE
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update WO_SC_YOUTH_INNOVATE_BASE
        <set>
            <if test="record.societyId != null">
                SOCIETY_ID = #{record.societyId,jdbcType=BIGINT},
            </if>
            <if test="record.societyName != null">
                SOCIETY_NAME = #{record.societyName,jdbcType=VARCHAR},
            </if>
            <if test="record.campusId != null">
                CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
            </if>
            <if test="record.orgCode != null">
                ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
            </if>
            <if test="record.societyLogoUrl != null">
                SOCIETY_LOGO_URL = #{record.societyLogoUrl,jdbcType=VARCHAR},
            </if>
            <if test="record.societyIntroduction != null">
                SOCIETY_INTRODUCTION = #{record.societyIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="record.societyRemark != null">
                SOCIETY_REMARK = #{record.societyRemark,jdbcType=VARCHAR},
            </if>
            <if test="record.state != null">
                STATE = #{record.state,jdbcType=CHAR},
            </if>
            <if test="record.createdBy != null">
                CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createdTime != null">
                CREATED_TIME = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updatedTime != null">
                UPDATED_TIME = #{record.updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.reserve1 != null">
                RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
            </if>
            <if test="record.reserve2 != null">
                RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
            </if>
            <if test="record.reserve3 != null">
                RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update WO_SC_YOUTH_INNOVATE_BASE
        set SOCIETY_ID = #{record.societyId,jdbcType=BIGINT},
        SOCIETY_NAME = #{record.societyName,jdbcType=VARCHAR},
        CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
        ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
        SOCIETY_LOGO_URL = #{record.societyLogoUrl,jdbcType=VARCHAR},
        SOCIETY_INTRODUCTION = #{record.societyIntroduction,jdbcType=VARCHAR},
        SOCIETY_REMARK = #{record.societyRemark,jdbcType=VARCHAR},
        STATE = #{record.state,jdbcType=CHAR},
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
        CREATED_TIME = #{record.createdTime,jdbcType=TIMESTAMP},
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
        UPDATED_TIME = #{record.updatedTime,jdbcType=TIMESTAMP},
        RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
        RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
        RESERVE3 = #{record.reserve3,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBase">
        update WO_SC_YOUTH_INNOVATE_BASE
        <set>
            <if test="societyName != null">
                SOCIETY_NAME = #{societyName,jdbcType=VARCHAR},
            </if>
            <if test="campusId != null">
                CAMPUS_ID = #{campusId,jdbcType=BIGINT},
            </if>
            <if test="orgCode != null">
                ORG_CODE = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="societyLogoUrl != null">
                SOCIETY_LOGO_URL = #{societyLogoUrl,jdbcType=VARCHAR},
            </if>
            <if test="societyIntroduction != null">
                SOCIETY_INTRODUCTION = #{societyIntroduction,jdbcType=VARCHAR},
            </if>
            <if test="societyRemark != null">
                SOCIETY_REMARK = #{societyRemark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=CHAR},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reserve1 != null">
                RESERVE1 = #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null">
                RESERVE2 = #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="reserve3 != null">
                RESERVE3 = #{reserve3,jdbcType=VARCHAR},
            </if>
        </set>
        where SOCIETY_ID = #{societyId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.WoScYouthInnovateBase">
        update WO_SC_YOUTH_INNOVATE_BASE
        set SOCIETY_NAME         = #{societyName,jdbcType=VARCHAR},
            CAMPUS_ID            = #{campusId,jdbcType=BIGINT},
            ORG_CODE             = #{orgCode,jdbcType=VARCHAR},
            SOCIETY_LOGO_URL     = #{societyLogoUrl,jdbcType=VARCHAR},
            SOCIETY_INTRODUCTION = #{societyIntroduction,jdbcType=VARCHAR},
            SOCIETY_REMARK       = #{societyRemark,jdbcType=VARCHAR},
            STATE                = #{state,jdbcType=CHAR},
            CREATED_BY           = #{createdBy,jdbcType=VARCHAR},
            CREATED_TIME         = #{createdTime,jdbcType=TIMESTAMP},
            UPDATED_BY           = #{updatedBy,jdbcType=VARCHAR},
            UPDATED_TIME         = #{updatedTime,jdbcType=TIMESTAMP},
            RESERVE1             = #{reserve1,jdbcType=VARCHAR},
            RESERVE2             = #{reserve2,jdbcType=VARCHAR},
            RESERVE3             = #{reserve3,jdbcType=VARCHAR}
        where SOCIETY_ID = #{societyId,jdbcType=BIGINT}
    </update>

    <select id="selectInnovateList" parameterType="java.lang.Long" resultMap="InnovateListMap">
        SELECT C.*, D.CAMPUS_NAME
        FROM (SELECT A.CAMPUS_ID,
                     A.SOCIETY_ID,
                     A.SOCIETY_NAME,
                     A.SOCIETY_LOGO_URL,
                     A.SOCIETY_INTRODUCTION,
                     A.SOCIETY_REMARK,
                     CONCAT(
                         LEFT(B.PARTNER_CERT_NO, 6), REPEAT('*', 8),
                         RIGHT(B.PARTNER_CERT_NO, (CHAR_LENGTH(B.PARTNER_CERT_NO) - 14))
                                                ) AS PARTNER_CERT_NO
                      ,
                     B.PARTNER_NAME,
                     B.MBL_NBR,
                     B.ID
              FROM WO_SC_YOUTH_INNOVATE_BASE A
                       LEFT JOIN PARTNER B ON
                          A.SOCIETY_ID = B.SOCIETY_ID AND B.IS_MER_ADMIN = "2"
                      AND B.STATE = "1" AND B.PARTNER_TYPE = "2"
              WHERE A.CAMPUS_ID = #{campusId,jdbcType=BIGINT}
                AND A.STATE = "1") C
                 LEFT JOIN WO_SCHOOL_CAMPUS D ON C.CAMPUS_ID = D.CAMPUS_ID
    </select>

    <select id="getExportApplicationList" resultType="map" parameterType="map">
        SELECT
        ifnull(base.`SOCIETY_NAME`,'') society_name,
        ifnull(base.`SOCIETY_ID`,'') society_id,
        ifnull(base.`STATE`,'') state,
        ifnull(DATE_FORMAT(base.`CREATED_TIME`, '%Y-%m-%d %H:%i:%s'),'') AS create_time,
        ifnull(base.`ORG_CODE`,'') org_code,
        ifnull(c.`CAMPUS_NAME`,'') campus_name,
        ifnull(c.`CAMPUS_ID`,'') campus_id,
        ifnull(p.`mbl_nbr`,'') campus_nbr,
        ifnull(p.`partner_name`,'') partner_name
        FROM
        wo_sc_youth_innovate_base base
        LEFT JOIN WO_SCHOOL_CAMPUS c
        ON base.CAMPUS_ID = c.CAMPUS_ID
        LEFT JOIN WO_SCHOOL_CAMPUS_bind bb
        ON c.CAMPUS_ID = bb.CAMPUS_ID
        AND bb.type = '1'
        AND bb.`STATE` = '1'
        LEFT JOIN partner p
        ON bb.bind_id = p.id AND p.`state` = '1'
        LEFT JOIN partner pp
        ON base.SOCIETY_ID = pp.society_id AND pp.`state` = '1' AND pp.`is_mer_admin` = '2'
        WHERE 1=1
        <if test="orgCode != null and orgCode != ''">
            AND base.`ORG_CODE` = #{orgCode}
        </if>
        <if test="campusId != null and campusId != ''">
            AND c.`CAMPUS_ID` LIKE concat('%', #{campusId}, '%')
        </if>
        <if test="campusName != null and campusName != ''">
            AND c.`CAMPUS_NAME` LIKE concat('%', #{campusName}, '%')
        </if>
        <if test="mblNbr != null and mblNbr != ''">
            AND p.`mbl_nbr` LIKE concat('%', #{mblNbr}, '%')
        </if>
    </select>
    <select id="getWoscyouthLists" resultType="map" parameterType="map">
        SELECT
        ifnull(base.`SOCIETY_NAME`,'') society_name,
        ifnull(base.`SOCIETY_ID`,'') society_id,
        ifnull(base.`STATE`,'') state,
        ifnull(DATE_FORMAT(base.`CREATED_TIME`, '%Y-%m-%d %H:%i:%s'),'') AS create_time,
        ifnull(base.`ORG_CODE`,'') org_code,
        ifnull(c.`CAMPUS_NAME`,'') campus_name,
        ifnull(c.`CAMPUS_ID`,'') campus_id,
        ifnull(p.`mbl_nbr`,'') campus_nbr,
        ifnull(pp.`mbl_nbr`,'') mbl_nbr,
        ifnull(p.`partner_name`,'') partner_name,
        ifnull(pp.`partner_name`,'') mbl_name,
        ifnull(pp.`is_mer_admin`,'') is_mer_admin,
        ifnull(m.`merchant_name`,'') merchant_name,
        ifnull(wco.`COLLEGE_NAME`,'') college_name,
        ifnull(pp.`state`,'') state
        FROM
        wo_sc_youth_innovate_base base
        LEFT JOIN WO_SCHOOL_CAMPUS c
        ON base.CAMPUS_ID = c.CAMPUS_ID
        LEFT JOIN WO_SCHOOL_CAMPUS_bind bb
        ON c.CAMPUS_ID = bb.CAMPUS_ID
        AND bb.type = '1'
        AND bb.`STATE` = '1'
        LEFT JOIN partner p
        ON bb.bind_id = p.id AND p.`state` = '1'
        LEFT JOIN partner pp
        ON base.SOCIETY_ID = pp.society_id AND pp.`state` = '1'
        LEFT JOIN merchant m
        ON m.`id` = pp.`merchant_id`
        LEFT JOIN wo_school_campus_college wco
        ON wco.`COLLEGE_ID` = pp.`college_id`
        WHERE 1=1
        <if test="orgCode != null and orgCode != ''">
            AND base.`ORG_CODE` = #{orgCode}
        </if>
        <if test="campusId != null and campusId != ''">
            AND c.`CAMPUS_ID` LIKE concat('%', #{campusId}, '%')
        </if>
        <if test="campusName != null and campusName != ''">
            AND c.`CAMPUS_NAME` LIKE concat('%', #{campusName}, '%')
        </if>
        <if test="mblNbr != null and mblNbr != ''">
            AND p.`mbl_nbr` LIKE concat('%', #{mblNbr}, '%')
        </if>
    </select>
    <select id="queryMemberIListById" resultType="map" parameterType="map">
        SELECT
        p.id partner_id,
        ifnull(p.`is_mer_admin`,'') is_mer_admin,
          CASE
          WHEN is_mer_admin = 0
          THEN '团队成员'
          WHEN is_mer_admin = 1
          THEN '团队长'
          WHEN is_mer_admin = 2
          THEN '社长'
          ELSE '未知' -- 如果有可能出现其他值
          END AS is_mer_admin_name,
        ifnull(p.`partner_name`,'') partner_name,
        ifnull(p.`mbl_nbr`,'') mbl_nbr,
        ifnull(wco.`COLLEGE_NAME`,'') college_name,
        ifnull(DATE_FORMAT(p.`create_time`, '%Y-%m-%d %H:%i:%s'),'') AS create_time,
        ifnull(DATE_FORMAT(p.`audit_time`, '%Y-%m-%d %H:%i:%s'),'') AS audit_time,
        ifnull(m.`merchant_type`,'') merchant_type,
          CASE
          WHEN merchant_type = 1
          THEN '否'
          WHEN merchant_type = 2
          THEN '是'
          ELSE '未知' -- 如果有可能出现其他值
          END AS merchant_desc,
        ifnull(p.`pic3`,'') pic3,
          CASE
          WHEN pic3 IS NOT NULL
          THEN '是'
          ELSE '否'
          END AS pic_desc
        FROM
        partner p
          LEFT JOIN wo_school_campus_college wco
          ON wco.`COLLEGE_ID` = p.`college_id`
          LEFT JOIN merchant m
          ON m.`id` = p.`merchant_id`
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND p.`society_id` = #{societyId}
            AND p.`is_mer_admin` ='2'
        </if>
        <if test="merchantId != null and merchantId != ''">
            AND p.`merchant_id` = #{merchantId}
            AND p.`is_mer_admin` !='2'
        </if>

    </select>
</mapper>
