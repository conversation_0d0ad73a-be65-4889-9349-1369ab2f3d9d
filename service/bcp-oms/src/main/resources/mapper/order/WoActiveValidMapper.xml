<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.WoActiveValidMapper">

	<select id="getActiveOrders" parameterType="hashmap" resultType="hashmap">
		SELECT distinct
		GOODS.ORDER_ID,
		GOODS.GOODS_ORDER_ID,
		GOODS.GOODS_ID,
		CUST.CUST_NAME,
		CUST.PSPT_ID,
		CUST.END_DATE,
		CUST.PSPT_ADDRESSS,
		ORDMAIN.EPARCHY_CODE,
		ORDMAIN.DELIVERY_TYPE,
		ORDMAIN.ORDER_STATE,
		ORDMAIN.CREATE_DATE IN_TIME,
		ORDMAIN.STAFF_ID,
		ORDMAIN.CHANNEL_ID,
		ADDRESS.CUST_NAME LINK_PERSON,
		ADDRESS.PHONE,
		(SELECT PARAM_VALUE FROM td_m_sys_dict WHERE param_type = 'banWLAfterTime' AND param_key = 'banWLAfterTime') AS AFTERTIME
		FROM tf_order_item ITEM
		JOIN TF_ORD_GOODS_MAIN GOODS ON (item.ORDER_ID	= GOODS.ORDER_ID)
		JOIN TF_ORD_CUSTINFO CUST ON (item.ORDER_ID	= CUST.ORDER_ID)
		JOIN TF_ORD_MAIN ORDMAIN ON (GOODS.ORDER_ID = ORDMAIN.ORDER_ID)
		JOIN TF_ORD_ADDRESS ADDRESS ON (ORDMAIN.ORDER_ID = ADDRESS.ORDER_ID)
		WHERE 1=1
		<choose>
			<when test="orderId!=null and orderId!=''">
				AND GOODS.ORDER_ID = #{orderId}
			</when>
			<otherwise>
				<if test="receivePhone != null and receivePhone !='' ">
					AND  GOODS.ORDER_ID IN
					(SELECT ADDR.ORDER_ID
					FROM TF_ORD_ADDRESS ADDR
					WHERE ADDR.PHONE = #{receivePhone})
				</if>
				<if test="psptId != null and psptId !='' ">
					AND UPPER(SUBSTR(CUST.PSPT_ID,-6,6)) = UPPER(#{psptId})
				</if>
				AND  item.ATTR_TYPE='1' and item.ATTR_CODE='0014'
				AND  SUBSTR(ITEM.ATTR_VALUE, -8, 7) = #{iccid}
				AND ORDMAIN.ORDER_STATE NOT IN ('0060','0090')
			</otherwise>
		</choose>

	</select>

	<select id="getActiveOrders2" parameterType="hashmap" resultType="hashmap">
		select prodMain.product_order_id,
		       goods.order_id,
		       goods.goods_order_id,
		       goods.goods_state,
		       cust.cust_name,
		       cust.pspt_id,
		       ordMain.eparchy_code
		  from tf_ord_prod_main prodMain
		  left join tf_ord_goods_main goods
		    on (prodMain.goods_order_id = goods.goods_order_id)
		  left join tf_ord_custinfo cust
		    on (prodmain.product_order_id = cust.product_order_id)
		  left join tf_ord_main ordMain
		    on (goods.order_id = ordMain.order_id)
		  left join tf_ord_production_main productionMain
		    on (goods.goods_order_id = productionMain.goods_id)
		 where goods.order_id in
		       (select addr.order_id
		          from tf_ord_address addr
		         where addr.phone = #{receivePhone})
		   and substr(productionMain.simcard_no, 0, length(productionMain.simcard_no) - 1) like
		       concat('%', #{iccid})
	</select>

	<select id="getActiveInfo" parameterType="hashmap" resultType="hashmap">
		SELECT ACT.CUST_NAME, ACT.PSPT_ID, ACT.STATE, ACT.CREATE_DATE
			FROM TF_ORD_SVCNUM_ACTIVE ACT, tf_order_item ITEM
		 WHERE ITEM.ORDER_ID = #{orderId}
			 AND ACT.ORDER_ID = ITEM.ORDER_ID
			 AND ITEM.ATTR_TYPE='1'
			 AND ITEM.ATTR_CODE = '0006'
	 		 AND ACT.SERIAL_NUMBER = ITEM.ATTR_VALUE
		 ORDER BY ACT.CREATE_DATE DESC
	</select>

	<select id="getPackageInfo" parameterType="hashmap" resultType="hashmap">
		SELECT * FROM tf_order_item WHERE ORDER_ID=#{orderId} AND ATTR_TYPE='1' AND ATTR_CODE IN ('0006','0002')
	</select>

	<select id="queryExistOperators" parameterType="hashmap" resultType="hashmap">

		SELECT * FROM TD_ORD_OPERATOR
		WHERE 1=1
		<if test="operatorId != null and operatorId != ''">
			and OPERATOR_ID = #{operatorId}
		</if>
		<if test="operId != null and operId != ''">
			and OPER_ID = #{operId}
		</if>
		<choose>
			<when test="eparchyCode != null">
				AND EPARCHY_CODE = #{eparchyCode}
			</when>
			<otherwise>
				AND EPARCHY_CODE IS NULL
			</otherwise>
		</choose>
		<if test="sourceSystemId != null and sourceSystemId != ''">
			AND SOURCE_SYSTEM_ID = #{sourceSystemId}
		</if>

	</select>

	<select id="insertSkipTask" parameterType="hashmap" >

		insert into TF_ORD_SKIPTASK a
		(a.ID,a.ORDER_ID,a.TASK_KEY,a.EPARCHY_CODE,a.SERIAL_NUMBER,a.DEAL_TAG,a.remark,
		a.UPDATE_TIME)
		values
		(getUnifyID('TF_ORD_SKIPTASK'),#{order_id,jdbcType=VARCHAR},
		#{task_key,jdbcType=VARCHAR},#{eparchy_code,jdbcType=VARCHAR},#{serial_number,jdbcType=VARCHAR},'0',#{remark,jdbcType=VARCHAR},sysdate)
	</select>

</mapper>
