<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.OrderQueryMapper">

    <sql id="orderListQueryPart">
        SELECT ORD.ORDER_ID AS orderId
        FROM TF_ORD_MAIN ORD
        JOIN TF_ORD_GOODS_MAIN GOODS ON (ORD.ORDER_ID=GOODS.ORDER_ID)
        LEFT JOIN TF_ORD_ADDRESS ADDR ON(ORD.ORDER_ID=ADDR.ORDER_ID)
        -- LEFT JOIN TF_ORD_PAY PAY ON (ORD.ORDER_ID=PAY.ORDER_ID)
        <where>
            <if test="orderId != null and orderId != ''">
                AND (ORD.ORDER_ID = #{orderId} OR ORD.EXT_ORDER_ID=#{orderId})
            </if>
            <if test="orderStateList != null">
                AND ORD.ORDER_STATE IN
                <foreach item="eachOrderState" index="index" collection="orderStateList"
                         open="(" close=")" separator=",">
                    #{eachOrderState}
                </foreach>
            </if>

            <if test="inModeCodeList != null">
                AND ORD.IN_MODE_CODE IN
                <foreach item="inModeCode" index="index" collection="inModeCodeList"
                         open="(" close=")" separator=",">
                    #{inModeCode}
                </foreach>
            </if>
            <if test="cityCodeList != null">
                AND ORD.EPARCHY_CODE IN
                <foreach item="cityCode" index="index" collection="cityCodeList"
                         open="(" close=")" separator=",">
                    #{cityCode}
                </foreach>
            </if>
            <if test="orderAmount != null and orderAmount != ''">
                AND ORD.ORDER_AMOUNT = #{orderAmount}
            </if>

            <if test="staffId != null and staffId != ''">
                AND ORD.STAFF_ID = #{staffId}
            </if>

            <if test="custName != null and custName != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN ORDGOODS,TF_ORD_CUSTINFO ORDCUST
                WHERE ORD.ORDER_ID=ORDGOODS.ORDER_ID
                AND ORD.ORDER_ID = ORDCUST.ORDER_ID
                AND ORDCUST.CUST_NAME LIKE CONCAT('%',CONCAT(#{custName},'%'))
                )
            </if>
            <if test="custPsptNo != null and custPsptNo != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN ORDGOODS,TF_ORD_CUSTINFO ORDCUST
                WHERE ORD.ORDER_ID=ORDGOODS.ORDER_ID
                AND ORD.ORDER_ID = ORDCUST.ORDER_ID
                AND ORDCUST.PSPT_ID=#{custPsptNo}
                )
            </if>
            <if test="serialNumber != null and serialNumber != ''">
            AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN GOODSMAIN
            WHERE GOODSMAIN.ORDER_ID = ORD.ORDER_ID
            AND GOODSMAIN.MAIN_NUMBER=#{serialNumber}
            )
            </if>
            <if test="dealStaffId != null and dealStaffId != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_LINE OL
                WHERE ORD.ORDER_ID=OL.ORDER_ID
                AND (OL.DEAL_STAFF_ID=#{dealStaffId} OR OL.DEAL_STAFF_NAME LIKE CONCAT('%',#{dealStaffId},'%'))
                )
            </if>

            <if test="payResultList != null">
                AND ORD.PAY_RESULT IN
                <foreach item="eachPayState" index="index" collection="payResultList"
                         open="(" close=")" separator=",">
                    #{eachPayState}
                </foreach>
            </if>

            <if test="orderCreateTimeStart != null">
                AND ORD.create_date <![CDATA[ >= ]]> #{orderCreateTimeStart}
            </if>
            <if test="orderCreateTimeEnd != null">
                AND ORD.create_date <![CDATA[ <= ]]> #{orderCreateTimeEnd}
            </if>

            <if test="busiTypeList != null">
                AND EXISTS (SELECT 1
                FROM TF_ORD_GOODS_MAIN GOODSMAIN
                WHERE GOODSMAIN.ORDER_ID = ORD.ORDER_ID
                <if test="busiTypeList != null">
                    AND GOODSMAIN.BUSI_TYPE IN
                    <foreach item="eachBusiType" index="index" collection="busiTypeList"
                             open="(" close=")" separator=",">
                        #{eachBusiType}
                    </foreach>
                </if>
                )
            </if>
        </where>
    </sql>

    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.vo.OrderVo">
        <result property="orderId" column="ORDER_ID"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="orderState" column="ORDER_STATE"/>
        <result property="realAmount" column="REAL_AMOUNT"/>
        <result property="orderAmount" column="ORDER_AMOUNT"/>
        <result property="payResult" column="PAY_RESULT"/>
        <result property="cityCode" column="EPARCHY_CODE"/>
        <result property="dealStaffId" column="DEAL_STAFF_ID"/>
        <result property="dealStaffName" column="DEAL_STAFF_NAME"/>
        <result property="dealStaffPhone" column="DEAL_STAFF_PHONE"/>
        <result property="channelId" column="CHANNEL_ID"/>
        <result property="busiType" column="BUSI_TYPE"/>
        <result property="goodsName" column="GOODS_NAME"/>
        <result property="mainNumber" column="MAIN_NUMBER"/>
        <result property="custName" column="CUST_NAME"/>
        <result property="address" column="ADDRESS"/>
        <result property="phone" column="PHONE"/>
        <result property="orderNodeState" column="ORDER_NODE_STATE"/>
        <result property="orderNodeCode" column="ORDER_NODE_CODE"/>
        <result property="orderNodeName" column="ORDER_NODE_NAME"/>
        <result property="staffId" column="STAFF_ID"/>
        <result property="numStatus" column="NUM_STATUS"/>
        <result property="developerName" column="developerName"/>
        <result property="receiveProvinceCode" column="PROVINCE_CODE"/>
        <result property="receiveCityCode" column="CITY_CODE"/>
        <result property="receiveCountyCode" column="COUNTY_CODE"/>
        <result property="qq" column="QQ"/>
        <!--<result property="iccid" column="ATTR_CODE"/>-->
    </resultMap>

    <select id="queryOrderList" parameterType="com.jsunicom.oms.po.order.vo.OrderVo" resultMap="BaseResultMap">
        SELECT
        ORD.`ORDER_ID`,
        ORD.`ORDER_STATE`,
        ORD.`CREATE_DATE`,
        ORD.`REAL_AMOUNT`,
        ORD.`ORDER_AMOUNT`,
        ORD.`PAY_RESULT`,
        ORD.`EPARCHY_CODE`,
        ORD.STAFF_ID,
        OL.`DEAL_STAFF_ID`,
        OL.`DEAL_STAFF_NAME`,
        OL.`DEAL_STAFF_PHONE`,
        OL.`CHANNEL_ID`,
        GOOD.`MAIN_NUMBER`,
        OL.`ORDER_NODE_CODE`,
        OL.`ORDER_NODE_NAME`,
        OL.`ORDER_NODE_STATE`,
        GOOD.`BUSI_TYPE`,
        GOOD.`GOODS_NAME`,
        cust.`CUST_NAME`,
        ADDR.`PHONE`,
        ADDR.`PROVINCE_CODE`,
        ADDR.`CITY_CODE`,
        ADDR.`COUNTY_CODE`,
        ADDR.`ADDRESS`,
        cust.`IS_BENWANG` as NUM_STATUS,
        item.`ATTR_VALUE` developerName,
        item1.`ATTR_VALUE` QQ
        FROM tf_ord_main ORD
        LEFT JOIN tf_ord_address ADDR ON ORD.`ORDER_ID` = ADDR.`ORDER_ID`
        LEFT JOIN tf_ord_goods_main GOOD ON ORD.`ORDER_ID` = GOOD.`ORDER_ID`
        LEFT JOIN tf_ord_line OL ON ORD.`ORDER_ID` = OL.`order_id`
        LEFT JOIN tf_ord_custinfo cust ON ORD.`ORDER_ID` = cust.`ORDER_ID`
        LEFT JOIN tf_order_item item ON ORD.`ORDER_ID` = item.`ORDER_ID` AND item.ATTR_CODE = 'developerName'
        LEFT JOIN tf_order_item item1 ON ORD.`ORDER_ID` = item1.`ORDER_ID` AND item1.ATTR_CODE = 'custQQ'
        <where>
            <if test="orderId != null and orderId != ''">
                AND ORD.ORDER_ID = #{orderId}
            </if>
            <if test="orderStateList != null">
                AND ORD.ORDER_STATE IN
                <foreach item="eachOrderState" index="index" collection="orderStateList"
                         open="(" close=")" separator=",">
                    #{eachOrderState}
                </foreach>
            </if>

            <if test="inModeCodeList != null">
                AND ORD.IN_MODE_CODE IN
                <foreach item="inModeCode" index="index" collection="inModeCodeList"
                         open="(" close=")" separator=",">
                    #{inModeCode}
                </foreach>
            </if>
            <if test="cityCodeList != null">
                AND ORD.EPARCHY_CODE IN
                <foreach item="cityCode" index="index" collection="cityCodeList"
                         open="(" close=")" separator=",">
                    #{cityCode}
                </foreach>
            </if>
            <if test="orderAmount != null and orderAmount != ''">
                AND ORD.ORDER_AMOUNT = #{orderAmount}
            </if>

            <if test="queryType != null and queryType != ''">
                AND ORD.ORDER_STATE NOT IN ('0060','0070','0090')
            </if>

            <if test="staffId != null and staffId != ''">
                AND ORD.STAFF_ID = #{staffId}
            </if>

            <if test="custName != null and custName != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_CUSTINFO ORDCUST
                WHERE ORD.ORDER_ID = ORDCUST.ORDER_ID
                AND ORDCUST.CUST_NAME LIKE CONCAT('%',CONCAT(#{custName},'%'))
                )
            </if>
            <if test="custPsptNo != null and custPsptNo != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_CUSTINFO ORDCUST
                WHERE ORD.ORDER_ID = ORDCUST.ORDER_ID
                AND ORDCUST.PSPT_ID=#{custPsptNo}
                )
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN GOODSMAIN
                WHERE GOODSMAIN.ORDER_ID = ORD.ORDER_ID
                AND GOODSMAIN.MAIN_NUMBER=#{serialNumber}
                )
            </if>
            <if test="dealStaffId != null and dealStaffId != ''">
                AND OL.DEAL_STAFF_ID=#{dealStaffId} OR OL.DEAL_STAFF_NAME LIKE CONCAT('%',#{dealStaffId},'%')
            </if>

            <if test="payResultList != null">
                AND ORD.PAY_RESULT IN
                <foreach item="eachPayState" index="index" collection="payResultList"
                         open="(" close=")" separator=",">
                    #{eachPayState}
                </foreach>
            </if>

            <if test="orderCreateTimeStart != null">
                AND ORD.create_date <![CDATA[ >= ]]> #{orderCreateTimeStart}
            </if>
            <if test="orderCreateTimeEnd != null">
                AND ORD.create_date <![CDATA[ <= ]]> #{orderCreateTimeEnd}
            </if>

            <if test="busiTypeList != null">
                AND EXISTS (SELECT 1
                FROM TF_ORD_GOODS_MAIN GOODSMAIN
                WHERE GOODSMAIN.ORDER_ID = ORD.ORDER_ID
                <if test="busiTypeList != null">
                    AND GOODSMAIN.BUSI_TYPE IN
                    <foreach item="eachBusiType" index="index" collection="busiTypeList"
                             open="(" close=")" separator=",">
                        #{eachBusiType}
                    </foreach>
                </if>
                )
            </if>
        </where>
        ORDER BY ORD.CREATE_DATE DESC
    </select>

    <resultMap id="OrderDetailMap" type="com.jsunicom.oms.po.order.vo.OrderDetailVo">
        <result column="ORDER_ID" property="orderId"/>
        <result column="ORDER_TIME" property="orderTime"/>
        <result column="ORDER_AMOUNT" property="orderAmount"/>
        <result column="EPARCHY_CODE" property="cityCode"/>
        <result column="CHANNEL_ID" property="channelId"/>
        <result column="PAY_RESULT" property="payResult"/>
        <result column="ORDER_NODE_NAME" property="orderNodeName"/>
        <result column="ORDER_NODE_CODE" property="orderNodeCode"/>
        <result column="MAIN_NUMBER" property="mainNumber"/>
        <result column="DEVELOPER_ID" property="developerId"/>
        <result column="BUSI_TYPE" property="busiType"/>
        <result column="GOODS_NAME" property="goodsName"/>
        <result column="CUST_NAME" property="custName"/>
        <result column="PSPT_ID" property="psptId"/>
        <result column="PSPT_ADDRESSS" property="psptAddresss"/>
        <result column="DELIVERY_TYPE" property="deliveryType"/>
        <result column="PHONE" property="phone"/>
        <result column="ADDRESS" property="address"/>
        <result column="REMARK" property="remark"/>
        <result column="receiveName" property="receiveName"/>
        <result column="STAFF_ID" property="staffId"/>
        <result column="STAFF_NAME" property="staffName"/>
        <result column="STAFF_PHONE" property="staffPhone"/>
        <result column="DEAL_STAFF_ID" property="dealStaffId"/>
        <result column="GOODS_DESC" property="goodsDesc"/>
        <result column="IN_MODE_CODE" property="inModeCode"/>
        <result column="EXT_ORDER_ID" property="extOrderId"/>
        <result column="ORDER_STATE" property="orderState"/>
        <result column="GZT_CHECK_REMARK" property="gztCheckRemark"/>
        <result column="GZT_CHECK_STATE" property="gztCheckState"/>
        <result column="GZT_CHECK_TIME" property="gztCheckTime"/>
        <result column="HMD_CHECK_REMARK" property="hmdCheckRemark"/>
        <result column="HMD_CHECK_STATE" property="hmdCheckState"/>
        <result column="HMD_CHECK_TIME" property="hmdCheckTime"/>
        <result property="frontState" column="FRONT_STATE" jdbcType="VARCHAR"/>
        <result property="frontRemark" column="FRONT_REMARK" jdbcType="VARCHAR"/>
        <result property="backState" column="BACK_STATE" jdbcType="VARCHAR"/>
        <result property="backRemark" column="BACK_REMARK" jdbcType="VARCHAR"/>
        <result property="similarity" column="SIMILARITY" jdbcType="VARCHAR"/>
        <result property="faceState" column="FACE_STATE" jdbcType="VARCHAR"/>
        <result property="faceRemark" column="FACE_REMARK" jdbcType="VARCHAR"/>
        <result column="GOODS_ID" property="goodsId"/>
        <result column="EPARCHY_CODE" property="eparchyCode"/>
        <result column="PROVINCE_CODE" property="PROVINCE_CODE"/>
        <result column="CITY_CODE" property="CITY_CODE"/>
        <result column="COUNTY_CODE" property="COUNTY_CODE"/>
        <result property="numStatus" column="NUM_STATUS"/>
    </resultMap>

    <select id="queryOrderDetails" resultMap="OrderDetailMap"
            parameterType="com.jsunicom.oms.po.order.dto.OrderDTO">
        SELECT ord.`ORDER_ID`,
               ord.`ORDER_TIME`,
               ord.`ORDER_AMOUNT`,
               ord.`EPARCHY_CODE`,
               ol.`CHANNEL_ID`,
               ord.`REMARK`,
               ord.`ORDER_STATE`,
               ord.`IN_MODE_CODE`,
               ord.`PAY_RESULT`,
               ord.`EXT_ORDER_ID`,
               ol.`ORDER_NODE_NAME`,
               ol.`ORDER_NODE_CODE`,
               good.`MAIN_NUMBER`,
               ol.`DEVELOPER_ID`,
               ol.`STAFF_ID`,
               ol.`STAFF_NAME`,
               ol.`STAFF_PHONE`,
               ol.`DEAL_STAFF_ID`,
               good.`BUSI_TYPE`,
               good.`GOODS_NAME`,
               good.`GOODS_DESC`,
               cust.`CUST_NAME`,
               cust.`PSPT_ID`,
               cust.`PSPT_ADDRESSS`,
               cust.`GZT_CHECK_STATE`,
               cust.`GZT_CHECK_TIME`,
               cust.`GZT_CHECK_REMARK`,
               cust.`HMD_CHECK_REMARK`,
               cust.`HMD_CHECK_STATE`,
               cust.`HMD_CHECK_TIME`,
               cust.`FRONT_STATE`,
               cust.`FRONT_REMARK`,
               cust.`BACK_STATE`,
               cust.`BACK_REMARK`,
               cust.`SIMILARITY`,
               cust.`FACE_STATE`,
               cust.`FACE_REMARK`,
               cust.`IS_BENWANG` as NUM_STATUS,
               addr.`cust_name` AS receiveName,
               addr.`DELIVERY_TYPE`,
               addr.`PHONE`,
               addr.`ADDRESS`,
               addr.`PROVINCE_CODE`,
               addr.`CITY_CODE`,
               addr.`COUNTY_CODE`,
               ord.`EPARCHY_CODE`,
               good.`GOODS_ID`
        FROM tf_ord_main ORD
                 LEFT JOIN tf_ord_address addr ON ord.`ORDER_ID` = addr.`ORDER_ID`
                 LEFT JOIN tf_ord_custinfo cust ON ord.`ORDER_ID` = cust.`ORDER_ID`
                 LEFT JOIN tf_ord_goods_main good ON ord.`ORDER_ID` = good.`ORDER_ID`
                 LEFT JOIN tf_ord_line ol ON ord.`ORDER_ID` = ol.`order_id`
                 LEFT JOIN tf_ord_school_order_detail school ON ord.`ORDER_ID` = school.`ORDER_ID`
        WHERE ord.order_id = #{orderId}

    </select>
    <select id="queryCounty" resultType="com.jsunicom.oms.po.order.TdOrdMAreaAll"
            parameterType="java.lang.String">
        select `id`, `name`, parent_id parentId
        from TD_ORD_M_AREA_ALL
        where PARENT_ID = #{cityCode}
    </select>
    <select id="selectSchoolPayOrder" parameterType="Map" resultType="Map">
        SELECT DISTINCT OM.ORDER_ID
                      , OM.EPARCHY_CODE
                      , GOOD.GOODS_ID
                      , GOOD.GOODS_NAME
                      , LINE.STAFF_ID
                      , LINE.STAFF_NAME
                      , LINE.STAFF_PHONE
                      , LINE.CHANNEL_ID
                      , ITEM.ATTR_VALUE
                      , OC.CUST_NAME
        FROM TF_ORD_MAIN OM
        LEFT JOIN TF_ORD_GOODS_MAIN GOOD ON OM.ORDER_ID = GOOD.ORDER_ID
        LEFT JOIN TF_F_PAY_LOG_SETTLECENTER PAY ON OM.ORDER_ID = PAY.ORDER_ID AND PAY.CANCEL_TAG = '0' AND PAY.PAY_RESULT = 'SUCCESS'
        LEFT JOIN TF_ORD_LINE LINE ON OM.ORDER_ID = LINE.ORDER_ID
        LEFT JOIN TF_ORD_CUSTINFO OC ON OM.ORDER_ID = OC.ORDER_ID
        LEFT JOIN TF_ORDER_ITEM ITEM ON OM.ORDER_ID = ITEM.ORDER_ID AND ITEM.ATTR_CODE = '0006'
        WHERE GOOD.BUSI_TYPE != '60'
	    AND ITEM.ATTR_VALUE = #{serialNumber}
	    AND UPPER(SUBSTR(OC.PSPT_ID, -6, 6)) = #{psptId}
	    AND OM.ORDER_STATE NOT IN ('0010', '0070', '0090')
    </select>
    <select id="selectSubOrdersForSchoolPay" resultType="java.util.Map">
        SELECT IFNULL(SUM(PAY.BUSI_AMT), 0) PAY_MONEY,
               COUNT(CASE
                         WHEN PAY.BUSI_AMT > 0 THEN 1
                     ELSE
                         NULL
                     END)  ORDER_COUNT
        FROM TF_ORD_MAIN OM
        LEFT JOIN TF_F_PAY_LOG_SETTLECENTER PAY ON OM.ORDER_ID = PAY.ORDER_ID
        WHERE PAY.CANCEL_TAG = '0'
          AND PAY.PAY_RESULT = 'SUCCESS'
          AND OM.ORDER_ID = #{ORDERID}
    </select>
    <select id="queryCustPhoto" resultType="java.util.Map">
        select
            CARD_PHOTO_A,
            CARD_PHOTO_B,
            CARD_PHOTO_HAND
        from tf_ord_custinfo e where e.order_id = #{orderId}
    </select>
    <select id="getCodeName" resultType="java.lang.String">
        select name from td_ord_m_area_all where id = #{code}
    </select>
    <select id="qryAreaByCode" resultType="java.lang.String" parameterType="java.lang.String">
        select id from td_ord_m_area_all where name = #{name} and parent_id = #{city}
    </select>
    <select id="qryAreaByProvince" resultType="java.lang.String" parameterType="java.lang.String">
        select id from td_ord_m_area_all where province_name = #{provinceName}
    </select>

    <select id="querySchoolOrderDetails" resultType="java.util.Map">
        select om.order_id,gm.main_number,oc.cust_name,om.eparchy_code,oa.county_code,om.developer_id,om.create_date
             ,case when om.order_state in ('0090','0060') then (select max(ol.update_time) from tl_ord_operator_log ol where ol.order_id = om.order_id) else '' end as update_time,om.order_state
             ,od.campus_name,od.school_id,od.school_name,od.member_name,oi.attr_value
        from tf_ord_main om left join tf_ord_goods_main gm on om.order_id = gm.order_id
                            left join tf_ord_address oa on om.order_id  = oa.order_id
                            left join tf_ord_custinfo oc on om.order_id = oc.order_id
                            left join tf_ord_school_order_detail od on om.order_id = od.order_id
                            left join tf_order_item oi on om.order_id = oi.order_id and oi.attr_type = '0' and oi.attr_code = 'memberName'
    </select>
    <select id="querySchoolOrderDetailsInfos" resultType="java.util.Map">
        SELECT od.order_id,od.main_number,oc.cust_name,om.eparchy_code,'' county_code,om.developer_id,DATE_FORMAT(od.in_time,'%Y-%m-%d %H:%i:%s') as in_time
             ,CASE WHEN om.order_state IN ('0090','0060','0070') THEN (SELECT MAX(ol.update_time) FROM tl_ord_operator_log ol WHERE ol.order_id = om.order_id) ELSE '' END AS close_time,om.order_state
             ,od.campus_name,od.school_id,od.school_name,od.member_name,od.school_manager_pid,
             (SELECT CONCAT(partner.partner_id,'|',partner.staff_name,'|',partner.partner_id,'|',partner.store_manager_name,'|',partner.store_manager_devel_id) FROM TF_ORD_SCHOOL_PARTNER partner WHERE partner.staff_id=od.member_id ORDER BY partner.update_time ASC LIMIT 1) AS partnerInfo
        FROM tf_ord_school_order_detail od LEFT JOIN tf_ord_main om ON om.order_id = od.order_id
                            LEFT JOIN tf_ord_custinfo oc ON om.order_id = oc.order_id
    </select>
    <select id="qryAreaByCityCode" resultType="java.lang.String" parameterType="java.lang.String">
        select id from td_ord_m_area_all where name = #{name}
    </select>
    <select id="queryAreaAll" resultType="com.jsunicom.oms.po.order.TdOrdMAreaAll">
        select id,`name`,parent_id parentId,lvl_id lvlId
        from TD_ORD_M_AREA_ALL
    </select>

    <select id="queryAreaByParentIdAndLvlId" resultType="com.jsunicom.oms.po.order.TdOrdMAreaAll">
        select id,name,parent_id parentId
        from TD_ORD_M_AREA_ALL where parent_id = #{parentId,jdbcType=VARCHAR} and lvl_id = #{lvlId,jdbcType=VARCHAR}
    </select>

</mapper>
