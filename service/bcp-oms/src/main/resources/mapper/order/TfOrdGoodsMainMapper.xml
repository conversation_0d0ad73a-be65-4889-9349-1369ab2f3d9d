<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.TfOrdGoodsMainMapper">

    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.tf.TfOrdGoodsMain">
            <id property="goodsOrderId" column="GOODS_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
            <result property="goodsId" column="GOODS_ID" jdbcType="VARCHAR"/>
            <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
            <result property="goodsDesc" column="GOODS_DESC" jdbcType="VARCHAR"/>
            <result property="goodsType" column="GOODS_TYPE" jdbcType="VARCHAR"/>
            <result property="busiType" column="BUSI_TYPE" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP"/>
            <result property="productId" column="PRODUCT_ID" jdbcType="VARCHAR"/>
            <result property="productName" column="PRODUCT_NAME" jdbcType="VARCHAR"/>
            <result property="productDesc" column="PRODUCT_DESC" jdbcType="VARCHAR"/>
            <result property="productType" column="PRODUCT_TYPE" jdbcType="VARCHAR"/>
            <result property="mainNumber" column="MAIN_NUMBER" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        GOODS_ORDER_ID,ORDER_ID,GOODS_ID,
        GOODS_NAME,GOODS_DESC,BUSI_TYPE,GOODS_TYPE,
        CREATE_DATE,PRODUCT_ID,PRODUCT_NAME,
        PRODUCT_DESC,PRODUCT_TYPE,MAIN_NUMBER,
        UPDATE_DATE
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tf_ord_goods_main
        where GOODS_ORDER_ID = #{goodsOrderId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from tf_ord_goods_main
        where GOODS_ORDER_ID = #{goodsOrderId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.jsunicom.oms.po.order.tf.TfOrdGoodsMain">
        insert into tf_ord_goods_main (GOODS_ORDER_ID, ORDER_ID, GOODS_ID,
                                       GOODS_NAME, GOODS_DESC, BUSI_TYPE,
                                       CREATE_DATE, PRODUCT_ID, PRODUCT_NAME,
                                       PRODUCT_DESC, PRODUCT_TYPE, MAIN_NUMBER,
                                       UPDATE_DATE)
        values (#{goodsOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR},
                #{goodsName,jdbcType=VARCHAR}, #{goodsDesc,jdbcType=VARCHAR}, #{busiType,jdbcType=VARCHAR},
                #{createDate,jdbcType=TIMESTAMP}, #{productId,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR},
                #{productDesc,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{mainNumber,jdbcType=VARCHAR},
                #{updateDate,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdGoodsMain">
        insert into tf_ord_goods_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsOrderId != null">
                GOODS_ORDER_ID,
            </if>
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="goodsId != null">
                GOODS_ID,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="goodsDesc != null">
                GOODS_DESC,
            </if>
            <if test="busiType != null">
                BUSI_TYPE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="productId != null">
                PRODUCT_ID,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="productDesc != null">
                PRODUCT_DESC,
            </if>
            <if test="productType != null">
                PRODUCT_TYPE,
            </if>
            <if test="mainNumber != null">
                MAIN_NUMBER,
            </if>
            <if test="updateDate != null">
                UPDATE_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsOrderId != null">
                #{goodsOrderId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsDesc != null">
                #{goodsDesc,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null">
                #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDesc != null">
                #{productDesc,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=VARCHAR},
            </if>
            <if test="mainNumber != null">
                #{mainNumber,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                #{updateDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdGoodsMain">
        update tf_ord_goods_main
        <set>
            <if test="orderId != null">
                ORDER_ID = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                GOODS_ID = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsDesc != null">
                GOODS_DESC = #{goodsDesc,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null">
                BUSI_TYPE = #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="productId != null">
                PRODUCT_ID = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productDesc != null">
                PRODUCT_DESC = #{productDesc,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="mainNumber != null">
                MAIN_NUMBER = #{mainNumber,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where GOODS_ORDER_ID = #{goodsOrderId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.order.tf.TfOrdGoodsMain">
        update tf_ord_goods_main
        set ORDER_ID = #{orderId,jdbcType=VARCHAR},
            GOODS_ID = #{goodsId,jdbcType=VARCHAR},
            GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            GOODS_DESC = #{goodsDesc,jdbcType=VARCHAR},
            BUSI_TYPE = #{busiType,jdbcType=VARCHAR},
            CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            PRODUCT_ID = #{productId,jdbcType=VARCHAR},
            PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            PRODUCT_DESC = #{productDesc,jdbcType=VARCHAR},
            PRODUCT_TYPE = #{productType,jdbcType=VARCHAR},
            MAIN_NUMBER = #{mainNumber,jdbcType=VARCHAR},
            UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP}
        where GOODS_ORDER_ID = #{goodsOrderId,jdbcType=VARCHAR}
    </update>
    <select id="selectByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tf_ord_goods_main
        where ORDER_ID = #{orderId}
    </select>
</mapper>
