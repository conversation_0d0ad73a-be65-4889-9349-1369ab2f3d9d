<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.TfOrdAddressMapper">

    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.tf.TfOrdAddress">
            <id property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
            <result property="addrId" column="ADDR_ID" jdbcType="VARCHAR"/>
            <result property="deliveryType" column="DELIVERY_TYPE" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="PROVINCE_CODE" jdbcType="VARCHAR"/>
            <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
            <result property="countyCode" column="COUNTY_CODE" jdbcType="VARCHAR"/>
            <result property="custName" column="CUST_NAME" jdbcType="VARCHAR"/>
            <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
            <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORDER_ID,ADDR_ID,DELIVERY_TYPE,
        PROVINCE_CODE,CITY_CODE,COUNTY_CODE,
        CUST_NAME,PHONE,ADDRESS,
        UPDATE_TIME
    </sql>
    <update id="updateOrdAddressByOrderId" parameterType="com.jsunicom.oms.po.order.tf.TfOrdAddress">
        update tf_ord_address
        <set>
            <if test="provinceCode != null and provinceCode != ''">
                PROVINCE_CODE = #{provinceCode},
            </if>
            <if test="cityCode != null and cityCode != ''">
                CITY_CODE = #{cityCode},
            </if>
            <if test="countyCode != null and countyCode != ''">
                COUNTY_CODE = #{countyCode},
            </if>
            <if test="custName != null and custName != ''">
                CUST_NAME = #{custName},
            </if>
            <if test="phone != null and phone != ''">
                PHONE = #{phone},
            </if>
            <if test="address != null and address != ''">
                ADDRESS = #{address},
            </if>
            update_time = now()
        </set>
        where order_id = #{orderId}
    </update>
    <select id="qryOrdAddressByOrderId" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select <include refid="Base_Column_List"/>
        from tf_ord_address
        where order_id = #{orderId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from tf_ord_address
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.jsunicom.oms.po.order.tf.TfOrdAddress">
        insert into tf_ord_address (ORDER_ID, ADDR_ID, DELIVERY_TYPE,
                                    PROVINCE_CODE, CITY_CODE, COUNTY_CODE,
                                    CUST_NAME, PHONE, ADDRESS,
                                    UPDATE_TIME)
        values (#{orderId,jdbcType=VARCHAR}, #{addrId,jdbcType=VARCHAR}, #{deliveryType,jdbcType=VARCHAR},
                #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{countyCode,jdbcType=VARCHAR},
                #{custName,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdAddress">
        insert into tf_ord_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="addrId != null">
                ADDR_ID,
            </if>
            <if test="deliveryType != null">
                DELIVERY_TYPE,
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE,
            </if>
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="countyCode != null">
                COUNTY_CODE,
            </if>
            <if test="custName != null">
                CUST_NAME,
            </if>
            <if test="phone != null">
                PHONE,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="addrId != null">
                #{addrId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryType != null">
                #{deliveryType,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="countyCode != null">
                #{countyCode,jdbcType=VARCHAR},
            </if>
            <if test="custName != null">
                #{custName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdAddress">
        update tf_ord_address
        <set>
            <if test="addrId != null">
                ADDR_ID = #{addrId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryType != null">
                DELIVERY_TYPE = #{deliveryType,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="countyCode != null">
                COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
            </if>
            <if test="custName != null">
                CUST_NAME = #{custName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                PHONE = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.order.tf.TfOrdAddress">
        update tf_ord_address
        set ADDR_ID = #{addrId,jdbcType=VARCHAR},
            DELIVERY_TYPE = #{deliveryType,jdbcType=VARCHAR},
            PROVINCE_CODE = #{provinceCode,jdbcType=VARCHAR},
            CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            COUNTY_CODE = #{countyCode,jdbcType=VARCHAR},
            CUST_NAME = #{custName,jdbcType=VARCHAR},
            PHONE = #{phone,jdbcType=VARCHAR},
            ADDRESS = #{address,jdbcType=VARCHAR},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </update>
</mapper>
