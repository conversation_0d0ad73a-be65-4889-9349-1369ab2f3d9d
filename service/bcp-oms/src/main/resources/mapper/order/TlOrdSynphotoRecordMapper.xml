<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.TlOrdSynphotoRecordMapper">

    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord">
            <id property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
            <result property="activieId" column="ACTIVIE_ID" jdbcType="VARCHAR"/>
            <result property="serialNumber" column="SERIAL_NUMBER" jdbcType="VARCHAR"/>
            <result property="synState" column="SYN_STATE" jdbcType="VARCHAR"/>
            <result property="errorMsg" column="ERROR_MSG" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="DATE"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORDER_ID,ACTIVIE_ID,SERIAL_NUMBER,
        SYN_STATE,ERROR_MSG,CREATE_DATE,
        REMARK,UPDATE_DATE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tl_ord_synphoto_record
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from tl_ord_synphoto_record
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord">
        insert into tl_ord_synphoto_record (ORDER_ID, ACTIVIE_ID, SERIAL_NUMBER,
                                            SYN_STATE, ERROR_MSG, CREATE_DATE,
                                            REMARK, UPDATE_DATE)
        values (#{orderId,jdbcType=VARCHAR}, #{activieId,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR},
                #{synState,jdbcType=VARCHAR}, #{errorMsg,jdbcType=VARCHAR}, now(),
                #{remark,jdbcType=VARCHAR}, now())
    </insert>
    <insert id="insertSelective" parameterType="com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord">
        insert into tl_ord_synphoto_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="activieId != null">
                ACTIVIE_ID,
            </if>
            <if test="serialNumber != null">
                SERIAL_NUMBER,
            </if>
            <if test="synState != null">
                SYN_STATE,
            </if>
            <if test="errorMsg != null">
                ERROR_MSG,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="updateDate != null">
                UPDATE_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="activieId != null">
                #{activieId,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="synState != null">
                #{synState,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=DATE},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                #{updateDate,jdbcType=DATE},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord">
        update tl_ord_synphoto_record
        <set>
            <if test="activieId != null">
                ACTIVIE_ID = #{activieId,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="synState != null">
                SYN_STATE = #{synState,jdbcType=VARCHAR},
            </if>
            <if test="errorMsg != null">
                ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=DATE},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null">
                UPDATE_DATE = #{updateDate,jdbcType=DATE},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord">
        update tl_ord_synphoto_record
        set ACTIVIE_ID = #{activieId,jdbcType=VARCHAR},
            SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
            SYN_STATE = #{synState,jdbcType=VARCHAR},
            ERROR_MSG = #{errorMsg,jdbcType=VARCHAR},
            CREATE_DATE = #{createDate,jdbcType=DATE},
            REMARK = #{remark,jdbcType=VARCHAR},
            UPDATE_DATE = #{updateDate,jdbcType=DATE}
        where ORDER_ID = #{orderId,jdbcType=VARCHAR}
    </update>
</mapper>
