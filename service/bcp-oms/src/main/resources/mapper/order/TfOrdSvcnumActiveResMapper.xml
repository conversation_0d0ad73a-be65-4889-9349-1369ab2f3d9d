<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.TfOrdSvcnumActiveResMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ACTIVIE_ID" jdbcType="VARCHAR" property="activieId" />
    <result column="RES_TYPE" jdbcType="VARCHAR" property="resType" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="RES_NAME" jdbcType="VARCHAR" property="resName" />
    <result column="RES_DESC" jdbcType="VARCHAR" property="resDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ACTIVIE_ID, RES_TYPE, URL, RES_NAME, RES_DESC
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tf_ord_svcnum_active_res
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from tf_ord_svcnum_active_res
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes">
    insert into tf_ord_svcnum_active_res (ID, ACTIVIE_ID, RES_TYPE,
      URL, RES_NAME, RES_DESC
      )
    values (#{id,jdbcType=VARCHAR}, #{activieId,jdbcType=VARCHAR}, #{resType,jdbcType=VARCHAR},
      #{url,jdbcType=VARCHAR}, #{resName,jdbcType=VARCHAR}, #{resDesc,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes">
    insert into tf_ord_svcnum_active_res
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="activieId != null">
        ACTIVIE_ID,
      </if>
      <if test="resType != null">
        RES_TYPE,
      </if>
      <if test="url != null">
        URL,
      </if>
      <if test="resName != null">
        RES_NAME,
      </if>
      <if test="resDesc != null">
        RES_DESC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="activieId != null">
        #{activieId,jdbcType=VARCHAR},
      </if>
      <if test="resType != null">
        #{resType,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="resName != null">
        #{resName,jdbcType=VARCHAR},
      </if>
      <if test="resDesc != null">
        #{resDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes">
    update tf_ord_svcnum_active_res
    <set>
      <if test="activieId != null">
        ACTIVIE_ID = #{activieId,jdbcType=VARCHAR},
      </if>
      <if test="resType != null">
        RES_TYPE = #{resType,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="resName != null">
        RES_NAME = #{resName,jdbcType=VARCHAR},
      </if>
      <if test="resDesc != null">
        RES_DESC = #{resDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getAllByActivieId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from tf_ord_svcnum_active_res
    where ACTIVIE_ID = #{activieId}
  </select>

  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes">
    update tf_ord_svcnum_active_res
    set ACTIVIE_ID = #{activieId,jdbcType=VARCHAR},
      RES_TYPE = #{resType,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      RES_NAME = #{resName,jdbcType=VARCHAR},
      RES_DESC = #{resDesc,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
