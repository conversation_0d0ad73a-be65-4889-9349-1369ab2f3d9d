<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.TfOrdLineMapper">

    <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.order.tf.TfOrdLine">
            <id property="orderLineId" column="ORDER_LINE_ID" jdbcType="VARCHAR"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="busiType" column="busi_type" jdbcType="VARCHAR"/>
            <result property="inModeCode" column="IN_MODE_CODE" jdbcType="VARCHAR"/>
            <result property="extOrderId" column="EXT_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP"/>
            <result property="dealStaffId" column="DEAL_STAFF_ID" jdbcType="VARCHAR"/>
            <result property="dealStaffName" column="DEAL_STAFF_NAME" jdbcType="VARCHAR"/>
            <result property="dealStaffPhone" column="DEAL_STAFF_PHONE" jdbcType="VARCHAR"/>
            <result property="staffId" column="STAFF_ID" jdbcType="VARCHAR"/>
            <result property="staffName" column="STAFF_NAME" jdbcType="VARCHAR"/>
            <result property="developerId" column="DEVELOPER_ID" jdbcType="VARCHAR"/>
            <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
            <result property="eparchyCode" column="EPARCHY_CODE" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="PROVINCE_CODE" jdbcType="VARCHAR"/>
            <result property="departId" column="DEPART_ID" jdbcType="VARCHAR"/>
            <result property="channelId" column="CHANNEL_ID" jdbcType="VARCHAR"/>
            <result property="channelType" column="CHANNEL_TYPE" jdbcType="VARCHAR"/>
            <result property="orderNodeState" column="ORDER_NODE_STATE" jdbcType="VARCHAR"/>
            <result property="orderNodeCode" column="ORDER_NODE_CODE" jdbcType="VARCHAR"/>
            <result property="orderNodeName" column="ORDER_NODE_NAME" jdbcType="VARCHAR"/>
            <result property="mainNumber" column="MAIN_NUMBER" jdbcType="VARCHAR"/>
            <result property="busiSysOrderId" column="BUSI_SYS_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="serialNumber" column="SERIAL_NUMBER" jdbcType="VARCHAR"/>
            <result property="productionResult" column="PRODUCTION_RESULT" jdbcType="VARCHAR"/>
            <result property="netType" column="NET_TYPE" jdbcType="VARCHAR"/>
            <result property="productionDate" column="PRODUCTION_DATE" jdbcType="TIMESTAMP"/>
            <result property="cancelTag" column="CANCEL_TAG" jdbcType="CHAR"/>
            <result property="cancelDate" column="CANCEL_DATE" jdbcType="TIMESTAMP"/>
            <result property="cancelReason" column="CANCEL_REASON" jdbcType="VARCHAR"/>
            <result property="cancelStaffId" column="CANCEL_STAFF_ID" jdbcType="VARCHAR"/>
            <result property="cancelDepartId" column="CANCEL_DEPART_ID" jdbcType="VARCHAR"/>
            <result property="acceptDate" column="ACCEPT_DATE" jdbcType="TIMESTAMP"/>
            <result property="finishDate" column="FINISH_DATE" jdbcType="TIMESTAMP"/>
            <result property="backOrderId" column="BACK_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="staffPhone" column="STAFF_PHONE" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORDER_LINE_ID,order_id,busi_type,
        IN_MODE_CODE,EXT_ORDER_ID,CREATE_DATE,
        DEAL_STAFF_ID,DEAL_STAFF_NAME,DEAL_STAFF_PHONE,
        STAFF_ID,STAFF_NAME,DEVELOPER_ID,
        CITY_CODE,EPARCHY_CODE,PROVINCE_CODE,
        DEPART_ID,CHANNEL_ID,CHANNEL_TYPE,
        ORDER_NODE_STATE,ORDER_NODE_CODE,ORDER_NODE_NAME,
        MAIN_NUMBER,BUSI_SYS_ORDER_ID,SERIAL_NUMBER,
        PRODUCTION_RESULT,NET_TYPE,PRODUCTION_DATE,
        CANCEL_TAG,CANCEL_DATE,CANCEL_REASON,
        CANCEL_STAFF_ID,CANCEL_DEPART_ID,ACCEPT_DATE,
        FINISH_DATE,BACK_ORDER_ID,update_time,
        remark
    </sql>
    <update id="updateTfOrdLine" parameterType="com.jsunicom.oms.po.order.tf.TfOrdLine">
        update tf_ord_line
        <set>
            <if test="dealStaffId != null and dealStaffId != ''">
                DEAL_STAFF_ID = #{dealStaffId},
            </if>
            <if test="dealStaffName != null and dealStaffName != ''">
                DEAL_STAFF_NAME = #{dealStaffName},
            </if>
            <if test="dealStaffPhone != null and dealStaffPhone != ''">
                DEAL_STAFF_PHONE = #{dealStaffPhone},
            </if>
            <if test="orderNodeState != null and orderNodeState != ''">
                ORDER_NODE_STATE = #{orderNodeState},
            </if>
            <if test="orderNodeCode != null and orderNodeCode != ''">
                ORDER_NODE_CODE = #{orderNodeCode},
            </if>
            <if test="orderNodeName != null and orderNodeName != ''">
                ORDER_NODE_NAME = #{orderNodeName},
            </if>
            <if test="productionResult != null and productionResult != ''">
                PRODUCTION_RESULT = #{productionResult},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="busiSysOrderId != null and busiSysOrderId != ''">
                BUSI_SYS_ORDER_ID = #{busiSysOrderId},
            </if>

            update_time = now()
        </set>
        where order_id = #{orderId}
    </update>
    <select id="qryTfOrdLineByOrderId" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select <include refid="Base_Column_List"/>
        from tf_ord_line
        where order_id = #{orderId}
    </select>

    <insert id="insert" parameterType="com.jsunicom.oms.po.order.tf.TfOrdLine">
        insert into tf_ord_line (ORDER_LINE_ID, order_id, busi_type,
                                 IN_MODE_CODE, EXT_ORDER_ID, CREATE_DATE,
                                 DEAL_STAFF_ID, DEAL_STAFF_NAME, DEAL_STAFF_PHONE,
                                 STAFF_ID, STAFF_NAME, DEVELOPER_ID,
                                 CITY_CODE, EPARCHY_CODE, PROVINCE_CODE,
                                 DEPART_ID, CHANNEL_ID, CHANNEL_TYPE,
                                 ORDER_NODE_STATE, ORDER_NODE_CODE, ORDER_NODE_NAME,
                                 MAIN_NUMBER, BUSI_SYS_ORDER_ID, SERIAL_NUMBER,
                                 PRODUCTION_RESULT, NET_TYPE, PRODUCTION_DATE,
                                 CANCEL_TAG, CANCEL_DATE, CANCEL_REASON,
                                 CANCEL_STAFF_ID, CANCEL_DEPART_ID, ACCEPT_DATE,
                                 FINISH_DATE, BACK_ORDER_ID, update_time,
                                 remark, staff_phone)
        values (#{orderLineId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{busiType,jdbcType=VARCHAR},
                #{inModeCode,jdbcType=VARCHAR}, #{extOrderId,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{dealStaffId,jdbcType=VARCHAR}, #{dealStaffName,jdbcType=VARCHAR}, #{dealStaffPhone,jdbcType=VARCHAR},
                #{staffId,jdbcType=VARCHAR}, #{staffName,jdbcType=VARCHAR}, #{developerId,jdbcType=VARCHAR},
                #{cityCode,jdbcType=VARCHAR}, #{eparchyCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR},
                #{departId,jdbcType=VARCHAR}, #{channelId,jdbcType=VARCHAR}, #{channelType,jdbcType=VARCHAR},
                #{orderNodeState,jdbcType=VARCHAR}, #{orderNodeCode,jdbcType=VARCHAR}, #{orderNodeName,jdbcType=VARCHAR},
                #{mainNumber,jdbcType=VARCHAR}, #{busiSysOrderId,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR},
                #{productionResult,jdbcType=VARCHAR}, #{netType,jdbcType=VARCHAR}, #{productionDate,jdbcType=TIMESTAMP},
                #{cancelTag,jdbcType=CHAR}, #{cancelDate,jdbcType=TIMESTAMP}, #{cancelReason,jdbcType=VARCHAR},
                #{cancelStaffId,jdbcType=VARCHAR}, #{cancelDepartId,jdbcType=VARCHAR}, #{acceptDate,jdbcType=TIMESTAMP},
                #{finishDate,jdbcType=TIMESTAMP}, #{backOrderId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR}, #{staffPhone,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.jsunicom.oms.po.order.tf.TfOrdLine">
        insert into tf_ord_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderLineId != null">
                ORDER_LINE_ID,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="busiType != null">
                busi_type,
            </if>
            <if test="inModeCode != null">
                IN_MODE_CODE,
            </if>
            <if test="extOrderId != null">
                EXT_ORDER_ID,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="dealStaffId != null">
                DEAL_STAFF_ID,
            </if>
            <if test="dealStaffName != null">
                DEAL_STAFF_NAME,
            </if>
            <if test="dealStaffPhone != null">
                DEAL_STAFF_PHONE,
            </if>
            <if test="staffId != null">
                STAFF_ID,
            </if>
            <if test="staffName != null">
                STAFF_NAME,
            </if>
            <if test="developerId != null">
                DEVELOPER_ID,
            </if>
            <if test="cityCode != null">
                CITY_CODE,
            </if>
            <if test="eparchyCode != null">
                EPARCHY_CODE,
            </if>
            <if test="provinceCode != null">
                PROVINCE_CODE,
            </if>
            <if test="departId != null">
                DEPART_ID,
            </if>
            <if test="channelId != null">
                CHANNEL_ID,
            </if>
            <if test="channelType != null">
                CHANNEL_TYPE,
            </if>
            <if test="orderNodeState != null">
                ORDER_NODE_STATE,
            </if>
            <if test="orderNodeCode != null">
                ORDER_NODE_CODE,
            </if>
            <if test="orderNodeName != null">
                ORDER_NODE_NAME,
            </if>
            <if test="mainNumber != null">
                MAIN_NUMBER,
            </if>
            <if test="busiSysOrderId != null">
                BUSI_SYS_ORDER_ID,
            </if>
            <if test="serialNumber != null">
                SERIAL_NUMBER,
            </if>
            <if test="productionResult != null">
                PRODUCTION_RESULT,
            </if>
            <if test="netType != null">
                NET_TYPE,
            </if>
            <if test="productionDate != null">
                PRODUCTION_DATE,
            </if>
            <if test="cancelTag != null">
                CANCEL_TAG,
            </if>
            <if test="cancelDate != null">
                CANCEL_DATE,
            </if>
            <if test="cancelReason != null">
                CANCEL_REASON,
            </if>
            <if test="cancelStaffId != null">
                CANCEL_STAFF_ID,
            </if>
            <if test="cancelDepartId != null">
                CANCEL_DEPART_ID,
            </if>
            <if test="acceptDate != null">
                ACCEPT_DATE,
            </if>
            <if test="finishDate != null">
                FINISH_DATE,
            </if>
            <if test="backOrderId != null">
                BACK_ORDER_ID,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="staffPhone != null">
                staff_phone,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderLineId != null">
                #{orderLineId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null">
                #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="inModeCode != null">
                #{inModeCode,jdbcType=VARCHAR},
            </if>
            <if test="extOrderId != null">
                #{extOrderId,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="dealStaffId != null">
                #{dealStaffId,jdbcType=VARCHAR},
            </if>
            <if test="dealStaffName != null">
                #{dealStaffName,jdbcType=VARCHAR},
            </if>
            <if test="dealStaffPhone != null">
                #{dealStaffPhone,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null">
                #{staffId,jdbcType=VARCHAR},
            </if>
            <if test="staffName != null">
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="developerId != null">
                #{developerId,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="eparchyCode != null">
                #{eparchyCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                #{departId,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="orderNodeState != null">
                #{orderNodeState,jdbcType=VARCHAR},
            </if>
            <if test="orderNodeCode != null">
                #{orderNodeCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNodeName != null">
                #{orderNodeName,jdbcType=VARCHAR},
            </if>
            <if test="mainNumber != null">
                #{mainNumber,jdbcType=VARCHAR},
            </if>
            <if test="busiSysOrderId != null">
                #{busiSysOrderId,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="productionResult != null">
                #{productionResult,jdbcType=VARCHAR},
            </if>
            <if test="netType != null">
                #{netType,jdbcType=VARCHAR},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTag != null">
                #{cancelTag,jdbcType=CHAR},
            </if>
            <if test="cancelDate != null">
                #{cancelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelReason != null">
                #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="cancelStaffId != null">
                #{cancelStaffId,jdbcType=VARCHAR},
            </if>
            <if test="cancelDepartId != null">
                #{cancelDepartId,jdbcType=VARCHAR},
            </if>
            <if test="acceptDate != null">
                #{acceptDate,jdbcType=TIMESTAMP},
            </if>
            <if test="finishDate != null">
                #{finishDate,jdbcType=TIMESTAMP},
            </if>
            <if test="backOrderId != null">
                #{backOrderId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="staffPhone != null">
                #{staffPhone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
