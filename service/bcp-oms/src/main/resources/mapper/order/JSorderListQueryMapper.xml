<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.order.JSorderListQueryMapper">

    <resultMap id="orderListMap" type="com.jsunicom.oms.po.order.OrderListRsp">
        <result property="orderCenterId" column="ORDER_ID" jdbcType="VARCHAR"/>
        <result property="provinceCode" column="AREA_CODE" jdbcType="VARCHAR"/>
        <result property="eparchyCode" column="EPARCHY_CODE" jdbcType="VARCHAR"/>
        <result property="sourceSystemId" column="SOURCE_SYSTEM_ID" jdbcType="VARCHAR"/>
        <result property="orderState" column="ORDER_STATUS" jdbcType="VARCHAR"/>
        <result property="extOrderId" column="EXT_ORDER_ID" jdbcType="VARCHAR"/>
        <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP"/>
        <result property="createOperId" column="OPER_ID" jdbcType="VARCHAR"/>
<!--        <result property="operRole" column="OPER_ROLE" jdbcType="VARCHAR"/>-->
        <result property="payType" column="PAY_TYPE" jdbcType="VARCHAR"/>
        <result property="payState" column="PAY_STATE" jdbcType="VARCHAR"/>
<!--        <result property="paySeq" column="PAY_SEQ" jdbcType="VARCHAR"/>-->
        <result property="payTime" column="PAY_TIME" jdbcType="TIMESTAMP"/>
        <result property="orderAmount" column="ORDER_AMOUNT" jdbcType="VARCHAR"/>
<!--        <result property="orderTag" column="ORDER_TAG" jdbcType="VARCHAR"/>-->
<!--        <result property="estimatedComm" column="ASTIMATED_COMM" jdbcType="VARCHAR"/>-->
<!--        <result property="cancelTag" column="CANCEL_TAG" jdbcType="VARCHAR"/>-->
<!--        <result property="refundTag" column="REFUND_TAG" jdbcType="VARCHAR"/>-->
<!--        <result property="modifyTag" column="MODIFY_TAG" jdbcType="VARCHAR"/>-->
<!--        <result property="prepay" column="PRE_PAY" jdbcType="VARCHAR"/>-->
<!--        <result property="invalidTime" column="INVALID_TIME" jdbcType="TIMESTAMP"/>-->
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
<!--        <result property="refundTime" column="REFUND_TIME" jdbcType="TIMESTAMP"/>-->
        <result property="lastOperId" column="STAFF_ID" jdbcType="VARCHAR"/>
        <result property="busiType" column="BUSI_TYPE" jdbcType="VARCHAR"/>
<!--        <result property="productCustId" column="PRODUCT_CUST_ID" jdbcType="VARCHAR"/>-->
<!--        <result property="productItemId" column="PRODUCT_ITEM_ID" jdbcType="VARCHAR"/>-->
<!--        <result property="productOrderId" column="PRODUCT_ORDER_ID" jdbcType="VARCHAR"/>-->
        <result property="psptAddress" column="PSPT_ADDRESSS" jdbcType="VARCHAR"/>
        <result property="psptId" column="PSPT_ID" jdbcType="VARCHAR"/>
        <result property="networkNumber" column="SVCNUM" jdbcType="VARCHAR"/>
<!--        <result property="tradeNo" column="TRADE_NO" jdbcType="VARCHAR"/>-->
<!--        <result property="dispatchFee" column="DISPATCH_FEE" jdbcType="VARCHAR"/>-->
<!--        <result property="saleFee" column="SALE_FEE" jdbcType="VARCHAR"/>-->
<!--        <result property="standFee" column="STAND_FEE" jdbcType="VARCHAR"/>-->
<!--        <result property="taskKey" column="TASKKEY" jdbcType="VARCHAR"/>-->
<!--        <result property="flowDefId" column="FLOW_DEF_ID" jdbcType="VARCHAR"/>-->
<!--        <result property="curOperId" column="ASSIGNEE_" jdbcType="VARCHAR"/>-->
        <result property="custName" column="CUST_NAME" jdbcType="VARCHAR"/>
        <result property="receiveAmount" column="ORDER_AMOUNT" jdbcType="VARCHAR"/>
        <result property="mainNumber" column="MAIN_NUMBER" jdbcType="VARCHAR"/>
        <collection property="goods" ofType="com.jsunicom.oms.po.order.OrderListGoodsRsp">
            <result property="goodsOrderId" column="GOODS_ORDER_ID" jdbcType="VARCHAR"/>
<!--            <result property="goodsState" column="GOODS_STATE" jdbcType="VARCHAR"/>-->
            <result property="goodsId" column="GOODS_ID" jdbcType="VARCHAR"/>
            <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
            <result property="goodsDesc" column="GOODS_DESC" jdbcType="VARCHAR"/>
            <result property="goodsType" column="GOODS_TYPE" jdbcType="VARCHAR"/>
<!--            <result property="goodsUrl" column="GOODS_URL" jdbcType="VARCHAR"/>-->
<!--            <result property="extWebUrl" column="EXT_WEB_URL" jdbcType="VARCHAR"/>-->
        </collection>
<!--        <collection property="postInfo" ofType="com.jsunicom.oms.po.order.PostInfoRsp">-->
<!--            <result property="postNo" column="LOGISTICS_NO" jdbcType="VARCHAR"/>-->
<!--            <result property="postType" column="DELIVERY_TYPE" jdbcType="VARCHAR"/>-->
<!--            <result property="postFee" column="POST_FEE" jdbcType="VARCHAR"/>-->
<!--            <result property="postCompany" column="LOGISTICS_COM" jdbcType="VARCHAR"/>-->
<!--            <result property="postDate" column="POST_DATE" jdbcType="TIMESTAMP"/>-->
<!--            <result property="signDate" column="SIGN_DATE" jdbcType="TIMESTAMP"/>-->
<!--        </collection>-->
        <collection property="addressInfo" ofType="com.jsunicom.oms.po.order.AddressInfoRsp">
            <result property="deliveryType" column="DELIVERYTYPE"/>
            <result property="provinceCode" column="AREA_CODE" jdbcType="VARCHAR"/>
            <result property="cityCode" column="EPARCHY_CODE" jdbcType="VARCHAR"/>
            <result property="countyCode" column="COUNTY_CODE" jdbcType="VARCHAR"/>
            <result property="custName" column="CUST_NAME" jdbcType="VARCHAR"/>
            <result property="phone" column="PHONE" jdbcType="VARCHAR"/>
            <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
<!--            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>-->
<!--            <result property="postCode" column="POST_CODE" jdbcType="VARCHAR"/>-->
            <result property="postRemark" column="POST_REMARK" jdbcType="VARCHAR"/>
<!--            <result property="fixPhone" column="FIX_PHONE" jdbcType="VARCHAR"/>-->
<!--            <result property="selfGetAddressId" column="SELF_GET_ADDRESS_ID" jdbcType="VARCHAR"/>-->
        </collection>
    </resultMap>

    <select id="getOrderListNew" parameterType="java.util.Map" resultMap="orderListMap">
        <if test="veriFlag != null">
            SELECT OUTORD.ORDER_ID,OUTORD.PAY_RESULT,OUTORD.ORDER_STATE,'' as GOODS_STATE,OUTORD.CREATE_DATE,OUTGOODS.GOODS_ORDER_ID
            FROM TF_ORD_MAIN OUTORD JOIN TF_ORD_GOODS_MAIN OUTGOODS ON (OUTORD.ORDER_ID = OUTGOODS.ORDER_ID)
            WHERE OUTORD.ORDER_ID IN
            (SELECT ORD.ORDER_ID
            FROM TF_ORD_MAIN ORD left join TF_ORD_GOODS_MAIN GOODS on ORD.ORDER_ID = GOODS.ORDER_ID
            left join tf_order_item oi on oi.ORDER_ID = ORD.ORDER_ID and oi.ATTR_CODE = 'fixedSerialNumberRH'
            <if test="productOperator != null and productOperator!='' ">
                left JOIN tl_ord_operator_log ol ON ORD.ORDER_ID = ol.ORDER_ID AND ol.NODE_CODE = 'AUDIT'
            </if>
            WHERE 1 = 1
            <if test="mainNumber != null and mainNumber != ''">
                AND GOODS.MAIN_NUMBER = #{mainNumber}
            </if>
            <if test="sourceSystemIdList != null">
                AND ORD.IN_MODE_CODE IN
                <foreach collection="sourceSystemIdList" item="eachSourceSystemId" close=")" open="(" separator=",">
                    #{eachSourceSystemId}
                </foreach>
            </if>
            <if test="goodsTypeList != null">
                AND GOODS.GOODS_TYPE IN
                <foreach collection="goodsTypeList" item="eachGoodsType" close=")" open="(" separator=",">
                    #{eachGoodsType}
                </foreach>
            </if>
            AND oi.ATTR_VALUE = #{networkNumber}
            <if test="startDate != null">
                AND ORD.CREATE_DATE &gt;= CONCAT(#{startDate},' 00:00:00')
            </if>
            <if test="endDate != null">
                AND ORD.CREATE_DATE &lt;= CONCAT(#{endDate},' 23:59:59')
            </if>
            <if test="productOperator != null and productOperator!='' ">
                AND ol.operator_id = #{productOperator}
            </if>
            )
        </if>
        <if test="veriFlag == null">
            SELECT
            '' as STAND_FEE,'' as SALE_FEE, '' as DISPATCH_FEE,GOODS.BUSI_TYPE,CUSTINFO.PSPT_ADDRESSS,GOODS.MAIN_NUMBER,
            c.ADDRESS as ADDRESSS,CUSTINFO.CUST_NAME,'' as PRODUCT_CUST_ID,CUSTINFO.PSPT_ID,
            '' as EXT_WEB_URL,GOODS.GOODS_DESC,GOODS.GOODS_ID,GOODS.GOODS_NAME,GOODS.GOODS_ORDER_ID,'' as GOODS_STATE,GOODS.GOODS_TYPE,'' as GOODS_URL,GOODS.BUSI_TYPE,
            ITEM.ATTR_VALUE AS SVCNUM, ITEM.ATTR_CODE as ITEM_TYPE,'' as PRODUCT_ITEM_ID,
            '' AS SERIAL_NUMBER_TAG,
            ORD.PROVINCE_CODE as AREA_CODE,ORD.CREATE_DATE,ORD.EPARCHY_CODE,ORD.EXT_ORDER_ID,ORD.STAFF_ID as OPER_ID,'' as OPER_ROLE,ORD.REAL_AMOUNT,ORD.ORDER_ID,ORD.ORDER_STATE as ORDER_STATUS,ORD.PAY_RESULT as PAY_STATE,ORD.PAY_MODE as PAY_TYPE,ORD.IN_MODE_CODE as SOURCE_SYSTEM_ID,ORD.DELIVERY_TYPE,'' as ORDER_TAG,
            '' as INVALID_TIME,ORD.REMARK,ORD.ORDER_AMOUNT,
            '' as TRADE_NO,PAY.PAY_TIME as PAY_TIME,PAY.PAY_ORDER_ID as PAY_SEQ,
            '' as LOGISTICS_NO,'' as LOGISTICS_COM, '' as POST_DATE,'' as SIGN_DATE,
            '' as PRODUCT_ORDER_ID,
            '' as FLOW_DEF_ID,
            '' AS TASKKEY,'' as ASSIGNEE_,
            C.CUST_NAME,C.PHONE,C.ADDRESS,C.DELIVERY_TYPE AS DELIVERYTYPE,C.PROVINCE_CODE,C.CITY_CODE,
            C.COUNTY_CODE,'' as EMAIL,'' as POST_CODE,C.POST_REMARK,'' as FIX_PHONE,'' as SELF_GET_ADDRESS_ID
            FROM TF_ORD_MAIN ORD LEFT JOIN TF_ORD_ADDRESS ADDR ON ORD.ORDER_ID = ADDR.ORDER_ID
            LEFT JOIN TF_ORD_GOODS_MAIN GOODS ON ORD.ORDER_ID = GOODS.ORDER_ID
            LEFT JOIN TF_ORD_CUSTINFO CUSTINFO ON ORD.order_id = CUSTINFO.order_id
            LEFT JOIN tf_order_item ITEM ON ORD.ORDER_ID = ITEM.ORDER_ID and ITEM.ATTR_CODE = 'fixedSerialNumberRH'
            LEFT JOIN tf_f_pay_log_settlecenter PAY ON ORD.ORDER_ID = PAY.ORDER_ID and PAY.ORDER_TYPE = '01' and PAY.PAY_RESULT = 'REFUND_SUCCESS'
            LEFT JOIN TF_ORD_ADDRESS C ON ORD.ORDER_ID = C.ORDER_ID
            WHERE ORD.ORDER_ID IN (
            SELECT ORD.ORDER_ID FROM TF_ORD_MAIN ORD LEFT JOIN TF_ORD_ADDRESS ADDR ON(ORD.ORDER_ID=ADDR.ORDER_ID)
            left join tf_ord_goods_main GOODS on ord.order_id = GOODS.order_id
            <if test="productOperator != null and productOperator !='' or curOperator != null and curOperator != ''">
                left JOIN tl_ord_operator_log ol ON ORD.ORDER_ID = ol.ORDER_ID AND ol.NODE_CODE = 'AUDIT'
            </if>
            <where>
                <if test="sourceSystemIdList != null">
                    ORD.IN_MODE_CODE IN
                    <foreach collection="sourceSystemIdList" item="eachSourceSystemId" close=")" open="(" separator=",">
                        #{eachSourceSystemId}
                    </foreach>
                </if>
                <if test="extOrderId != null">
                    AND ORD.EXT_ORDER_ID = #{extOrderId}
                </if>
                <if test="areaCode != null">
                    AND ORD.PROVINCE_CODE = #{areaCode}
                </if>
                <if test="eparchyCode != null">
                    AND ORD.EPARCHY_CODE = #{eparchyCode}
                </if>
                <if test="countyCode != null">
                    AND ORD.CITY_CODE = #{countyCode}
                </if>
                <if test="operId != null">
                    AND ORD.STAFF_ID = #{operId}
                </if>
                <if test="startDate != null and endDate != null">
                    AND ORD.CREATE_DATE &gt;= CONCAT(#{startDate},' 00:00:00') AND ORD.CREATE_DATE &lt;= CONCAT(#{endDate},' 23:59:59')
                </if>
                <if test="custName != null">
                    AND CUSTINFO.CUST_NAME LIKE concat('%',#{custName},'%')
                </if>
                <if test="orderId != null">
                    AND ORD.ORDER_ID = #{orderId}
                </if>
                <if test="goodsOrderId != null">
                    AND GOODS.goods_order_id = #{goodsOrderId}
                </if>
                <if test="orderStateList != null">
                    AND ORD.ORDER_STATE IN
                    <foreach collection="orderStateList" item="eachOrderState" close=")" open="(" separator=",">
                        #{eachOrderState}
                    </foreach>
                </if>
                <if test="psptId != null">
                    AND CUSTINFO.PSPT_ID LIKE concat('%',#{psptId},'%')
                </if>
                <if test="networkNumber != null">
                    AND EXISTS (
                        select 1 from tf_order_item oi
                        where oi.order_id = ORD.order_id
                        and oi.ATTR_CODE = 'fixedSerialNumberRH'
                        and oi.ATTR_VALUE = #{networkNumber}
                    )
                </if>
                <if test="iccid != null">
                    AND EXISTS (
                    select 1 from tf_order_item oi
                    where oi.order_id = ORD.order_id
                    and oi.ATTR_CODE = '0014'
                    and oi.ATTR_TYPE = '1'
                    and oi.ATTR_VALUE like concat('%',#{iccid},'%')
                    )
                </if>
                <if test="receivePhone != null">
                    AND ADDR.PHONE = #{receivePhone}
                </if>
                <if test="goodsTypeList != null">
                    AND GOODS.GOODS_TYPE IN
                    <foreach collection="goodsTypeList" item="eachGoodsType" close=")" open="(" separator=",">
                        #{eachGoodsType}
                    </foreach>
                </if>
                <if test="busiTypeList != null">
                    AND GOODS.BUSI_TYPE IN
                    <foreach collection="busiTypeList" item="eachBusiType" open="(" close=")" separator=",">
                        #{eachBusiType}
                    </foreach>
                </if>
                <if test="fuzzyQuery != null">
                    AND
                    (ORD.ORDER_ID LIKE concat('%',#{fuzzyQuery},'%')
                    OR CUSTINFO.CUST_NAME LIKE concat('%',#{fuzzyQuery},'%')
                    OR GOODS.MAIN_NUMBER LIKE concat('%',#{fuzzyQuery},'%')
                    OR CUSTINFO.PSPT_ID LIKE concat('%',#{fuzzyQuery}),'%')
                </if>
                <if test="curOperator != null">
                    AND ol.operator_id = #{curOperator}
                </if>
                <if test="hasRemark != null">
                    AND ORD.REMARK IS
                    <if test="hasRemark == '1'">
                        NOT
                    </if>
                    NULL
                </if>
                <if test="mainNumber != null and mainNumber != ''">
                    AND GOODS.MAIN_NUMBER = #{mainNumber}
                </if>
                <if test="productOperator != null and productOperator!='' ">
                    AND ol.operator_id = #{productOperator}
                </if>
            </where>
            <if test="networkNumber != null">
                UNION ALL
                SELECT ORD.ORDER_ID FROM TF_ORD_MAIN ORD LEFT JOIN TF_ORD_ADDRESS ADDR ON(ORD.ORDER_ID=ADDR.ORDER_ID)
                <if test="productOperator != null and productOperator!='' or curOperator != null curOperator != ''">
                    left JOIN tl_ord_operator_log ol ON ORD.ORDER_ID = ol.ORDER_ID AND ol.NODE_CODE = 'AUDIT'
                </if>
                <if test="mainNumber != null and mainNumber != ''">
                    LEFT JOIN TF_ORD_GOODS_MAIN GOODS ON ORD.ORDER_ID = GOODS.ORDER_ID
                </if>
                <where>
                    <if test="sourceSystemIdList != null">
                        ORD.SOURCE_SYSTEM_ID IN
                        <foreach collection="sourceSystemIdList" item="eachSourceSystemId" close=")" open="(" separator=",">
                            #{eachSourceSystemId}
                        </foreach>
                    </if>
                    <if test="extOrderId != null">
                        AND ORD.EXT_ORDER_ID = #{extOrderId}
                    </if>
                    <if test="areaCode != null">
                        AND ORD.PROVINCE_CODE = #{areaCode}
                    </if>
                    <if test="eparchyCode != null">
                        AND ORD.EPARCHY_CODE = #{eparchyCode}
                    </if>
                    <if test="countyCode != null">
                        AND ORD.CITY_CODE = #{countyCode}
                    </if>
                    <if test="operId != null">
                        AND ORD.STAFF_ID = #{operId}
                    </if>
                    <if test="startDate != null and endDate != null">
                        AND ORD.CREATE_DATE &gt;= CONCAT(#{startDate},' 00:00:00') AND ORD.CREATE_DATE &lt;= CONCAT(#{endDate},' 23:59:59')
                    </if>
                    <if test="custName != null">
                        AND EXISTS (SELECT 1 FROM TF_ORD_CUSTINFO CUSTINFO
                        WHERE CUSTINFO.ORDER_ID=ORD.ORDER_ID AND CUSTINFO.CUST_NAME LIKE concat('%',#{custName},'%'))
                    </if>
                    <if test="orderId != null">
                        AND ORD.ORDER_ID = #{orderId}
                    </if>
                    <if test="orderStateList != null">
                        AND ORD.ORDER_STATE IN
                        <foreach collection="orderStateList" item="eachOrderState" close=")" open="(" separator=",">
                            #{eachOrderState}
                        </foreach>
                    </if>
                    <if test="psptId != null">
                        AND EXISTS (SELECT 1 FROM TF_ORD_CUSTINFO CUSTINFO
                        WHERE CUSTINFO.ORDER_ID=ORD.ORDER_ID AND CUSTINFO.PSPT_ID LIKE concat('%',#{psptId},'%'))
                    </if>
                    <if test="networkNumber != null">
                        AND EXISTS (
                        select 1 from tf_order_item oi
                        where oi.order_id = ORD.order_id
                        and oi.ATTR_CODE = 'fixedSerialNumberRH'
                        and oi.ATTR_VALUE = #{networkNumber}
                        )
                    </if>
                    <if test="iccid != null">
                        AND EXISTS (
                        select 1 from tf_order_item oi
                        where oi.order_id = ORD.order_id
                        and oi.ATTR_CODE = '0014'
                        and oi.ATTR_TYPE = '1'
                        and oi.ATTR_VALUE like concat('%',#{iccid},'%')
                        )
                    </if>
                    <if test="receivePhone != null">
                        AND ADDR.PHONE = #{receivePhone}
                    </if>
                    <if test="goodsTypeList != null">
                        AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN GOODS WHERE GOODS.ORDER_ID=ORD.ORDER_ID
                        AND GOODS.GOODS_TYPE IN
                        <foreach collection="goodsTypeList" item="eachGoodsType" close=")" open="(" separator=",">
                            #{eachGoodsType}
                        </foreach>
                        )
                    </if>
                    <if test="goodsStateList != null">
                        AND EXISTS (SELECT 1 FROM TF_ORD_GOODS_MAIN GOODS WHERE GOODS.ORDER_ID=ORD.ORDER_ID
                        AND GOODS.GOODS_STATE
                        <if test="goodsStateTag != null">
                            NOT
                        </if>
                        IN
                        <foreach collection="goodsStateList" item="eachGoodsState" open="(" close=")" separator=",">
                            #{eachGoodsState}
                        </foreach>
                        )
                    </if>
                    <if test="fuzzyQuery != null">
                        AND
                        (ORD.ORDER_ID LIKE concat('%',#{fuzzyQuery},'%')
                        OR CUSTINFO.CUST_NAME LIKE concat('%',#{fuzzyQuery},'%')
                        OR GOODS.MAIN_NUMBER LIKE concat('%',#{fuzzyQuery},'%')
                        OR CUSTINFO.PSPT_ID LIKE concat('%',#{fuzzyQuery}),'%')
                    </if>
                    <if test="curOperator != null">
                        AND l.operator_id = #{curOperator}
                    </if>
                    <if test="hasRemark != null">
                        AND ORD.REMARK IS
                        <if test="hasRemark == '1'">
                            NOT
                        </if>
                        NULL
                    </if>
                    <if test="productOperator != null and productOperator!='' ">
                        AND ol.operator_id = #{productOperator}
                    </if>
                    <if test="mainNumber != null and mainNumber != ''">
                        AND GOODS.MAIN_NUMBER = #{mainNumber}
                    </if>
                </where>
            </if>
            ORDER BY ORDER_ID DESC)
        </if>
    </select>

</mapper>
