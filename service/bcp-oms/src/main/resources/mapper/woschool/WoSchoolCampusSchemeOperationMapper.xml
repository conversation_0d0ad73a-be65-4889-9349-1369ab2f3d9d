<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.woschool.WoSchoolCampusSchemeOperationMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    <!--@mbg.generated-->
    <!--@Table wo_school_campus_scheme_operation-->
    <id column="SCHEME_ID" jdbcType="BIGINT" property="schemeId" />
    <id column="MERCHANT_ID" jdbcType="BIGINT" property="merchantId" />
    <result column="MERCHANT_PARTNER_NUMBER" jdbcType="BIGINT" property="merchantPartnerNumber" />
    <result column="MERCHANT_JOIN_NUMBER" jdbcType="BIGINT" property="merchantJoinNumber" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SCHEME_ID, MERCHANT_ID, MERCHANT_PARTNER_NUMBER, MERCHANT_JOIN_NUMBER, REMARK, CREATE_BY, 
    CREATE_TIME, UPDATE_BY, UPDATE_TIME, RESERVE1, RESERVE2, RESERVE3
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus_scheme_operation
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      <if test="merchantId != null">
        and MERCHANT_ID = #{merchantId,jdbcType=BIGINT}
      </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from wo_school_campus_scheme_operation
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      <if test="merchantId != null">
        and MERCHANT_ID = #{merchantId,jdbcType=BIGINT}
      </if>

  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    <!--@mbg.generated-->
    insert into wo_school_campus_scheme_operation (SCHEME_ID, MERCHANT_ID, MERCHANT_PARTNER_NUMBER, 
      MERCHANT_JOIN_NUMBER, REMARK, CREATE_BY, 
      CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
      RESERVE1, RESERVE2, RESERVE3
      )
    values (#{schemeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{merchantPartnerNumber,jdbcType=BIGINT}, 
      #{merchantJoinNumber,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reserve1,jdbcType=VARCHAR}, #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    <!--@mbg.generated-->
    insert into wo_school_campus_scheme_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schemeId != null">
        SCHEME_ID,
      </if>
      <if test="merchantId != null">
        MERCHANT_ID,
      </if>
      <if test="merchantPartnerNumber != null">
        MERCHANT_PARTNER_NUMBER,
      </if>
      <if test="merchantJoinNumber != null">
        MERCHANT_JOIN_NUMBER,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schemeId != null">
        #{schemeId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="merchantPartnerNumber != null">
        #{merchantPartnerNumber,jdbcType=BIGINT},
      </if>
      <if test="merchantJoinNumber != null">
        #{merchantJoinNumber,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    <!--@mbg.generated-->
    update wo_school_campus_scheme_operation
    <set>
      <if test="merchantPartnerNumber != null">
        MERCHANT_PARTNER_NUMBER = #{merchantPartnerNumber,jdbcType=BIGINT},
      </if>
      <if test="merchantJoinNumber != null">
        MERCHANT_JOIN_NUMBER = #{merchantJoinNumber,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      and MERCHANT_ID = #{merchantId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    <!--@mbg.generated-->
    update wo_school_campus_scheme_operation
    set MERCHANT_PARTNER_NUMBER = #{merchantPartnerNumber,jdbcType=BIGINT},
      MERCHANT_JOIN_NUMBER = #{merchantJoinNumber,jdbcType=BIGINT},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR}
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      and MERCHANT_ID = #{merchantId,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultType="com.jsunicom.oms.po.WoSchoolCampusSchemeOperation">
    select scso.*,m.merchant_name as merchantName,yib.society_name societyName  from wo_school_campus_scheme_operation scso inner join merchant m on scso.merchant_id = m.id
    inner join wo_sc_youth_innovate_base yib on yib.society_id=m.society_id and yib.state='1'
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
    <if test="merchantId != null">
      and MERCHANT_ID = #{merchantId,jdbcType=BIGINT}
    </if>
    order by UPDATE_TIME desc
  </select>
</mapper>