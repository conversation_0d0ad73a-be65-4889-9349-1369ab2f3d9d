<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.woschool.WoSchoolCampusSchemeStockMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    <!--@mbg.generated-->
    <!--@Table wo_school_campus_scheme_stock-->
    <id column="SCHEME_ID" jdbcType="BIGINT" property="schemeId" />
    <id column="OPERATOR_TYPE" jdbcType="VARCHAR" property="operatorType" />
    <result column="YW_STOCK_USER" jdbcType="BIGINT" property="ywStockUser" />
    <result column="KD_STOCK_USER" jdbcType="BIGINT" property="kdStockUser" />
    <result column="RH_STOCK_USER" jdbcType="BIGINT" property="rhStockUser" />
    <result column="SHARE_RATIO" jdbcType="VARCHAR" property="shareRatio" />
    <result column="YW_PREVIOUS_STOCK_USER" jdbcType="BIGINT" property="ywPreviousStockUser" />
    <result column="KD_PREVIOUS_STOCK_USER_1" jdbcType="BIGINT" property="kdPreviousStockUser1" />
    <result column="RH_PREVIOUS_STOCK_USER_1" jdbcType="BIGINT" property="rhPreviousStockUser1" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SCHEME_ID, OPERATOR_TYPE, YW_STOCK_USER, KD_STOCK_USER, RH_STOCK_USER, SHARE_RATIO, 
    YW_PREVIOUS_STOCK_USER, KD_PREVIOUS_STOCK_USER_1, RH_PREVIOUS_STOCK_USER_1, REMARK, 
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, RESERVE1, RESERVE2, RESERVE3
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus_scheme_stock
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from wo_school_campus_scheme_stock
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
    <if test="operatorType != null">
      and OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR}
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    <!--@mbg.generated-->
    insert into wo_school_campus_scheme_stock (SCHEME_ID, OPERATOR_TYPE, YW_STOCK_USER, 
      KD_STOCK_USER, RH_STOCK_USER, SHARE_RATIO, 
      YW_PREVIOUS_STOCK_USER, KD_PREVIOUS_STOCK_USER_1, 
      RH_PREVIOUS_STOCK_USER_1, REMARK, CREATE_BY, 
      CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
      RESERVE1, RESERVE2, RESERVE3
      )
    values (#{schemeId,jdbcType=BIGINT}, #{operatorType,jdbcType=VARCHAR}, #{ywStockUser,jdbcType=BIGINT}, 
      #{kdStockUser,jdbcType=BIGINT}, #{rhStockUser,jdbcType=BIGINT}, #{shareRatio,jdbcType=VARCHAR}, 
      #{ywPreviousStockUser,jdbcType=BIGINT}, #{kdPreviousStockUser1,jdbcType=BIGINT}, 
      #{rhPreviousStockUser1,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reserve1,jdbcType=VARCHAR}, #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    <!--@mbg.generated-->
    insert into wo_school_campus_scheme_stock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="schemeId != null">
        SCHEME_ID,
      </if>
      <if test="operatorType != null">
        OPERATOR_TYPE,
      </if>
      <if test="ywStockUser != null">
        YW_STOCK_USER,
      </if>
      <if test="kdStockUser != null">
        KD_STOCK_USER,
      </if>
      <if test="rhStockUser != null">
        RH_STOCK_USER,
      </if>
      <if test="shareRatio != null">
        SHARE_RATIO,
      </if>
      <if test="ywPreviousStockUser != null">
        YW_PREVIOUS_STOCK_USER,
      </if>
      <if test="kdPreviousStockUser1 != null">
        KD_PREVIOUS_STOCK_USER_1,
      </if>
      <if test="rhPreviousStockUser1 != null">
        RH_PREVIOUS_STOCK_USER_1,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="schemeId != null">
        #{schemeId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=VARCHAR},
      </if>
      <if test="ywStockUser != null">
        #{ywStockUser,jdbcType=BIGINT},
      </if>
      <if test="kdStockUser != null">
        #{kdStockUser,jdbcType=BIGINT},
      </if>
      <if test="rhStockUser != null">
        #{rhStockUser,jdbcType=BIGINT},
      </if>
      <if test="shareRatio != null">
        #{shareRatio,jdbcType=VARCHAR},
      </if>
      <if test="ywPreviousStockUser != null">
        #{ywPreviousStockUser,jdbcType=BIGINT},
      </if>
      <if test="kdPreviousStockUser1 != null">
        #{kdPreviousStockUser1,jdbcType=BIGINT},
      </if>
      <if test="rhPreviousStockUser1 != null">
        #{rhPreviousStockUser1,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    <!--@mbg.generated-->
    update wo_school_campus_scheme_stock
    <set>
      <if test="ywStockUser != null">
        YW_STOCK_USER = #{ywStockUser,jdbcType=BIGINT},
      </if>
      <if test="kdStockUser != null">
        KD_STOCK_USER = #{kdStockUser,jdbcType=BIGINT},
      </if>
      <if test="rhStockUser != null">
        RH_STOCK_USER = #{rhStockUser,jdbcType=BIGINT},
      </if>
      <if test="shareRatio != null">
        SHARE_RATIO = #{shareRatio,jdbcType=VARCHAR},
      </if>
      <if test="ywPreviousStockUser != null">
        YW_PREVIOUS_STOCK_USER = #{ywPreviousStockUser,jdbcType=BIGINT},
      </if>
      <if test="kdPreviousStockUser1 != null">
        KD_PREVIOUS_STOCK_USER_1 = #{kdPreviousStockUser1,jdbcType=BIGINT},
      </if>
      <if test="rhPreviousStockUser1 != null">
        RH_PREVIOUS_STOCK_USER_1 = #{rhPreviousStockUser1,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      and OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    <!--@mbg.generated-->
    update wo_school_campus_scheme_stock
    set YW_STOCK_USER = #{ywStockUser,jdbcType=BIGINT},
      KD_STOCK_USER = #{kdStockUser,jdbcType=BIGINT},
      RH_STOCK_USER = #{rhStockUser,jdbcType=BIGINT},
      SHARE_RATIO = #{shareRatio,jdbcType=VARCHAR},
      YW_PREVIOUS_STOCK_USER = #{ywPreviousStockUser,jdbcType=BIGINT},
      KD_PREVIOUS_STOCK_USER_1 = #{kdPreviousStockUser1,jdbcType=BIGINT},
      RH_PREVIOUS_STOCK_USER_1 = #{rhPreviousStockUser1,jdbcType=BIGINT},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR}
    where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
      and OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR}
  </update>
  <select id="selectList" resultType="com.jsunicom.oms.po.WoSchoolCampusSchemeStock">
    select * from wo_school_campus_scheme_stock where SCHEME_ID = #{schemeId,jdbcType=BIGINT}
    <if test="operatorType != null">
      and OPERATOR_TYPE = #{operatorType,jdbcType=VARCHAR}
    </if>
    order by  OPERATOR_TYPE asc
  </select>
</mapper>