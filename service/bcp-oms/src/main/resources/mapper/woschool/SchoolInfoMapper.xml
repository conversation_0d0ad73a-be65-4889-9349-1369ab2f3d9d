<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsunicom.oms.mapper.woschool.SchoolInfoDao">
    <sql id="allColumns">
        id, school_name, merchant_Id, login_title, login_tip, login_logo,
        bg_img, wlcm_img, wlcm_logo, school_manager_id, login_type, create_time,
        create_by, update_time, update_by,wlcm_introduce,benefit_info
    </sql>
    <delete id="delete">
        delete from wo_school_info
        where id = #{id}
    </delete>
    <insert id="save" parameterType="com.jsunicom.oms.model.woshcool.SchoolInfo">
        insert into wo_school_info (id, school_name,merchant_Id,
        <if test="loginTitle != null and loginTitle != ''">
            login_title,
        </if>
        <if test="loginTip != null and loginTip != ''">
            login_tip,
        </if>
        <if test=" loginLogo != null and  loginLogo != ''">
            login_logo,
        </if>
        <if test="bgImg != null and bgImg != ''">
            bg_img,
        </if>
        <if test="wlcmImg != null and wlcmImg != ''">
            wlcm_img,
        </if>
        <if test="wlcmLogo != null and wlcmLogo != ''">
            wlcm_logo,
        </if>
        <if test="loginType != null and loginType != ''">
            login_type,
        </if>
        <if test="wlcmIntroduce != null and wlcmIntroduce != ''">
            wlcm_introduce,
        </if>
        <if test="benefitInfo != null and benefitInfo != ''">
            benefit_info,
        </if>
        <if test="solidLayeringA != null and solidLayeringA != ''">
            solid_layering_a,
        </if>
        <if test="solidLayeringB!= null and solidLayeringB != ''">
            solid_layering_b,
        </if>
        school_manager_id, create_time, create_by,
        update_time, update_by
        )
        values (#{id}, #{schoolName},
        #{merchantId},
        <if test="loginTitle != null and loginTitle != ''">
            #{loginTitle},
        </if>
        <if test="loginTip != null and loginTip != ''">
            #{loginTip},
        </if>
        <if test="loginLogo != null and  loginLogo != ''">
            #{loginLogo},
        </if>
        <if test="bgImg != null and bgImg != ''">
            #{bgImg},
        </if>
        <if test="wlcmImg != null and wlcmImg != ''">
            #{wlcmImg},
        </if>
        <if test="wlcmLogo != null and wlcmLogo != ''">
            #{wlcmLogo},
        </if>
        <if test="loginType != null and loginType != ''">
            #{loginType},
        </if>
        <if test="wlcmIntroduce != null and wlcmIntroduce != ''">
            #{wlcmIntroduce},
        </if>
        <if test="benefitInfo != null and benefitInfo != ''">
            #{benefitInfo},
        </if>
        <if test="solidLayeringA != null and solidLayeringA != ''">
            #{solidLayeringA},
        </if>
        <if test="solidLayeringB!= null and solidLayeringB != ''">
            #{solidLayeringB},
        </if>
        #{schoolManagerId}, now(), #{createBy},
        #{updateTime}, #{updateBy}
        )
    </insert>

    <update id="updateIgnoreNull" parameterType="com.jsunicom.oms.model.woshcool.SchoolInfo">
        update wo_school_info
        <set>
            <if test="schoolName != null and schoolName != ''">
                school_name = #{schoolName},
            </if>
            <if test="merchantId != null">
                merchant_Id = #{merchantId},
            </if>
            <if test="loginTitle != null">
                login_title = #{loginTitle},
            </if>
            <if test="loginTip != null">
                login_tip = #{loginTip},
            </if>
            <if test="loginLogo != null">
                login_logo = #{loginLogo},
            </if>
            <if test="bgImg != null">
                bg_img = #{bgImg},
            </if>
            <if test="wlcmImg != null">
                wlcm_img = #{wlcmImg},
            </if>
            <if test="wlcmLogo != null">
                wlcm_logo = #{wlcmLogo},
            </if>
            <if test="schoolManagerId != null">
                school_manager_id = #{schoolManagerId},
            </if>
            <if test="loginType != null">
                login_type = #{loginType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="wlcmIntroduce != null">
                wlcm_introduce = #{wlcmIntroduce},
            </if>
            <if test="benefitInfo != null">
                benefit_info = #{benefitInfo},
            </if>
            <if test="prodOperId != null">
                prod_oper_id = #{prodOperId},
            </if>
            <if test="develChannel != null">
                devel_channel = #{develChannel},
            </if>
            <if test="wlcmTitle != null">
                wlcmTitle = #{wlcmTitle},
            </if>
            <if test="solidLayeringA != null and solidLayeringA != ''">
                <choose>
                    <when test='solidLayeringA == "imagedelete" '>
                        solid_layering_a=null,
                    </when>
                    <otherwise>
                        solid_layering_a=#{solidLayeringA},
                    </otherwise>
                </choose>
            </if>
            <if test="solidLayeringB!= null and solidLayeringB != ''">
                <choose>
                    <when test='solidLayeringB == "imagedelete" '>
                        solid_layering_b=null,
                    </when>
                    <otherwise>
                        solid_layering_b=#{solidLayeringB},
                    </otherwise>
                </choose>
            </if>
        </set>
        where id = #{id}
    </update>

    <!---条件查询学校列表-->
    <select id="findWithManagerPhone" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfoDto"
            parameterType="map">
        SELECT
        ws.id,
        ws.merchant_id,
        CASE WHEN ws.school_name IS NULL OR ws.school_name='' THEN m.merchant_name ELSE ws.school_name END school_name,
        ws.school_manager_id,
        ws.create_time,
        m.org_code,
        p.mbl_nbr manager_phone,
        p.partner_name manager_name
        FROM wo_school_info ws
        LEFT JOIN merchant m
        ON ws.merchant_Id = m.id
        LEFT JOIN partner p
        ON ws.school_manager_id=p.id
        <where>
            1=1
            <if test="orgCodes != null">
                and m.org_code in
                <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="schoolName != null and schoolName !=''">
                AND ws.school_name like #{schoolName}
            </if>
            <if test="schoolId != null">
                AND ws.id=#{schoolId}
            </if>
            <if test="schoolManagerMbl != null">
                AND p.mbl_nbr = #{schoolManagerMbl}
            </if>
        </where>
        ORDER BY ws.id DESC
    </select>
    <select id="findWithManagerPhoneNew" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfoDto"
            parameterType="map">
        SELECT
        ws.id,
        ws.merchant_id,
        CASE WHEN ws.school_name IS NULL OR ws.school_name='' THEN m.merchant_name ELSE ws.school_name END school_name,
        ws.school_manager_id,
        ws.create_time,
        m.org_code,
        par.partner_name ,
        par.mbl_nbr,
        p.mbl_nbr manager_phone,
        p.partner_name manager_name
        FROM wo_school_info ws
        LEFT JOIN merchant m
        ON ws.merchant_Id = m.id
        LEFT JOIN partner p
        ON ws.school_manager_id=p.id
        LEFT JOIN (select part.merchant_Id ,part.partner_name , part.mbl_nbr
        from merchant mer left join partner part on mer.id =part.merchant_Id where part.is_mer_admin='1') par
        on par.merchant_Id=ws.merchant_Id
        <where>
            1=1
            <if test="orgCodes != null">
                and m.org_code in
                <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="schoolName != null and schoolName !=''">
                AND ws.school_name like #{schoolName}
            </if>
            <if test="schoolId != null">
                AND ws.id=#{schoolId}
            </if>
            <if test="schoolManagerMbl != null">
                AND p.mbl_nbr = #{schoolManagerMbl}
            </if>
        </where>
        ORDER BY ws.id DESC
    </select>
    <!--<select id="findWithManagerPhone" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfoDto" parameterType="map">-->
    <!--SELECT-->
    <!--ws.id,-->
    <!--ws.merchant_id,-->
    <!--ws.school_name,-->
    <!--ws.school_name_short,-->
    <!--ws.school_manager_id,-->
    <!--ws.create_time,-->
    <!--p.mbl_nbr manager_phone,-->
    <!--p.org_code,-->
    <!--p.partner_name manager_name-->
    <!--FROM wo_school_info ws,-->
    <!--partner p-->
    <!--<where>-->
    <!--ws.school_manager_id = p.id-->
    <!--<if test="orgCodes != null">-->
    <!--and p.org_code in-->
    <!--<foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">-->
    <!--#{item}-->
    <!--</foreach>-->
    <!--</if>-->

    <!--<if test="schoolName != null and schoolName !=''">-->
    <!--AND ws.school_name like #{schoolName}-->
    <!--</if>-->
    <!--<if test="schoolManagerMbl!= null">-->
    <!--AND p.mbl_nbr = #{schoolManagerMbl}-->
    <!--</if>-->
    <!--</where>-->
    <!--ORDER BY ws.id DESC-->
    <!--</select>-->
    <!---根据商户id查询学校列表-->
    <select id="findWithMerchantId" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfoDto">
        SELECT
        ws.id,
        ws.merchant_id,
        ws.school_name,
        ws.school_manager_id,
        ws.create_time
        FROM wo_school_info ws
        <where>
            1=1
            <if test="merchantId!= null">
                AND ws.merchant_id = #{merchantId}
            </if>
        </where>
    </select>

    <!--<update id="updateSchoolManager">-->
    <!--update wo_school_info s-->
    <!--set s.school_manager_id=#{schoolManagerId}-->
    <!--where s.id=#{schoolId}-->
    <!--</update>-->

    <select id="findByPK" resultType="com.jsunicom.oms.model.woshcool.SchoolInfo">
        SELECT * FROM wo_school_info
        WHERE id=#{id}
    </select>


    <select id="countSchoolByMgrId" resultType="int">
        select count(id) from wo_school_info where school_manager_id = #{partnerId}
    </select>

    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO wo_school_check_code(school_id,check_code,operator,create_time )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.schoolId},#{item.checkCode},#{item.operator},now() )
        </foreach>
    </insert>
    <!--根据校区经理id查询学校信息-->
    <select id="findList" resultType="com.jsunicom.oms.model.woshcool.WoSchoolInfoDto">
        select ws.prod_oper_id prodOperId,ws.school_name merchantName,m.leg_rep_name legRepName,m.leg_rep_phone legRepPhone,ws.id,ws.login_logo loginLogo ,ws.wlcm_introduce wlcmIntroduce ,ws.merchant_Id merchantId
        from merchant m,wo_school_info ws
        where m.id = ws.merchant_Id and ws.school_manager_id  = #{partnerId} order by ws.create_time desc;
    </select>

    <select id="listSchool" parameterType="long" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfo">
      select wsi.id,
             wsi.school_name,
             wsi.login_logo     schoolLogo,
             wsi.wlcm_introduce schoolDesc,
             wsi.wlcm_img       bannerUrl,
             wscb.CAMPUS_ID
      from wo_school_info wsi
       left join wo_school_campus_bind wscb on wsi.school_manager_id = wscb.BIND_ID and wscb.TYPE = '1'
      where wsi.school_manager_id = #{saleMgrId}
      order by wsi.create_time desc
  </select>

    <select id="getSchool" parameterType="long" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfo">
      select wsi.id,
             wsi.school_name,
             wsi.login_logo     schoolLogo,
             wsi.wlcm_introduce schoolDesc,
             wsi.wlcm_img       bannerUrl,
             wscb.CAMPUS_ID
      from wo_school_info wsi
      left join wo_school_campus_bind wscb on wsi.merchant_Id = wscb.BIND_ID and wscb.TYPE = '0'
      where wsi.merchant_id = #{merchantId}
      order by wsi.create_time desc
  </select>


    <select id="countCheckCode" resultType="int">
      SELECT count(id) from wo_school_check_code where check_code = #{checkCode} and school_id = #{schoolId};
    </select>

    <select id="findSchoolInfoByMerchantId" resultType="com.jsunicom.oms.model.woshcool.SchoolInfo">
      SELECT * from wo_school_info where merchant_Id = #{merchantId}
    </select>

    <select id="getOrgCodeBySchoolId" resultType="java.lang.String">
      select m.org_code from merchant m ,wo_school_info wo where m.id = wo.merchant_Id and wo.id = #{schoolId}
    </select>

    <delete id="deleteWoSchoolInfoByMerchantId">
        delete from wo_school_info
        where merchant_Id = #{merchantId}
    </delete>

    <select id="queryAppletSchoolInfo" resultType="com.jsunicom.oms.common.dto.WoAppletSchoolInfo">
        SELECT wo.id,
        wo.applet_school_name,
        wo.org_code,
        p.id as partnerId,
        wo.school_id,
        wo.applet_school_logo
          FROM  wo_school_applet_info wo
        left join partner p on wo.devel_id = p.devel_id
        <where>
            <if test="id!= null">
                AND wo.id = #{id}
            </if>
            <if test="appletSchoolName!= null">
                AND wo.applet_school_name like #{appletSchoolName}
            </if>
            <if test="orgCode != null">
                AND wo.org_code = #{orgCode}
            </if>
            <if test="quickEntrance != null">
                AND wo.quick_entrance = #{quickEntrance}
            </if>
        </where>
    </select>
    <select id="selectMerchantIdByManagerId" resultType="java.lang.Long">
        select merchant_Id from wo_school_info where school_manager_id=#{partnerId}
    </select>

    <select id="selectBySchoolId" resultType="com.jsunicom.oms.model.woshcool.SchoolInfo">
        select * from wo_school_info where id=#{schoolId}
    </select>

    <select id="selectSchoolIdByMerchantId" resultType="java.lang.Long">
        select id from wo_school_info where merchant_Id=#{merchantId}
    </select>

    <select id="querySchoolAppletBy" resultType="com.jsunicom.oms.model.woshcool.SchoolInfo">
        select * from wo_school_info a
        where exists(select 1 from wo_school_applet_info b where a.id = b.school_id)
        <if test="schoolId != null">
            and a.id = #{schoolId}
        </if>
        <if test="merchantId != null">
            and a.merchant_Id = #{merchantId}
        </if>

    </select>

    <select id="querySchoolAppletBySchool" resultType="map" parameterType="map">
        select c1.school_name as s_name, c1.campus_name,ap.*, p.mbl_nbr as manager_phone ,p.partner_name as manager_name
        , wo.school_name,wo.login_logo, wo.bg_img,wo.wlcm_img,wo.wlcm_logo
        from wo_school_applet_info ap
        left join wo_school_info wo on ap.school_id = wo.id
        LEFT join partner p on p.id=wo.school_manager_id
        LEFT JOIN
        ( SELECT a1.school_name,a1.campus_name,b1.* FROM wo_school_campus a1 LEFT JOIN wo_school_campus_bind b1 ON
        a1.campus_id = b1.campus_id WHERE b1.type='0')
        c1 ON c1.bind_id = wo.merchant_id
        where 1=1
        <if test="schoolName != null">
            and wo.school_name like CONCAT('%',#{schoolName},'%')
        </if>
        <if test="schoolId != null">
            and ap.school_ID = #{schoolId}
        </if>
        <if test="mblNbr != null">
            and p.mbl_nbr = #{mblNbr}
        </if>
        <if test="orgCodes != null">
            and ap.org_code in
            <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by school_id

    </select>

    <select id="querySchoolInfoDetails" resultType="map" parameterType="map">
        SELECT
        ws.id,
        ws.merchant_id,
        CASE WHEN ws.school_name IS NULL OR ws.school_name='' THEN m.merchant_name ELSE ws.school_name END school_name,
        ws.school_manager_id,
        ws.create_time,
        ws.login_type,
        ws.create_by ,
        ws.update_time ,
        ws.update_by ,
        ws.prod_oper_id ,
        ws.devel_channel ,
        ws.login_title ,
        ws.login_tip ,
        ws.login_logo ,
        ws.bg_img ,
        ws.wlcm_img ,
        ws.wlcm_logo ,
        ws.wlcm_introduce,
        ws.benefit_info ,
        ws.wlcmTitle,
        m.org_code,
        par.partner_name ,
        par.mbl_nbr,
        p.mbl_nbr manager_phone,
        p.partner_name manager_name,
        c1.school_name AS s_name,
        c1.campus_name AS campus_name
        FROM wo_school_info ws
        LEFT JOIN merchant m
        ON ws.merchant_Id = m.id
        LEFT JOIN partner p
        ON ws.school_manager_id=p.id
        LEFT JOIN (SELECT part.merchant_Id ,part.partner_name , part.mbl_nbr
        FROM merchant mer LEFT JOIN partner part ON mer.id =part.merchant_Id WHERE part.is_mer_admin='1') par
        ON par.merchant_Id=ws.merchant_Id
        LEFT JOIN
        ( SELECT a1.school_name,a1.campus_name,b1.* FROM wo_school_campus a1 LEFT JOIN wo_school_campus_bind b1 ON
        a1.campus_id = b1.campus_id WHERE b1.type='0')
        c1 ON c1.bind_id = ws.merchant_id
        <where>
            1=1
            <if test="orgCodes != null">
                and m.org_code in
                <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="schoolName != null and schoolName !=''">
                AND ws.school_name like #{schoolName}
            </if>
            <if test="schoolId != null">
                AND ws.id=#{schoolId}
            </if>
            <if test="schoolManagerMbl != null">
                AND p.mbl_nbr = #{schoolManagerMbl}
            </if>
        </where>
        ORDER BY ws.id DESC
    </select>
    <select id="querySchoolAppletBySchoolNew" resultType="map" parameterType="map">
        SELECT
        c1.campus_id AS school_campus_id,
        c1.campus_name AS school_campus_name,
        ap.school_id AS school_id,
        ap.applet_school_name AS applet_school_name,
        ap.org_code AS org_code,
        wo.merchant_id AS merchant_id,
        wo.school_name AS s_name,
        p.mbl_nbr AS manager_phone,
        p.partner_name AS manager_name,
        teamlead.partner_name as team_leader_name,
        ( CASE WHEN c1.campus_id IS NULL THEN '0' ELSE '1' END ) is_bind,
        ( CASE WHEN school_goods.goods_school_id IS NULL THEN 0 ELSE school_goods.picture_count END ) AS picture_count,
        ( CASE WHEN school_goods.goods_school_id IS NULL THEN 0 ELSE school_goods.good_count END ) AS good_count
        FROM
        wo_school_applet_info ap
        LEFT JOIN wo_school_info wo ON ap.school_id = wo.id
        LEFT JOIN partner teamlead on wo.merchant_id=teamlead.merchant_id and teamlead.is_mer_admin='1'
        LEFT JOIN partner p ON p.id = wo.school_manager_id
        LEFT JOIN (
        SELECT
        a1.school_name,
        a1.campus_name,
        b1.*
        FROM
        wo_school_campus a1
        LEFT JOIN wo_school_campus_bind b1 ON a1.campus_id = b1.campus_id
        WHERE
        b1.type = '0'
        ) c1 ON c1.bind_id = wo.merchant_id
        LEFT JOIN (
        SELECT
        school_id AS goods_school_id,
        SUM( ( CASE WHEN goods_pic_url IS NULL THEN 0 ELSE 1 END ) ) AS picture_count,
        COUNT( 1 ) AS good_count
        FROM
        wo_school_goods goods
        GROUP BY
        school_id
        ) school_goods ON ap.school_id = school_goods.goods_school_id
        WHERE
        1 = 1
        <if test="schoolName != null">
            and wo.school_name like CONCAT('%',#{schoolName},'%')
        </if>
        <if test="schoolId != null">
            and ap.school_ID = #{schoolId}
        </if>
        <if test="mblNbr != null">
            and p.mbl_nbr = #{mblNbr}
        </if>
        <if test="appletSchoolName != null">
            and ap.applet_school_name like CONCAT('%',#{appletSchoolName},'%')
        </if>
        <if test="orgCodes != null">
            and ap.org_code in
            <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by school_id

    </select>
</mapper>