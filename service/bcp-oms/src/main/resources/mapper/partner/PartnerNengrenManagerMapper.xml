<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jsunicom.oms.mapper.partner.PartnerNengrenManagerDao" >
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.model.partner.PartnerNengrenManager" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="STAFF_ID" property="staffId" jdbcType="VARCHAR" />
    <result column="STAFF_NAME" property="staffName" jdbcType="VARCHAR" />
    <result column="EPARCHY_CODE" property="eparchyCode" jdbcType="VARCHAR" />
    <result column="EPARCHY_NAME" property="eparchyName" jdbcType="VARCHAR" />
    <result column="PROVINCE_ID" property="provinceId" jdbcType="VARCHAR" />
    <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR" />
    <result column="DEPART_ID" property="departId" jdbcType="VARCHAR" />
    <result column="DEPART_NAME" property="departName" jdbcType="VARCHAR" />
    <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
    <result column="MOTIVATION_MODEL" property="motivationModel" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
    <result column="channel_type" property="channelType" jdbcType="VARCHAR" />

  </resultMap>
  <sql id="Base_Column_List" >
    id, STAFF_ID, STAFF_NAME, EPARCHY_CODE, EPARCHY_NAME, PROVINCE_ID, PROVINCE_NAME, 
    DEPART_ID, DEPART_NAME, LINK_PHONE, MOTIVATION_MODEL, CREATE_TIME, UPDATE_TIME,channel_id,channel_type
  </sql>
  <select id="selectByLinkPhone" resultType="com.jsunicom.oms.model.partner.PartnerNengrenManager" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from partner_nengren_manager
    where STAFF_ID = #{linkPhone} or LINK_PHONE = #{linkPhone}
  </select>

  <select id="selectByOrgCode" resultType="com.jsunicom.oms.model.partner.PartnerNengrenManager" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from partner_nengren_manager
    where EPARCHY_CODE = #{orgCode} limit 1
  </select>

  <select id="selectByMap" resultType="com.jsunicom.oms.model.partner.PartnerNengrenManager" parameterType="java.util.Map" >
    select
    <include refid="Base_Column_List" />
    from partner_nengren_manager
    <where>
      <if test="staffId!= null">
        AND STAFF_ID = #{staffId}
      </if>
      <if test="staffName!= null">
        AND STAFF_NAME = #{staffName}
      </if>
      <if test="eparchyCode!= null">
        AND  EPARCHY_CODE = #{eparchyCode}
      </if>
      <if test="eparchyName!= null">
        AND  EPARCHY_NAME = #{eparchyName}
      </if>
      <if test="provinceId!= null">
        AND  PROVINCE_ID = #{provinceId}
      </if>
      <if test="provinceName!= null">
        AND  PROVINCE_NAME = #{provinceName}
      </if>
      <if test="departId!= null">
        AND  DEPART_ID = #{departId}
      </if>
      <if test="departName!= null">
        AND  DEPART_NAME = #{departName}
      </if>
      <if test="linkPhone!= null">
        AND  LINK_PHONE = #{linkPhone}
      </if>
      <if test="motivationModel!= null">
        AND  MOTIVATION_MODEL = #{motivationModel}
      </if>
      <if test="createTime!= null">
        AND CREATE_TIME = #{createTime}
      </if>
      <if test="updateTime!= null">
        AND UPDATE_TIME = #{updateTime}
      </if>
      <if test="channelId!= null">
        AND channel_id= #{channelId}
      </if>
      <if test="channelType!= null">
        AND channel_type = #{channelType}
      </if>
    </where>

  </select>



</mapper>