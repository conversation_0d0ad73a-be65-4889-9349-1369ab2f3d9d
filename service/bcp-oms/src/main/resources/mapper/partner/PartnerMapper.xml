<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.partner.PartnerDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner( id,partner_type,partner_name,acct_no,partner_cert_no,pic1,pic2,pic3,
                         mbl_nbr,profession_type,org_code,prov_code,prov_name,city_code,city_name,
                         area_code,area_name,address,devel_id,emploee_type,is_mer_admin,commis_acct_id,
                         college_id,dormitory_id,reference_id,society_id,merchant_id,sale_mgr_name,
                         sale_mgr_phone,bus_line,bus_grid,invite_code,devel_channel,gzt_rs,state,
                         audit_by,audit_time,audit_refuse_reson,audit_remark,remark,create_by,create_time,
                         update_by,update_time,reserve1,reserve2,reserve3,reserve4,reserve5)
        VALUES ( #{id},#{partnerType},#{partnerName},#{acctNo},#{partnerCertNo},#{pic1},#{pic2},
                 #{pic3},#{mblNbr},#{professionType},#{orgCode},#{provCode},#{provName},#{cityCode},
                 #{cityName},#{areaCode},#{areaName},#{address},#{develId},#{emploeeType},#{isMerAdmin},
                 #{commisAcctId},#{collegeId},#{dormitoryId},#{referenceId},#{societyId},#{merchantId},#{saleMgrName},
                 #{saleMgrPhone},#{busLine},#{busGrid},#{inviteCode},#{develChannel},#{gztRs},#{state},
                 #{auditBy},#{auditTime},#{auditRemark},#{auditRefuseReson},#{remark},#{createBy},#{createTime},
                 #{updateBy},#{updateTime},#{reserve1},#{reserve2},#{reserve3},#{reserve4},#{reserve5})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO partner( id,partner_type,partner_name,acct_no,partner_cert_no,pic1,pic2,pic3,
        mbl_nbr,profession_type,org_code,prov_code,prov_name,city_code,city_name,
        area_code,area_name,address,devel_id,emploee_type,is_mer_admin,commis_acct_id,
        college_id,dormitory_id,reference_id,society_id,merchant_id,sale_mgr_name,
        sale_mgr_phone,bus_line,bus_grid,invite_code,devel_channel,gzt_rs,state,
        audit_by,audit_time,audit_refuse_reson,audit_remark,remark,create_by,create_time,
        update_by,update_time,reserve1,reserve2,reserve3,reserve4,reserve5)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id},#{item.partnerType},#{item.partnerName},#{item.acctNo},#{item.partnerCertNo},#{item.pic1},#{item.pic2},
              #{item.pic3},#{item.mblNbr},#{item.professionType},#{item.orgCode},#{item.provCode},#{item.provName},#{item.cityCode},
              #{item.cityName},#{item.areaCode},#{item.areaName},#{item.address},#{item.develId},#{item.emploeeType},
              #{item.isMerAdmin},#{item.commisAcctId},#{item.collegeId},#{item.dormitoryId},#{item.referenceId},#{item.societyId},#{item.merchantId}#{item.saleMgrName},#{item.saleMgrPhone},#{item.busLine},#{item.busGrid},#{item.inviteCode},
             #{item.develChannel},#{item.gztRs},#{item.state}, #{item.auditBy},#{item.auditTime},#{item.auditRemark},
             #{item.auditRefuseReson},#{item.remark},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},
             #{item.reserve1},#{item.reserve2},#{item.reserve3},#{item.reserve4},#{item.reserve5})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO partner( id,partner_type,partner_name,scence_id,wx_acct,acct_no,partner_cert_no,
                          mbl_nbr,intent_bus_code,intent_bus_name,refer_name,refer_phone,profession_type,promot_area,
                          org_code,industry,prov_code,prov_name,city_code,city_name,area_code,
                          area_name,address,devel_id,commis_acct_id,create_by,create_time,update_by,
                          update_time,audit_by,audit_time,state,audit_remark,wo_acct,wo_state,
                          wo_remark,merchant_id,wx_entp_uid,pricipal_name,pricipal_cert_no,sale_mgr_name,sale_mgr_phone,
                          bus_line,bus_grid,emploee_type,is_mer_admin,invite_code,gzt_rs,audit_refuse_reson )
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id},#{item.partnerType},#{item.partnerName},#{item.scenceId},#{item.wxAcct},#{item.acctNo},#{item.partnerCertNo},
              #{item.mblNbr},#{item.intentBusCode},#{item.intentBusName},#{item.referName},#{item.referPhone},#{item.professionType},#{item.promotArea},
              #{item.orgCode},#{item.industry},#{item.provCode},#{item.provName},#{item.cityCode},#{item.cityName},#{item.areaCode},
              #{item.areaName},#{item.address},#{item.develId},#{item.commisAcctId},#{item.createBy},#{item.createTime},#{item.updateBy},
              #{item.updateTime},#{item.auditBy},#{item.auditTime},#{item.state},#{item.auditRemark},#{item.woAcct},#{item.woState},
              #{item.woRemark},#{item.merchantId},#{item.wxEntpUid},#{item.pricipalName},#{item.pricipalCertNo},#{item.saleMgrName},#{item.saleMgrPhone},
              #{item.busLine},#{item.busGrid},#{item.emploeeType},#{item.isMerAdmin},#{item.inviteCode},#{item.gztRs},#{item.auditRefuseReson}
              FROM DUAL
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE partner
        <set>
            partner_type=#{partnerType},
            partner_name=#{partnerName},
            acct_no=#{acctNo},
            partner_cert_no=#{partnerCertNo},
            pic1=#{pic1},
            pic2=#{pic2},
            pic3=#{pic3},
            mbl_nbr=#{mblNbr},
            profession_type=#{professionType},
            org_code=#{orgCode},
            prov_code=#{provCode},
            prov_name=#{provName},
            city_code=#{cityCode},
            city_name=#{cityName},
            area_code=#{areaCode},
            area_name=#{areaName},
            address=#{address},
            devel_id=#{develId},
            emploee_type=#{emploeeType},
            is_mer_admin=#{isMerAdmin},
            commis_acct_id=#{commisAcctId},
            college_id=#{collegeId},
            dormitory_id=#{dormitoryId},
            reference_id=#{referenceId},
            society_id=#{societyId},
            merchant_id=#{merchantId},
            sale_mgr_name=#{saleMgrName},
            sale_mgr_phone=#{saleMgrPhone},
            bus_line=#{busLine},
            bus_grid=#{busGrid},
            invite_code=#{inviteCode},
            devel_channel=#{develChannel},
            gzt_rs=#{gztRs},
            state=#{state},
            audit_by=#{auditBy},
            audit_time=#{auditTime},
            audit_refuse_reson=#{auditRemark},
            audit_remark=#{auditRefuseReson},
            remark=#{remark},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
            reserve1=#{reserve1},
            reserve2=#{reserve2},
            reserve3=#{reserve3},
            reserve4=#{reserve4},
            reserve5=#{reserve5}
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateIgnoreNull">
        UPDATE partner
        <set>
            <if test="partnerType!= null">partner_type=#{partnerType},</if>
            <if test="partnerName!= null">partner_name=#{partnerName},</if>
            <if test="acctNo!= null">acct_no=#{acctNo},</if>
            <if test="partnerCertNo!= null">partner_cert_no=#{partnerCertNo},</if>
            <if test="pic1!= null">pic1=#{pic1},</if>
            <if test="pic2!= null">pic2=#{pic2},</if>
            <if test="pic3!= null">pic3=#{pic3},</if>
            <if test="mblNbr!= null">mbl_nbr=#{mblNbr},</if>
            <if test="professionType!= null">profession_type=#{professionType},</if>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="provCode!= null">prov_code=#{provCode},</if>
            <if test="provName!= null">prov_name=#{provName},</if>
            <if test="cityCode!= null">city_code=#{cityCode},</if>
            <if test="cityName!= null">city_name=#{cityName},</if>
            <if test="areaCode!= null">area_code=#{areaCode},</if>
            <if test="areaName!= null">area_name=#{areaName},</if>
            <if test="address!= null">address=#{address},</if>
            <if test="develId!= null">devel_id=#{develId},</if>
            <if test="emploeeType!= null">emploee_type=#{emploeeType},</if>
            <if test="isMerAdmin!= null">is_mer_admin=#{isMerAdmin},</if>
            <if test="commisAcctId!= null">commis_acct_id=#{commisAcctId},</if>
            <if test="collegeId!= null">college_id=#{collegeId},</if>
            <if test="dormitoryId!= null">dormitory_id=#{dormitoryId},</if>
            <if test="referenceId!= null">reference_id=#{referenceId},</if>
            <if test="societyId!= null">society_id=#{societyId},</if>
            <if test="saleMgrName!= null">sale_mgr_name=#{saleMgrName},</if>
            <if test="saleMgrPhone!= null">sale_mgr_phone=#{saleMgrPhone},</if>
            <if test="busLine!= null">bus_line=#{busLine},</if>
            <if test="busGrid!= null">bus_grid=#{busGrid},</if>
            <if test="inviteCode!= null">invite_code=#{inviteCode},</if>
            <if test="develChannel!= null">devel_channel=#{develChannel},</if>
            <if test="gztRs!= null">gzt_rs=#{gztRs},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="auditBy!= null">audit_by=#{auditBy},</if>
            <if test="auditTime!= null">audit_time=#{auditTime},</if>
            <if test="auditRemark!= null">audit_refuse_reson=#{auditRemark},</if>
            <if test="auditRefuseReson!= null">audit_remark=#{auditRefuseReson},</if>
            <if test="remark!= null">remark=#{remark},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
            <if test="reserve1!= null">reserve1=#{reserve1},</if>
            <if test="reserve2!= null">reserve2=#{reserve2},</if>
            <if test="reserve3!= null">reserve3=#{reserve3},</if>
            <if test="reserve4!= null">reserve4=#{reserve4},</if>
            <if test="reserve5!= null">reserve5=#{reserve5},</if>
            merchant_id=#{merchantId}
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE partner
            <set>
                partner_type=#{item.partnerType},
                partner_name=#{item.partnerName},
                acct_no=#{item.acctNo},
                partner_cert_no=#{item.partnerCertNo},
                pic1=#{item.pic1},
                pic2=#{item.pic2},
                pic3=#{item.pic3},
                mbl_nbr=#{item.mblNbr},
                profession_type=#{item.professionType},
                org_code=#{item.orgCode},
                prov_code=#{item.provCode},
                prov_name=#{item.provName},
                city_code=#{item.cityCode},
                city_name=#{item.cityName},
                area_code=#{item.areaCode},
                area_name=#{item.areaName},
                address=#{item.address},
                devel_id=#{item.develId},
                emploee_type=#{item.emploeeType},
                is_mer_admin=#{item.isMerAdmin},
                commis_acct_id=#{item.commisAcctId},
                college_id=#{item.collegeId},
                dormitory_id=#{item.dormitoryId},
                reference_id=#{item.referenceId},
                society_id=#{item.societyId},
                merchant_id=#{item.merchantId},
                sale_mgr_name=#{item.saleMgrName},
                sale_mgr_phone=#{item.saleMgrPhone},
                bus_line=#{item.busLine},
                bus_grid=#{item.busGrid},
                invite_code=#{item.inviteCode},
                devel_channel=#{item.develChannel},
                gzt_rs=#{item.gztRs},
                state=#{item.state},
                audit_by=#{item.auditBy},
                audit_time=#{item.auditTime},
                audit_refuse_reson=#{item.auditRemark},
                audit_remark=#{item.auditRefuseReson},
                remark=#{item.remark},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
                reserve1=#{item.reserve1},
                reserve2=#{item.reserve2},
                reserve3=#{item.reserve3},
                reserve4=#{item.reserve4},
                reserve5=#{item.reserve5}
            </set>
            WHERE id=#{item.id}
        </foreach>
    </update>

    <!--<update id="updateWOAccountById">
        update partner
        set wo_acct = #{wo_acct},
        wo_state = #{wo_state},
        wo_remark = #{wo_remark},
        update_time = current_timestamp()
        where id = #{id}
    </update>-->

    <update id="updateUserInfo" parameterType="com.jsunicom.oms.model.partner.Partner">
        UPDATE partner
        SET update_time=now(),
        <if test="provCode!= null">prov_code=#{provCode},</if>
        <if test="provName!= null">prov_name=#{provName},</if>
        <if test="cityCode!= null">city_code=#{cityCode},</if>
        <if test="cityName!= null">city_name=#{cityName},</if>
        <if test="areaCode!= null">area_code=#{areaCode},</if>
        <if test="areaName!= null">area_name=#{areaName},</if>
        <if test="address!= null">address=#{address}</if>
        WHERE id = #{id}
    </update>

    <update id="updatePartnerDeveID" parameterType="com.jsunicom.oms.model.partner.Partner">
        UPDATE partner
        SET
        <if test="develId!= null">devel_id=#{develId},</if>
        update_time = current_timestamp()
        WHERE id = #{id}
</update>

    <update id="updateEmploeeType">
        update partner
        set emploee_type = #{type},update_time=now()
        where partner_cert_no = #{certNo}
    </update>

    <update id="updateSalseManager">
        update partner a
        set a.sale_mgr_name=#{saleMgrName},
        a.bus_line=#{busLine},
        a.bus_grid=#{busGrid},update_time=now()
        where a.sale_mgr_phone=#{saleMgrPhone}
    </update>

    <!--<update id="updateOperId">
        update partner a
        set a.oper_id=#{operId},update_time=now()
        where a.id=#{id}
    </update>-->

    <update id="updateAgencyNo">
        update partner
        set agency_no=#{agencyNo},update_time=now()
        where id=#{partnerId}
    </update>

    <update id="update2StoreManager">
        update partner a
        set a.is_mer_admin=#{isMerAdmin},
         update_time=now()
        where a.mbl_nbr=#{storephone}
    </update>

    <update id="updateDevelChannelByMerchantId">
        update partner p
        set p.devel_channel = #{develChannel},update_time=now()
        where p.merchant_id=#{merchantId}
    </update>

    <update id="update2UnicomPerson">
        update partner a
        set a.partner_type=#{partnerType},
        a.is_mer_admin=#{isMerAdmin},
        a.merchant_id=#{merchantId},
        a.update_time=now()
        where a.mbl_nbr=#{storephone}
    </update>
    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner
        WHERE id=#{id}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM partner
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id}
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM partner
        WHERE id=#{id}
    </delete>

    <delete id="deleteAll">
        DELETE FROM partner
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE id=#{id}
    </select>

    <select id="findByDevelId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE devel_id=#{develId}
    </select>

    <select id="findByInviteCode" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE binary invite_code=#{inviteCode}
    </select>

    <select id="findByPartner" resultType="com.jsunicom.oms.model.partner.Partner" >
        SELECT * FROM partner
        WHERE
          society_id=#{societyId}
          and state='1'
          <if test="merchantId!= null and merchantId !='' ">
              and merchant_id = #{merchantId}
          </if>
          <if test="partnerName!= null and partnerName !='' ">
              and partner_name like concat('%',#{partnerName},'%')
          </if>
          <if test="mblNbr!= null and mblNbr !='' ">
              and mbl_nbr like concat('%',#{mblNbr},'%')
          </if>
    </select>

	<select id="findById" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE id=#{id}
    </select>

    <select id="findPartnerByUserAcct" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*, m.rights_flag FROM partner p LEFT JOIN merchant m ON p.merchant_id = m.id
        WHERE p.acct_no=#{acctNo}
    </select>

    <select id="findPartnerByUserAcctAndOrgCode" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*, m.rights_flag FROM partner p LEFT JOIN merchant m ON p.merchant_id = m.id
        <where>
            p.acct_no=#{acctNo}
            <if test="orgCode!= null and orgCode !='' and orgCode!='root' ">
                and p.org_code = #{orgCode}
            </if>
        </where>
    </select>

    <select id="findPartnerByFlowId" resultType="com.jsunicom.oms.model.partner.Partner">
        select DISTINCT p.org_code from partner p
                 inner join commis_pay_record cpr on p.id=cpr.partner_id
                  where cpr.flow_id=#{flowId}
    </select>

    <select id="findPartnerBySceneId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE scence_id=#{scenceId}
    </select>

    <select id="findPartnerByMblNbr" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE mbl_nbr=#{mblNbr}
    </select>

    <select id="findPartnerDevelIdByMblNbr" resultType="com.jsunicom.oms.po.WoScYiMember">
        SELECT b.* FROM partner a left join WO_SC_YI_MEMBER b on a.partner_cert_no = b.CERT_ID and b.state = '1'
        WHERE  a.state = '0' and a.mbl_nbr=#{mblNbr}
    </select>

    <select id="findPartnerByWoAcct" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE wo_acct=#{woAcct}
    </select>

    <select id="findPartnerByCertNo" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE partner_cert_no=#{partnerCertNo}
    </select>

    <select id="checkPartner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE partner_cert_no=#{partnerCertNo} OR mbl_nbr=#{mblNbr}
    </select>

    <select id="findRefusePartner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE state=#{state}
    </select>

    <select id="queryByState" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.`org_code`,p.`id`,p.`bus_line`,p.`emploee_type`,p.`partner_type`,p.`is_mer_admin`, tpm.id AS tag_id
        FROM partner p
        LEFT JOIN tag_partner_merchant tpm ON p.id =tpm.partner_merchant_id AND tpm.first_type='0'
        WHERE p.state='1' AND p.update_time &gt;= date_format(#{date},'%Y-%m-%d') AND p.update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>
    <select id="findBySaleMgrPhone" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE sale_mgr_phone=#{saleMgrPhone}
    </select>

    <select id="checkMblNbr" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
        <where>
            <if test="id!= null">
                AND id != #{id}
            </if>
            <if test="mblNbr!= null">
                AND mbl_nbr = #{mblNbr}
            </if>
        </where>
    </select>

    <select id="checkCertNo" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
        <where>
            <if test="id!= null">
                AND id != #{id}
            </if>
            <if test="partnerCertNo!= null">
                AND partner_cert_no = #{partnerCertNo}
            </if>
        </where>
    </select>

    <select id="checkDevelId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
        <where>
            <if test="id!= null">
                AND id != #{id}
            </if>
            <if test="develId!= null">
                AND devel_id = #{develId}
            </if>
        </where>
    </select>

    <select id="getMemberCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
        <where>
            <if test="referPhone != null">
                AND refer_phone = #{referPhone}
            </if>
            AND state = '1'
        </where>
    </select>

    <select id="findMerchantManager" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE merchant_id = #{merchantId}
        and is_mer_admin = '1'
        and state='1'
    </select>

    <!--查询商户对应店长信息-->
    <select id="findMerchantAdminByMerchantId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
        and is_mer_admin='1'
        and state='1'
    </select>

    <select id="findMerchantAdminByMerchantIdAndLegRepPhone" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE merchant_id = #{merchantId}
        and mbl_nbr=#{LegRepPhone}
        and is_mer_admin = '1'
        and state='1'
    </select>
    <!--根据手机号查询销售经理信息-->
    <select id="findMerchantSalesMan" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE mbl_nbr = #{saleMgrPhone}
        and state='1'
    </select>
    <select id="findNoWOAccount" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE wo_acct is null
        and ( wo_state = '1' or wo_state is null)
        <!-- and DATEDIFF(CURDATE(),update_time)=#{day} -->
        and state='1'
    </select>
    <select id="findPartnerUpdateYesterday" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        WHERE date(update_time) = #{updateTime}
        and state='1'
    </select>
    <select id="findMerchantPartner" resultType="com.jsunicom.oms.dto.partner.MerchantPartner">
        SELECT p.id,p.partner_type,p.partner_name,p.partner_cert_no,p.mbl_nbr
        ,p.intent_bus_code,p.intent_bus_name,p.refer_phone,p.profession_type,p.promot_area,p.org_code,p.industry
        ,p.merchant_id,p.prov_code,p.prov_name,p.city_code,p.city_name,p.area_code,p.area_name,p.address
        ,p.devel_id,p.state,m.bus_license,m.merchant_name,p.oper_id
        FROM partner p JOIN merchant m
        ON p.merchant_id = m.id
        <where>
            <if test="partnerCertNo!= null">
                AND p.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND p.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND p.org_code = #{orgCode}
            </if>
            <if test="state!= null">
                AND p.state = #{state}
            </if>
            <if test="busLicense!= null">
                AND m.bus_license LIKE #{busLicense}
            </if>
            <if test="merchantName!= null">
                AND m.merchant_name LIKE #{merchantName}
            </if>
        </where>
        ORDER BY p.create_time DESC, p.org_code ASC
    </select>

    <select id="findMerchantPartnerAll" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*
        FROM partner p JOIN merchant m
        ON p.merchant_id = m.id
        WHERE p.state='1'
        AND p.partner_type!='0'
    </select>

    <select id="findSuccPartner" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.*, b.merchant_name, b.merchant_type,b.commis_flag, b.rights_flag
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            <if test="deleteFlag!= null">
                AND a.delete_flag = #{deleteFlag}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>

    <select id="findQudaoPartnerList" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.*, b.merchant_name, b.merchant_type,b.commis_flag, b.rights_flag
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="merchantType!= null">
                AND b.merchant_type = #{merchantType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            <if test="deleteFlag!= null">
                AND a.delete_flag = #{deleteFlag}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>

    <select id="findSuccPartnerExport" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.*, b.merchant_name, b.merchant_type,c.classification_first_id as firstClassificationId,c.classification_id as classificationId
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id left join partner_classification c on a.id=c.partner_id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>

    <select id="findQudaoPartnerExport" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.*, b.merchant_name, b.merchant_type,c.classification_first_id as firstClassificationId,c.classification_id as classificationId
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id left join partner_classification c on a.id=c.partner_id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="merchantType!= null">
                AND b.merchant_type = #{merchantType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>


    <select id="findPartnerRolesExport" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        select * from (
        select a.id, a.create_time,a.org_code,GROUP_CONCAT(case WHEN b.role_name is null then  r.role_id  else b.role_name end separator '、') as selectRoles from partner a left join partner_role r on
        a.id=r.partner_id
        LEFT JOIN partner_auth_role b on r.role_id=b.id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
        GROUP BY a.id
        ) z
        order by z.create_time DESC,
        z.org_code  ASC
    </select>

    <select id="findQudaoPartnerRolesExport" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        select * from (
        select a.id, a.create_time,a.org_code,m.merchant_type,GROUP_CONCAT(case WHEN b.role_name is null then  r.role_id  else b.role_name end separator '、') as selectRoles from partner a left join partner_role r on
        a.id=r.partner_id LEFT JOIN merchant m on a.merchant_id=m.id
        LEFT JOIN partner_auth_role b on r.role_id=b.id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="merchantType!= null">
                AND m.merchant_type = #{merchantType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
        GROUP BY a.id
        ) z
        order by z.create_time DESC,
        z.org_code  ASC
    </select>


    <select id="findPartnerExportUnion" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        select * from (
        SELECT a.*, b.merchant_name, b.merchant_type,c.classification_first_id as
        firstClassificationId,c.classification_id as classificationId,GROUP_CONCAT(r.role_id) as selectRoles
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id left join partner_classification c on a.id=c.partner_id left join partner_role r on
        a.id=r.partner_id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
        GROUP BY a.id
        ) z
        order by z.create_time DESC,
        z.org_code  ASC
    </select>

    <select id="findAuditPartner" resultType="com.jsunicom.oms.model.partner.Partner" parameterType="map">
        SELECT
            a.*,
            b.merchant_type,
            wscb.CAMPUS_ID                           as campusId,
            wsc.CAMPUS_NAME as campusName,
            CASE
                WHEN (a.partner_type = '2' AND a.is_mer_admin = '1') THEN 1
                WHEN (a.partner_type = '2' AND a.is_mer_admin = '0') THEN 2
                ELSE 0
            END                                  AS campusMemberType
        FROM partner a
        LEFT JOIN merchant b ON a.merchant_id = b.id
        LEFT JOIN wo_school_campus_bind wscb ON wscb.BIND_ID = a.merchant_id AND wscb.TYPE = '0'
        lEFT JOIN wo_school_campus wsc ON wsc.CAMPUS_ID = wscb.CAMPUS_ID
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="busGrid!= null">
                AND a.bus_grid = #{busGrid}
            </if>
            <if test="campusMemberType != null and campusMemberType == 0">
                AND a.partner_type = '0'
            </if>
            <if test="campusMemberType != null and campusMemberType == 1">
                AND a.is_mer_admin = '1'
                AND a.partner_type = '2'
            </if>
            <if test="campusMemberType != null and campusMemberType == 2">
                AND a.is_mer_admin = '0'
                AND a.partner_type = '2'
            </if>
            AND a.state in ('0', '4')
        </where>
        order by a.create_Time desc,a.org_code asc
    </select>

    <select id="findAuditPartner2" resultType="com.jsunicom.oms.model.partner.Partner" parameterType="map">
        SELECT
        a.*,
        b.merchant_type,
        wscb.CAMPUS_ID                           as campusId,
        wsc.CAMPUS_NAME as campusName,
        CASE
        WHEN (a.partner_type = '2' AND a.is_mer_admin = '1') THEN 1
        WHEN (a.partner_type = '2' AND a.is_mer_admin = '0') THEN 2
        ELSE 0
        END                                  AS campusMemberType
        FROM partner a
        LEFT JOIN merchant b ON a.merchant_id = b.id
        LEFT JOIN wo_school_campus_bind wscb ON wscb.BIND_ID = a.merchant_id AND wscb.TYPE = '0'
        lEFT JOIN wo_school_campus wsc ON wsc.CAMPUS_ID = wscb.CAMPUS_ID
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="saleMgrName!= null">
                AND a.sale_mgr_name LIKE #{saleMgrName}
            </if>
            <if test="saleMgrPhone!= null">
                AND a.sale_mgr_phone LIKE #{saleMgrPhone}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="busGrid!= null">
                AND a.bus_grid = #{busGrid}
            </if>
            <if test="campusMemberType != null and campusMemberType == 0">
                AND a.partner_type = '0'
            </if>
            <if test="campusMemberType != null and campusMemberType == 1">
                AND a.is_mer_admin = '1'
                AND a.partner_type = '2'
            </if>
            <if test="campusMemberType != null and campusMemberType == 2">
                AND a.is_mer_admin = '0'
                AND a.partner_type = '2'
            </if>
            AND a.state in ('0', '4')
        </where>
    </select>

    <select id="findMember" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT id,partner_type,partner_name,scence_id,wx_acct,acct_no,partner_cert_no,mbl_nbr
        ,intent_bus_code,intent_bus_name,refer_name,refer_phone,profession_type,promot_area,org_code
        ,industry,prov_code,prov_name,city_code,city_name,area_code,area_name
        ,address,devel_id,commis_acct_id,create_by,create_time,update_by,update_time
        ,audit_by,audit_time,state,audit_remark,wo_acct,wo_state,wo_remark
        ,merchant_id,wx_entp_uid,pricipal_name,pricipal_cert_no,sale_mgr_name,sale_mgr_phone,bus_line
        ,bus_grid,emploee_type,is_mer_admin,invite_code,gzt_rs,audit_refuse_reson,oper_id,rights_coupon,register_path
        FROM partner
        <where>
            <if test="referPhone != null">
                AND refer_phone = #{referPhone}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            AND (refer_phone IS NOT NULL AND state != '3')
        </where>
        order by create_Time desc,org_code asc
    </select>

    <select id="findDeliveryManagers" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT *
        FROM partner
        <where>
            <if test="mblNbr != null">
                AND mbl_nbr = #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            and oper_id is not null
            and oper_id != ''
        </where>
    </select>


    <select id="find" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT id,partner_type,partner_name,scence_id,wx_acct,acct_no,partner_cert_no,mbl_nbr
        ,intent_bus_code,intent_bus_name,refer_name,refer_phone,profession_type,promot_area,org_code
        ,industry,prov_code,prov_name,city_code,city_name,area_code,area_name
        ,address,devel_id,commis_acct_id,create_by,create_time,update_by,update_time
        ,audit_by,audit_time,state,audit_remark,wo_acct,wo_state,wo_remark
        ,merchant_id,wx_entp_uid,pricipal_name,pricipal_cert_no,sale_mgr_name,sale_mgr_phone,bus_line
        ,bus_grid,emploee_type,is_mer_admin,invite_code,gzt_rs,audit_refuse_reson,oper_id,rights_coupon
        FROM partner
        <where>
            <if test="id!= null">
                AND id = #{id}
            </if>
            <if test="partnerType!= null">
                AND partner_type = #{partnerType}
            </if>
            <if test="partnerName!= null">
                AND partner_name = #{partnerName}
            </if>
            <if test="scenceId!= null">
                AND scence_id = #{scenceId}
            </if>
            <if test="wxAcct!= null">
                AND wx_acct = #{wxAcct}
            </if>
            <if test="acctNo!= null">
                AND acct_no = #{acctNo}
            </if>
            <if test="partnerCertNo!= null">
                AND partner_cert_no = #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND mbl_nbr = #{mblNbr}
            </if>
            <if test="intentBusCode!= null">
                AND intent_bus_code = #{intentBusCode}
            </if>
            <if test="intentBusName!= null">
                AND intent_bus_name = #{intentBusName}
            </if>
            <if test="referName!= null">
                AND refer_name = #{referName}
            </if>
            <if test="referPhone!= null">
                AND refer_phone = #{referPhone}
            </if>
            <if test="professionType!= null">
                AND profession_type = #{professionType}
            </if>
            <if test="promotArea!= null">
                AND promot_area = #{promotArea}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            <if test="industry!= null">
                AND industry = #{industry}
            </if>
            <if test="provCode!= null">
                AND prov_code = #{provCode}
            </if>
            <if test="provName!= null">
                AND prov_name = #{provName}
            </if>
            <if test="cityCode!= null">
                AND city_code = #{cityCode}
            </if>
            <if test="cityName!= null">
                AND city_name = #{cityName}
            </if>
            <if test="areaCode!= null">
                AND area_code = #{areaCode}
            </if>
            <if test="areaName!= null">
                AND area_name = #{areaName}
            </if>
            <if test="address!= null">
                AND address = #{address}
            </if>
            <if test="develId!= null">
                AND devel_id = #{develId}
            </if>
            <if test="commisAcctId!= null">
                AND commis_acct_id = #{commisAcctId}
            </if>
            <if test="createBy!= null">
                AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
                AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
                AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
                AND update_time = #{updateTime}
            </if>
            <if test="auditBy!= null">
                AND audit_by = #{auditBy}
            </if>
            <if test="auditTime!= null">
                AND audit_time = #{auditTime}
            </if>
            <if test="state!= null">
                AND state = #{state}
            </if>
            <if test="auditRemark!= null">
                AND audit_remark = #{auditRemark}
            </if>
            <if test="woAcct!= null">
                AND wo_acct = #{woAcct}
            </if>
            <if test="woState!= null">
                AND wo_state = #{woState}
            </if>
            <if test="woRemark!= null">
                AND wo_remark = #{woRemark}
            </if>
            <if test="merchantId!= null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="wxEntpUid!= null">
                AND wx_entp_uid = #{wxEntpUid}
            </if>
            <if test="pricipalName!= null">
                AND pricipal_name = #{pricipalName}
            </if>
            <if test="pricipalCertNo!= null">
                AND pricipal_cert_no = #{pricipalCertNo}
            </if>
            <if test="saleMgrName!= null">
                AND sale_mgr_name = #{saleMgrName}
            </if>
            <if test="saleMgrPhone!= null">
                AND sale_mgr_phone = #{saleMgrPhone}
            </if>
            <if test="busLine!= null">
                AND bus_line = #{busLine}
            </if>
            <if test="busGrid!= null">
                AND bus_grid = #{busGrid}
            </if>
            <if test="emploeeType!= null">
                AND emploee_type = #{emploeeType}
            </if>
            <if test="isMerAdmin!= null">
                AND is_mer_admin = #{isMerAdmin}
            </if>
            <if test="inviteCode!= null">
                AND invite_code = #{inviteCode}
            </if>
            <if test="gztRs!= null">
                AND gzt_rs = #{gztRs}
            </if>
            <if test="auditRefuseReson!= null">
                AND audit_refuse_reson = #{auditRefuseReson}
            </if>
        </where>
    </select>

    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner
    </select>

    <select id="findReferPhoneByPage" resultType="java.lang.String">
        SELECT refer_phone FROM partner where refer_phone is not null GROUP BY refer_phone
    </select>


    <select id="findPartnerByReferPhone" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner where refer_phone = #{referPhone}
    </select>

    <select id="findPartnerForShopkeeper" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
		WHERE merchant_id IN (SELECT id FROM merchant WHERE merchant_type = '4') and state ='1'
    </select>

    <select id="findPartnerByMerchantId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
        and is_mer_admin='0'
        and state='1' order by create_time desc
    </select>
    <select id="findPartnerByMerchantIdAndIsadmin" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
          and is_mer_admin='1'
          and state='1' order by create_time desc
    </select>

    <select id="findPartnerByMerchantIds" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where state='1'
        <if test="merchantIds != null">
            and merchant_id in
            <foreach collection="merchantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findPartnerByMerchantIdAll" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
        and state='1' order by create_time desc
    </select>



    <select id="findMembers" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner
        where merchant_id = #{merchantId}
        and is_mer_admin='0'
        AND refer_phone = #{referPhone}
        and state='1' order by create_time desc
    </select>

    <select id="findPartnersByMerchantId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner where merchant_id = #{merchantId}
    </select>

    <select id="findPartnersByMainMerchantId" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT b.* FROM merchant a , partner b where a.head_office = #{merchantId} and b.merchant_id = a.id
    </select>
    <select id="findByCertNo" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT id, partner_type, partner_name, scence_id, wx_acct, acct_no, partner_cert_no
        	, mbl_nbr, contact_name, contact_cert_no, intent_bus_code, intent_bus_name, refer_name
        	, refer_phone, profession_type, promot_area, org_code, industry, chain_mer_id, prov_code
        	, prov_name, city_code, city_name, area_code, area_name, address, devel_id, commis_acct_id
        	, create_by, create_time, update_by, update_time, audit_by, audit_time, state, audit_remark
        	, wo_acct, wo_state, wo_remark, merchant_id, wx_entp_uid, pricipal_name, pricipal_cert_no
        	, sale_mgr_name, sale_mgr_phone, bus_line, bus_grid, emploee_type, is_mer_admin
        	, invite_code, gzt_rs, audit_refuse_reson, devel_channel, oper_id, rights_coupon
          FROM partner
         WHERE partner_cert_no = #{partnerCertNo}
    </select>

    <select id="findAllNoQrcodeBranches" resultType="com.jsunicom.oms.model.partner.Partner" parameterType="map">
        select p.*
          from partner p ,merchant m
         where p.merchant_id=m.id
           and m.head_office=#{headOffice}
           and p.is_mer_admin=1
           and p.state='1'
           and not EXISTS
           (select 1 from partner_qr_code q where p.id=q.partner_id and q.qr_code_type=1 and q.promote_id=#{promotId})
    </select>

    <select id="findAllBranches" resultType="com.jsunicom.oms.model.partner.Partner" parameterType="long">
        select p.*
          from partner p,merchant m
         where p.merchant_id=m.id
           and m.head_office=#{headOffice}
           and p.is_mer_admin = '1'
           and p.state='1'
    </select>

    <select id="findBD4DroiyanThirdQuarter" resultType="int">
        select count(*)
          from partner_bd_employ p
         where
             p.acct_no = #{acctNo}
    </select>
	<select id="getAllWhiteListPtner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner WHERE id NOT IN (
			SELECT p.id FROM
			whitelist_partner w,
			partner p
		WHERE
			w.cert_no = p.partner_cert_no
		AND w.type = '1'
	) AND state = '1'
    </select>
    <select id="findPartnersByMap" resultType="com.jsunicom.oms.model.partner.Partner">
            select p.*
            from partner p
             where 1=1
            <if test="merchantId!= null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="referPhone!= null">
                AND refer_phone = #{referPhone}
            </if>
            <if test="nameOrPhone!= null">
                AND (partner_name like #{nameOrPhone}  or acct_no like #{nameOrPhone})
            </if>
    </select>
    <select id="getPartnerCount" resultType="int">
        select count(*)
        from partner p
        where 1=1
        <if test="merchantId!= null">
            AND merchant_id = #{merchantId}
        </if>
        <if test="referPhone!= null">
            AND refer_phone = #{referPhone}
        </if>
        and state='1'
    </select>

    <select id="findCountByDateWithOrgCode" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT COUNT(*) as count, org_code FROM partner
        where <![CDATA[ create_time >= #{beginDate} ]]> <![CDATA[ AND create_time <= #{endDate} ]]>
        group by org_code
    </select>

    <select id="findPartner" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner
        where 1=1
         <if test="merchantId!=null">
             and merchant_id=#{merchantId}
         </if>
        <if test="isMerAdmin!=null">
            and is_mer_admin=#{isMerAdmin}
        </if>
        <if test="partnerType!=null">
            and partner_type  <![CDATA[!= ]]> #{partnerType}
        </if>

    </select>

    <select id="queryPartnerByMblNbr" resultType="com.jsunicom.oms.model.partner.Partner">
         SELECT a.*, b.merchant_name, b.merchant_type
          FROM partner a LEFT JOIN merchant b
          ON a.merchant_id = b.id
          WHERE mbl_nbr=#{mblNbr}
    </select>


<!--=======================================================================new=======================================================================================-->

    <select id="getMerchantPartnerCount" resultType="int">
        select count(*)
        from partner p
        where 1=1
        <if test="merchantId!= null">
            AND merchant_id = #{merchantId}
        </if>
        <if test="referPhone!= null">
            AND refer_phone = #{referPhone}
        </if>
        and state='1'
    </select>

	<select id="findPartnerByPhoneNum" resultType="com.jsunicom.oms.model.partner.Partner">
		SELECT * FROM partner WHERE mbl_nbr IN
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

    <select id="queryAddModifyPartner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*
        FROM partner p JOIN merchant m
        ON p.merchant_id = m.id
        WHERE p.state in ('1','2')
        AND p.partner_type!='0'
        AND p.update_time &gt;= date_format(#{date},'%Y-%m-%d') AND p.update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>

    <select id="queryDeletePartner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*
        FROM partner_del_bak p JOIN merchant m
        ON p.merchant_id = m.id
        WHERE  p.partner_type!='0'
        AND p.operate_time  &gt;= date_format(#{date},'%Y-%m-%d') AND p.operate_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>
    <select id="queryPartner" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT p.*
        FROM partner p
        WHERE  p.partner_type='0'
        and merchant_id=-1
        AND p.update_time  &gt;= date_format(#{date},'%Y-%m-%d') AND p.update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>

    <select id="getPartnerInfo" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from (
        (SELECT c.id, c.partner_type,c.partner_name,c.mbl_nbr,c.partner_cert_no,c.devel_id,
        c.mapping_point_code,c.prov_code,c.prov_name,c.city_code,c.city_name,c.area_code,c.area_name,c.address,
        c.business_classification,c.create_time,c.audit_time,'已删除' as state,
        c.org_code,d.id as merchnatKey,d.merchant_name,
        d.merchant_type
        FROM partner_del_bak c LEFT JOIN merchant_del_bak d
        ON c.merchant_id = d.id where 1=1
        <if test="orgCode!= null">
            AND c.org_code = #{orgCode}
        </if>
        <if test="partnerCertNo!= null">
            AND c.partner_cert_no LIKE #{partnerCertNo}
        </if>
        <if test="mblNbr!= null">
            AND c.mbl_nbr LIKE #{mblNbr}
        </if>
        )

        union all
        (SELECT c.id, c.partner_type,c.partner_name,c.mbl_nbr,c.partner_cert_no,c.devel_id,
        c.mapping_point_code,c.prov_code,c.prov_name,c.city_code,c.city_name,c.area_code,c.area_name,c.address,
        c.business_classification,c.create_time,c.audit_time,
        CASE WHEN c.state='0' THEN '待审核' when c.state='1' THEN '启用' when c.state='2' THEN '禁用' when c.state='3' THEN '审核拒绝' when c.state='4' THEN '待补充资料' else '其他' end state,
        c.org_code,d.id as merchnatKey,d.merchant_name,
        d.merchant_type
        FROM partner c LEFT JOIN merchant d
        ON c.merchant_id = d.id where 1=1
        <if test="orgCode!= null">
            AND c.org_code = #{orgCode}
        </if>
        <if test="partnerCertNo!= null">
            AND c.partner_cert_no LIKE #{partnerCertNo}
        </if>
        <if test="mblNbr!= null">
            AND c.mbl_nbr LIKE #{mblNbr}
        </if>

        )

    ) y


    </select>

    <select id="queryNoAudit"  resultType="java.lang.Long">
        SELECT merchant_id FROM partner  WHERE state = '0'  AND partner_type != '0' and is_mer_admin='1'
        AND update_time &gt;= DATE_FORMAT(#{date},'%Y-%m-%d') AND update_time &lt; DATE_ADD(DATE_FORMAT(#{date},'%Y-%m-%d'),INTERVAL 1 DAY)
    </select>


    <select id="queryPartnerInfoStatis"  resultType="com.jsunicom.oms.model.partner.PartnerInfoStatis">
        SELECT
  id,
  partner_id,
  partner_name,
  partner_phone,
  active_days,
  register_days,
  firm_index,
  firm_percent,
  order_count,
  order_ranking,
  order_percent,
  orders_everyday as orderEveryDay,
  friends_count,
  goods_Name,
  active_index,
  commis_count,
  commis_total,
  commis_percent,
  partner_role,
  valor_index
FROM
  data_statistics where partner_phone = #{phoneNo}
    </select>
    <select id="queryGroundPromotionById"  resultType="int">
        SELECT count(1)
        FROM
        ground_promotion where partner_id = #{partnerId}
    </select>


    <select id="queryAllPartnerInfo"  resultType="com.jsunicom.oms.model.partner.WowPartnerInfo">
        SELECT p.id, p.partner_name, p.partner_cert_no, p.org_code, p.devel_id, p.mbl_nbr, p.wo_acct, acct.card_no, acct.card_name,
            CASE
            WHEN w.id IS NULL THEN '0'
            ELSE '1'
            END AS whiteFlag
            FROM partner p LEFT JOIN partner_bank_account acct ON p.id = acct.partner_id
            LEFT JOIN whitelist_partner w ON p.partner_cert_no = w.cert_no
          WHERE p.state IN('1', '2') AND p.partner_type = '0'

          UNION ALL

          SELECT p.id, p.partner_name, p.partner_cert_no , p.org_code, p.devel_id, p.mbl_nbr, p.wo_acct, acct.card_no, acct.card_name,
            CASE
            WHEN m.commis_flag = 0  THEN '0'
            WHEN w.id IS NULL THEN '0'
            ELSE '1'
            END AS whiteFlag
            FROM partner p LEFT JOIN partner_bank_account acct ON p.id = acct.partner_id
            LEFT JOIN merchant m ON m.id = p.merchant_id
            LEFT JOIN whitelist_merchant w ON m.id = w.merchant_id
          WHERE p.state IN('1', '2') AND <![CDATA[p.partner_type <> '0']]>
    </select>

    <select id="getExportPartnerCount" resultType="int">
        SELECT COUNT(*) FROM partner a
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>

    <select id="getQudaoExportPartnerCount" resultType="int">
        SELECT COUNT(*) FROM partner a, merchant b
        <where>
            a.merchant_id=b.id
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="merchantType!= null">
                AND b.merchant_type = #{merchantType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1' OR a.state = '2')
        </where>
    </select>


    <update id="updateDevIdByPhone">
        update partner
          set devel_id = #{develId}, update_time= DATE_FORMAT(sysdate(),'%Y-%m-%d %H:%i:%s')
         where mbl_nbr = #{mblNbr}
    </update>


    <update id="updateRefInfo">
        update partner set refer_phone='' ,refer_name=''  where mbl_nbr = #{mblNbr}
    </update>

    <update id="updateDeleteFlag">
        update partner a
        set a.delete_flag = '1'
        where a.id = #{partnerId}
    </update>


    <select id="findOfflinePartnerByMblnbr" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT *
        FROM partner
        <where>
            <if test="mblNbr != null">
                AND mbl_nbr = #{mblNbr}
            </if>
            <if test="orgCode!= null and orgCode !='' and orgCode!='root' ">
                and org_code = #{orgCode}
            </if>
            and state='1'
        </where>
    </select>

    <select id="findOfflinePartnerByPhones" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT * FROM partner WHERE state='1' and
        mbl_nbr IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="findPersonCustomerOrderList" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.*, b.merchant_name, b.merchant_type,b.commis_flag, b.rights_flag
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="partnerType!= null">
                AND a.partner_type = #{partnerType}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            AND (a.state = '1') and a.customer_order_limit is not null
        </where>
    </select>

    <update id="updateCustomerOrderLimitById">
        update partner
        set customer_order_limit=#{customerOrderLimit},update_by=#{updateBy}, update_time= DATE_FORMAT(sysdate(),'%Y-%m-%d %H:%i:%s')
        where id = #{id}
    </update>

    <select id="findByMblnbr" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT *
        FROM partner
        <where>
            <if test="mblNbr != null">
                AND mbl_nbr = #{mblNbr}
            </if>
            <if test="orgCode!= null and orgCode !='' and orgCode!='root' ">
                and org_code = #{orgCode}
            </if>
        </where>
    </select>

    <select id="findNoCheckGztData" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner where gzt_rs='2' or gzt_rs='3'
    </select>


    <update id="updateGztCheck">
        update partner set gzt_rs=#{gztRs}  where id = #{id}
    </update>

    <select id="findPartnerByParam" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner p where p.partner_name=#{partnerName} and p.partner_cert_no=#{partnerCertNo} and p.org_code=#{orgCode}
    </select>

    <select id="findDataSyn" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner p where p.update_time &gt;= DATE_FORMAT(#{synTime},'%Y-%m-%d %H:%i:%s') and p.update_time &lt;= DATE_FORMAT(#{nextTime},'%Y-%m-%d %H:%i:%s') order by p.update_time
    </select>

    <select id="findFirstUpdate" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner p  order by p.update_time limit 0,1
    </select>

    <select id="getPartnerChnlInfo" resultType="com.jsunicom.oms.model.partner.Partner">
        SELECT id,partner_name,mbl_nbr,org_code,city_name,mapping_point_code
        FROM partner
        where 1=1
        <if test="orgCode != null and orgCode != ''" >
            AND org_code = #{orgCode}
        </if>
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="mblNbr != null and mblNbr != ''">
            AND mbl_nbr = #{mblNbr}
        </if>
    </select>

    <update id="updateChannelCode">
        UPDATE partner
        set mapping_point_code = #{mappingPointCode}, update_by = #{updateBy}, update_time = DATE_FORMAT(sysdate(),'%Y-%m-%d %H:%i:%s')
        WHERE id = #{id}
    </update>

    <select id="findPartnerByTime" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from Partner where create_time &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s') and create_time &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s') order by create_time
    </select>

    <select id="selectClassificationIdByPhone" resultType="java.lang.String">
    select b.classification_id classificationId from `partner` a left join `partner_classification` b on a.id=b.partner_id where a.mbl_nbr=#{phone}
    </select>

    <select id="selectMerchantMember" resultType="com.jsunicom.oms.model.partner.Partner">
        select * from partner where  state='1'
        <if test="mblNbr != null and mblNbr != ''" >
            and mbl_nbr =#{mblNbr}
        </if>
        <if test="merchantId != null and merchantId != ''" >
            and merchant_id=#{merchantId}
        </if>
    </select>

    <select id="selectTeamMembers" resultType="com.jsunicom.oms.model.partner.Partner" fetchSize="30000">
        SELECT a.id,a.partner_type,a.partner_name,a.scence_id,a.wx_acct,pu.teamURL
        ,a.acct_no,'' partner_cert_no,a.mbl_nbr,a.contact_name,'' contact_cert_no
        ,a.intent_bus_code,a.intent_bus_name,a.refer_name,a.refer_phone,a.profession_type
        ,a.promot_area,a.org_code,a.industry,a.chain_mer_id,a.prov_code,a.prov_name
        ,a.city_code,a.city_name,a.area_code,a.area_name,a.address,a.devel_id,a.commis_acct_id
        ,a.create_by,a.create_time,a.update_by,a.update_time,a.audit_by,a.audit_time
        ,a.state,a.audit_remark,a.wo_acct,a.wo_state,a.wo_remark,a.merchant_id,a.wx_entp_uid,a.pricipal_name,a.pricipal_cert_no
        ,a2.partner_name saleMgrName,a2.mbl_nbr saleMgrPhone,a.bus_line,a.bus_grid,a.emploee_type,a.is_mer_admin
        ,a.invite_code,a.gzt_rs,a.audit_refuse_reson,a.devel_channel,a.oper_id,a.rights_coupon
        ,a.register_path,a.place_order_white_flag,a.business_classification,a.mapping_point_code
        ,a.delete_flag,a.customer_order_limit,a.remark,a.agency_no
        , b.merchant_name, b.merchant_type,b.commis_flag, b.rights_flag
        ,wsc.SCHOOL_NAME schoolName,wsc.CAMPUS_NAME campusName,wsc.CAMPUS_ID campusId
        ,pcc.name classificationName,pcc.id classificationId,par.partner_name captainName,par.mbl_nbr captainPhone
        FROM partner a
        left join partner_url pu on a.id=pu.partner_Id
        LEFT JOIN merchant b
        ON a.merchant_id = b.id
        left join partner_classification pc on pc.partner_id =a.id
        left join partner_classification_code pcc on pcc.id = pc.classification_id
        left join wo_school_info wsi on wsi.merchant_Id=b.id
        left join partner a2 on a2.id=wsi.school_manager_id
        left join (select BIND_ID,CAMPUS_ID from wo_school_campus_bind where `TYPE`='0' and STATE='1')wscb on wscb.BIND_ID = b.id
        left join wo_school_campus wsc on wsc.CAMPUS_ID=wscb.CAMPUS_ID
        LEFT JOIN (select part.merchant_Id ,part.partner_name , part.mbl_nbr
        from partner part  left join merchant mer on mer.id =part.merchant_Id where part.is_mer_admin='1') par
        on par.merchant_Id=a.merchant_id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="campusId!= null">
                AND wsc.CAMPUS_ID = #{campusId}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="classificationId!= null">
                AND pc.classification_id = #{classificationId}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            <if test="isMerAdmin != null and isMerAdmin != ''">
                AND a.is_mer_admin = #{isMerAdmin}
            </if>
            <if test="saleMgrPhone != null and saleMgrPhone != ''">
                AND a2.mbl_nbr = #{saleMgrPhone}
            </if>
            <if test="merchantIdList != null">
                and a.merchant_id in
                <foreach collection="merchantIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND (a.state = '1' OR a.state = '2')
            and wsi.id is not null
        </where>
        order by a.create_Time desc,a.org_code asc
    </select>
    <select id="getExportPartnerCountNew" resultType="int" fetchSize="30000">
        SELECT count(*)
        FROM partner a LEFT JOIN merchant b
        ON a.merchant_id = b.id
        left join partner_classification pc on pc.partner_id =a.id
        left join partner_classification_code pcc on pcc.id = pc.classification_id
        left join wo_school_info wsi on wsi.merchant_Id=b.id
        left join (select BIND_ID,CAMPUS_ID from wo_school_campus_bind where `TYPE`='0' and STATE='1')wscb on wscb.BIND_ID = b.id
        left join wo_school_campus wsc on wsc.CAMPUS_ID=wscb.CAMPUS_ID
        LEFT JOIN (select part.merchant_Id ,part.partner_name , part.mbl_nbr
        from partner part  left join merchant mer on mer.id =part.merchant_Id where part.is_mer_admin='1') par
        on par.merchant_Id=a.merchant_id
        <where>
            <if test="partnerCertNo!= null">
                AND a.partner_cert_no LIKE #{partnerCertNo}
            </if>
            <if test="campusId!= null">
                AND wsc.CAMPUS_ID = #{campusId}
            </if>
            <if test="mblNbr!= null">
                AND a.mbl_nbr LIKE #{mblNbr}
            </if>
            <if test="orgCode!= null">
                AND a.org_code = #{orgCode}
            </if>
            <if test="merchantId!= null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="classificationId!= null">
                AND pc.classification_id = #{classificationId}
            </if>
            <if test="develId!= null">
                AND a.devel_id = #{develId}
            </if>
            <if test="id!= null">
                AND a.id = #{id}
            </if>
            <if test="isMerAdmin != null and isMerAdmin != ''">
                AND a.is_mer_admin = #{isMerAdmin}
            </if>
            <if test="merchantIdList != null">
                and a.merchant_id in
                <foreach collection="merchantIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND (a.state = '1' OR a.state = '2')
            and wsi.id is not null
        </where>
    </select>
    <insert id="insertTeamURL">
        insert into  partner_url(partner_id,teamURL) values (#{id},#{url})
    </insert>
    <delete id="deleteTeamURL">
        delete from partner_url where partner_id =#{id}
    </delete>

    <select id="selectPartnerNum" resultType="java.lang.Integer">
        select count(*) from partner p
        where  p.is_mer_admin in ('0','1') and p.state = '1'
    </select>
</mapper>
