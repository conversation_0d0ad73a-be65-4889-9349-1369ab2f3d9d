<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.partner.PartnerDevelChannelCodeDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner_devel_channel_code (id, channel_code, channel_name, channel_province, channel_city, channel_area
                        , create_time,channel_org_code,channel_type)
        VALUES (#{id}, #{channelCode}, #{channelName}, #{channelProvince}, #{channelCity}, #{channelArea}
              , #{createTime},#{channelOrgCode},#{channelType})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO partner_devel_channel_code (channel_code, channel_name, channel_province, channel_city, channel_area
                        , create_time,channel_org_code,channel_type)
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
              ( #{item.channelCode}, #{item.channelName}, #{item.channelProvince}, #{item.channelCity}, #{item.channelArea}
             , #{item.createTime},#{item.channelOrgCode},#{item.channelType})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO partner_devel_channel_code(id, channel_code, channel_name, channel_province, channel_city, channel_area
                          , create_time)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id}, #{item.channelCode}, #{item.channelName}, #{item.channelProvince}, #{item.channelCity}, #{item.channelArea}
                 , #{item.createTime} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE partner_devel_channel_code
        <set>
            channel_code=#{channelCode},
            channel_name=#{channelName},
            channel_province=#{channelProvince},
            channel_city=#{channelCity},
            channel_area=#{channelArea},
            create_time=#{createTime},
            channel_org_code=#{channelOrgCode},
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE partner_devel_channel_code
        <set>
            <if test="channelCode!= null">channel_code=#{channelCode},</if>
            <if test="channelName!= null">channel_name=#{channelName},</if>
            <if test="channelProvince!= null">channel_province=#{channelProvince},</if>
            <if test="channelCity!= null">channel_city=#{channelCity},</if>
            <if test="channelArea!= null">channel_area=#{channelArea},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="channelOrgCode!= null">channel_org_code=#{channelOrgCode},</if>
            <if test="channelType!= null">channel_type=#{channelType}</if>
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE partner_devel_channel_code
            <set>
                channel_code=#{item.channelCode},
                channel_name=#{item.channelName},
                channel_province=#{item.channelProvince},
                channel_city=#{item.channelCity},
                channel_area=#{item.channelArea},
                create_time=#{item.createTime},
                channel_org_code=#{item.channelOrgCode},
            </set>
            WHERE id=#{item.id} 
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner_devel_channel_code
        WHERE id=#{id} 
    </delete>

    <delete id="deleteBatch">
        DELETE FROM partner_devel_channel_code
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM partner_devel_channel_code
        WHERE id=#{id} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM partner_devel_channel_code
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner_devel_channel_code
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT * FROM partner_devel_channel_code
        WHERE id=#{id} 
    </select>
    <select id="findByChannelCode" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT * FROM partner_devel_channel_code
        WHERE channel_code = #{channelCode}
        AND channel_org_code = #{channelOrgCode}
    </select>
    <select id="find" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT id,channel_code,channel_name,channel_province,channel_city,channel_area,create_time, channel_org_code
         FROM partner_devel_channel_code
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="channelCode!= null">
               AND channel_code = #{channelCode}
            </if>
            <if test="channelName!= null">
               AND channel_name = #{channelName}
            </if>
            <if test="channelProvince!= null">
               AND channel_province = #{channelProvince}
            </if>
            <if test="channelCity!= null">
               AND channel_city = #{channelCity}
            </if>
            <if test="channelArea!= null">
               AND channel_area = #{channelArea}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="channelOrgCode!= null">
                AND channel_org_code = #{channelOrgCode}
            </if>
        </where>
    </select>

    <select id="findMappingPointByName" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT * FROM partner_devel_channel_code
        WHERE 1=1
        <if test="channelName!=null and channelName!=''">
           and channel_name like #{channelName} or channel_code= #{channelCode}
        </if>
        and  channel_org_code=#{channelOrgCode}
    </select>

    <select id="findByPage" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT *
        FROM partner_devel_channel_code
        <where>
            <if test="channelName!=null and channelName!=''">
                and channel_name like #{channelName}
            </if>
            <if test="channelCode!=null and channelCode!=''">
                and channel_code=#{channelCode}
            </if>
            <if test="channelOrgCode!=null and channelOrgCode!=''">
                and channel_org_code=#{channelOrgCode}
            </if>
        </where>
    </select>

    <select id="findInfoByChannelCode" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT *
        FROM partner_devel_channel_code
        where channel_code=#{channelCode}
    </select>

    <select id="findDataSyn" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        select * from partner_devel_channel_code p where p.create_time &gt;= DATE_FORMAT(#{synTime},'%Y-%m-%d %H:%i:%s') and p.create_time &lt;= DATE_FORMAT(#{nextTime},'%Y-%m-%d %H:%i:%s') order by p.create_time
    </select>

    <select id="findFirstUpdate" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        select * from partner_devel_channel_code p order by p.create_time limit 0,1
    </select>

    <select id="findByOrgCodeAndChannelCode" resultType="com.jsunicom.oms.model.partner.PartnerDevelChannelCode">
        SELECT *
        FROM partner_devel_channel_code
        where 1=1
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode}
        </if>
        <if test="channelOrgCode != null and channelOrgCode != ''">
            and channel_org_code = #{channelOrgCode}
        </if>
    </select>
</mapper>
