<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.partner.PartnerOperAcctDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner_oper_acct (id, org_code, partner_id, partner_name, partner_phone, partner_devel_id
                        , wo_oper_acct, cbss_oper_acct, bss_oper_acct, remark, create_by, create_time
                        , update_by, update_time)
        VALUES (#{id}, #{orgCode}, #{partnerId}, #{partner<PERSON><PERSON>}, #{partner<PERSON><PERSON>}, #{partnerDevelId}
              , #{woOperAcct}, #{cbssOperAcct}, #{bssOperAcct}, #{remark}, #{createBy}, #{createTime}
              , #{updateBy}, #{updateTime})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO partner_oper_acct (id, org_code, partner_id, partner_name, partner_phone, partner_devel_id
                        , wo_oper_acct, cbss_oper_acct, bss_oper_acct, remark, create_by, create_time
                        , update_by, update_time)
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
              (#{id}, #{item.orgCode}, #{item.partnerId}, #{item.partnerName}, #{item.partnerPhone}, #{item.partnerDevelId}
             , #{item.woOperAcct}, #{item.cbssOperAcct}, #{item.bssOperAcct}, #{item.remark}, #{item.createBy}, #{item.createTime}
             , #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO partner_oper_acct(id, org_code, partner_id, partner_name, partner_phone, partner_devel_id
                          , wo_oper_acct, cbss_oper_acct, bss_oper_acct, remark, create_by, create_time
                          , update_by, update_time)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.id}, #{item.orgCode}, #{item.partnerId}, #{item.partnerName}, #{item.partnerPhone}, #{item.partnerDevelId}
                 , #{item.woOperAcct}, #{item.cbssOperAcct}, #{item.bssOperAcct}, #{item.remark}, #{item.createBy}, #{item.createTime}
                 , #{item.updateBy}, #{item.updateTime} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE partner_oper_acct
        <set>
            org_code=#{orgCode},
            partner_id=#{partnerId},
            partner_name=#{partnerName},
            partner_phone=#{partnerPhone},
            partner_devel_id=#{partnerDevelId},
            wo_oper_acct=#{woOperAcct},
            cbss_oper_acct=#{cbssOperAcct},
            bss_oper_acct=#{bssOperAcct},
            remark=#{remark},
            create_by=#{createBy},
            create_time=#{createTime},
            update_by=#{updateBy},
            update_time=#{updateTime},
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE partner_oper_acct
        <set>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="partnerId!= null">partner_id=#{partnerId},</if>
            <if test="partnerName!= null">partner_name=#{partnerName},</if>
            <if test="partnerPhone!= null">partner_phone=#{partnerPhone},</if>
            <if test="partnerDevelId!= null">partner_devel_id=#{partnerDevelId},</if>
            <if test="woOperAcct!= null">wo_oper_acct=#{woOperAcct},</if>
            <if test="cbssOperAcct!= null">cbss_oper_acct=#{cbssOperAcct},</if>
            <if test="bssOperAcct!= null">bss_oper_acct=#{bssOperAcct},</if>
            <if test="remark!= null">remark=#{remark},</if>
            <if test="createBy!= null">create_by=#{createBy},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateBy!= null">update_by=#{updateBy},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE id=#{id} 
    </update>

    <update id="updateWoOperAcct">
        UPDATE partner_oper_acct
        <set>
            <if test="woOperAcct!= null">wo_oper_acct=#{woOperAcct},</if>
            <if test="bssOperAcct!= null">bss_oper_acct=#{bssOperAcct},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
        </set>
        WHERE partner_id=#{partnerId}
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE partner_oper_acct
            <set>
                org_code=#{item.orgCode},
                partner_id=#{item.partnerId},
                partner_name=#{item.partnerName},
                partner_phone=#{item.partnerPhone},
                partner_devel_id=#{item.partnerDevelId},
                wo_oper_acct=#{item.woOperAcct},
                cbss_oper_acct=#{item.cbssOperAcct},
                bss_oper_acct=#{item.bssOperAcct},
                remark=#{item.remark},
                create_by=#{item.createBy},
                create_time=#{item.createTime},
                update_by=#{item.updateBy},
                update_time=#{item.updateTime},
            </set>
            WHERE id=#{item.id} 
        </foreach>
    </update>

    <update id="updateWoOperAcctForEntity">
        UPDATE partner_oper_acct
        <set>
            wo_oper_acct=""
        </set>
        WHERE id=#{id}
    </update>

    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner_oper_acct
        WHERE id=#{id} 
    </delete>

    <delete id="deletePartnerOperAcctByPhone">
        DELETE FROM partner_oper_acct
        WHERE partner_phone=#{phone}
    </delete>

    <delete id="deleteBatch">
        DELETE FROM partner_oper_acct
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            id=#{item.id} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM partner_oper_acct
        WHERE id=#{id} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM partner_oper_acct
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM partner_oper_acct
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        SELECT * FROM partner_oper_acct
        WHERE id=#{id}
    </select>


    <select id="findByPartnerid" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        SELECT * FROM partner_oper_acct
        WHERE   partner_id = #{partnerId}
    </select>

    <select id="findDeliveryManagersNew" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        SELECT *
        FROM partner_oper_acct
        <where>
            <if test="partnerPhone != null">
                AND partner_phone = #{partnerPhone}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
        </where>
    </select>


    <select id="find" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        SELECT id,org_code,partner_id,partner_name,partner_phone,partner_devel_id,wo_oper_acct,cbss_oper_acct
               ,bss_oper_acct,remark,create_by,create_time,update_by,update_time
         FROM partner_oper_acct
        <where>
            <if test="id!= null">
               AND id = #{id}
            </if>
            <if test="orgCode!= null">
               AND org_code = #{orgCode}
            </if>
            <if test="partnerId!= null">
               AND partner_id = #{partnerId}
            </if>
            <if test="partnerName!= null">
               AND partner_name = #{partnerName}
            </if>
            <if test="partnerPhone!= null">
               AND partner_phone = #{partnerPhone}
            </if>
            <if test="partnerDevelId!= null">
               AND partner_devel_id = #{partnerDevelId}
            </if>
            <if test="woOperAcct!= null">
               AND wo_oper_acct = #{woOperAcct}
            </if>
            <if test="cbssOperAcct!= null">
               AND cbss_oper_acct = #{cbssOperAcct}
            </if>
            <if test="bssOperAcct!= null">
               AND bss_oper_acct = #{bssOperAcct}
            </if>
            <if test="remark!= null">
               AND remark = #{remark}
            </if>
            <if test="createBy!= null">
               AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
               AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
        </where>
    </select>


    <select id="findWithPartnerOperAcct" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct" parameterType="map">
        SELECT id,org_code,partner_id,partner_name,partner_phone,partner_devel_id,wo_oper_acct,cbss_oper_acct
        ,bss_oper_acct,remark,create_by,create_time,update_by,update_time
        FROM partner_oper_acct
        <where>
            <if test="id!= null">
                AND id = #{id}
            </if>
            <if test="orgCode!= null">
                AND org_code = #{orgCode}
            </if>
            <if test="partnerId!= null">
                AND partner_id = #{partnerId}
            </if>
            <if test="partnerName!= null">
                AND partner_name = #{partnerName}
            </if>
            <if test="partnerPhone!= null">
                AND partner_phone = #{partnerPhone}
            </if>
            <if test="partnerDevelId!= null">
                AND partner_devel_id = #{partnerDevelId}
            </if>
            <if test="woOperAcct!= null">
                AND wo_oper_acct = #{woOperAcct}
            </if>
            <if test="cbssOperAcct!= null">
                AND cbss_oper_acct = #{cbssOperAcct}
            </if>
            <if test="bssOperAcct!= null">
                AND bss_oper_acct = #{bssOperAcct}
            </if>
            <if test="remark!= null">
                AND remark = #{remark}
            </if>
            <if test="createBy!= null">
                AND create_by = #{createBy}
            </if>
            <if test="createTime!= null">
                AND create_time = #{createTime}
            </if>
            <if test="updateBy!= null">
                AND update_by = #{updateBy}
            </if>
            <if test="updateTime!= null">
                AND update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="queryByPhone" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        select * from partner_oper_acct where partner_phone=#{phone}
    </select>

    <select id="queryByPhoneOrId" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        select * from partner_oper_acct where partner_phone=#{phone} or partner_id = #{id}
    </select>

    <select id="getAllEntityOperAcct" resultType="com.jsunicom.oms.model.partner.PartnerOperAcct">
        select b.*,a.devel_id from partner a,partner_oper_acct b,partner_classification c where a.id = b.partner_id and a.id = c.partner_id and c.classification_first_id = 6
    </select>


</mapper>
