<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.partner.PartnerPromotActvDelBakDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO partner_promot_actv_del_bak( id,partner_id,promote_id,acct_no,scence_id,wx_acct,
                         state,create_by,create_time,update_by,update_time,operate_by,operate_time )
        VALUES ( #{id},#{partnerId},#{promoteId},#{acctNo},#{scenceId},#{wxAcct},
                 #{state},#{createBy},#{createTime},#{updateBy},#{updateTime},#{operateBy},#{operateTime})
    </insert>
    <!-- ============================= UPDATE ============================= -->

    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM partner_promot_actv_del_bak
        WHERE id=#{id}
    </delete>
    <!-- ============================= SELECT ============================= -->
    <select id="findByPK" resultType="com.jsunicom.oms.model.partner.PartnerPromotActv">
        SELECT * FROM partner_promot_actv_del_bak
        WHERE id=#{id}
    </select>

</mapper>
