<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.campus.ActivitiesCampusRelationMapper">

    <insert id="batchInsertActivitiesCampus" >
        INSERT INTO activities_campus_relation (activities_notify_id,
        campus_id,has_read,udpate_time,update_user) VALUES
        <foreach collection="activitiesCampusRelationList" item="item" separator=",">
            (#{item.activitiesNotifyId},
            #{item.campusId},
            #{item.hasRead},
            #{item.updateTime},
            #{item.updateUser}
            )
        </foreach>
    </insert>
    <select id="selectActivitiesNotifyCompus" resultType="com.jsunicom.oms.dto.campus.ActivitiesNotifyCompusVo">
        select sm.name as partnerName,
        sm.id as partnerId ,
        wsc.CAMPUS_ID as campusId,
        wsc.CAMPUS_NAME as campusName,
        oi.org_code as orgCode,
        oi.org_name as orgName ,
        wscmi.NEW_STUDENT_START_DATE as newStudentStartDate,
        acr.has_read as hasRead ,
        case
        when acr.has_read = '0' then '未确认'
        when acr.has_read = '1' then '已确认'
        end as hasReadName,
        acr.udpate_time as udpateTime,
        '是' as isCreateActivitiy,
        (select count(merchant_id)
        from activities_user_relation
        where type = '0'
        and activities_id = abi.activities_id
        group by merchant_id) as TeamNum,
        (select count(merchant_id)
        from activities_user_relation
        where type = '0' and activities_id = abi.activities_id) as TeamStaffNum,
        '0.0%' as completionRate
        from wo_school_campus wsc
        inner join wo_school_campus_bind wscb on wsc.CAMPUS_ID = wscb.CAMPUS_ID
        inner join sales_manager sm on wscb.BIND_ID = sm.id and wscb.`TYPE` = 1
        inner join activities_campus_relation acr on wsc.CAMPUS_ID = acr.campus_id
        left join activities_base_info abi
        on acr.activities_notify_id = abi.activities_notify_id and acr.campus_id = abi.campus_id
        left join org_info oi on wsc.ORG_CODE = oi.org_code
        left join wo_school_campus_marketing_info wscmi on wsc.CAMPUS_ID = wscmi.CAMPUS_ID
        <where>
            wsc.state = '1'
            and wscb.state = '1'
            and sm.state = '1'
            and acr.activities_notify_id = #{campusListVo.activitiesNotifyId}
            <if test="campusListVo.campusName != null and campusListVo.campusName != '' ">
                and wsc.CAMPUS_NAME like concat('%',#{campusListVo.campusName},'%')
            </if>
            <if test="campusListVo.orgCode != null and campusListVo.orgCode != '' ">
                and oi.org_code = #{campusListVo.orgCode}
            </if>
            <if test="campusListVo.hasRead != null and campusListVo.hasRead != '' ">
                and acr.has_read = #{campusListVo.hasRead}
            </if>
        </where>
    </select>
    <select id="getCampusList" resultType="com.jsunicom.oms.dto.campus.CampusChooseDto">
        select
        wsc.CAMPUS_ID as campusId,
        wsc.CAMPUS_NAME as campusName,
        oi.org_code as orgCode,
        oi.org_name as orgName,
        wscmi.NEW_STUDENT_START_DATE as newStudentStartDate,
        wscmi.TASK_VOLUME as taskVolume,
        0 as completeTarget ,
        '0.0%' as completionRate
        from wo_school_campus wsc
        left join org_info oi on wsc.ORG_CODE = oi.org_code
        left join wo_school_campus_marketing_info wscmi on wsc.CAMPUS_ID = wscmi.CAMPUS_ID
        <where>
            wsc.STATE = '1'
            <if test="campusListVo.campusName != null and campusListVo.campusName != ''">
                and wsc.CAMPUS_NAME like concat('%',#{campusListVo.campusName},'%')
            </if>
            <if test="campusListVo.orgCode != null and campusListVo.orgCode != ''">
                and oi.org_code = #{campusListVo.orgCode}
            </if>

            <if test="campusListVo.startTime != null">
                and wscmi.NEW_STUDENT_START_DATE &gt; #{campusListVo.startTime}
            </if>

            <if test="campusListVo.endTime != null">
                and wscmi.NEW_STUDENT_START_DATE  &lt;= #{campusListVo.endTime}
            </if>
        </where>
    </select>
</mapper>
