<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.campus.ActivitiesNotifyBaseInfoMapper">

<!--    <resultMap id="BaseResultMap" type="generator.domain.ActivitiesNotifyBaseInfo">-->
<!--            <id property="activitiesNotifyId" column="activities_notify_id" />-->
<!--            <result property="activityName" column="activity_name" />-->
<!--            <result property="activityType" column="activity_type" />-->
<!--            <result property="activityAttributes" column="activity_attributes" />-->
<!--            <result property="distributionUnit" column="distribution_unit" />-->
<!--            <result property="taskTimeStart" column="task_time_start" />-->
<!--            <result property="taskTimeEnd" column="task_time_end" />-->
<!--            <result property="activitiesStatus" column="activities_status" />-->
<!--            <result property="descripiton" column="descripiton" />-->
<!--            <result property="updateTime" column="update_time" />-->
<!--            <result property="updateUser" column="update_user" />-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        activities_notify_id,activity_name,activity_type,activity_attributes,distribution_unit,task_time_start,-->
<!--        task_time_end,activities_status,descripiton,update_time,update_user-->
<!--    </sql>-->
    <select id="getActivitiesNotifyBaseInfoList"
            resultType="com.jsunicom.oms.dto.campus.ActivitiesNotifyBaseInfoExtendVO">

        SELECT activities_notify_id,
               activity_notify_name,
               activity_type,
               ''                  as activityTypeName,
               activity_attributes,
               case
                   when activity_attributes = '0' then '日常活动'
                   when activity_attributes = '1' then '节日活动'
                   when activity_attributes = '2' then '春开活动'
                   when activity_attributes = '3' then '秋开活动'
                   else '未知' end as activityAttributesName,
               distribution_unit,
               task_time_start,
               task_time_end,
               activities_status,
               descripiton,
               update_time,
               update_user,
               create_user,
               create_time
        FROM activities_notify_base_info anbi
        <where>
            1=1
            <if test="activitiesNotifyListMessageVO.activityNotifyName != null and activitiesNotifyListMessageVO.activityNotifyName !='' ">
                and anbi.activity_notify_name like  concat('%',#{activitiesNotifyListMessageVO.activityNotifyName},'%')
            </if>
            <if test="activitiesNotifyListMessageVO.activityAttributes != null and activitiesNotifyListMessageVO.activityAttributes !='' ">
                and anbi.activity_attributes like  #{activitiesNotifyListMessageVO.activityAttributes}
            </if>
            <if test="activitiesNotifyListMessageVO.taskTimeStart != null ">
                and anbi.task_time_start &gt;=  #{activitiesNotifyListMessageVO.taskTimeStart}
            </if>
            <if test="activitiesNotifyListMessageVO.taskTimeEnd != null ">
                and anbi.task_time_end &lt;=  #{activitiesNotifyListMessageVO.taskTimeEnd}
            </if>
        </where>
    </select>
</mapper>
