<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.campus.ActivitiesUserRelationMapper">

    <select id="getActivitiesDetailInfos" resultType="com.jsunicom.oms.dto.campus.ActivitiesDetailsListVo">

        select
        aur.activities_id as activitiesId,
        aur.merchant_id as merchantId,
        aur.merchant_name as merchantName,
        p.is_mer_admin as isMerAdmin,
        case
        when p.is_mer_admin = '0' then '团队成员'
        when p.is_mer_admin = '1' then '团队长'
        when p.is_mer_admin = '2' then '社长'
        else '未知' end as isMerAdminName ,
        aur.has_read as hasRead ,
        case
        when aur.has_read = '0' then '未确定'
        when aur.has_read = '1' then '已确定'
        else '未知' end as hasReadName,
        aur.has_sign as hasSign,
        case
        when aur.has_sign = '0' then '未签到'
        when aur.has_sign = '1' then '已签到'
        else '未知' end as hasSignName,
        ifnull(wscsb.YW_DEVELOP_TARGET,0) as ywDevelopTarget,
        ifnull(wscsb.KD_DEVELOP_TARGET,0) as kdDevelopTarget,
        ifnull(wscsb.RH_DEVELOP_TARGET,0) as rhDevelopTarget,
        ifnull((wscss.YW_STOCK_USER+wscss.KD_STOCK_USER+wscss.RH_STOCK_USER),0) as userStock,
        0 as userDevelop
        from activities_base_info abi
        inner join activities_user_relation aur
        left join partner p on aur.user_id = p.id
        left join wo_school_campus_scheme_base wscsb on abi.campus_id = wscsb.CAMPUS_ID
        left join wo_school_campus_scheme_stock wscss on wscsb.SCHEME_ID = wscss.SCHEME_ID
        <where>
            aur.type = '0'
            <if test="activitiesDetailDto.merchantId != null and activitiesDetailDto.merchantId !='' ">
               and aur.merchant_id = #{activitiesDetailDto.merchantId}
            </if>
            <if test="activitiesDetailDto.hasSign != null and activitiesDetailDto.hasSign !='' ">
               and aur.has_sign  = #{activitiesDetailDto.hasSign}
            </if>
            and aur.activities_id = #{activitiesDetailDto.activitiesId}
        </where>
    </select>
</mapper>
