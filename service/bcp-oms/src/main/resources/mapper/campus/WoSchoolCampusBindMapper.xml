<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.campus.CampusBindDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save">
        INSERT INTO wo_school_campus_bind (campus_id, bind_id, state, create_time, update_time, type
                        , remark)
        VALUES (#{campusId}, #{bindId}, #{state}, #{createTime}, #{updateTime}, #{type}
              , #{remark})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO wo_school_campus_bind (campus_id, bind_id, state, create_time, update_time, type
                        , remark)
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
              (#{item.campusId}, #{item.bindId}, #{item.state}, #{item.createTime}, #{item.updateTime}, #{item.type}
             , #{item.remark})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO wo_school_campus_bind(campus_id, bind_id, state, create_time, update_time, type
                          , remark)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.campusId}, #{item.bindId}, #{item.state}, #{item.createTime}, #{item.updateTime}, #{item.type}
                 , #{item.remark} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE wo_school_campus_bind
        <set>
            state=#{state},
            create_time=#{createTime},
            update_time=#{updateTime},
            remark=#{remark},
        </set>
        WHERE bind_id=#{bindId} AND type=#{type} AND campus_id=#{campusId} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE wo_school_campus_bind
        <set>
            <if test="state!= null">state=#{state},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
            <if test="remark!= null">remark=#{remark},</if>
        </set>
        WHERE bind_id=#{bindId} AND type=#{type} AND campus_id=#{campusId} 
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE wo_school_campus_bind
            <set>
                state=#{item.state},
                create_time=#{item.createTime},
                update_time=#{item.updateTime},
                remark=#{item.remark},
            </set>
            WHERE bind_id=#{item.bindId} AND type=#{item.type} AND campus_id=#{item.campusId} 
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM wo_school_campus_bind
        WHERE bind_id=#{bindId} AND type=#{type} AND campus_id=#{campusId} 
    </delete>

    <delete id="deleteBatch">
        DELETE FROM wo_school_campus_bind
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            bind_id=#{item.bindId} AND type=#{item.type} AND campus_id=#{item.campusId} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM wo_school_campus_bind
        WHERE bind_id=#{bindId} AND type=#{type} AND campus_id=#{campusId} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM wo_school_campus_bind
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM wo_school_campus_bind
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.campus.CampusBind">
        SELECT * FROM wo_school_campus_bind
        WHERE bind_id=#{bindId} AND type=#{type} AND campus_id=#{campusId} 
    </select>

    <select id="find" resultType="com.jsunicom.oms.model.campus.CampusBind">
        SELECT campus_id,bind_id,state,create_time,update_time,type,remark
         FROM wo_school_campus_bind
        <where>
            <if test="campusId!= null">
               AND campus_id = #{campusId}
            </if>
            <if test="bindId!= null">
               AND bind_id = #{bindId}
            </if>
            <if test="state!= null">
               AND state = #{state}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
            <if test="type!= null">
               AND type = #{type}
            </if>
            <if test="remark!= null">
               AND remark = #{remark}
            </if>
        </where>
    </select>

    <select id="queryByCondition" resultType="com.jsunicom.oms.model.campus.CampusBind"
            parameterType="com.jsunicom.oms.model.campus.CampusQuery">
        SELECT * FROM wo_school_campus_bind
        WHERE state = '1'
        <if test="campusId!= null">
        AND campus_id = #{campusId}
        </if>
        <if test="bindId!= null">
            AND bind_id = #{bindId}
        </if>
        <if test="type!= null">
            AND type = #{type}
        </if>

    </select>
    <select id="queryListByCondition" resultType="com.jsunicom.oms.model.campus.CampusBind" parameterType="com.jsunicom.oms.model.campus.CampusQuery">
        SELECT * FROM wo_school_campus_bind
        WHERE state = '1'
        <if test="campusId!= null">
            AND campus_id = #{campusId}
        </if>
        <if test="bindId!= null">
            AND bind_id = #{bindId}
        </if>
        <if test="type!= null">
            AND type = #{type}
        </if>
    </select>

    <delete id="deleteByCampusIdAndType">
        DELETE FROM wo_school_campus_bind
        WHERE 1=1
            AND campus_id = #{campusId}
        <if test="bindId!= null">
            AND bind_id = #{bindId}
        </if>
        <if test="state!= null">
            AND state = #{state}
        </if>
        <if test="createTime!= null">
            AND create_time = #{createTime}
        </if>
        <if test="updateTime!= null">
            AND update_time = #{updateTime}
        </if>
        <if test="type!= null">
            AND type = #{type}
        </if>
        <if test="remark!= null">
            AND remark = #{remark}
        </if>
    </delete>

    <delete id="deleteByBindIdAndType">
        DELETE FROM wo_school_campus_bind
        WHERE 1=1
        AND bind_id = #{bindId}

        <if test="campusId!= null">
        AND campus_id = #{campusId}
        </if>

        <if test="state!= null">
            AND state = #{state}
        </if>
        <if test="createTime!= null">
            AND create_time = #{createTime}
        </if>
        <if test="updateTime!= null">
            AND update_time = #{updateTime}
        </if>
        <if test="type!= null">
            AND type = #{type}
        </if>
        <if test="remark!= null">
            AND remark = #{remark}
        </if>
    </delete>

</mapper>
