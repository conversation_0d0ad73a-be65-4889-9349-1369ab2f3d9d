<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.campus.ActivitiesBaseInfoMapper">


    <select id="getActivitiesBaseInfos" resultType="com.jsunicom.oms.dto.campus.ActivitiesBaseInfoExtendDto">
        SELECT activities_id,
               activities_notify_id,
               campus_name,
               activity_name,
               activity_type,
               activity_attributes,
               case
                   when activity_attributes = '0' then '日常活动'
                   when activity_attributes = '1' then '节日活动'
                   when activity_attributes = '2' then '春开活动'
                   when activity_attributes = '3' then '秋开活动'
                   else '未知' end as activityAttributesName,
               create_user,
               activity_start_time,
               activity_end_time,
               update_user,
               update_time,
               activities_status,
               case
                   when activities_status = '0' then '进行中'
                   when activities_status = '1' then '终止'
                   else '未知' end as activitiesStatusName,
               campus_id,
               create_time
        FROM activities_base_info
        <where>
         1=1
            <if test="activitiesDto.activityName != null and activitiesDto.activityName != ''">
               and activity_name like concat('%',#{activitiesDto.activityName},'%')
            </if>
            <if test="activitiesDto.campusName != null and activitiesDto.campusName != ''">
                and  campus_name like concat('%',#{activitiesDto.campusName},'%')
            </if>
            <if test="activitiesDto.activityAttributes != null and activitiesDto.activityAttributes != ''">
                and  activity_attributes = #{activitiesDto.activityAttributes}
            </if>
            <if test="activitiesDto.startTime != null">
                and activity_start_time &gt;= #{activitiesDto.startTime}
            </if>
            <if test="activitiesDto.endTime != null">
                and activity_end_time  &lt;= #{activitiesDto.endTime}
            </if>
        </where>
    </select>
    <select id="getActivitiesBaseInfosExport"
            resultType="com.jsunicom.oms.dto.campus.ActivitiesBaseInfoExcelVo">
        SELECT activities_id,
               activities_notify_id,
               campus_name,
               activity_name,
               activity_type,
               activity_attributes,
               case
                   when activity_attributes = '0' then '日常活动'
                   when activity_attributes = '1' then '节日活动'
                   when activity_attributes = '2' then '春开活动'
                   when activity_attributes = '3' then '秋开活动'
                   else '未知' end as activityAttributesName,
               create_user,
               activity_start_time,
               activity_end_time,
               update_user,
               update_time,
               activities_status,
               case
                   when activities_status = '0' then '进行中'
                   when activities_status = '1' then '终止'
                   else '未知' end as activitiesStatusName,
               campus_id,
               create_time
        FROM activities_base_info
    </select>
    <select id="getTeamDownListByActivitiesId"
            resultType="com.jsunicom.oms.dto.campus.ActivitiesDetailsListVo">
        select merchant_id, merchant_name
        from activities_user_relation
        where type = '0'
          and activities_id = #{activitiesId}
        group by merchant_id, merchant_name
    </select>
</mapper>
