<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.jsunicom.oms.mapper.resource.TnSerialIdleMapper">

    <sql id="BASE_COLUMN_LIST">
        si.ID
        ,si.POOL_CODE,si.SERIAL_NO,si.STATE,si.ICCID,si.PSPT_ID,si.PRE_STORE,si.SERIAL_TYPE,si.SUPER_TYPE,si.CITY_CODE,si.BATCH_ID,si.CREATE_STAFF_ID,si.CREATE_TIME,si.UPDATE_TIME,si.UPDATE_STAFF_ID
    </sql>

    <resultMap id="BASE_RESULT_MAP" type="com.jsunicom.oms.po.resource.TnSerialIdle">
        <result property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="poolCode" column="POOL_CODE" jdbcType="VARCHAR"/>
        <result property="serialNo" column="SERIAL_NO" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="iccid" column="ICCID" jdbcType="VARCHAR"/>
        <result property="psptId" column="PSPT_ID" jdbcType="VARCHAR"/>
        <result property="preStore" column="PRE_STORE" jdbcType="DECIMAL"/>
        <result property="serialType" column="SERIAL_TYPE" jdbcType="VARCHAR"/>
        <result property="superType" column="SUPER_TYPE" jdbcType="VARCHAR"/>
        <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
        <result property="batchId" column="BATCH_ID" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createStaffId" column="CREATE_STAFF_ID" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateStaffId" column="UPDATE_STAFF_ID" jdbcType="VARCHAR"/>
        <result property="schoolName" column="SCHL_NAME" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryThSerialIdleListByCondition" resultMap="BASE_RESULT_MAP" parameterType="java.util.Map">
        SELECT
        a.SERIAL_NO,
        a.STATE,
        a.ICCID,
        a.PSPT_ID,
        a.CITY_CODE,
        a.BATCH_ID,
        a.CREATE_TIME,
        a.UPDATE_TIME,
        c.SCHOOL_NAME,
        c.GOODS_NAME
        FROM
        TN_SERIAL_IDLE a,
        TN_SERIAL_NUMBER_POOL c
        WHERE
        a.POOL_CODE = c.POOL_CODE
        AND c.SCHOOL_ID = b.ID
        <if test="serialNo != null and serialNo != ''">
            AND a.SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            AND a.STATE = #{state,jdbcType=VARCHAR}
        </if>
        <if test="psptId != null and psptId != ''">
            AND a.PSPT_ID = #{psptId,jdbcType=VARCHAR}
        </if>
        <if test="iccid != null and iccid != ''">
            AND a.ICCID = #{iccid,jdbcType=VARCHAR}
        </if>
        <if test="poolCode != null and poolCode != ''">
            AND a.POOL_CODE = #{poolCode,jdbcType=VARCHAR}
        </if>
        order by a.CREATE_TIME desc
    </select>

    <select id="queryTnSerialIdleList" parameterType="java.util.Map" resultMap="BASE_RESULT_MAP">
        select
        <include refid="BASE_COLUMN_LIST"/>
        from
        TN_SERIAL_IDLE si
        <if test="serialNumber != null and serialNumber != ''">
            left join TN_SERIAL_NUMBER_POOL np on si.POOL_CODE = np.POOL_CODE
            left join wo_school_campus_bind cb on np.SCHOOL_ID = cb.CAMPUS_ID
            right JOIN
            (select b.id from sales_manager a ,partner b
            where a.phone = b.mbl_nbr and a.bus_line ='20'
            AND a.PHONE = #{serialNumber,jdbcType=VARCHAR}
            ) cc on cb.BIND_ID = cc.id and cb.type='1' and cb.STATE ='1'
        </if>
        <if test="cbssStaffId != null and cbssStaffId != ''">
            left join TN_SERIAL_NUMBER_POOL np on si.POOL_CODE = np.POOL_CODE
            left join wo_school_campus_bind cb on np.SCHOOL_ID = cb.CAMPUS_ID
            right JOIN (
            select si.id from wo_school_info si where si.prod_oper_id = #{cbssStaffId,jdbcType=VARCHAR}
            ) ee on ee.id = cb.BIND_ID and cb.type='1' and cb.STATE ='1'
        </if>
<!--        <if test="cbssStaffId != null and cbssStaffId != ''">-->
<!--            left join TN_SERIAL_NUMBER_POOL np on si.POOL_CODE = np.POOL_CODE and np.CREATE_STAFF_ID = #{cbssStaffId,jdbcType=VARCHAR}-->
<!--        </if>-->
        where 1=1
        <if test="serialNo != null and serialNo != ''">
            AND SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
        </if>

        <if test="state != null and state != ''">
            AND si.STATE = #{state,jdbcType=VARCHAR}
        </if>

        <if test="psptId != null and psptId != ''">
            AND PSPT_ID = #{psptId,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="iccid != null and iccid != ''">
            AND ICCID = #{iccid,jdbcType=VARCHAR}
        </if>

        <if test="poolCode != null and poolCode != ''">
            AND si.POOL_CODE = #{poolCode,jdbcType=VARCHAR}
        </if>
        order by CREATE_TIME desc
    </select>

    <select id="selectTnSerialIdleDuplicateInfo" resultType="java.util.Map" parameterType="java.util.Map">
        select
        t1.SERIAL_NO,t1.ICCID,t1.PSPT_ID,t2.SCHOOL_NAME,t2.CREATE_STAFF_NAME,t2.POOL_NAME,t1.STATE
        from
        TN_SERIAL_IDLE t1
        left join TN_SERIAL_NUMBER_POOL t2 on t1.POOL_CODE = t2.POOL_CODE
        where 1=1
        <if test="serialNo != null and serialNo != ''">
            and t1.SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
        </if>
        <if test="psptId != null and psptId != ''">
            or t1.PSPT_ID = #{psptId,jdbcType=VARCHAR}
        </if>
        <if test="iccId != null and iccId != ''">
            or t1.ICCID = #{iccId,jdbcType=VARCHAR}
        </if>
        order by t1.CREATE_TIME DESC
    </select>

    <select id="queryTnSerialBySerialNo" resultMap="BASE_RESULT_MAP">
        select
        SERIAL_NO,
        STATE,
        ICCID,
        PSPT_ID,
        CITY_CODE,
        BATCH_ID,
        POOL_CODE
        from TN_SERIAL_IDLE
        where 1=1
        <if test="serialNo != null and serialNo != ''">
            and SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="updateTnSerialIdleStateBySerialNo" parameterType="java.util.Map">
        update TN_SERIAL_IDLE
        <set>
            <if test="psptId != null and psptId != ''">
                PSPT_ID = #{psptId,jdbcType=VARCHAR},
            </if>
            <if test="iccid != null and iccid != ''">
                ICCID = #{iccid,jdbcType=VARCHAR},
            </if>
            <if test="newState != null and newState != ''">
                STATE = #{newState,jdbcType=VARCHAR},
            </if>
            <if test="updateTime == null">
                UPDATE_TIME = now(),
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            UPDATE_STAFF_ID = #{updateStaffId,jdbcType=VARCHAR}
        </set>
        where SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
    </update>

    <select id="queryTnSerialCampusList" parameterType="java.util.Map" resultType="java.util.Map">
        select DISTINCT z.CAMPUS_ID as id,z.CAMPUS_NAME as SCHL_NAME from wo_school_campus z where  z.CAMPUS_ID in (
        select m.CAMPUS_ID from wo_school_campus_bind m
        <if test="serialNumber != '' and serialNumber != null">
            right JOIN
            (select b.id from sales_manager a ,partner b
            where a.phone = b.mbl_nbr and a.bus_line ='20'
            AND a.PHONE = #{serialNumber,jdbcType=VARCHAR}
            ) cc on m.BIND_ID = cc.id
        </if>
        <if test="cbssStaffId != '' and cbssStaffId != null">
            right JOIN (
                select si.id from wo_school_info si where si.prod_oper_id = #{cbssStaffId,jdbcType=VARCHAR}
            ) ee on ee.id = m.BIND_ID
        </if>
        where m.type='1' and STATE ='1')
        <if test="cityCode != null and cityCode != ''">
            and z.ORG_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="schlName != null and schlName != ''">
            and z.CAMPUS_NAME like CONCAT('%',#{schlName,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <delete id="delTnSerialIdleBySerialNo" parameterType="java.lang.String">
        delete
        from tn_serial_idle
        where SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
    </delete>

    <delete id="clearSerialNumber" parameterType="java.lang.Integer">
        delete
        from TN_SERIAL_IDLE
        where STATE = '05'
    </delete>

    <update id="updateTnSerialIdleStateByPoolCode">
        update
            TN_SERIAL_IDLE
        set STATE          = #{newState,jdbcType=VARCHAR},
            UPDATE_TIME    = now(),
            UPDATE_STAFF_ID=#{updateStaffId,jdbcType=VARCHAR}
        where POOL_CODE = #{poolCode,jdbcType=VARCHAR}
    </update>

    <!--修改idel表状态改为05,占用时间记录为当前时间-90天-->
    <update id="updateIdelStateTime" parameterType="java.lang.String">
        UPDATE t_serial_idle r
        SET r.state        = '05',
            r.update_time  = sysdate() - 90,
            r.update_staff = 'symSerialState'
        WHERE r.serial_no = #{serialNo }
    </update>
    <update id="resetOccupiedNumber">
        UPDATE TN_SERIAL_IDLE
        SET state = '01'
        WHERE state = '03'
        AND update_time &lt;= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    </update>

    <insert id="insertTnSerialIdle">
        INSERT INTO TN_SERIAL_IDLE
        (ID, POOL_CODE, SERIAL_NO, STATE, ICCID, PSPT_ID, PRE_STORE, SERIAL_TYPE, SUPER_TYPE, CITY_CODE, BATCH_ID,
         CREATE_STAFF_ID, UPDATE_STAFF_ID, CREATE_TIME, UPDATE_TIME)
        VALUES (#{id}, #{poolCode}, #{serialNo}, #{state}, #{iccid}, #{psptId,jdbcType=VARCHAR}, #{preStore},
                #{serialType}, #{superType}, #{cityCode}, #{batchId}, #{createStaffId}, #{updateStaffId}, now(), now())
    </insert>
    <insert id="insertSerialIdleBackUp" parameterType="com.jsunicom.oms.po.resource.TSerialIdleBackup">
        insert into T_SERIAL_IDLE_BACKUP
            (ID, SERIAL_NO, STATE, CITY_CODE, UPDATE_STAFF, UPDATE_TIME)
        values (#{id,jdbcType = NUMERIC },
                #{serialNo,jdbcType = VARCHAR },
                #{state,jdbcType = VARCHAR },
                #{cityCode,jdbcType = VARCHAR },
                #{updateStaff,jdbcType = VARCHAR },
                sysdate())
    </insert>

    <select id="qryPoolCodeCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1)
        from TN_SERIAL_IDLE
        where pool_code = #{poolCode}
    </select>

    <select id="qryPoolCount" resultType="java.lang.Integer">
        select count(ID) from TN_SERIAL_IDLE where state = '01'
        and pool_code in
        <foreach collection="queryData" item="item" index="index" open="(" close=")" separator=",">
            #{item.poolCode}
        </foreach>
    </select>

    <select id="selectSerialInfoByPsptId" resultMap="BASE_RESULT_MAP">
        select
        <include refid="BASE_COLUMN_LIST"/>
        from
        TN_SERIAL_IDLE si
        where PSPT_ID = #{psptId}
        and STATE = '01'
        order by CREATE_TIME desc
    </select>

    <select id="selectSerialInfoBySchIdAndGoodsId" resultMap="BASE_RESULT_MAP" parameterType="java.util.Map">
        select
        t1.ID,
        t1.POOL_CODE,
        t1.SERIAL_NO,
        t1.STATE,
        t1.ICCID,
        t1.PSPT_ID,
        t1.PRE_STORE,
        t1.SERIAL_TYPE,
        t1.SUPER_TYPE,
        t1.CITY_CODE,
        t1.BATCH_ID,
        t1.CREATE_STAFF_ID,
        t1.CREATE_TIME,
        t1.UPDATE_TIME,
        t1.UPDATE_STAFF_ID
        from
        TN_SERIAL_IDLE t1
        left join TN_SERIAL_NUMBER_POOL t2 on t1.POOL_CODE = t2.POOL_CODE
        where
        t2.SCHOOL_ID = #{schoolId,jdbcType=VARCHAR}
        and t2.GOODS_ID = #{goodsId,jdbcType=VARCHAR}
        <if test="teamId != null and teamId != ''">
            and t2.TEAM_ID = #{teamId,jdbcType=VARCHAR}
        </if>
        <if test="teamId == null or teamId == ''">
            and t2.TEAM_ID is null
        </if>
        <if test="state == null or state == ''">
            and t1.STATE = '01'
        </if>
        and (t1.PSPT_ID = '' or t1.PSPT_ID is null)
        <if test="cityCode != null and cityCode != ''">
            and t1.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="serialNo != null and serialNo != ''">
            and t1.SERIAL_NO like CONCAT('%', #{serialNo,jdbcType=VARCHAR}, '%')
        </if>
        order by CREATE_TIME desc
    </select>
    <select id="querySerials" resultType="java.util.Map">
        SELECT i.id, i.serial_no, i.state, i.city_code
        FROM t_serial_idle i
        WHERE i.state in ('00', '05')
          AND i.update_time <![CDATA[<]]> SYSDATE() - 30
    </select>
    <select id="querySerialsByCity" resultType="java.util.Map"
            parameterType="java.util.Map">
        SELECT i.serial_no, i.id
        FROM t_serial_idle i
        WHERE i.state = '04'
          AND i.CITY_CODE = #{cityCode}
          AND i.update_time <![CDATA[<]]> SYSDATE() - #{predate}
          AND rownum <![CDATA[<=]]> #{symNumCount}
    </select>
    <select id="queryPersonInfo" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT a.employee_id,
               a.chl_type_code,
               a.chl_code,
               a.city_no,
               a.city_id
        FROM t_s_ess_empno a
        WHERE a.city_id = #{cityCode}
    </select>
    <select id="selectSerialForCurrentImportAndMobileNetwork" resultMap="BASE_RESULT_MAP">
        select distinct si.SERIAL_NO,si.CITY_CODE,si.ICCID,si.PSPT_ID,si.POOL_CODE
        from tn_serial_idle si
                 left join tn_serial_number_pool np on si.POOL_CODE = np.POOL_CODE
        where si.STATE = '01'
          and si.CREATE_TIME &gt;= #{currentTime,jdbcType=VARCHAR}
          and np.goods_id in (select sd.param_key  from td_ord_sys_dict sd where sd.param_type = 'goodsList' and sd.param1 = 'mobileProduct')
    </select>

    <select id="selectSerialForCurrentImportAndFusion" resultMap="BASE_RESULT_MAP">
        select distinct si.SERIAL_NO,si.CITY_CODE,si.ICCID,si.PSPT_ID,si.POOL_CODE
        from tn_serial_idle si
                 left join tn_serial_number_pool np on si.pool_code = np.pool_code
        where si.STATE = '01'
          and si.CREATE_TIME &gt;= #{currentTime,jdbcType=VARCHAR}
          and np.goods_id in (select sd.param_key  from td_ord_sys_dict sd where sd.param_type = 'goodsList' and sd.param1 = 'fusionProduct')
    </select>

    <select id="queryPersonInfoByCityNo" resultType="java.util.Map">
        SELECT
            ee.EMPLOYEE_ID as STAFFID,
            ee.CHL_TYPE_CODE as CHANNELTYPE,
            ee.CHL_CODE as CHANNELID,
            ee.CITY_NO as CITYNO,
            ee.CITY_CODE as CITYCODE,
            ee.IS_USE as INMODECODE,
            ee.REMARK as DEPARTID
        FROM
            t_s_ess_empno ee
        WHERE
            ee.CITY_ID = #{cityId,jdbcType=VARCHAR}
    </select>

    <update id="changeSerialState" parameterType="java.lang.String">
        update tn_serial_idle set STATE = '07',UPDATE_STAFF_ID = 'admin',UPDATE_TIME = now() where SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
    </update>

    <select id="qryGoodsLists" parameterType="java.util.Map" resultType="com.jsunicom.oms.po.resource.Goods">
        select distinct goods.code as commId,goods.name as commName
        from goods goods
            left join wo_school_goods sg on goods.id = sg.goods_id
            left join wo_school_info si on sg.school_id = si.id
            left join WO_SCHOOL_CAMPUS_BIND cb on cb.Bind_id = si.merchant_Id
        where cb.type = '0' and cb.STATE = '1'
        <if test="schoolId != null and schoolId != ''">
            and cb.campus_id = #{schoolId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="qryProcessingSerialNoByPoolCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT gm.main_number
        FROM tf_ord_main om
                 INNER JOIN tf_ord_goods_main gm ON om.order_id = gm.order_id
        WHERE om.order_state IN ('0020', '0030', '0031', '0050')
          AND gm.main_number IN (
            SELECT serial_no
            FROM tn_serial_idle
            WHERE pool_code = #{poolCode,jdbcType=VARCHAR}
        )
    </select>

</mapper>

