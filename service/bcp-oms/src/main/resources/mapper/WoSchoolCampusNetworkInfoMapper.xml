<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.WoSchoolCampusNetworkInfoMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.WoSchoolCampusNetworkInfo">
    <id column="CAMPUS_ID" jdbcType="BIGINT" property="campusId" />
    <result column="IS_INCOMING_LINE" jdbcType="VARCHAR" property="isIncomingLine" />
    <result column="INCOMING_LINE_TIME" jdbcType="TIMESTAMP" property="incomingLineTime" />
    <result column="BROADBAND_NETWORK_STANDARD" jdbcType="VARCHAR" property="broadbandNetworkStandard" />
    <result column="MAX_SPEED" jdbcType="VARCHAR" property="maxSpeed" />
    <result column="IS_ONE" jdbcType="VARCHAR" property="isOne" />
    <result column="IS_NEW_FUSION_MODE" jdbcType="VARCHAR" property="isNewFusionMode" />
    <result column="COVERAGE_AREA" jdbcType="VARCHAR" property="coverageArea" />
    <result column="COVERAGE_DORMITORY_NUM" jdbcType="INTEGER" property="coverageDormitoryNum" />
    <result column="COVERAGE_FAMILY_BUILDING_NUM" jdbcType="INTEGER" property="coverageFamilyBuildingNum" />
    <result column="COVERAGE_OFFICE_BUILDING_NUM" jdbcType="INTEGER" property="coverageOfficeBuildingNum" />
    <result column="IS_ONLY" jdbcType="VARCHAR" property="isOnly" />
    <result column="ACCESS_OPERATOR" jdbcType="VARCHAR" property="accessOperator" />
    <result column="LINE_STANDARD_ADDRESS_CODE" jdbcType="VARCHAR" property="lineStandardAddressCode" />
    <result column="LINE_STANDARD_ADDRESS_VALUE" jdbcType="VARCHAR" property="lineStandardAddressValue" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    CAMPUS_ID, IS_INCOMING_LINE, INCOMING_LINE_TIME, BROADBAND_NETWORK_STANDARD, MAX_SPEED, 
    IS_ONE, IS_NEW_FUSION_MODE, COVERAGE_AREA, COVERAGE_DORMITORY_NUM, COVERAGE_FAMILY_BUILDING_NUM, 
    COVERAGE_OFFICE_BUILDING_NUM, IS_ONLY, ACCESS_OPERATOR, LINE_STANDARD_ADDRESS_CODE, 
    LINE_STANDARD_ADDRESS_VALUE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, RESERVE1, 
    RESERVE2, RESERVE3
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_school_campus_network_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus_network_info
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wo_school_campus_network_info
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfoExample">
    delete from wo_school_campus_network_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfo">
    insert into wo_school_campus_network_info (CAMPUS_ID, IS_INCOMING_LINE, INCOMING_LINE_TIME, 
      BROADBAND_NETWORK_STANDARD, MAX_SPEED, IS_ONE, 
      IS_NEW_FUSION_MODE, COVERAGE_AREA, COVERAGE_DORMITORY_NUM, 
      COVERAGE_FAMILY_BUILDING_NUM, COVERAGE_OFFICE_BUILDING_NUM, 
      IS_ONLY, ACCESS_OPERATOR, LINE_STANDARD_ADDRESS_CODE, 
      LINE_STANDARD_ADDRESS_VALUE, CREATE_BY, CREATE_TIME, 
      UPDATE_BY, UPDATE_TIME, RESERVE1, 
      RESERVE2, RESERVE3)
    values (#{campusId,jdbcType=BIGINT}, #{isIncomingLine,jdbcType=VARCHAR}, #{incomingLineTime,jdbcType=TIMESTAMP}, 
      #{broadbandNetworkStandard,jdbcType=VARCHAR}, #{maxSpeed,jdbcType=VARCHAR}, #{isOne,jdbcType=VARCHAR}, 
      #{isNewFusionMode,jdbcType=VARCHAR}, #{coverageArea,jdbcType=VARCHAR}, #{coverageDormitoryNum,jdbcType=INTEGER}, 
      #{coverageFamilyBuildingNum,jdbcType=INTEGER}, #{coverageOfficeBuildingNum,jdbcType=INTEGER}, 
      #{isOnly,jdbcType=VARCHAR}, #{accessOperator,jdbcType=VARCHAR}, #{lineStandardAddressCode,jdbcType=VARCHAR}, 
      #{lineStandardAddressValue,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{reserve1,jdbcType=VARCHAR}, 
      #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfo">
    insert into wo_school_campus_network_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        CAMPUS_ID,
      </if>
      <if test="isIncomingLine != null">
        IS_INCOMING_LINE,
      </if>
      <if test="incomingLineTime != null">
        INCOMING_LINE_TIME,
      </if>
      <if test="broadbandNetworkStandard != null">
        BROADBAND_NETWORK_STANDARD,
      </if>
      <if test="maxSpeed != null">
        MAX_SPEED,
      </if>
      <if test="isOne != null">
        IS_ONE,
      </if>
      <if test="isNewFusionMode != null">
        IS_NEW_FUSION_MODE,
      </if>
      <if test="coverageArea != null">
        COVERAGE_AREA,
      </if>
      <if test="coverageDormitoryNum != null">
        COVERAGE_DORMITORY_NUM,
      </if>
      <if test="coverageFamilyBuildingNum != null">
        COVERAGE_FAMILY_BUILDING_NUM,
      </if>
      <if test="coverageOfficeBuildingNum != null">
        COVERAGE_OFFICE_BUILDING_NUM,
      </if>
      <if test="isOnly != null">
        IS_ONLY,
      </if>
      <if test="accessOperator != null">
        ACCESS_OPERATOR,
      </if>
      <if test="lineStandardAddressCode != null">
        LINE_STANDARD_ADDRESS_CODE,
      </if>
      <if test="lineStandardAddressValue != null">
        LINE_STANDARD_ADDRESS_VALUE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campusId != null">
        #{campusId,jdbcType=BIGINT},
      </if>
      <if test="isIncomingLine != null">
        #{isIncomingLine,jdbcType=VARCHAR},
      </if>
      <if test="incomingLineTime != null">
        #{incomingLineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="broadbandNetworkStandard != null">
        #{broadbandNetworkStandard,jdbcType=VARCHAR},
      </if>
      <if test="maxSpeed != null">
        #{maxSpeed,jdbcType=VARCHAR},
      </if>
      <if test="isOne != null">
        #{isOne,jdbcType=VARCHAR},
      </if>
      <if test="isNewFusionMode != null">
        #{isNewFusionMode,jdbcType=VARCHAR},
      </if>
      <if test="coverageArea != null">
        #{coverageArea,jdbcType=VARCHAR},
      </if>
      <if test="coverageDormitoryNum != null">
        #{coverageDormitoryNum,jdbcType=INTEGER},
      </if>
      <if test="coverageFamilyBuildingNum != null">
        #{coverageFamilyBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="coverageOfficeBuildingNum != null">
        #{coverageOfficeBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="isOnly != null">
        #{isOnly,jdbcType=VARCHAR},
      </if>
      <if test="accessOperator != null">
        #{accessOperator,jdbcType=VARCHAR},
      </if>
      <if test="lineStandardAddressCode != null">
        #{lineStandardAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="lineStandardAddressValue != null">
        #{lineStandardAddressValue,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfoExample" resultType="java.lang.Long">
    select count(*) from wo_school_campus_network_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_school_campus_network_info
    <set>
      <if test="record.campusId != null">
        CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      </if>
      <if test="record.isIncomingLine != null">
        IS_INCOMING_LINE = #{record.isIncomingLine,jdbcType=VARCHAR},
      </if>
      <if test="record.incomingLineTime != null">
        INCOMING_LINE_TIME = #{record.incomingLineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.broadbandNetworkStandard != null">
        BROADBAND_NETWORK_STANDARD = #{record.broadbandNetworkStandard,jdbcType=VARCHAR},
      </if>
      <if test="record.maxSpeed != null">
        MAX_SPEED = #{record.maxSpeed,jdbcType=VARCHAR},
      </if>
      <if test="record.isOne != null">
        IS_ONE = #{record.isOne,jdbcType=VARCHAR},
      </if>
      <if test="record.isNewFusionMode != null">
        IS_NEW_FUSION_MODE = #{record.isNewFusionMode,jdbcType=VARCHAR},
      </if>
      <if test="record.coverageArea != null">
        COVERAGE_AREA = #{record.coverageArea,jdbcType=VARCHAR},
      </if>
      <if test="record.coverageDormitoryNum != null">
        COVERAGE_DORMITORY_NUM = #{record.coverageDormitoryNum,jdbcType=INTEGER},
      </if>
      <if test="record.coverageFamilyBuildingNum != null">
        COVERAGE_FAMILY_BUILDING_NUM = #{record.coverageFamilyBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="record.coverageOfficeBuildingNum != null">
        COVERAGE_OFFICE_BUILDING_NUM = #{record.coverageOfficeBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="record.isOnly != null">
        IS_ONLY = #{record.isOnly,jdbcType=VARCHAR},
      </if>
      <if test="record.accessOperator != null">
        ACCESS_OPERATOR = #{record.accessOperator,jdbcType=VARCHAR},
      </if>
      <if test="record.lineStandardAddressCode != null">
        LINE_STANDARD_ADDRESS_CODE = #{record.lineStandardAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lineStandardAddressValue != null">
        LINE_STANDARD_ADDRESS_VALUE = #{record.lineStandardAddressValue,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reserve1 != null">
        RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve2 != null">
        RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve3 != null">
        RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_school_campus_network_info
    set CAMPUS_ID = #{record.campusId,jdbcType=BIGINT},
      IS_INCOMING_LINE = #{record.isIncomingLine,jdbcType=VARCHAR},
      INCOMING_LINE_TIME = #{record.incomingLineTime,jdbcType=TIMESTAMP},
      BROADBAND_NETWORK_STANDARD = #{record.broadbandNetworkStandard,jdbcType=VARCHAR},
      MAX_SPEED = #{record.maxSpeed,jdbcType=VARCHAR},
      IS_ONE = #{record.isOne,jdbcType=VARCHAR},
      IS_NEW_FUSION_MODE = #{record.isNewFusionMode,jdbcType=VARCHAR},
      COVERAGE_AREA = #{record.coverageArea,jdbcType=VARCHAR},
      COVERAGE_DORMITORY_NUM = #{record.coverageDormitoryNum,jdbcType=INTEGER},
      COVERAGE_FAMILY_BUILDING_NUM = #{record.coverageFamilyBuildingNum,jdbcType=INTEGER},
      COVERAGE_OFFICE_BUILDING_NUM = #{record.coverageOfficeBuildingNum,jdbcType=INTEGER},
      IS_ONLY = #{record.isOnly,jdbcType=VARCHAR},
      ACCESS_OPERATOR = #{record.accessOperator,jdbcType=VARCHAR},
      LINE_STANDARD_ADDRESS_CODE = #{record.lineStandardAddressCode,jdbcType=VARCHAR},
      LINE_STANDARD_ADDRESS_VALUE = #{record.lineStandardAddressValue,jdbcType=VARCHAR},
      CREATE_BY = #{record.createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{record.updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{record.reserve3,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfo">
    update wo_school_campus_network_info
    <set>
      <if test="isIncomingLine != null">
        IS_INCOMING_LINE = #{isIncomingLine,jdbcType=VARCHAR},
      </if>
      <if test="incomingLineTime != null">
        INCOMING_LINE_TIME = #{incomingLineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="broadbandNetworkStandard != null">
        BROADBAND_NETWORK_STANDARD = #{broadbandNetworkStandard,jdbcType=VARCHAR},
      </if>
      <if test="maxSpeed != null">
        MAX_SPEED = #{maxSpeed,jdbcType=VARCHAR},
      </if>
      <if test="isOne != null">
        IS_ONE = #{isOne,jdbcType=VARCHAR},
      </if>
      <if test="isNewFusionMode != null">
        IS_NEW_FUSION_MODE = #{isNewFusionMode,jdbcType=VARCHAR},
      </if>
      <if test="coverageArea != null">
        COVERAGE_AREA = #{coverageArea,jdbcType=VARCHAR},
      </if>
      <if test="coverageDormitoryNum != null">
        COVERAGE_DORMITORY_NUM = #{coverageDormitoryNum,jdbcType=INTEGER},
      </if>
      <if test="coverageFamilyBuildingNum != null">
        COVERAGE_FAMILY_BUILDING_NUM = #{coverageFamilyBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="coverageOfficeBuildingNum != null">
        COVERAGE_OFFICE_BUILDING_NUM = #{coverageOfficeBuildingNum,jdbcType=INTEGER},
      </if>
      <if test="isOnly != null">
        IS_ONLY = #{isOnly,jdbcType=VARCHAR},
      </if>
      <if test="accessOperator != null">
        ACCESS_OPERATOR = #{accessOperator,jdbcType=VARCHAR},
      </if>
      <if test="lineStandardAddressCode != null">
        LINE_STANDARD_ADDRESS_CODE = #{lineStandardAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="lineStandardAddressValue != null">
        LINE_STANDARD_ADDRESS_VALUE = #{lineStandardAddressValue,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.WoSchoolCampusNetworkInfo">
    update wo_school_campus_network_info
    set IS_INCOMING_LINE = #{isIncomingLine,jdbcType=VARCHAR},
      INCOMING_LINE_TIME = #{incomingLineTime,jdbcType=TIMESTAMP},
      BROADBAND_NETWORK_STANDARD = #{broadbandNetworkStandard,jdbcType=VARCHAR},
      MAX_SPEED = #{maxSpeed,jdbcType=VARCHAR},
      IS_ONE = #{isOne,jdbcType=VARCHAR},
      IS_NEW_FUSION_MODE = #{isNewFusionMode,jdbcType=VARCHAR},
      COVERAGE_AREA = #{coverageArea,jdbcType=VARCHAR},
      COVERAGE_DORMITORY_NUM = #{coverageDormitoryNum,jdbcType=INTEGER},
      COVERAGE_FAMILY_BUILDING_NUM = #{coverageFamilyBuildingNum,jdbcType=INTEGER},
      COVERAGE_OFFICE_BUILDING_NUM = #{coverageOfficeBuildingNum,jdbcType=INTEGER},
      IS_ONLY = #{isOnly,jdbcType=VARCHAR},
      ACCESS_OPERATOR = #{accessOperator,jdbcType=VARCHAR},
      LINE_STANDARD_ADDRESS_CODE = #{lineStandardAddressCode,jdbcType=VARCHAR},
      LINE_STANDARD_ADDRESS_VALUE = #{lineStandardAddressValue,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR}
    where CAMPUS_ID = #{campusId,jdbcType=BIGINT}
  </update>
</mapper>