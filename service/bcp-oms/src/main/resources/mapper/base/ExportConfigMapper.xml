<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.base.ExportConfigDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="id" >
        INSERT INTO export_config(id,type,title,width,value,calc)
        VALUES (#{id},#{type},#{title},#{width},#{value},#{calc})
    </insert>


    <!-- ============================= SELECT ============================= -->
    <select id="findByType" resultType="com.jsunicom.oms.model.base.ExportConfig">
        SELECT id,type,title,width,value,calc
         FROM export_config
        <where>
            type = #{type}
        </where>
    </select>

</mapper>
