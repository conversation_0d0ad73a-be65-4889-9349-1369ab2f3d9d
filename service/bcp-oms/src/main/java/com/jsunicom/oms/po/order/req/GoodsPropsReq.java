package com.jsunicom.oms.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel(description = "订单修改请求报文商品属性")
public class GoodsPropsReq {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "构成属性编码", required = true, position = 1)
	private String propCode;

	@ApiModelProperty(value = "构成属性值", required = true, position = 2)
	private String propValue;

	@ApiModelProperty(value = "构成属性名称", required = true, position = 3)
	private String propName;

	@ApiModelProperty(value = "构成属性描述", required = true, position = 4)
	private String propDesc;

	@ApiModelProperty(value = "构成属性组", required = true, position = 5)
	private String propGroup;


}
