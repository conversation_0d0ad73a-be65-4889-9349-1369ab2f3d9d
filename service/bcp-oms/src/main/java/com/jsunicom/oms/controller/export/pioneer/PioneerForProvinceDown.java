package com.jsunicom.oms.controller.export.pioneer;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PioneerForProvinceDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 1,value = "地市名称")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 2,value = "校区数量")
    @JSONField(name = "campusNum")
    private String campusNum;
    @ExcelProperty(index = 3,value = "宿舍楼栋数量")
    @JSONField(name = "dormitorNum")
    private String dormitorNum;
    @ExcelProperty(index = 4,value = "已覆盖宽带先锋楼栋数量")
    @JSONField(name = "dormitorCoverNum")
    private String dormitorCoverNum;
    @ExcelProperty(index = 5,value = "宿舍楼栋宽带先锋覆盖率")
    @JSONField(name = "coverageRate")
    private String coverageRate;
}
