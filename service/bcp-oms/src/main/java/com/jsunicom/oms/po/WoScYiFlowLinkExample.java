package com.jsunicom.oms.po;

import java.util.ArrayList;
import java.util.List;

public class WoScYiFlowLinkExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYiFlowLinkExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("FLOW_ID is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("FLOW_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("FLOW_ID =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("FLOW_ID <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("FLOW_ID >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("FLOW_ID >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("FLOW_ID <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("FLOW_ID <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("FLOW_ID like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("FLOW_ID not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("FLOW_ID in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("FLOW_ID not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("FLOW_ID between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("FLOW_ID not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowNameIsNull() {
            addCriterion("FLOW_NAME is null");
            return (Criteria) this;
        }

        public Criteria andFlowNameIsNotNull() {
            addCriterion("FLOW_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andFlowNameEqualTo(String value) {
            addCriterion("FLOW_NAME =", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameNotEqualTo(String value) {
            addCriterion("FLOW_NAME <>", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameGreaterThan(String value) {
            addCriterion("FLOW_NAME >", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameGreaterThanOrEqualTo(String value) {
            addCriterion("FLOW_NAME >=", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameLessThan(String value) {
            addCriterion("FLOW_NAME <", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameLessThanOrEqualTo(String value) {
            addCriterion("FLOW_NAME <=", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameLike(String value) {
            addCriterion("FLOW_NAME like", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameNotLike(String value) {
            addCriterion("FLOW_NAME not like", value, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameIn(List<String> values) {
            addCriterion("FLOW_NAME in", values, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameNotIn(List<String> values) {
            addCriterion("FLOW_NAME not in", values, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameBetween(String value1, String value2) {
            addCriterion("FLOW_NAME between", value1, value2, "flowName");
            return (Criteria) this;
        }

        public Criteria andFlowNameNotBetween(String value1, String value2) {
            addCriterion("FLOW_NAME not between", value1, value2, "flowName");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIsNull() {
            addCriterion("LINK_CODE is null");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIsNotNull() {
            addCriterion("LINK_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andLinkCodeEqualTo(String value) {
            addCriterion("LINK_CODE =", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotEqualTo(String value) {
            addCriterion("LINK_CODE <>", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeGreaterThan(String value) {
            addCriterion("LINK_CODE >", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LINK_CODE >=", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLessThan(String value) {
            addCriterion("LINK_CODE <", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLessThanOrEqualTo(String value) {
            addCriterion("LINK_CODE <=", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeLike(String value) {
            addCriterion("LINK_CODE like", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotLike(String value) {
            addCriterion("LINK_CODE not like", value, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeIn(List<String> values) {
            addCriterion("LINK_CODE in", values, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotIn(List<String> values) {
            addCriterion("LINK_CODE not in", values, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeBetween(String value1, String value2) {
            addCriterion("LINK_CODE between", value1, value2, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkCodeNotBetween(String value1, String value2) {
            addCriterion("LINK_CODE not between", value1, value2, "linkCode");
            return (Criteria) this;
        }

        public Criteria andLinkNameIsNull() {
            addCriterion("LINK_NAME is null");
            return (Criteria) this;
        }

        public Criteria andLinkNameIsNotNull() {
            addCriterion("LINK_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andLinkNameEqualTo(String value) {
            addCriterion("LINK_NAME =", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotEqualTo(String value) {
            addCriterion("LINK_NAME <>", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameGreaterThan(String value) {
            addCriterion("LINK_NAME >", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameGreaterThanOrEqualTo(String value) {
            addCriterion("LINK_NAME >=", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLessThan(String value) {
            addCriterion("LINK_NAME <", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLessThanOrEqualTo(String value) {
            addCriterion("LINK_NAME <=", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameLike(String value) {
            addCriterion("LINK_NAME like", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotLike(String value) {
            addCriterion("LINK_NAME not like", value, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameIn(List<String> values) {
            addCriterion("LINK_NAME in", values, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotIn(List<String> values) {
            addCriterion("LINK_NAME not in", values, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameBetween(String value1, String value2) {
            addCriterion("LINK_NAME between", value1, value2, "linkName");
            return (Criteria) this;
        }

        public Criteria andLinkNameNotBetween(String value1, String value2) {
            addCriterion("LINK_NAME not between", value1, value2, "linkName");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeIsNull() {
            addCriterion("BACK_LINK_CODE is null");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeIsNotNull() {
            addCriterion("BACK_LINK_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeEqualTo(String value) {
            addCriterion("BACK_LINK_CODE =", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeNotEqualTo(String value) {
            addCriterion("BACK_LINK_CODE <>", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeGreaterThan(String value) {
            addCriterion("BACK_LINK_CODE >", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeGreaterThanOrEqualTo(String value) {
            addCriterion("BACK_LINK_CODE >=", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeLessThan(String value) {
            addCriterion("BACK_LINK_CODE <", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeLessThanOrEqualTo(String value) {
            addCriterion("BACK_LINK_CODE <=", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeLike(String value) {
            addCriterion("BACK_LINK_CODE like", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeNotLike(String value) {
            addCriterion("BACK_LINK_CODE not like", value, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeIn(List<String> values) {
            addCriterion("BACK_LINK_CODE in", values, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeNotIn(List<String> values) {
            addCriterion("BACK_LINK_CODE not in", values, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeBetween(String value1, String value2) {
            addCriterion("BACK_LINK_CODE between", value1, value2, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andBackLinkCodeNotBetween(String value1, String value2) {
            addCriterion("BACK_LINK_CODE not between", value1, value2, "backLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeIsNull() {
            addCriterion("NEXT_LINK_CODE is null");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeIsNotNull() {
            addCriterion("NEXT_LINK_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeEqualTo(String value) {
            addCriterion("NEXT_LINK_CODE =", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeNotEqualTo(String value) {
            addCriterion("NEXT_LINK_CODE <>", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeGreaterThan(String value) {
            addCriterion("NEXT_LINK_CODE >", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeGreaterThanOrEqualTo(String value) {
            addCriterion("NEXT_LINK_CODE >=", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeLessThan(String value) {
            addCriterion("NEXT_LINK_CODE <", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeLessThanOrEqualTo(String value) {
            addCriterion("NEXT_LINK_CODE <=", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeLike(String value) {
            addCriterion("NEXT_LINK_CODE like", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeNotLike(String value) {
            addCriterion("NEXT_LINK_CODE not like", value, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeIn(List<String> values) {
            addCriterion("NEXT_LINK_CODE in", values, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeNotIn(List<String> values) {
            addCriterion("NEXT_LINK_CODE not in", values, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeBetween(String value1, String value2) {
            addCriterion("NEXT_LINK_CODE between", value1, value2, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andNextLinkCodeNotBetween(String value1, String value2) {
            addCriterion("NEXT_LINK_CODE not between", value1, value2, "nextLinkCode");
            return (Criteria) this;
        }

        public Criteria andUploadFlagIsNull() {
            addCriterion("UPLOAD_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andUploadFlagIsNotNull() {
            addCriterion("UPLOAD_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andUploadFlagEqualTo(String value) {
            addCriterion("UPLOAD_FLAG =", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagNotEqualTo(String value) {
            addCriterion("UPLOAD_FLAG <>", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagGreaterThan(String value) {
            addCriterion("UPLOAD_FLAG >", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagGreaterThanOrEqualTo(String value) {
            addCriterion("UPLOAD_FLAG >=", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagLessThan(String value) {
            addCriterion("UPLOAD_FLAG <", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagLessThanOrEqualTo(String value) {
            addCriterion("UPLOAD_FLAG <=", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagLike(String value) {
            addCriterion("UPLOAD_FLAG like", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagNotLike(String value) {
            addCriterion("UPLOAD_FLAG not like", value, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagIn(List<String> values) {
            addCriterion("UPLOAD_FLAG in", values, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagNotIn(List<String> values) {
            addCriterion("UPLOAD_FLAG not in", values, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagBetween(String value1, String value2) {
            addCriterion("UPLOAD_FLAG between", value1, value2, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andUploadFlagNotBetween(String value1, String value2) {
            addCriterion("UPLOAD_FLAG not between", value1, value2, "uploadFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagIsNull() {
            addCriterion("AUDIT_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andAuditFlagIsNotNull() {
            addCriterion("AUDIT_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andAuditFlagEqualTo(String value) {
            addCriterion("AUDIT_FLAG =", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagNotEqualTo(String value) {
            addCriterion("AUDIT_FLAG <>", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagGreaterThan(String value) {
            addCriterion("AUDIT_FLAG >", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagGreaterThanOrEqualTo(String value) {
            addCriterion("AUDIT_FLAG >=", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagLessThan(String value) {
            addCriterion("AUDIT_FLAG <", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagLessThanOrEqualTo(String value) {
            addCriterion("AUDIT_FLAG <=", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagLike(String value) {
            addCriterion("AUDIT_FLAG like", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagNotLike(String value) {
            addCriterion("AUDIT_FLAG not like", value, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagIn(List<String> values) {
            addCriterion("AUDIT_FLAG in", values, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagNotIn(List<String> values) {
            addCriterion("AUDIT_FLAG not in", values, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagBetween(String value1, String value2) {
            addCriterion("AUDIT_FLAG between", value1, value2, "auditFlag");
            return (Criteria) this;
        }

        public Criteria andAuditFlagNotBetween(String value1, String value2) {
            addCriterion("AUDIT_FLAG not between", value1, value2, "auditFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
