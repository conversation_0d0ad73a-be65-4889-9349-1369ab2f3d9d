package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/12 19:21
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class Props implements Serializable {
    private static final long serialVersionUID = 1L;

    private String propCode;
    private String propValue;
    private String propName;
    private String propDesc;

}
