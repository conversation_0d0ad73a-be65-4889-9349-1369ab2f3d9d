package com.jsunicom.oms.po.order.req;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class ResourceInfo implements Serializable {

	private static final long serialVersionUID = 6247568443034188835L;

	@ApiModelProperty(value = "资源名称", required = true, position = 1)
	private String resourceName;

	@ApiModelProperty(value = "资源描述", required = true, position = 2)
	private String resourceDesc;

	@ApiModelProperty(value = "资源类型", required = true, position = 3)
	private String resourceType;

	@ApiModelProperty(value = "资源存放地址", required = true, position = 4)
	private String resourceUrl;

	public String getResourceName() {
		return resourceName;
	}

	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public String getResourceDesc() {
		return resourceDesc;
	}

	public void setResourceDesc(String resourceDesc) {
		this.resourceDesc = resourceDesc;
	}

	public String getResourceType() {
		return resourceType;
	}

	public void setResourceType(String resourceType) {
		this.resourceType = resourceType;
	}

	public String getResourceUrl() {
		return resourceUrl;
	}

	public void setResourceUrl(String resourceUrl) {
		this.resourceUrl = resourceUrl;
	}

}
