package com.jsunicom.oms.service.wobuy;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.entity.TdOrdSysDict;
import com.jsunicom.oms.entity.TfOrdPersonalInfo;
import com.jsunicom.oms.entity.TfWscTdcGoods;
import com.jsunicom.oms.entity.TfWscTdcGoodsItem;
import com.jsunicom.oms.request.CallBackNoticeReq;
import com.jsunicom.oms.request.CallBackNoticeRsp;
import com.jsunicom.oms.response.ResponseResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/11 10:02
 * @Since: V1.00
 */
public interface WoBuyService {

    TdOrdSysDict getSysDictByTypeAndKey(String paramType, String paramKey);

    String getDevArea(String developerId,String eparchyCode);

    Map<String,Object> getDevAreaNew(String developerId,String eparchyCode);

    void putGoodsInfo2MV(JSONObject json, String goodsId);

    String getAreaName(String areaCode);

    List<Map<String, Object>> getAreaAllJson(String areaType);

    ResponseResult querySchlNumberByTeam(Map<String,Object> param);

    ResponseResult querySchlNumber(Map<String,Object> param);

    ResponseResult queryNumberById(Map<String,Object> param);

    ResponseResult querySchlNumberOld(Map<String,Object> param);

    ResponseResult queryNumberByIdOld(Map<String,Object> param);

    ResponseResult numOccupy(Map<String,Object> param);

    ResponseResult numOccupyOld(Map<String,Object> param);

    TfOrdPersonalInfo getPersonnelInfo(String personalId);

    ResponseResult checkCustInfo(Map<String, Object> param) throws Exception;

    String getSysId(String goodsId);

    ResponseResult createOrder(Map<String, Object> param, HttpServletRequest request);

    ResponseResult autoCheckIdentity(Map<String, Object> param);

    ResponseResult qryOnWayByIdentity(Map<String,Object> param);

    ResponseResult updateOnlinePhoneState(Map<String,Object> param);

    JSONObject threeModelReqAstore(Map<String, Object> productReqBody) throws Exception;

    ResponseResult queryPayInfo(Map<String, Object> param) throws Exception;

    ResponseResult uoloadPic(MultipartFile file, Map<String, Object> param) throws IOException;

    ResponseResult getSchProtoInfos(Map<String, Object> param);

    ResponseResult cashierPay(Map<String,Object> reqInfo);

    ResponseResult payStatusQuery(Map<String,Object> reqInfo);

    CallBackNoticeRsp<JSONObject> liquidationInfo(CallBackNoticeReq req);

    ResponseResult payRefund(Map<String,Object> param);

    ResponseResult savePersonnelInfoSql(Map<String, Object> param);

    TfWscTdcGoods qryGoodsInfo(String goodsId);

    void putChnnelInfo(JSONObject json, Map<String, Object > param);

    HashMap<String, Object> queryOrdDetail(String orderId);

    void putChnnelJSIDInfo(Map<String, Object> param);

    ResponseResult checkOCR(Map<String, Object> param,HashMap map) throws Exception;

    ResponseResult getImgInfo(Map<String, Object> param) throws Exception;

    ResponseResult getDevelopInfo(Map<String, Object> param);

    Boolean checkGoodsState(String goodsId);

    String getDevArea(String developerId);

    TfWscTdcGoods getGoodsInfo(String goodsId);

    TfWscTdcGoodsItem getGoodsItem(String goodsId, String s);

    List<Map<String, String>> queryTdMEssEmpno(Map<String, Object> param);

    ResponseResult weChatUploadImage(String namePic, String base64);

    ResponseResult addWaterMark(Map<String, String> param);

}
