package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.mapper.WoSchoolCampusChannelMapper;
import com.jsunicom.oms.po.WoSchoolCampusChannel;
import com.jsunicom.oms.po.WoSchoolCampusChannelExample;
import com.jsunicom.oms.service.CampusChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Project:CampusMarketingInfoServiceImpl
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
@Slf4j
@Service
public class CampusChannelServiceImpl implements CampusChannelService {

    @Resource
    private WoSchoolCampusChannelMapper woSchoolCampusChannelMapper;

    @Override
    public List<WoSchoolCampusChannel> getCollegeList(WoSchoolCampusChannel record) {
        WoSchoolCampusChannelExample example = new WoSchoolCampusChannelExample();
        WoSchoolCampusChannelExample.Criteria criteria = example.createCriteria();
        if(record.getCampusId() != null){
            criteria.andCampusIdEqualTo(record.getCampusId());
        }
        List<WoSchoolCampusChannel> collegeList = woSchoolCampusChannelMapper.selectByExample(example);
        return collegeList;
    }

    @Override
    public List<WoSchoolCampusChannel> getChannelList(WoSchoolCampusChannel record) {
        WoSchoolCampusChannelExample example = new WoSchoolCampusChannelExample();
        WoSchoolCampusChannelExample.Criteria criteria = example.createCriteria();
        if(record.getCampusId() != null){
            criteria.andCampusIdEqualTo(record.getCampusId());
        }
        if(record.getChannelId() != null){
            criteria.andChannelIdEqualTo(record.getChannelId());
        }
        List<WoSchoolCampusChannel> collegeList = woSchoolCampusChannelMapper.selectByExample(example);
        return collegeList;    }

    @Override
    public Integer selectChannelNum(WoSchoolCampusChannel record) {

        Integer collegeList = woSchoolCampusChannelMapper.selectChannelNum(record.getCampusId());
        return collegeList;
    }

    @Override
    public Integer selectChannelZYNum(WoSchoolCampusChannel record) {

        Integer collegeList = woSchoolCampusChannelMapper.selectChannelZYNum(record.getCampusId());
        return collegeList;
    }

    @Override
    public Integer selectChannelDLNum(WoSchoolCampusChannel record) {

        Integer collegeList = woSchoolCampusChannelMapper.selectChannelDLNum(record.getCampusId());
        return collegeList;
    }

    @Override
    public int saveCollegeExtendInfo(WoSchoolCampusChannel record){
        return woSchoolCampusChannelMapper.insertSelective(record);
    }

    public int updateCollegeInfo(WoSchoolCampusChannel record)
    {
        return woSchoolCampusChannelMapper.updateByPrimaryKeySelective(record);
    }
    public int deleteCollegeInfo(Integer collegeId,String channelId){
        return woSchoolCampusChannelMapper.deleteByPrimaryKey(collegeId,channelId);
    }
}
