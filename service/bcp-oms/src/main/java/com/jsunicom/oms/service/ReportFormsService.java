package com.jsunicom.oms.service;

import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;

public interface ReportFormsService {

    /**
     * 查询青创社报表数据
     * @param orgCode 地市编码 (可选)
     * @param campusName 校区名称 (可选, 模糊查询)
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 报表数据列表 (分页)
     */
    PageInfo<Map<String, Object>> getYouthInnovationReportData(
            String orgCode,
            String campusName,
            int pageNum,
            int pageSize
    );

    /**
     * 查询活动报表数据
     * @param orgCode 地市编码 (可选)
     * @param campusName 校区名称 (可选, 模糊查询)
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 报表数据列表 (分页)
     */
    PageInfo<Map<String, Object>> getActivityReportData(
            String orgCode,
            String campusName,
            int pageNum,
            int pageSize
    );
} 