package com.jsunicom.oms.entity;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * .
 *
 * @ClassName: CampusExcelDown
 * @Auther: LJ.<PERSON>
 * @Date: 2022/3/25 17:31
 * @Version: bcpV1.0
 * @Description: down
 **/
@Data
@Getter
@Setter
public class CampusExcelDown extends BaseRowModel implements Serializable {
  private static final long serialVersionUID = 6072062293213929503L;


  @ExcelProperty(index = 0,value = "校区ID")
  @JSONField(name = "CAMPUS_ID")
  private Long campusId;   //null

  /*@ExcelProperty(index = 1,value = "学校名称")
  @JSONField(name = "SCHOOL_NAME")
  private String schoolName;*/   //null

  @ExcelProperty(index = 2,value = "校区名称")
  @JSONField(name = "CAMPUS_NAME")
  private String campusName;   //null

  @ExcelProperty(index = 3,value = "校区经理")
  @JSONField(name = "PARTNER_NAME")
  private String partnerName;   //null

  @ExcelProperty(index = 4,value = "校区经理手机号")
  @JSONField(name = "MBL_NBR")
  private String mblNbr;   //null

  @ExcelProperty(index = 5,value = "地市名称")
  @JSONField(name = "ORG_NAME")
  private String orgName;   //null

  @ExcelProperty(index = 6,value = "地市编码")
  @JSONField(name = "ORG_CODE")
  private String orgCode;   //null

 /* @ExcelProperty(index = 7,value = "宽带是否接入")
  @JSONField(name = "IS_BROADBAND_ACCESS_NAME")
  private String isBroadbandAccessName;   //null

  @ExcelProperty(index = 8,value = "宽带接入类型")
  @JSONField(name = "BROADBAND_ACCESS_TYPE_NAME")
  private String broadbandAccessTypeName;   //null

  @ExcelProperty(index = 9,value = "宽带覆盖学生占比")
  @JSONField(name = "BROADBAND_COVERAGE_RATIO_NAME")
  private String broadbandCoverageRatioName;   //null

  @ExcelProperty(index = 10,value = "在校生（专本）人数")
  @JSONField(name = "JU_STUDENT")
  private String juStudent;   //null

  @ExcelProperty(index = 11,value = "在校生（硕博）人数")
  @JSONField(name = "MD_STUDENT")
  private String mdStudent;   //null

  @ExcelProperty(index = 12,value = "新生（专本）人数")
  @JSONField(name = "NEW_JU_STUDENT")
  private String newJuStudent;   //null

  @ExcelProperty(index = 13,value = "新生（硕博）人数")
  @JSONField(name = "NEW_MD_STUDENT")
  private String newMdStudent;   //null

  @ExcelProperty(index = 14,value = "学校类型")
  @JSONField(name = "SCHOOL_CAMPUS_TYPE_NAME")
  private String schoolCampusTypeName;   //null

  @ExcelProperty(index = 15,value = "5G随行专网是否覆盖")
  @JSONField(name = "IS_5G_COVERAGE_NAME")
  private String is5gCoverageName;   //null

  @ExcelProperty(index = 16,value = "电话")
  @JSONField(name = "TEL")
  private String tel;   //null

  @ExcelProperty(index = 17,value = "邮编")
  @JSONField(name = "POST_CODE")
  private String postCode;   //null

  @ExcelProperty(index = 18,value = "地址")
  @JSONField(name = "ADDRESS")
  private String address;   //null*/

  @ExcelProperty(index = 19,value = "创建人")
  @JSONField(name = "CREATE_BY")
  private String CREATE_BY;   //null

  @ExcelProperty(index = 20,value = "创建时间")
  @JSONField(name = "CREATE_TIME")
  private java.sql.Timestamp createTime;   //null

  @ExcelProperty(index = 21,value = "更新人")
  @JSONField(name = "UPDATE_BY")
  private String updateBy;   //null

  @ExcelProperty(index = 22,value = "更新时间")
  @JSONField(name = "UPDATE_TIME")
  private java.sql.Timestamp updateTime;   //null

  /*@ExcelProperty(index = 23,value = "备注")
  @JSONField(name = "REMARK")
  private String remark; */  //null

}
