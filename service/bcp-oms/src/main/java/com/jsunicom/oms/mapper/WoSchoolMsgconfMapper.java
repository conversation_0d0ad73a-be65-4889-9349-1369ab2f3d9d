package com.jsunicom.oms.mapper;

import com.jsunicom.oms.po.WoSchoolMsgconf;
import com.jsunicom.oms.po.WoSchoolMsgconfExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WoSchoolMsgconfMapper {
    long countByExample(WoSchoolMsgconfExample example);

    int deleteByExample(WoSchoolMsgconfExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WoSchoolMsgconf record);

    int insertSelective(WoSchoolMsgconf record);

    List<WoSchoolMsgconf> selectByExample(WoSchoolMsgconfExample example);

    WoSchoolMsgconf selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") WoSchoolMsgconf record, @Param("example") WoSchoolMsgconfExample example);

    int updateByExample(@Param("record") WoSchoolMsgconf record, @Param("example") WoSchoolMsgconfExample example);

    int updateByPrimaryKeySelective(WoSchoolMsgconf record);

    int updateByPrimaryKey(WoSchoolMsgconf record);
}