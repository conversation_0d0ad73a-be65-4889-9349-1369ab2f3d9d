package com.jsunicom.oms.model.woshcool;


import com.jsunicom.oms.model.base.BasicModel;

/**
 * Created by delina-tt on 2018/12/24.
 */
public class WoSchoolParamDto extends BasicModel {
    private Long id;
    private String schooleCampusName;
    private String schooleManagePhone;
    private String eparchyCode;
    private String campusName;
    private String startTime;
    private String endTime;
    private Integer pageNumber;
    private Integer pageSize;
    private String meetName;
    private String activityName;
    private String taskName;
    private String taskSceneTypeCode;
    private String orderState;

    public void setTaskSceneTypeCode(String taskSceneTypeCode) {
        this.taskSceneTypeCode = taskSceneTypeCode;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getTaskSceneTypeCode() {
        return taskSceneTypeCode;
    }

    public String getOrderState() {
        return orderState;
    }

    public String getMeetName() {
        return meetName;
    }

    public void setMeetName(String meetName) {
        this.meetName = meetName;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getSchooleCampusName() {
        return schooleCampusName;
    }

    public void setSchooleCampusName(String schooleCampusName) {
        this.schooleCampusName = schooleCampusName;
    }

    public String getSchooleManagePhone() {
        return schooleManagePhone;
    }

    public void setSchooleManagePhone(String schooleManagePhone) {
        this.schooleManagePhone = schooleManagePhone;
    }

    public String getEparchyCode() {
        return eparchyCode;
    }

    public void setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "WoSchoolParamDto{" +
                "id=" + id +
                ", schooleCampusName=" + schooleCampusName +
                ", schooleManagePhone='" + schooleManagePhone + '\'' +
                ", eparchyCode='" + eparchyCode + '\'' +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
