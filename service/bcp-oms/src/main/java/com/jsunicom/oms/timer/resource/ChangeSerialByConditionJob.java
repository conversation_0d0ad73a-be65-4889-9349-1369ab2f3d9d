package com.jsunicom.oms.timer.resource;

import com.jsunicom.oms.mapper.resource.TnSerialIdleMapper;
import com.jsunicom.oms.po.resource.TnSerialIdle;
import com.jsunicom.oms.service.resource.ChangeSerialByConditionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * 定时任务，从号池中取出当天导入的号码，判断是否有融合关系，存在融合关系的号码自动下架
 * <AUTHOR>
 * @date 2023/5/10
 **/

@Slf4j
@Component
public class ChangeSerialByConditionJob {

    @Resource(name = "changeSerialByConditionService")
    private ChangeSerialByConditionService changeSerialByConditionService;

    @Resource
    private TnSerialIdleMapper tnSerialIdleMapper;

    @XxlJob("changeSerialByConditionJob")
    public ReturnT<String> changeSerialByConditionJob(String param) {
        log.info("ChangeSerialByConditionJob start...");
        //查询当天导入的号码
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        String currentTime = dateFormat.format(calendar.getTime());
        log.info("ChangeSerialByConditionJob currentTime is={}",currentTime);
        //查询移网号池中的号码
        List<TnSerialIdle> mobileSerialList = tnSerialIdleMapper.selectSerialForCurrentImportAndMobileNetwork(currentTime);
        for (TnSerialIdle serialIdle : mobileSerialList) {
            changeSerialByConditionService.expireMobileSerialNo(serialIdle);
        }
        //查询融合号池中的号码
        List<TnSerialIdle> fusionSerialList = tnSerialIdleMapper.selectSerialForCurrentImportAndFusion(currentTime);
        for (TnSerialIdle serialIdle : fusionSerialList) {
            changeSerialByConditionService.expireFusionSerialNo(serialIdle);
        }
        log.info("ChangeSerialByConditionJob end...");
        return ReturnT.SUCCESS;
    }

}
