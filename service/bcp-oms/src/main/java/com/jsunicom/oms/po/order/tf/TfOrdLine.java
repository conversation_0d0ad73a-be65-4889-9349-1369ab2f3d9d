package com.jsunicom.oms.po.order.tf;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单拆分表
 * @TableName tf_ord_line
 */
@Data
public class TfOrdLine implements Serializable {
    /**
     *
     */
    private String orderLineId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 业务类型
     */
    private String busiType;

    /**
     *
     */
    private String inModeCode;

    /**
     *
     */
    private String extOrderId;

    /**
     * 订单生成时间
     */
    private Date createDate;

    /**
     * 当前处理人工号
     */
    private String dealStaffId;

    /**
     * 当前处理人
     */
    private String dealStaffName;

    /**
     * 当前处理人号码
     */
    private String dealStaffPhone;

    /**
     * 接入渠道的工号
     */
    private String staffId;

    /**
     * 接入渠道的工号
     */
    private String staffName;

    /**
     * 发展人编码
     */
    private String developerId;

    /**
     *
     */
    private String cityCode;

    /**
     *
     */
    private String eparchyCode;

    /**
     *
     */
    private String provinceCode;

    /**
     *
     */
    private String departId;

    /**
     *
     */
    private String channelId;

    /**
     *
     */
    private String channelType;

    /**
     *
     */
    private String orderNodeState;

    /**
     *
     */
    private String orderNodeCode;

    /**
     *
     */
    private String orderNodeName;

    /**
     * 主号码
     */
    private String mainNumber;

    /**
     * cbss流水id
     */
    private String busiSysOrderId;

    /**
     * 开户号码
     */
    private String serialNumber;

    /**
     * 生成结果
     */
    private String productionResult;

    /**
     * 0 移网号码,1 固话号码,2 宽带号码,3 融合号码,4 IPTV号码,5 终端号,9 权益电子券
     */
    private String netType;

    /**
     * 生产时间
     */
    private Date productionDate;

    /**
     *
     */
    private String cancelTag;

    /**
     * 操作时间
     */
    private Date cancelDate;

    /**
     *
     */
    private String cancelReason;

    /**
     *
     */
    private String cancelStaffId;

    /**
     *
     */
    private String cancelDepartId;

    /**
     *
     */
    private Date acceptDate;

    /**
     *
     */
    private Date finishDate;

    /**
     *
     */
    private String backOrderId;

    /**
     * 操作时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    private String staffPhone;

    private static final long serialVersionUID = 1L;
}
