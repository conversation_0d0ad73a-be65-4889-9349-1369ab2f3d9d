package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.export.AppletLoginLogDown;
import com.jsunicom.oms.controller.export.InnovateTaskDown;
import com.jsunicom.oms.dto.common.LoginLogDto;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.service.AppletLoginLogService;
import com.jsunicom.oms.service.DataMonitorFacade;
import com.jsunicom.oms.utils.DateUtils;
import com.jsunicom.oms.utils.ExcelUtil;
import com.jsunicom.oms.utils.MapKeyTransformation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.controller
 * @ClassName: AppletLoginLogController
 * @Author: zhaowang
 * @CreateTime: 2023-11-23  14:11
 * @Description: TODO 小程序用户登录日志查询
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/appletLoginLog")
public class AppletLoginLogController {
    @Autowired
    private AppletLoginLogService appletLoginLogService;
    @Autowired
    private DataMonitorFacade dataMonitorFacade;

    @PostMapping(value = "/queryAppletLoginLogList", name = "小程序用户登录日志列表查询")
    public CustomResult queryAppletLoginLogList(@RequestBody LoginLogDto loginLogDto, HttpServletRequest request){
        log.info("进入小程序用户登录日志查询接口 AppletLoginLogController.queryAppletLoginLogList loginLogDto:{}",loginLogDto.toString());
        try {
            PageInfo<Map<String, Object>> pageResult = appletLoginLogService.queryAppletLoginLogList(loginLogDto);
            HashMap res=new HashMap();
            res.put("list",pageResult.getList());
            res.put("total",pageResult.getTotal());
            log.info("进入小程序用户登录日志查询接口 AppletLoginLogController.queryAppletLoginLogList  success");
            return ResultUtil.success(res);
        } catch (Exception e) {
            log.info("进入小程序用户登录日志查询接口 AppletLoginLogController.queryAppletLoginLogList  fail:"+e.getMessage());
            return ResultUtil.error("9999",e.getMessage());
        }
    }

    @PostMapping(value = "/exportAppletLoginLogList", name = "小程序用户登录日志导出")
    public void exportAppletLoginLogList(@RequestBody LoginLogDto paramDto, HttpServletRequest request, HttpServletResponse response){
        log.info("exportAppletLoginLogList start>>>> ");
        try {
            PageInfo<Map<String, Object>> pageResult = appletLoginLogService.queryAppletLoginLogList(paramDto);
            paramDto.setPageNumber(1);
            paramDto.setPageSize(Integer.valueOf(pageResult.getTotal()+""));
            pageResult = appletLoginLogService.queryAppletLoginLogList(paramDto);

            List<Map<String, Object>> list = MapKeyTransformation.toReplaceKeyLowForList(pageResult.getList());
            List<AppletLoginLogDown> appletLoginLogDowns = new ArrayList<>();
            for (Map map : list) {
                AppletLoginLogDown appletLoginLogDown= JSONObject
                        .parseObject(JSONObject.toJSONString(map),AppletLoginLogDown.class);
                appletLoginLogDowns.add(appletLoginLogDown);
            }

            dataMonitorFacade.save("applet_login_log","export","",request.getHeader("staffNo"),JSONObject.toJSONString(paramDto));

            ExcelUtil.writeExcel(response,appletLoginLogDowns,"小程序用户登录日志","小程序用户登录日志",
                    new AppletLoginLogDown());
            log.info("导出任务记录列表exportAppletLoginLogList success");
        } catch (Exception e) {
            log.error("导出任务记录列表exportAppletLoginLogList error", e);
        }
    }

}
