package com.jsunicom.oms.service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024-01-31-15:33
 */
public interface DevelopCollectService {
    HashMap boardForProvince(String orgCode, String startTime, String endTime);

    HashMap boardForCity(String orgCode, String campusName, String phone, String startTime, String endTime,String pageNum,String size);

    HashMap boardForPartner(String startTime, String endTime, String orgCode, String campusName, String societyName, String phone,String pageNum,String size);

    HashMap channelForProvince(String orgCode, String startTime, String endTime);

    HashMap channelForCity(String orgCode, String campusName, String phone, String startTime, String endTime, String pageNum, String size);

    HashMap channelForPartner(String startTime, String endTime, String orgCode, String campusName, String channelName, String phone, String pageNum, String size);
}
