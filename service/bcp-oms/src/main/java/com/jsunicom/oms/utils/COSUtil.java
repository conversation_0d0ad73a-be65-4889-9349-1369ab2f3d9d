package com.jsunicom.oms.utils;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * S3Test
 */
@Component
public class COSUtil {

    @Value("${common.object.cos.private.bucket:0}")
    private String bucket;
    @Autowired
    private AmazonS3 conn;


    public  String upload(String key, String content) {
        PutObjectResult result = conn.putObject(bucket, key, content);
        return key;
    }

    // 列出所有桶列表
    public  List<Bucket> getBuckets(){
        List<Bucket> buckets = conn.listBuckets();
        return buckets;
    }

    // 列出桶内所有对象
    public  List<String> getObjects(){
        List<String> keyNames = new ArrayList<>();
        ObjectListing objects = conn.listObjects(bucket);
        do {
            for (S3ObjectSummary objectSummary : objects.getObjectSummaries()) {
                keyNames.add(objectSummary.getKey());
            }
            objects = conn.listNextBatchOfObjects(objects);
        } while (objects.isTruncated());
        return keyNames;
    }

    // 创建对象
    public  void putObject(String keyName, InputStream input){
        //ByteArrayInputStream input = new ByteArrayInputStream("Hello World!".getBytes());
        ObjectMetadata objectMetadata = new ObjectMetadata();
       // objectMetadata.setContentLength(instream.available());
        objectMetadata.setCacheControl("no-cache");
        objectMetadata.setHeader("Pragma", "no-cache");
        objectMetadata.setContentType(getcontentType(keyName.substring(keyName.lastIndexOf("."))));
        objectMetadata.setContentDisposition("inline;filename=" + keyName);
        PutObjectResult putObjectResult = conn.putObject(bucket, keyName, input, objectMetadata);
    }

    // 修改对象的访问控制权限
    public  void setObjectAcl(String keyName, CannedAccessControlList acl){
        conn.setObjectAcl(bucket, keyName, acl);
    }

    // 下载一个对象(到本地文件)
    public  void getObject(String keyName, String filePath, String fileName){
        conn.getObject(new GetObjectRequest(bucket, keyName), new File(filePath + fileName));
    }

    // 生成对象下载链接（带签名）
    public  String getKeyUrl(String keyName, Date expiration){
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, keyName);
        if (null != expiration){
            request.withExpiration(expiration);
        }
        URL url = conn.generatePresignedUrl(request);
        if (null == url){
            return null;
        }
        return url.toString();
    }

    // 删除一个对象
    public  void deleteObject(String keyName){
        conn.deleteObject(bucket, keyName);
    }
    /**
     * Description: 判断OSS服务文件上传时文件的contentType
     *
     * @param FilenameExtension 文件后缀
     * @return String
     */
    public static String getcontentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase("bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase("gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase("jpeg") ||
                FilenameExtension.equalsIgnoreCase("jpg") ||
                FilenameExtension.equalsIgnoreCase("png")) {
            return "image/jpeg";
        }
        if (FilenameExtension.equalsIgnoreCase("html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase("txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase("vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase("pptx") ||
                FilenameExtension.equalsIgnoreCase("ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase("docx") ||
                FilenameExtension.equalsIgnoreCase("doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase("xml")) {
            return "text/xml";
        }
        return "image/jpeg";
    }


    /*public static void main(String[] args) {

        // 创建桶
        /*Bucket bucket = conn.createBucket("my-new-bucket");
        System.out.println(bucket.getName());*/

        //删除桶
        //conn.deleteBucket(bucket.getName());

        // 修改对象的访问控制权限
        //setObjectAcl("hello2.txt", CannedAccessControlList.PublicRead);


        // 生成对象下载链接（带签名）*/
        /*String s = getKeyUrl("hello2.txt", null);
        System.out.println(s);*/

        // 删除一个对象
        //conn.deleteObject(bucket.getName(), "hello.txt");



    //}
}
