package com.jsunicom.oms.mapper;

import com.jsunicom.oms.dto.report.ExtendReportDto;
import com.jsunicom.oms.dto.report.ReportDto;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.po.WoScYiMeeting;
import com.jsunicom.oms.po.WoScYiMeetingExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReportMapper {
    List<Map<String,Object>> queryMeettingAndActivity(WoSchoolParamDto paramDto);

    List<Map<String,Object>> queryTaskCompletionRate(WoSchoolParamDto paramDto);

    List<ReportDto> queryCompletionRateByEarpchy(Map<String,Object> map);

    List<ExtendReportDto> queryExtendReport(Map<String,Object> map);
}
