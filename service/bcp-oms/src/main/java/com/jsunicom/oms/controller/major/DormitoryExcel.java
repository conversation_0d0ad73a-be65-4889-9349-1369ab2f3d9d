package com.jsunicom.oms.controller.major;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-07-06-17:15
 */
@Data
public class DormitoryExcel extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 2672917869360006647L;
    @ExcelProperty(index = 0)
    private String dormitoryName;
    @Override
    public boolean equals(Object object){
        if (object instanceof DormitoryExcel) {
            DormitoryExcel o = (DormitoryExcel) object;
            return StringUtils.equals(this.dormitoryName,o.getDormitoryName());
        }
        else{
            return false;
        }

    }
}
