package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/12 18:55
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TfWscTdcItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String propId;
    private String tdcId;
    private String proptCode;
    private String propValue;
    private String propName;
    private String propDesc;

    public static final String PROP_ID = "PROP_ID";
    public static final String TDC_ID = "TDC_ID";
    public static final String PROPT_CODE = "PROPT_CODE";
    public static final String PROP_VALUE = "PROP_VALUE";
    public static final String PROP_NAME = "PROP_NAME";
    public static final String PROP_DESC = "PROP_DESC";
}
