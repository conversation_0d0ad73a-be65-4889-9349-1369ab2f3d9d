package com.jsunicom.oms.controller.export.develop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class BoardForPartnerDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "地市名称")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 1,value = "归属校区")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 2,value = "校区经理姓名")
    @JSONField(name = "partnerName")
    private String partnerName;
    @ExcelProperty(index = 3,value = "校区经理手机号")
    @JSONField(name = "phone")
    private String phone;
    @ExcelProperty(index = 3,value = "青创社名称")
    @JSONField(name = "societyName")
    private String societyName;
    @ExcelProperty(index = 4,value = "青创社人员数量")
    @JSONField(name = "memberNum")
    private String memberNum;
    @ExcelProperty(index = 5,value = "已发展用户社员数量")
    @JSONField(name = "devMemNum")
    private String devMemNum;
    @ExcelProperty(index = 6,value = "用户存量")
    @JSONField(name = "hisMemNum")
    private String developerId;
    @ExcelProperty(index = 7,value = "用户发展量")
    @JSONField(name = "nowMemNum")
    private String hisMemNum;
}
