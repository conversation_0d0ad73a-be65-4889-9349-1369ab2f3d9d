package com.jsunicom.oms.service.order.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.common.constants.CommonProperties;
import com.jsunicom.oms.entity.TdOrdSysDict;
import com.jsunicom.oms.mapper.order.*;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.po.order.TdOrdOperator;
import com.jsunicom.oms.po.order.req.CurtCustReq;
import com.jsunicom.oms.po.order.dto.DeveloperInfo;
import com.jsunicom.oms.po.order.req.*;
import com.jsunicom.oms.po.order.tf.*;
import com.jsunicom.oms.po.order.tl.TlOrdOperatorLog;
import com.jsunicom.oms.po.order.tl.TlOrdSynphotoRecord;
import com.jsunicom.oms.service.order.*;
import com.jsunicom.oms.utils.AaopUtil;
import com.jsunicom.oms.utils.HttpUtil;
import com.jsunicom.oms.utils.S3Util;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class WoActiveNoticeServiceImpl implements WoActiveNoticeService {

    @Autowired
    private GenerateId generateId;

    @Autowired
    private TfOrdSvcnumActiveMapper tfOrdSvcnumActiveMapper;

    @Autowired
    private WoActiveNoticeMapper woActiveNoticeMapper;

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private TfOrderItemMapper tfOrderItemMapper;

    @Autowired
    private TfOrdGoodsMainMapper tfOrdGoodsMainMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private TlOrdOperatorLogService tlOrdOperatorLogService;

    @Autowired
    private TlOrdOperatorLogMapper tlOrdOperatorLogMapper;

    @Resource
    private CommonProperties commonProperties;

    @Autowired
    private TfOrdCustinfoMapper  tfOrdCustinfoMapper;

    @Autowired
    private TfOrdAddressMapper tfOrdAddressMapper;

    @Autowired
    private TfOrdLineMapper tfOrdLineMapper;

    @Autowired
    private OrderQueryService orderQueryService;

    @Autowired
    private QueryCBSSTradeLog queryCBSSTradeLog;

    @Autowired
    private TlOrdSynphotoRecordMapper tlOrdSynphotoRecordMapper;

    @Autowired
    private SendSmsService sendSmsService;

    @Resource
    private OrderMonitorJobMapper orderMonitorJobMapper;

    private final static String ACTIVATION_CONDITION = "activationstate";
    private final static String IMAGE_PREFIX = "https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/";

    @Override
    public WoActiveNoticeOutModel woActiveNotice(WoActiveNoticeInModel inModel) throws Exception {
        WoActiveNoticeOutModel outModel = new WoActiveNoticeOutModel();
        try{
            checkParam(inModel);

            //判断订单金额，如果订单有钱，则状态为待充值
            TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(inModel.getOrderBody().getOrderCenterId());
            if (ordMain == null) {
                throw new RuntimeException("根据订单号" + inModel.getOrderBody().getOrderCenterId()+ "未查询到订单");
            }
            //已经激活过的订单，直接返回成功
            if(ordMain.getOrderState().equals("0050") || ordMain.getOrderState().equals("0060")){
                outModel.setRespCode("0000");
                outModel.setRespDesc("成功");
                return outModel;
            }

            String activeId = insertActiveInfo(inModel.getOrderBody());
            insertActiveResourceInfo(inModel.getOrderBody(), activeId);
            //保存操作员位置信息 add by guqf6 190605
            saveOperPosition(inModel.getOrderBody());
            String liveSource = inModel.getOrderBody().getLiveSource();
            String goodsOrderId = inModel.getOrderBody().getGoodsOrderId();
            TfOrdGoodsMain tfOrdGoodsMain = tfOrdGoodsMainMapper.selectByPrimaryKey(goodsOrderId);
            String orderCenterId = tfOrdGoodsMain.getOrderId();

            Map<String, Object> param = new HashMap<String, Object>();
            param.put("goodsOrderId", goodsOrderId);
            Map<String, Object> resTemp = woActiveNoticeMapper.getDeliveryCANWL(param);
            if (resTemp == null) {
                throw new RuntimeException("根据客户订单号" + goodsOrderId + "未找到订单");
            }

            // 拦截订单请求, 如果是微信侧并且配送方式非DT01, 则报错
            String deliveryType = resTemp.get("DELIVERY_TYPE").toString();
            String canWL = resTemp.get("CAN_WL").toString();
            String sign = inModel.getOrderHead().getSign();
            if ("0".equals(canWL) && sign != null && sign.startsWith("weixin") && !"DT01".equals(deliveryType)) {
                outModel.setRespCode("9999");
                outModel.setRespDesc("非物流订单请联系工作人员");
                return outModel;
            }

            JSONObject result = new JSONObject();
            result.put("goodsOrderId", goodsOrderId);
            result.put("liveSource", liveSource);

            //激活返档处理
            Result rsp=this.activityHandle(goodsOrderId,liveSource);
            if(rsp.getSuccess()){
                try{
                    //激活后生成待同步照片工单数据
                    TlOrdSynphotoRecord record =new TlOrdSynphotoRecord();
                    record.setOrderId(orderCenterId);
                    record.setSynState("0");
                    record.setActivieId("");
                    record.setSerialNumber("");
                    record.setErrorMsg("");
                    record.setRemark("");
                    tlOrdSynphotoRecordMapper.insert(record);


                    BigDecimal realMoney = new BigDecimal(ordMain.getRealAmount());
                    int r=realMoney.compareTo(BigDecimal.ZERO);
                    if(r==0){
                        //0元
                        //暂时实时归档
                        TlOrdOperatorLog operatorLog=new TlOrdOperatorLog();
                        operatorLog.setOrderId(orderCenterId);
                        operatorLog.setNodeCode("file");
                        operatorLog.setReasonName("归档");
                        operatorLog.setReasonDesc("自动归档" );
                        operatorLog.setReasonCode("success");
                        operatorLog.setOrderState("0060");
                        operatorLog.setOperatorId(ordMain.getStaffId());
                        tlOrdOperatorLogService.orderOperComplete(operatorLog);


                    }else{
                        //归档中，生成待充值记录
                        TfOrdPayRecord payRecord = new TfOrdPayRecord();
                        List<Map<String,String>> payInfoList = orderMonitorJobMapper.getPayInfoByOrderId(orderCenterId);
                        String serialNumber = resTemp.get("MAIN_NUMBER").toString();
                        Iterator<Map<String, String>> iterator = payInfoList.iterator();
                        while (iterator.hasNext()){
                            log.info("===woActiveNotice==11111：");
                            Map<String, String> stringMap = iterator.next();
                            String orderId = stringMap.get("ORDER_ID");
                            String busiOrderId = stringMap.get("BUSI_ORDER_ID");
                            payRecord.setPayId("order"+generateId.getId("TF_ORD_PAY_RECORD"));
                            payRecord.setOrderId(orderId);
                            payRecord.setBusiOrderId(busiOrderId);
                            log.info("===woActiveNotice==444：");

                        /*    BigDecimal fee =new BigDecimal(stringMap.get("PAY_ACT_AMT"));
                            BigDecimal orgAmount = fee.multiply(new BigDecimal(100)); //单位 元转分
                            payRecord.setFee(orgAmount);*/
                            int fee = Double.valueOf(stringMap.get("PAY_ACT_AMT")).intValue()*100;//单位 元转分
                            payRecord.setFee(String.valueOf(fee));

                            payRecord.setSerialNumber(serialNumber);
                            payRecord.setOperTag("0");  //缴费
                            payRecord.setOperResult("2"); //初始化
                            payRecord.setCreateDate(new Date());
                            payRecord.setPayNum("0");
                            payRecord.setEparchyCode(stringMap.get("EPARCHY_CODE"));
                            payRecord.setFeeCode("100006");  //待确认  现网校园用100006
                            orderMonitorJobMapper.insertTfOrdPayRecordSelective(payRecord);

                            TlOrdOperatorLog operatorLog=new TlOrdOperatorLog();
                            operatorLog.setOrderId(orderCenterId);
                            operatorLog.setNodeCode("pay");
                            operatorLog.setReasonName("插入缴费记录表");
                            operatorLog.setReasonDesc("插入缴费记录表：" + serialNumber + " 缴费金额:" + new BigDecimal(stringMap.get("PAY_ACT_AMT")) + "元");
                            operatorLog.setReasonCode("insertLog");
                            operatorLog.setOperType("pay");
                            tlOrdOperatorLogService.recordOrderOperInfo(operatorLog);
                        }

                    }
                }catch (Exception e){
                    log.error("===woActiveNotice==激活后归档处理失败："+e.getMessage());
                }


            }


        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(), e);
            throw new Exception("激活通知接口出错," + (StringUtils.isEmpty(e.getMessage()) ? "系统内部异常" : e.getMessage()));
        }
        return outModel;
    }

    private void insertOrderItem(String orderCenterId, String wslPhoneOrderId) throws ParseException {
        TfOrderItem item = new TfOrderItem();
        item.setOrderId(orderCenterId);
        item.setAttrType("1");
        item.setAttrCode("wslPhotoOrderId");
        item.setValueName("拍照流水号");
        item.setAttrValue(wslPhoneOrderId);
        item.setStartDate(new Date());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        item.setEndDate(sdf.parse("2050-12-30 23:59:59"));
        item.setModifyTag("0");
        tfOrderItemMapper.insert(item);
    }

    private void saveOperPosition(WoActiveNoticeBody woActiveNoticeBody) {

        Operator operatorPos = woActiveNoticeBody.getOperator();

        if (null != operatorPos) {

            String orderCenterId = woActiveNoticeBody.getOrderCenterId();
            // String taskKey = woActiveNoticeDao.getOrderTaskKey(orderCenterId);

            HashMap<String, Object> param = new HashMap();
            String id = generateId.getId("TF_ORD_OPERATOR_POS");
            param.put("id", id);
            param.put("orderId", orderCenterId);
            //TODO taskKey待确认
            // param.put("taskKey", taskKey);
            param.put("operValue", "success");
            param.put("staffId", operatorPos.getOperId());
            if (StringUtils.isBlank(operatorPos.getReason())) {
                param.put("latitude", operatorPos.getLatitude());
                param.put("longitude", operatorPos.getLongitude());
                param.put("address", operatorPos.getAddress());
            } else {
                param.put("reason", operatorPos.getReason());
            }
            woActiveNoticeMapper.insertOperPos(param);
        }
    }

    public void insertActiveResourceInfo(WoActiveNoticeBody woActiveNoticeBody, String activeId) {
        List<ResourceInfo> resourceInfos = woActiveNoticeBody.getResourceInfo();
        String activeType = woActiveNoticeBody.getActiveType();
        String orderCenterId = woActiveNoticeBody.getOrderCenterId();
        Map<String, Object> paramMap = new HashMap<>();
        if (resourceInfos != null && resourceInfos.size() > 0) {
            for (int i = 0; i < resourceInfos.size(); i++) {
                ResourceInfo resourceInfo = resourceInfos.get(i);
                // 当资源时07时, 文件类型为1时，进行加水印
                 if ("07".equals(activeType) && "1".equals(resourceInfo.getResourceType()) && StrUtil.isNotBlank(resourceInfo.getResourceUrl())) {
                     resourceInfo.setResourceType("3");
                 }
                String id = generateId.getId("TF_ORD_SVCNUM_ACTIVE_RES");
                paramMap.put("id", id);
                paramMap.put("activeId", activeId);
                paramMap.put("resDesc", resourceInfo.getResourceDesc());
                paramMap.put("url", IMAGE_PREFIX + resourceInfo.getResourceUrl());
                paramMap.put("resType", resourceInfo.getResourceType());
                paramMap.put("resName", resourceInfo.getResourceName());
                woActiveNoticeMapper.insertActiveResourceInfo(paramMap);
                // woActiveNoticeDao.insertActiveResourceInfo(paramMap);
            }
        }
    }

    //TODO 需要确认是否会更新非资源信息  需要确认是否删除原先照片
    public String insertActiveInfo(WoActiveNoticeBody body) {
        Map<String, Object> paramMap = new HashMap<>();
        String id = generateId.getId("TF_ORD_SVCNUM_ACTIVE");
        TfOrdSvcnumActive ordSvcnumActive = new TfOrdSvcnumActive();
        ordSvcnumActive.setId(id);
        ordSvcnumActive.setOrderId(body.getOrderCenterId());
        ordSvcnumActive.setSerialNumber(body.getSerialNumber());
        ordSvcnumActive.setState(body.getValidResult());
        ordSvcnumActive.setActiveDesc(body.getValidDesc());
        ordSvcnumActive.setRemark("");
        ordSvcnumActive.setCreateDate(new Date());
        ordSvcnumActive.setOperId("");
        ordSvcnumActive.setScore(StringUtils.isEmpty(body.getLiveSource()) ? "" : body.getLiveSource());
        ordSvcnumActive.setActiveType(body.getActiveType());
        ordSvcnumActive.setLiveMsg(StringUtils.isEmpty(body.getLiveMsg()) ? "" : body.getLiveMsg());
        ordSvcnumActive.setLiveState(StringUtils.isEmpty(body.getLiveState()) ? "" : body.getLiveState());
        String custName = null;
        String psptId = null;
        List<Map<String, Object>> custInfoList = woActiveNoticeMapper.getPsptInfo(body.getOrderCenterId());
        if (custInfoList != null && !custInfoList.isEmpty()) {
            Map<String, Object> custInfo = custInfoList.get(0);
            if (custInfo != null) {
                custName = (String) custInfo.get("custName");
                psptId = (String) custInfo.get("psptId");
            }
        }
        custName = StringUtils.isEmpty(custName) ? "" : custName;
        psptId = StringUtils.isEmpty(psptId) ? "" : psptId;
        ordSvcnumActive.setCustName(custName);
        ordSvcnumActive.setPsptId(psptId);
        tfOrdSvcnumActiveMapper.insertSelective(ordSvcnumActive);
        return id;

    }

    private void checkParam(WoActiveNoticeInModel inModel)throws Exception {
        if (inModel == null) {
            throw new Exception("传入参数不能为空!");
        }
        WoActiveNoticeBody body = inModel.getOrderBody();
        if (body == null) {
            throw new Exception("传入参数体不能为空!");
        }
        List<ResourceInfo> resourceInfos = body.getResourceInfo();
        if (resourceInfos == null || resourceInfos.isEmpty()) {
            throw new Exception("传入参数体资源信息不能为空!");
        }

        if (StringUtils.isEmpty(body.getOrderCenterId())) {
            throw new Exception("订单号不能为空!");
        }
        if (StringUtils.isEmpty(body.getGoodsOrderId())) {
            throw new Exception("商品订单号不能为空!");
        }
   /*     if (StringUtils.isEmpty(body.getProductOrderId())) {
            throw new Exception("产品订单号不能为空!");
        }*/
        if (StringUtils.isEmpty(body.getSerialNumber())) {
            throw new Exception("开户号码不能为空!");
        }
        /*
         * if (StringUtils.isEmpty(body.getValidDesc())) { throw new
         * Exception("校验结果描述不能为空!"); }
         */
        if (StringUtils.isEmpty(body.getValidResult())) {
            throw new Exception("校验结果不能为空!");
        }
        for (ResourceInfo resourceInfo : resourceInfos) {
            if (StringUtils.isEmpty(resourceInfo.getResourceName())) {
                throw new Exception("资源名称不能为空!");
            }
            if (StringUtils.isEmpty(resourceInfo.getResourceDesc())) {
                throw new Exception("资源描述不能为空!");
            }
            if (StringUtils.isEmpty(resourceInfo.getResourceType())) {
                throw new Exception("资源类型不能为空!");
            }
            if (StringUtils.isEmpty(resourceInfo.getResourceUrl())) {
                throw new Exception("资源存放地址不能为空!");
            }
        }

    }
    /**
     * 根据订单号，处理激活订单回调
     *
     * @param goodsOrderId
     */
    @Transactional(
            rollbackFor = {Exception.class}
    )
    public Result activityHandle(String goodsOrderId,String liveSource)throws Exception{
        Map<String, Object> currentNodeInfoMap = obtainOrderInfo(goodsOrderId);
        Map<String, Object> inParam = new HashMap<>();
        Result result=new Result();
        result.setSuccess(false);

        if (currentNodeInfoMap == null) {
            throw new Exception("根据goodsOrderId(" + goodsOrderId + ")未查询到当前订单流程节点");
        }
        String defkey = (String) currentNodeInfoMap.get("TASKKEY");
        String orderId =(String) currentNodeInfoMap.get("ORDER_ID");
        if ("activation".equals(defkey)) {
            inParam.put("varCode", ACTIVATION_CONDITION);
        } else {
            throw new Exception("当前订单处于" + defkey + "节点，不允许激活通过");
        }


        TdMSysDict dictParam = new TdMSysDict();
        dictParam.setParamType("liveSource");
        dictParam.setParamKey("liveSource");
        List<TdMSysDict> sysdictList=tdMSysDictMapper.queryAll(dictParam);
        if (sysdictList == null || sysdictList.size()==0) {
            throw new Exception("td_ord_sys_dict 表里需要配置活体人脸相似度的通过分数");
        }

        TlOrdOperatorLog tlOrdOperatorLog=new TlOrdOperatorLog();
        tlOrdOperatorLog.setOrderId(orderId);
        tlOrdOperatorLog.setNodeCode("activation");
        tlOrdOperatorLog.setNodeName("激活");
        tlOrdOperatorLog.setReasonCode("success");
        tlOrdOperatorLog.setReasonName("激活成功");
        tlOrdOperatorLog.setReasonDesc("激活成功" );

        try{
            int liveSourceDB = Integer.parseInt(sysdictList.get(0).getParam1());
            // 获取DB中所有激活分数, 设置最高的分数
            if (StringUtil.isBlank(liveSource)) {
                tlOrdOperatorLog.setReasonCode("fail");
                tlOrdOperatorLog.setReasonName("由于用户激活失败，无相似度分数，请中台人工审核");
                tlOrdOperatorLog.setReasonDesc("由于用户激活失败，无相似度分数，请中台人工审核" );
                tlOrdOperatorLogService.recordOrderOperInfo(tlOrdOperatorLog);
                return result;
                //    throw new Exception("用户激活失败，无相似度分数" );

            } else {
                int liveSourceInt = Integer.valueOf(liveSource);
                // 活体认证比对的分数 大于设定值
                if (hasMaxScore(liveSourceDB, liveSourceInt, orderId)) {
                    tlOrdOperatorLog.setReasonName("活体认证成功");
                    tlOrdOperatorLog.setReasonDesc("活体认证成功" );
                    tlOrdOperatorLogService.recordOrderOperInfo(tlOrdOperatorLog);

                    tlOrdOperatorLog.setReasonName("自动调用激活接口");
                    tlOrdOperatorLog.setReasonDesc("自动调用激活接口" );
                    tlOrdOperatorLog.setOperType("activationFinsh");
                    tlOrdOperatorLog.setReasonCode("success");
                    tlOrdOperatorLog.setOrderState("0050");
                    //客户返档
                    result=afterActiveService(goodsOrderId);
                    if(!result.getSuccess()){
                        tlOrdOperatorLog.setReasonCode("fail");
                        tlOrdOperatorLog.setReasonDesc("自动调用激活接口失败："+result.getMsg() );
                        tlOrdOperatorLogService.recordOrderOperInfo(tlOrdOperatorLog);
                        return result;
                        //    throw new Exception("自动调用激活接口失败" +result.getMsg() );
                    }
                    tlOrdOperatorLogService.recordOrderOperInfo(tlOrdOperatorLog);

                } else {
                    tlOrdOperatorLog.setReasonName("活体认证失败：活体分数" + liveSource + "小于标准分数" + liveSourceDB);
                    tlOrdOperatorLog.setReasonDesc("活体认证失败：活体分数" + liveSource + "小于标准分数" + liveSourceDB);
                    tlOrdOperatorLog.setReasonCode("fail");
                    tlOrdOperatorLogService.recordOrderOperInfo(tlOrdOperatorLog);
                    return result;
                    //    throw new Exception("活体认证失败：活体分数" + liveSource + "小于标准分数" + liveSourceDB);

                }

            }
        }catch (Exception e){
            throw e;
        }

        return result;


    }

    /**
     * 根据商品订单号查询订单信息
     *
     * @param goodsOrderId
     * @return
     */
    public Map<String, Object> obtainOrderInfo(String goodsOrderId) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("goodsOrderId", goodsOrderId);
        return woActiveNoticeMapper.getOrderInfoByGoodsOrderId(paramMap);
    }

    private boolean hasMaxScore(int liveSourceDB, int liveSource, String orderId) {
        if (liveSource >= liveSourceDB) {
            return true;
        }
        List<TfOrdSvcnumActive> tfOrdSvcnumActives = tfOrdSvcnumActiveMapper.getIdByOrderId(orderId);
        for (TfOrdSvcnumActive ordSvcnumActive : tfOrdSvcnumActives) {
            try {
                int score = Integer.parseInt(ordSvcnumActive.getScore());
                if (score >= liveSourceDB) {
                    return true;
                }
            } catch (NumberFormatException ignored) {
            }
        }
        return false;
    }

    /**
     * 微服务返档接口
     *
     * @param tfOrdMain
     * @param tfOrdGoodsMain
     * @return
     */
    public JSONObject microCustinfoReq(TfOrdMain tfOrdMain, TfOrdGoodsMain tfOrdGoodsMain) throws Exception {
        String orderId = tfOrdMain.getOrderId();

        // 封装操作人信息
        Map<String, Object> param1 = new HashMap<String, Object>();
        Map<String, Object> op = null;
        param1.put("eparchyCode", tfOrdMain.getEparchyCode());
        List<Map<String, Object>> operatorsList = tlOrdOperatorLogMapper.queryExistOperators(param1);
        //新增根据商品类型匹配, 若未匹配到工号, 则使用默认工号
        if (operatorsList != null && !operatorsList.isEmpty()) {
            for (Map<String, Object> operator : operatorsList) {
                if (StringUtils.equals((String) operator.get("GOODS_TYPE"), tfOrdGoodsMain.getGoodsType())) {
                    op = operator;
                    break;
                }
                if (StringUtils.isBlank((String) operator.get("GOODS_TYPE"))) {
                    op = operator;
                }
            }
        }
        if (op == null) {
            throw new Exception("orderId:" + orderId + "：操作人信息查询为空。。");
        }
        String opStr=JSONObject.toJSONString(op);
        TdOrdOperator opInfo=JSONObject.parseObject(opStr,TdOrdOperator.class);

        // 封装号码相关信息
        String serialNumber = null;
        {
            TfOrderItem item = new TfOrderItem();
            item.setOrderId(orderId);
            item.setAttrType("1");
            item.setAttrCode("0006");
            List<TfOrderItem> itemList=tfOrderItemMapper.qryOrderItems(item);
            if(itemList.size()>0){
                serialNumber=itemList.get(0).getAttrValue();
            }
        }

        // 拼接custInfo参数部分
        TfOrdCustinfo custinfo;
        {
            custinfo = tfOrdCustinfoMapper.qryTfOrdCustinfoByOrderId(orderId);
            if (custinfo == null) {
                throw new RuntimeException("orderId:" + orderId + "没有客户信息!!");
            }
        }
        TfOrdAddress tfOrdAddress;
        {
            tfOrdAddress=tfOrdAddressMapper.qryOrdAddressByOrderId(orderId);
        }

        // 获取订单的发展人信息

        DeveloperInfo developerInfo;
        {
            Result deveResult=orderQueryService.qryDeveloperInfo(orderId);

            if (deveResult.getSuccess()==false || deveResult.getData()==null) {
                throw new RuntimeException("orderId:" + orderId + "没有发展人信息!!");
            }
            developerInfo = (DeveloperInfo) deveResult.getData();
        }

        //add by zhangll9_20230427 校园融合返档业务   有特定的融合商品
        TdMSysDict dictParam = new TdMSysDict();
        dictParam.setParamType("goodsIdRH");
        dictParam.setParamKey("goodsIdRH");
        TdMSysDict dictRH=tdMSysDictMapper.query(dictParam);
        String goodsIdStr="";
        if(dictRH != null){
            goodsIdStr=dictRH.getParamValue();
        }
        String[] goodsIdArrary=goodsIdStr.split("|");
        if(goodsIdStr.contains(tfOrdGoodsMain.getGoodsId())){
            //判断移网号码是否有“校园融合宽带+移网绑定关系”
            JSONObject data =checkRelationRH(opInfo,serialNumber,orderId,tfOrdGoodsMain.getGoodsOrderId());
            if(data!=null){ //校园融合返档

                /*TdOrdMStaff tdOrdMStaff=new TdOrdMStaff();
                String staffId = op.getOperatorId();
                tdOrdMStaff.setStaffId(staffId).asCondition();
                tdOrdMStaff = dao().selectOne(tdOrdMStaff);
                if (tdOrdMStaff == null) {
                    throw new RuntimeException("orderId:" + orderId + "没有操作员信息!!");
                }
                String staffPhone=tdOrdMStaff.getPhoneNumber();*/

                String staffPhone="";

                //调用融合返档接口
                JSONObject comActiveReq = new JSONObject();
                JSONObject uniBssBody = new JSONObject();
                uniBssBody.put("COMPACTIVE_REQ", comActiveReq);
                comActiveReq.put("DEPART_ID",  opInfo.getChannelId());
                comActiveReq.put("CITY_CODE", opInfo.getCity());
                comActiveReq.put("CHANNEL_ID", opInfo.getChannelId());
                comActiveReq.put("IN_MODE_CODE", "M");
                comActiveReq.put("EPARCHY_CODE",opInfo.getEparchyCode());
                comActiveReq.put("STAFF_ID", opInfo.getOperatorId());
                comActiveReq.put("PROVINCE_CODE", "34");
                comActiveReq.put("CHANNEL_TYPE", opInfo.getChannelType());
                comActiveReq.put("LINK_NAME", custinfo.getCustName()); //联系人取客户名称
                comActiveReq.put("LINK_PHONE", data.getString("SERIAL_NUMBER"));//联系人号码，取移网成员号码

                Date now = new Date();
                String reqNo= DatePattern.PURE_DATETIME_MS_FORMAT.format(now) + RandomUtil.randomNumbers(3);
                comActiveReq.put("REQ_NO",reqNo);  //调用方流水号
                JSONObject developer = new JSONObject();
                developer.put("DEVELOPER_NAME", developerInfo.getRecomPersonName());
                developer.put("DEVELOPER_PHONE", StringUtils.defaultIfEmpty(developerInfo.getRecomPersonPhone(), staffPhone));
                developer.put("AGENT_CHANNEL_ID", opInfo.getChannelId());
                developer.put("DEVELOPER_ID", developerInfo.getRecomPersonId());
                comActiveReq.put("DEVELOPER_INFO", developer);

                JSONObject custInfo = new JSONObject();
                custInfo.put("CONTACT_NAME",StringUtils.defaultIfBlank(custinfo.getContactName(), custinfo.getCustName()));
                custInfo.put("CERT_ADDR", custinfo.getPsptAddresss() == null || custinfo.getPsptAddresss().length() < 6
                        ? "中华人民共和国" + custinfo.getPsptAddresss()
                        : custinfo.getPsptAddresss());
                custInfo.put("CERT_TYPE", "02");
                custInfo.put("POST_ADDRESS", tfOrdAddress == null ? "" : tfOrdAddress.getAddress());
                custInfo.put("CHECK_TYPE", "03");
                custInfo.put("CONTACT_PHONE", StrUtil.blankToDefault(custinfo.getContactPhone(), tfOrdAddress == null ? "" : tfOrdAddress.getPhone()));
                custInfo.put("CERT_NUM", custinfo.getPsptId());
                custInfo.put("CUST_NAME", custinfo.getCustName());
                comActiveReq.put("CUST_INFO", custInfo);
                JSONObject sysInfo = new JSONObject();
                sysInfo.put("SYSAPP_ID", "ERTfiMsKHy");
                sysInfo.put("SYSSTAFF_ID",  opInfo.getOperatorId());
                comActiveReq.put("SYSTEMCODE_INFOS", sysInfo);
                JSONObject tradeItem = new JSONObject();
                tradeItem.put("ATTR_VALUE", "1");
                tradeItem.put("ATTR_CODE",  "MAINCHK_CERT_METHOD");
                comActiveReq.put("TRADE_ITEM", tradeItem);
                JSONArray list =new JSONArray();
                JSONObject orderNo = new JSONObject();
                orderNo.put("SERIAL_NUMBER", data.getString("SERIAL_NUMBER"));
                orderNo.put("NET_TYPE_CODE",  data.getString("NET_TYPE_CODE"));
                orderNo.put("SUBSCRIBE_ID",  data.getString("SUBSCRIBE_ID"));
                list.add(orderNo);
                JSONObject orderNoFix = new JSONObject();
                orderNoFix.put("SERIAL_NUMBER", data.getString("FIXED_SERIAL_NUMBER"));
                orderNoFix.put("NET_TYPE_CODE",  data.getString("FIXED_NET_TYPE_CODE"));
                orderNoFix.put("SUBSCRIBE_ID",  data.getString("FIXED_SUBSCRIBE_ID"));
                //    orderNoFix.put("DETAIL_INSTALL_ADDRESS", "");
                list.add(orderNoFix);
                comActiveReq.put("ORDER_NO", list);
                JSONObject uniBssAttached = new JSONObject();
                uniBssAttached.put("MEDIA_INFO", "");
                String url = commonProperties.getCompactive();
                String res = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached,"compactive");
                JSONObject responseBody = JSONObject.parseObject(res);
                JSONObject head = responseBody.getJSONObject("UNI_BSS_HEAD");
                if (head == null) {
                    throw new RuntimeException("orderId: " + orderId + "返档失败!:接口未正常返回 " );
                }
                if (!"00000".equals(head.getString("RESP_CODE"))) {
                    throw new RuntimeException("orderId: " + orderId + "返档失败!: " + head.getString("RESP_CODE") + ":" + head.getString("RESP_DESC"));
                }
                uniBssBody = responseBody.getJSONObject("UNI_BSS_BODY");
                JSONObject rsp = uniBssBody.getJSONObject("COMPACTIVE_RSP").getJSONObject("RSP");
                if (rsp == null) {
                    throw new RuntimeException("orderId: " + orderId + "返档失败!:接口未正常返回 " );
                }
                if (!"0000".equals(rsp.getString("RSP_CODE"))) {
                    throw new RuntimeException("orderId: " + orderId + "返档失败!: "  + rsp.getString("RSP_DESC"));
                }

                return new JSONObject();

            }
        }
        //add by zhangll9_20230427_end 校园融合返档业务

        //普通客户资料返档
        String transId = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) + ((int)((Math.random()*9+1)*100000));
        CurtCustReq curtCustReq= new CurtCustReq()
                .setAPPKEY("jsordcen")
                .setAPPTX(transId)
                .setMSG(new CurtCustReq.MSG()
                        .setOperatorId(opInfo.getOperatorId())
                        .setProvince(opInfo.getProvince())
                        .setCity(opInfo.getCity())
                        .setDistrict(opInfo.getDistrict())
                        .setChannelId(opInfo.getChannelId())
                        .setChannelType(opInfo.getChannelType())
                        .setOpeSysType("2")

                        .setOrderId(orderId)
                        .setServiceClassCode("0000")
                        .setSerialNumber(serialNumber)
                        .setOperTag("02")
                        .setCustInfo(Collections.singletonList(new CurtCustReq.CustInfo()
                                .setCustName(custinfo.getCustName())
                                .setCertAddr(custinfo.getPsptAddresss() == null || custinfo.getPsptAddresss().length() < 6
                                        ? "中华人民共和国" + custinfo.getPsptAddresss()
                                        : custinfo.getPsptAddresss())
                                .setCertType("02")
                                .setCheckType("01")
                                .setCertNum(custinfo.getPsptId())
                                .setContactName(StringUtils.defaultIfBlank(custinfo.getContactName(), custinfo.getCustName()))
                                .setContactPhone(StrUtil.blankToDefault(custinfo.getContactPhone(), tfOrdAddress == null ? "" : tfOrdAddress.getPhone()))
                        ))
                        .setDeveloperInfo(Collections.singletonList(new CurtCustReq.DeveloperInfo()
                                .setDeveloperId(developerInfo.getRecomPersonId())
                                .setDeveloperName(developerInfo.getRecomPersonName())
                                .setDeveloperPhone(StringUtils.defaultIfEmpty(developerInfo.getRecomPersonPhone(), ""))
                                .setAgentChannelId(StringUtils.defaultIfEmpty(developerInfo.getAgentChannelId(), ""))
                        ))
                );

        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("CURT_CUST_REQ", curtCustReq);
//		JSONObject uniBssAttached = new JSONObject();
        //       uniBssAttached.put("MEDIA_INFO", "");
        //       String url = commonProperties.getCurtcust();
        //    String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached,"curtcust");
        //改成调用省份能开接口
//        AaopUtil aaopUtil = new AaopUtil();
//        String strReq = JSONObject.toJSONString(uniBssBody);
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        String curtcustUrl = "http://10.245.34.209:8000/api/microservice/custs/curtcust/v1";
        String strRsp = HttpUtil.sendRequest(curtcustUrl, uniBssBody, uniBssAttached);
//        String strRsp = aaopUtil.newReq("astore_curtcust", strReq,orderId);
        JSONObject head = JSONObject.parseObject(strRsp).getJSONObject("UNI_BSS_HEAD");

        //    JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
        if (head == null) {
            throw new Exception("订单中心订单号:" + orderId + "返档失败：接口未正常返回！");
        }

        if (StringUtils.isNotBlank(head.getString("RESP_CODE"))) {
            if (!"00000".equals(head.getString("RESP_CODE"))) {
                throw new Exception("订单中心订单号:" + orderId + "返档失败：" + head.getString("RESP_CODE") + ":" + head.getString("RESP_DESC"));
            }

        }
        JSONObject body = JSONObject.parseObject(strRsp).getJSONObject("UNI_BSS_BODY");
        //    JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
        JSONObject custRsp = body.getJSONObject("CURT_CUST_RSP");
        if (custRsp == null) {
            throw new Exception("订单中心订单号:" + orderId + "返档失败：接口未正常返回！");
        }
        if(!custRsp.getString("STATUS").equals("0000") || !custRsp.containsKey("RSP")){
            throw new Exception("订单中心订单号:" + orderId + "返档失败：接口未正常返回！");
        }
        JSONObject rsp = custRsp.getJSONObject("RSP");
        if(!rsp.getString("RSP_CODE").equals("0000") || !rsp.getString("SUB_CODE").equals("0000") ){
            throw new Exception("订单中心订单号:" + orderId + "返档失败："+rsp.getString("RSP_DESC"));
        }

        return new JSONObject();
    }



    public JSONObject checkRelationRH(TdOrdOperator op, String serialNumber, String orderId, String goodsOrderId) throws Exception{
        log.info("===checkRelationRH==");
        String isRH = "0"; // 没有校园融合关系

        if (op == null) {
            throw new Exception("orderId:" + orderId + "：操作人信息查询为空。");
        }
        JSONObject req = new JSONObject();
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("QUERYRELATION_REQ", req);
        req.put("SERIAL_NUMBER", serialNumber);
        req.put("DEPART_ID", op.getChannelId());
        req.put("CITY_CODE", op.getCity());
        req.put("CHANNEL_ID", op.getChannelId());
        req.put("EPARCHY_CODE", op.getEparchyCode());
        req.put("STAFF_ID", op.getOperatorId());
        req.put("CHANNEL_TYPE", op.getChannelType());
        req.put("PROVINCE_CODE", "34");
        req.put("IN_MODE_CODE", "M");
        Date now = new Date();
        String reqNo= DatePattern.PURE_DATETIME_MS_FORMAT.format(now) + RandomUtil.randomNumbers(3);
        req.put("REQ_NO", reqNo);  //调用方流水号
        JSONObject infos = new JSONObject();
        req.put("SYSTEMCODE_INFOS", infos);
        infos.put("SYSSTAFF_ID", op.getOperatorId());
        String methodName="queryrelation";
        String url = commonProperties.getQueryrelation();
        infos.put("SYSAPP_ID", "ERTfiMsKHy");
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached,"queryrelation");
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
        if (head == null) {
            throw new Exception("订单中心订单号:" + orderId + ":queryrelation接口未正常返回");
        }

        if (StringUtils.isNotBlank(head.getString("RESP_CODE"))) {
            if (!"00000".equals(head.getString("RESP_CODE"))) {
                throw new Exception("订单中心订单号:" + orderId + ":queryrelation" + head.getString("RESP_CODE") + ":" + head.getString("RESP_DESC"));
            }

        }
        JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
        JSONObject relationRsp = body.getJSONObject("QUERYRELATION_RSP");
        if (relationRsp == null) {
            throw new Exception("订单中心订单号:" + orderId + ":queryrelation接口未正常返回");
        }

        if (!"0000".equals(relationRsp.getString("STATUS")) ) {
            throw new Exception("订单中心订单号:" + orderId + ":queryrelation接口未正常返回");
        }
        JSONObject rsp = relationRsp.getJSONObject("RSP");
        if (!"0000".equals(rsp.getString("RSP_CODE"))) {
            isRH="0";
        }else{
            JSONArray list =rsp.getJSONArray("DATA");
            if(list.size()>0) {
                JSONObject data = list.getJSONObject(0);
                isRH="1";
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                TfOrderItem item =new TfOrderItem();
                item.setOrderId(orderId);
                item.setAttrType("0");
                item.setAttrCode("isSchlRH");
                item.setAttrValue(isRH);
                item.setValueName("校园融合业务");
                item.setStartDate(new Date());
                Date date = null;
                String time = "2050-12-30 23:59:59";
                date = sdf.parse(time);
                item.setEndDate(date);
                tfOrderItemMapper.insert(item);

                if(data.containsKey("TRADE_ID")){
                    TfOrderItem item1 =new TfOrderItem();
                    item1.setOrderId(orderId);
                    item1.setAttrType("0");
                    item1.setAttrCode("tradeIdRH");
                    item1.setAttrValue(data.getString("TRADE_ID"));
                    item1.setValueName("校园融合移网流水号");
                    item1.setStartDate(new Date());
                    item1.setEndDate(date);
                    tfOrderItemMapper.insert(item1);

                }
                if(data.containsKey("FIXED_TRADE_ID")){
                    TfOrderItem item2=new TfOrderItem();
                    item2.setOrderId(orderId);
                    item2.setAttrType("0");
                    item2.setAttrCode("fixedTradeIdRH");
                    item2.setAttrValue(data.getString("FIXED_TRADE_ID"));
                    item2.setValueName("校园融合宽带流水号");
                    item2.setStartDate(new Date());
                    item2.setEndDate(date);
                    tfOrderItemMapper.insert(item2);
                }
                if(data.containsKey("FIXED_SERIAL_NUMBER")){
                    TfOrderItem item2=new TfOrderItem();
                    item2.setOrderId(orderId);
                    item2.setAttrType("0");
                    item2.setAttrCode("fixedSerialNumberRH");
                    item2.setAttrValue(data.getString("FIXED_SERIAL_NUMBER"));
                    item2.setValueName("校园融合宽带号码");
                    item2.setStartDate(new Date());
                    item2.setEndDate(date);
                    tfOrderItemMapper.insert(item2);

                }
                if(data.containsKey("SUBSCRIBE_ID")){
                    TfOrderItem item2=new TfOrderItem();
                    item2.setOrderId(orderId);
                    item2.setAttrType("0");
                    item2.setAttrCode("subscribeIdRH");
                    item2.setAttrValue(data.getString("SUBSCRIBE_ID"));
                    item2.setValueName("校园融合移网cb订单");
                    item2.setStartDate(new Date());
                    item2.setEndDate(date);
                    tfOrderItemMapper.insert(item2);

                }
                if(data.containsKey("FIXED_SUBSCRIBE_ID")){
                    TfOrderItem item2=new TfOrderItem();
                    item2.setOrderId(orderId);
                    item2.setAttrType("0");
                    item2.setAttrCode("fixedSubscribeIdRH");
                    item2.setAttrValue(data.getString("FIXED_SUBSCRIBE_ID"));
                    item2.setValueName("校园融合宽带cb订单");
                    item2.setStartDate(new Date());
                    item2.setEndDate(date);
                    tfOrderItemMapper.insert(item2);

                }
                return  data;
            }

        }

        return new JSONObject();
    }

    /**
     * 后激活
     */
    public Result afterActiveService(String goodsOrderId) throws Exception {
        Result resResult = new Result();

        TfOrdGoodsMain ordGoodsMain=tfOrdGoodsMainMapper.selectByPrimaryKey(goodsOrderId);
        String orderId=ordGoodsMain.getOrderId();
        // 查询订单主表
        TfOrdMain tfOrdMain = new TfOrdMain();
        tfOrdMain.setOrderId(orderId);
        TfOrdMain ordMain = tfOrdMainMapper.qryOrderMainByOrderId(orderId);
        if (ordMain == null) {
            throw new RuntimeException("根据订单号" + orderId + "未查询到订单");
        }

        // 校园需求, 强制某些地市工号渠道黑名单, 无法返档业务
        if (checkDeny(orderId, goodsOrderId)) {
            resResult.setSuccess(false);
            resResult.setMsg("当前返档操作被禁止!");
            return resResult;
        }

        log.info("==afterActiveService==="+orderId+"系统来源为：CBSS，开始判断是否预开户订单");
        TfOrderItem item = new TfOrderItem();
        item.setOrderId(orderId);
        item.setAttrType("0");
        //   item.setAttrCode("0006");
        List<TfOrderItem> itemList=tfOrderItemMapper.qryOrderItems(item);
        if(itemList.size()>0){
            for (TfOrderItem goodsItem : itemList) {
                if (goodsItem.getAttrCode().equals("preCardMode")) {
                    String propValue = goodsItem.getAttrValue();
                    if (propValue.equals("1")) {
                        log.info("==afterActiveService===该订单为预开户订单，订单号："+ orderId);
                        TfOrdLine ordLine= tfOrdLineMapper.qryTfOrdLineByOrderId(orderId);

                        try {
                            // 检查预开户工单
                            Result result=checkPreSubmitOrder(ordLine);
                            if(result.getSuccess()){
                                // 微服务
                                JSONObject r = microCustinfoReq(ordMain, ordGoodsMain);
                                resResult.setData(r);
                                resResult.setSuccess(true);
                                ordLine.setProductionResult("0000");
                                ordLine.setRemark("返档成功");
                            }else{
                                resResult.setSuccess(false);
                                resResult.setMsg(resResult.getMsg());
                                ordLine.setProductionResult("9999");
                                ordLine.setRemark(resResult.getMsg());
                            }


                        } catch (Exception e) {
                            resResult.setSuccess(false);
                            resResult.setMsg(e.getMessage());
                            ordLine.setProductionResult("9999");
                            ordLine.setRemark("返档失败："+e.getMessage());
                            e.printStackTrace();
                        }
                        tfOrdLineMapper.updateTfOrdLine(ordLine);
                        return resResult;
                    }
                }
            }

        }

        TfOrdLine ordLine= tfOrdLineMapper.qryTfOrdLineByOrderId(orderId);
        if(StringUtil.isBlank(ordLine.getBusiSysOrderId())){
            resResult.setMsg("该订单没有查询到生产的CB订单号");
            resResult.setSuccess(false);
            return resResult;
        }

        return resResult;

    }

    public boolean checkDeny(String orderId, String goodsOrderId) {
       /* TfOrdMain tfOrdMain = new TfOrdMain();
        tfOrdMain.setOrderId(orderId).asCondition();
        tfOrdMain = dao().selectOne(tfOrdMain);

        TfOrdGoodsMain tfOrdGoodsMain = new TfOrdGoodsMain();
        tfOrdGoodsMain.setGoodsOrderId(goodsOrderId).asCondition();
        tfOrdGoodsMain = dao().selectOne(tfOrdGoodsMain);

        InParam<String, Object> inParam = new InParam<String, Object>();
        inParam.put("goodsOrderId", tfOrdGoodsMain.getGoodsOrderId());
        inParam.put("reasonCode", "updatefail");
        inParam.put("orderId", orderId);
        inParam.put("reasonName", "激活失败, 强制拦截！！！");
        inParam.put("reasonDesc", "激活失败, 强制拦截！！！");
        inParam.put("taskKey", "activation");
        inParam.put("operType", "updatefail");

        if (tfOrdMain != null && tfOrdGoodsMain != null) {
            if (Constants.operRole.SCHOOL.equals(tfOrdMain.getOperRole()) && Constants.DeliveryType.onLogistics.equals(tfOrdMain.getDeliveryType()) && "01".equals(tfOrdGoodsMain.getBusiType())) {
                TfOrdGoodsItem tfOrdGoodsItem = new TfOrdGoodsItem();
                tfOrdGoodsItem.setGoodsOrderId(tfOrdGoodsMain.getGoodsOrderId()).asCondition();
                tfOrdGoodsItem.setPropCode("channelId").asCondition();
                List<TfOrdGoodsItem> goodsItems = dao().selectList(tfOrdGoodsItem);
                // 校验地市
                if (StrUtil.containsAny(SysDictUtil.getSysDictValueByTypeAndKey("preCardDeny", "eparchyCodeList") + ",", tfOrdMain.getEparchyCode() + ",")) {
                    inParam.put("operValue", "当前订单的归属地市 目前不允许 返档操作!!");
                    inParam.put("remark", "当前订单的归属地市 目前不允许 返档操作!!");
                    orderOperService.recordOrderOperInfo(inParam, null);
                    return true;
                }
                // 校验下单工号
                if (StrUtil.isNotBlank(tfOrdMain.getOperId()) &&
                        StrUtil.containsAny(SysDictUtil.getSysDictValueByTypeAndKey("preCardDeny", "operIdList") + ",", tfOrdMain.getOperId() + ",")) {
                    inParam.put("operValue", "当前订单的下单工号 目前不允许 返档操作!!");
                    inParam.put("remark", "当前订单的下单工号 目前不允许 返档操作!!");
                    orderOperService.recordOrderOperInfo(inParam, null);
                    return true;
                }
                // 效验校园渠道
                if (CollectionUtil.isNotEmpty(goodsItems)) {
                    if (goodsItems.stream()
                            .map(TfOrdGoodsItem::getPropValue)
                            .filter(StrUtil::isNotBlank)
                            .anyMatch(v -> StrUtil.containsAny(SysDictUtil.getSysDictValueByTypeAndKey("preCardDeny", "channelIdList") + ",", v + ","))) {
                        inParam.put("operValue", "当前订单的校园渠道 目前不允许 返档操作!!");
                        inParam.put("remark", "当前订单的校园渠道 目前不允许 返档操作!!");
                        orderOperService.recordOrderOperInfo(inParam, null);
                        return true;
                    }
                }
            }
        }*/
        return false;
    }

    private Result checkPreSubmitOrder(TfOrdLine tfOrdLine) {
        Result result = queryCBSSTradeLog.queryPreOpenTradeIdByOrderId(tfOrdLine.getOrderId(), "08", "0006");
        if (result.getSuccess()&& StringUtils.isNotBlank(result.getMsg())) {
            tfOrdLine.setBusiSysOrderId(result.getMsg());
            tfOrdLineMapper.updateTfOrdLine(tfOrdLine);
        }
        return result;
    }




}
