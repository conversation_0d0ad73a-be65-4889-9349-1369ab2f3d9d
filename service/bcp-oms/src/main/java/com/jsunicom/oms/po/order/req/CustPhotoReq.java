package com.jsunicom.oms.po.order.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "客户照片查询base64")
public class CustPhotoReq  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号", required = true, position = 1)
    private String orderId;

    @ApiModelProperty(value = "是否需要正面照", position = 2)
    private String front;

    @ApiModelProperty(value = "是否需要背面照", position = 3)
    private String back;

    @ApiModelProperty(value = "是否需要手持照", position = 4)
    private String hand;

    public String getOrderId() {
        return orderId;
    }

    public CustPhotoReq setOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getFront() {
        return front;
    }

    public CustPhotoReq setFront(String front) {
        this.front = front;
        return this;
    }

    public String getBack() {
        return back;
    }

    public CustPhotoReq setBack(String back) {
        this.back = back;
        return this;
    }

    public String getHand() {
        return hand;
    }

    public CustPhotoReq setHand(String hand) {
        this.hand = hand;
        return this;
    }
}
