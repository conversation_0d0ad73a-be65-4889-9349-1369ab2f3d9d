package com.jsunicom.oms.mapper;


import java.util.List;

import com.jsunicom.oms.po.UserExportActionLog;
import com.jsunicom.oms.po.UserExportActionLogExample;
import org.apache.ibatis.annotations.Param;

public interface UserExportActionLogMapper {
    long countByExample(UserExportActionLogExample example);

    int deleteByExample(UserExportActionLogExample example);

    int deleteByPrimaryKey(Long logId);

    int insert(UserExportActionLog record);

    int insertSelective(UserExportActionLog record);

    List<UserExportActionLog> selectByExampleWithBLOBs(UserExportActionLogExample example);

    List<UserExportActionLog> selectByExample(UserExportActionLogExample example);

    UserExportActionLog selectByPrimaryKey(Long logId);

    int updateByExampleSelective(@Param("record") UserExportActionLog record, @Param("example") UserExportActionLogExample example);

    int updateByExampleWithBLOBs(@Param("record") UserExportActionLog record, @Param("example") UserExportActionLogExample example);

    int updateByExample(@Param("record") UserExportActionLog record, @Param("example") UserExportActionLogExample example);

    int updateByPrimaryKeySelective(UserExportActionLog record);

    int updateByPrimaryKeyWithBLOBs(UserExportActionLog record);

    int updateByPrimaryKey(UserExportActionLog record);
}