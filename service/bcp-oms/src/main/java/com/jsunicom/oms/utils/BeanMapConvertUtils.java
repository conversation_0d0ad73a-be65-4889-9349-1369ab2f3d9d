package com.jsunicom.oms.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joor.Reflect;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * bean2map map2bean请一起使用，要么都用这个类的要么都不要用
 */
public class BeanMapConvertUtils {
    public static <T> Map<String, String> bean2Map(T t) {

        Map<String, String> hash = Maps.newHashMap();
        if (t == null) {
            return hash;
        }
        JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(JSON.toJSONString(t));
        //过虑值为空字符串的key
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (StringUtils.isNotEmpty(entry.getKey()) && StringUtils.isNotEmpty(entry.getValue().toString())) {
                hash.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return hash;
    }


    public static <T> Map<String, Object> bean2Map2(T t) {
        if (t == null) {
            return Maps.newHashMap();
        }
        JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(JSON.toJSONString(t));
        return jsonObject;
    }

    /**
     * 只处理了常用类型，如果有其它类型请自己处理
     *
     * @param map
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T map2bean(Map<String, String> map, Class<T> clazz) {
        T t = Reflect.on(clazz).create().get();
        if (MapUtils.isEmpty(map)) {
            return t;
        }

        for (Map.Entry<String, String> entry : map.entrySet()) {
            Map<String, Reflect> files = Reflect.on(t).fields();
            Set<String> fileSet = files.keySet();
            if (!fileSet.contains(entry.getKey())) {
                continue;
            }

            Class filedClazz = files.get(entry.getKey()).type();

            if (filedClazz == String.class) {
                Reflect.on(t).set(entry.getKey(), entry.getValue());
            } else if (filedClazz == byte.class || filedClazz == Byte.class) {
                Reflect.on(t).set(entry.getKey(), Byte.valueOf(entry.getValue()));
            } else if (filedClazz == short.class || filedClazz == Short.class) {
                Reflect.on(t).set(entry.getKey(), Short.valueOf(entry.getValue()));
            } else if (filedClazz == int.class || filedClazz == Integer.class) {
                Reflect.on(t).set(entry.getKey(), Integer.valueOf(entry.getValue()));
            } else if (filedClazz == long.class || filedClazz == Long.class) {
                Reflect.on(t).set(entry.getKey(), Long.valueOf(entry.getValue()));
            } else if (filedClazz == float.class || filedClazz == Float.class) {
                Reflect.on(t).set(entry.getKey(), Float.valueOf(entry.getValue()));
            } else if (filedClazz == double.class || filedClazz == Double.class) {
                Reflect.on(t).set(entry.getKey(), Double.valueOf(entry.getValue()));
            } else if (filedClazz == char.class || filedClazz == Character.class) {
                Reflect.on(t).set(entry.getKey(), entry.getValue().charAt(0));
            } else if (filedClazz == boolean.class || filedClazz == Boolean.class) {
                Reflect.on(t).set(entry.getKey(), Boolean.valueOf(entry.getValue()));
            } else if (filedClazz == Date.class) {
                Reflect.on(t).set(entry.getKey(), new Date(Long.parseLong(entry.getValue())));
            } else if (filedClazz == BigDecimal.class) {
                Reflect.on(t).set(entry.getKey(), new BigDecimal(entry.getValue()));
            } else {
                //list map set 等
                Reflect.on(t).set(entry.getKey(), JSON.parseObject(entry.getValue(), filedClazz));
            }
        }
        return t;
    }

    public static <T> T map2bean2(Map<String, Object> map, Class<T> clazz) {
        return JSON.parseObject(map.toString(), clazz);
    }

}
