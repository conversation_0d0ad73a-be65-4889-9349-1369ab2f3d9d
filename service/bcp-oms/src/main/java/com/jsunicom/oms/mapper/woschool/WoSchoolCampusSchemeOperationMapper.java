package com.jsunicom.oms.mapper.woschool;

import com.jsunicom.oms.po.WoSchoolCampusSchemeOperation;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface WoSchoolCampusSchemeOperationMapper {
    int deleteByPrimaryKey(@Param("schemeId") Long schemeId, @Param("merchantId") Long merchantId);

    int insert(WoSchoolCampusSchemeOperation record);

    int insertSelective(WoSchoolCampusSchemeOperation record);

    WoSchoolCampusSchemeOperation selectByPrimaryKey(@Param("schemeId") Long schemeId, @Param("merchantId") Long merchantId);

    int updateByPrimaryKeySelective(WoSchoolCampusSchemeOperation record);

    int updateByPrimaryKey(WoSchoolCampusSchemeOperation record);

    List<WoSchoolCampusSchemeOperation> selectList(@Param("schemeId") Long schemeId, @Param("merchantId") Long merchantId);
}