package com.jsunicom.oms.service.order;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.common.constants.CommonProperties;
import com.jsunicom.oms.entity.TdOrdSysDict;
import com.jsunicom.oms.mapper.order.TfOrdCustinfoMapper;
import com.jsunicom.oms.mapper.wobuy.WoBuyMapper;
import com.jsunicom.oms.po.order.req.FaceCompareReq;
import com.jsunicom.oms.po.order.req.Para;
import com.jsunicom.oms.po.order.rsp.FaceCompareRsp;
import com.jsunicom.oms.po.order.tf.TfOrdCustinfo;
import com.jsunicom.oms.request.HeadQuartersPostRequest;
import com.jsunicom.oms.request.HeadQuartersPostRequestHead;
import com.jsunicom.oms.utils.HeadAbilityTools;
import com.jsunicom.oms.utils.HttpUtil;
import com.jsunicom.oms.utils.RemoteCallTools;
import com.jsunicom.oms.utils.S3Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OcrService {

    @Autowired
    private GenerateId generateId;

    @Autowired
    private WoBuyMapper woBuyMapper;

    @Autowired
    private RemoteCallTools remoteCallTools;

    @Autowired
    private HeadAbilityTools headAbilityTools;

    @Autowired
    private TfOrdCustinfoMapper tfOrdCustinfoMapper;

    @Resource
    private CommonProperties commonProperties;

    //ocr识别接口
    public Result doOCRFaceCompare(String cardPhotoUrl, String reqTransId, String eparchyCode)throws Exception {
        Result respResult = new Result();

        log.info("doOCRFaceCompare cardPhotoUrl={}",cardPhotoUrl);
        //https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/
        String fileName =cardPhotoUrl.replace("https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/","");
        log.info("doOCRFaceCompare fileName={}",fileName);
        String photoInfo = S3Util.getBase64(fileName);
        JSONObject OCR_INFO_REQ = new JSONObject();
        OCR_INFO_REQ.put("REQ_TRANS_ID", reqTransId);
        OCR_INFO_REQ.put("SYS_CODE", "34PA");
        OCR_INFO_REQ.put("PROVINCE_CODE", "34");
        OCR_INFO_REQ.put("EPARCHY_CODE", eparchyCode);
        OCR_INFO_REQ.put("PHOTO_INFO", photoInfo);
        JSONObject dollar = new JSONObject();
        dollar.put("OCR_INFO_REQ", OCR_INFO_REQ);
        HeadQuartersPostRequest headQuartersPostRequest = new HeadQuartersPostRequest();
        headQuartersPostRequest.setUniBssBody(dollar);
        TdOrdSysDict paperlessDict =woBuyMapper.getSysDictByTypeAndKey("ocrInfo", "ocrInfo");
        log.info("autoCheckIdentity paperlessDict is {}", JSONObject.toJSONString(paperlessDict));

        String signUrl = paperlessDict.getParam1();
        HeadQuartersPostRequestHead headQuartersPostRequestHead = headAbilityTools.createHeadQuartersPostRequestHead(paperlessDict.getParam2(), paperlessDict.getParam3());
        headQuartersPostRequest.setUniBssHead(headQuartersPostRequestHead);
        String requestStr = JSONObject.toJSONString(headQuartersPostRequest);
        HashMap<String, String> headQuartersHeadRequestMap = headAbilityTools.createHeadQuartersHeadRequestMap(false);
        JSONObject jsonObject = remoteCallTools.callRemote(requestStr, false, signUrl, headQuartersHeadRequestMap, reqTransId,
                null, "ocrInfoZ", JSONObject.class);
        JSONObject data = jsonObject.getJSONObject("DATA");
        JSONObject uniBssHead = data.getJSONObject("UNI_BSS_HEAD");
        JSONObject ocrInfoRsp = data.getJSONObject("UNI_BSS_BODY").getJSONObject("OCR_INFO_RSP");
        if (org.apache.commons.lang.StringUtils.equals("00000", uniBssHead.getString("RESP_CODE"))) {
            if (org.apache.commons.lang.StringUtils.equals("0000", ocrInfoRsp.getString("RESP_CODE"))) {

                respResult.setSuccess(true);
                respResult.setMsg(ocrInfoRsp.getString("RESP_DESC"));
                respResult.setData(ocrInfoRsp);
            } else {
                respResult.setSuccess(false);
                respResult.setMsg(ocrInfoRsp.getString("RESP_DESC"));
            }
        } else {
            respResult.setSuccess(false);
            respResult.setMsg(uniBssHead.getString("RESP_DESC"));
        }
        return respResult;
    }

    public  JSONObject ocrReqPost(JSONObject jsnb) throws Exception {
        // 调总部OCR接口
        // 正面 反面各调一次接口

        JSONObject response=new JSONObject();

        String cardPhotoA = jsnb.getString("cardPhotoA");
        String cardPhotoB = jsnb.getString("cardPhotoB");
        String eparchyCode = jsnb.getString("eparchyCode");
        String transId = generateId.reqTransId();
        Result responseZ= doOCRFaceCompare(cardPhotoA, transId,eparchyCode);
        Result responseF = doOCRFaceCompare(cardPhotoB, transId,eparchyCode);
        JSONObject respBodyA = new JSONObject();
        JSONObject respBodyB = new JSONObject();
        JSONObject respBodyFront = new JSONObject();
        JSONObject respBodyBack = new JSONObject();
        if(responseZ.getSuccess()){
            respBodyA=(JSONObject)responseZ.getData();
            if (respBodyA.getString("SIDE").equals("back")) {
                respBodyBack = respBodyA;
            }
            if (respBodyA.getString("SIDE").equals("front")) {
                respBodyFront = respBodyA;
            }
        }
        if(responseF.getSuccess()){
            respBodyB=(JSONObject)responseF.getData();
            if (respBodyB.getString("SIDE").equals("back")) {
                respBodyBack = respBodyB;
            }
            if (respBodyB.getString("SIDE").equals("front")) {
                respBodyFront = respBodyB;
            }
        }
        if (respBodyFront == null) {
            respBodyFront = respBodyA;
        }
        if (respBodyBack == null) {
            respBodyBack = respBodyB;
        }
        response.put("respBodyFront",respBodyFront);
        response.put("respBodyBack",respBodyBack);
        return response;
    }

    public Result<FaceCompareRsp> faceCompare(String orderId, String cardPhotoA, String cardPhotoHand, String eparchyCode) throws Exception {
        Result result=new Result();
        result.setSuccess(false);
        if (orderId == null) {
            throw new Exception("订单编号为空！");
        }
        TfOrdCustinfo tfOrdCustinfo=tfOrdCustinfoMapper.qryTfOrdCustinfoByOrderId(orderId);
        if(tfOrdCustinfo==null){
            log.error("根据订单编号：" + orderId + "未查找到客户信息！");
            throw new Exception("根据订单编号：" + orderId + "未查找到客户信息！");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("FACE_IMG_URL",cardPhotoHand);
        map.put("BASE_IMG_URL",cardPhotoA);
        map.put("PROVINCE_CODE","34");
        map.put("EPARCHY_CODE",eparchyCode);
        map.put("CERT_NUM",tfOrdCustinfo.getPsptId());
        map.put("CERT_NAME",tfOrdCustinfo.getCustName());
        FaceCompareReq faceCompareReq = new FaceCompareReq();

        TdOrdSysDict dict = woBuyMapper.getSysDictByTypeAndKey("naturePersonCenter", "paramConfig");
        TdOrdSysDict dict1 = woBuyMapper.getSysDictByTypeAndKey("naturePersonCenter", "faceCompare");


        setFaceCompareReq(faceCompareReq, dict, dict1, map, orderId);

        //body
        JSONObject uniBssBody = new JSONObject();
        JSONObject uniBssReq = new JSONObject();
        uniBssBody.put("FACE_COMPARE_REQ", faceCompareReq);
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");

    //    String url = commonProperties.getFaceCompare();
        String url = dict1.getParam1();
        String responseBody = HttpUtil.sendRequest2(dict1.getParam2(),dict1.getParam3(),url, uniBssBody, uniBssAttached,"faceCompare",orderId);
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
        String rspDesc="";
        if (StringUtils.isNotBlank(head.getString("RESP_CODE"))) {
            if (!head.getString("RESP_CODE").equals("00000")) {
                rspDesc=head.getString("RESP_DESC");
            }else{
                //调用成功
                JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
                JSONObject rsp = body.getJSONObject("FACE_COMPARE_RSP");
                log.info("人脸比对返回报文jsonRsp:" + rsp.toJSONString());
                result.setSuccess(true);
                result.setData(JSONObject.parseObject(rsp.toJSONString(), FaceCompareRsp.class));
                return result;
            }
        }
        return result;
    }

    private void setFaceCompareReq (FaceCompareReq faceCompareReq, TdOrdSysDict dict, TdOrdSysDict dict1, Map<String, Object> map, String orderId) throws Exception {
        faceCompareReq.setSysCode(dict.getParam1());
        //    String compareTransId = dict.getParam1() + DateUtil.now("yyyyMMddHHmmssSSS") + RandomNumberUtil.genRandomNumber(3);
        String compareTransId = generateId.reqTransId();
        faceCompareReq.setCompareTransId(compareTransId) ;
        faceCompareReq.setProvinceCode(map.get("PROVINCE_CODE") == null ? null : map.get("PROVINCE_CODE").toString());
        faceCompareReq.setEparchyCode(map.get("EPARCHY_CODE") == null ? null : map.get("EPARCHY_CODE").toString());
        faceCompareReq.setSystem(dict1.getParam4());
        faceCompareReq.setScene(dict1.getParam5());
        faceCompareReq.setCertNum(map.get("CERT_NUM") == null ? null : map.get("CERT_NUM").toString());
        faceCompareReq.setCertName(map.get("CERT_NAME") == null ? null : map.get("CERT_NAME").toString());
        if (map.get("FACE_IMG_URL") == null) {
            log.error("根据订单编号：" + orderId + "未查找到用户的人体照片！");
            throw new Exception("根据订单编号：" + orderId + "未查找到用户的人体照片！");
        }
        String faceImgUrl = map.get("FACE_IMG_URL").toString();
        log.info("setFaceCompareReq faceImgUrl={}",faceImgUrl);
        //https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/
        String fileName =faceImgUrl.replace("https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/","");
        log.info("doOCRFaceCompare fileName={}",fileName);
        faceCompareReq.setFaceImg(S3Util.getBase64(fileName));
        String baseImgUrl = map.get("BASE_IMG_URL") == null ? null : map.get("BASE_IMG_URL").toString();
        log.info("setFaceCompareReq baseImgUrl={}",baseImgUrl);
        //https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/
        String fileName1 =baseImgUrl.replace("https://cos.xx-pbc.cos.tg.unicom.local/345367434821:jscustomer/","");
        log.info("doOCRFaceCompare fileName1={}",fileName1);
        faceCompareReq.setBaseImg(S3Util.getBase64(fileName1));
        faceCompareReq.setPara(new ArrayList<Para>());
    }



}
