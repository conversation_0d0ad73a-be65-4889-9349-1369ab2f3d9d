package com.jsunicom.oms.mapper.report;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReportFormsDao {

    /**
     * 查询青创社报表数据
     * @param orgCode 地市编码 (可选)
     * @param campusName 校区名称 (可选, 模糊查询)
     * @return 报表数据列表
     */
    List<Map<String, Object>> getYouthInnovationReportData(
            @Param("orgCode") String orgCode,
            @Param("campusName") String campusName
    );

    /**
     * 查询活动报表数据
     * @param orgCode 地市编码 (可选)
     * @param campusName 校区名称 (可选, 模糊查询)
     * @return 报表数据列表
     */
    List<Map<String, Object>> getActivityReportData(
            @Param("orgCode") String orgCode,
            @Param("campusName") String campusName
    );
} 