package com.jsunicom.oms.common;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.json.JSONUtil;
import com.jsunicom.common.core.entity.user.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: Utils
 * @Auther: LJ.Huang
 * @Date: 2021/8/19 12:27
 * @Version: srcV1.0
 * @Description: utils
 **/
@Slf4j
public class Utils {
    public static boolean testUrlWithTimeOut(String urlString, int timeOutMillSeconds) {

        try {
            URL url = new URL(urlString);
            URLConnection co = url.openConnection();
            co.setConnectTimeout(timeOutMillSeconds);
            co.connect();
            log.info(urlString + " 地址连接成功");
            return true;
        } catch (Exception e1) {
            log.info(urlString + " 地址连接失败");
            return false;
        }
    }

    public static void delValEmpty(Map<String, Object> map) {
        List<Object> list = new ArrayList<>();
        if (null != map) {
            for (Map.Entry<String, Object> m : map.entrySet()) {
                if (null == m.getValue()) {
                    list.add(m.getKey());
                } else if (m.getValue() instanceof String && StringUtils.isEmpty((String) m.getValue())) {
                    list.add(m.getKey());
                }
            }
            for (Object o : list) {
                map.remove(o);
            }

        }

    }

    public static UserInfo getUserInfo(String userInfoBase64) {
        UserInfo userInfo = null;
        try {
            if (StringUtils.isNotEmpty(userInfoBase64)) {
                userInfo = JSONUtil
                        .toBean(Base64Decoder.decodeStr(userInfoBase64), UserInfo.class);
            } else {
                userInfo = new UserInfo();
            }
        } catch (Exception e) {
            log.error("userInfoBase64:{}", userInfoBase64);
            log.error("getUserInfo", e);
            userInfo = new UserInfo();
        }
        return userInfo;
    }

    public static LocalDateTime getLocalDateTime(Date d) {
        return d.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

}
