package com.jsunicom.oms.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Objects;

/**
 * @PackageName: cn.chinaunicom.planning.pojo.headquarters
 * @ClassName HeadQuartersPostRequestHead
 * @Description 总部能力平台POST请求请求报文头
 * @Date 2021/7/1 16:12
 * @Created by yangyj3
 */
public class HeadQuartersPostRequestHead {
    @JSONField(name = "APP_ID")
    private String appId;
    @JSONField(name = "TIMESTAMP")
    private String timeStamp;
    @JSONField(name = "TRANS_ID")
    private String transId;
    @JSONField(name = "TOKEN")
    private String token;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HeadQuartersPostRequestHead that = (HeadQuartersPostRequestHead) o;
        return Objects.equals(appId, that.appId) &&
                Objects.equals(timeStamp, that.timeStamp) &&
                Objects.equals(transId, that.transId) &&
                Objects.equals(token, that.token);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, timeStamp, transId, token);
    }

    @Override
    public String toString() {
        return "HeadQuartersPostRequestHead{" +
                "appId='" + appId + '\'' +
                ", timeStamp='" + timeStamp + '\'' +
                ", transId='" + transId + '\'' +
                ", token='" + token + '\'' +
                '}';
    }
}
