package com.jsunicom.oms.controller.report;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.service.ReportFormsService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/campus/report")
public class ReportFormsController {

    @Autowired
    private ReportFormsService reportFormsService;

    /**
     * 查询青创社报表数据接口
     * @param orgCode 地市编码 (可选) 代表筛选条件中的地市
     * @param campusName 校区名称 (可选) 代表筛选条件中的校区 (模糊查询)
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 报表数据列表 (分页), 封装在 CustomResult 中
     */
    @ApiOperation("查询青创社报表数据")
    @GetMapping("/youthInnovationReport")
    public CustomResult getYouthInnovationReport(
            @RequestParam(required = false) String orgCode,
            @RequestParam(required = false) String campusName,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        log.info("接收到查询青创社报表请求 (分页，按校区名称模糊查询)，地市编码：{}，校区名称：{}，页码：{}，每页数量：{}", orgCode, campusName, pageNum, pageSize);
        PageInfo<Map<String, Object>> pageInfo = reportFormsService.getYouthInnovationReportData(orgCode, campusName, pageNum, pageSize);
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return new CustomResult("9999", "暂无数据", pageInfo);
        }
        return new CustomResult("0000", "查询成功", pageInfo);
    }

    /**
     * 查询活动报表数据接口
     * @param orgCode 地市编码 (可选) 代表筛选条件中的地市
     * @param campusName 校区名称 (可选) 代表筛选条件中的校区 (模糊查询)
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 报表数据列表 (分页), 封装在 CustomResult 中
     */
    @ApiOperation("查询活动报表数据")
    @GetMapping("/activityReport")
    public CustomResult getActivityReport(
            @RequestParam(required = false) String orgCode,
            @RequestParam(required = false) String campusName,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize
    ) {
        log.info("接收到查询活动报表请求 (分页，按校区名称模糊查询)，地市编码：{}，校区名称：{}，页码：{}，每页数量：{}", orgCode, campusName, pageNum, pageSize);
        PageInfo<Map<String, Object>> pageInfo = reportFormsService.getActivityReportData(orgCode, campusName, pageNum, pageSize);
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return new CustomResult("9999", "暂无数据", pageInfo);
        }
        return new CustomResult("0000", "查询成功", pageInfo);
    }

}
