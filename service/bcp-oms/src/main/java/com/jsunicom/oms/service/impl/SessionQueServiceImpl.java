package com.jsunicom.oms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.oms.request.GetSessionInfoByIdV1V17ReqEntity;
import com.jsunicom.oms.response.GetSessionInfoByIdV1V17RspEntity;
import com.jsunicom.oms.service.SessionQueService;
import com.jsunicom.oms.utils.HttpUtil;
import com.jsunicom.oms.utils.MD5Util;
import com.jsunicom.oms.utils.SM4Utils;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.tomcat.util.http.MimeHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024-06-19-11:20
 */
@Service
@Slf4j
public class SessionQueServiceImpl implements SessionQueService {
    @Value("${sha.sm4key}")
    private  String sm4key;
    @Autowired
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${session.appId}")
    private String appId;
    @Value("${session.appSecret}")
    private String appSecret;
    @Value("${session.url}")
    private String url;
    @Value("${session.blackListQueUrl}")
    private String blackListQueUrl;

    @Override
    public JSONObject SessionQue(GetSessionInfoByIdV1V17ReqEntity getSessionInfoByIdV1V17ReqEntity) {
        log.info("进入h5->session信息查询方法：SessionQue："+ JSONObject.toJSONString(getSessionInfoByIdV1V17ReqEntity));
        String responseBody = null;
        JSONObject response =null;
        try {
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");
            responseBody = HttpUtil.sendRequestH5(url,appId,appSecret, getSessionInfoByIdV1V17ReqEntity, uniBssAttached);
            response = JSONObject.parseObject(responseBody);
            response= (JSONObject) response.get("UNI_BSS_BODY");
        } catch (Exception e) {
            log.info("进入h5->session信息查询方法error:"+e.getMessage());
        }
        return response;
    }


    //加密信息
    @Override
    public  String  encryptInfo(Object object){
        String encryptedContent ="";
        try {
            String objectString = JSONObject.toJSONString(object);
            encryptedContent = SM4Utils.encryptEcb(sm4key, objectString);
        } catch (Exception e) {
            log.info("参数加密异常："+e.getMessage());
        }
        return encryptedContent;
    }
    //解密信息
    @Override
    public String decryptInfo(String sig){
        String decryptEcb=new String();
        try {
            decryptEcb = SM4Utils.decryptEcb(sm4key, sig);
        } catch (Exception e) {
            log.info("参数解密异常："+e.getMessage());
        }
        log.info("参数解密："+decryptEcb);
        return decryptEcb;
    }

    @Override
    public UserInfo getSessionInfo(HttpServletRequest request){
        UserInfo userInfo=new UserInfo();
        String sessionId=request.getHeader("Session_id");
        log.info("SessionQueService_getSessionInfo_sessionId：{}",sessionId);
        if (redisTemplate.hasKey("H5session:"+sessionId)){
            String userInfoStr = (String)(redisTemplate.opsForValue().get("H5session:" + sessionId));
            HashMap hashMap = JSONObject.parseObject(userInfoStr, HashMap.class);
            addHeader(request,hashMap);
            userInfoAdd(userInfo,hashMap);
            return userInfo;
        }
        GetSessionInfoByIdV1V17ReqEntity req=new GetSessionInfoByIdV1V17ReqEntity();
        GetSessionInfoByIdV1V17ReqEntity.Req req1=new GetSessionInfoByIdV1V17ReqEntity.Req();
        req1.setSessionId(sessionId);
        req.setReq(req1);
        JSONObject response = SessionQue(req);
        //后续处理
        JSONObject getRsp=(JSONObject) response.get("GET_SESSION_INFO_BY_ID_RSP");
        JSONObject rsp=(JSONObject) getRsp.get("RSP");
        JSONObject rspData=(JSONObject) rsp.get("RESP_DATA");
        String loginPhoneNumber = (String)rspData.get("LOGIN_PHONE_NUMBER");
        if (StringUtil.isEmpty(loginPhoneNumber)){
            return null;
        }
        HashMap source=new HashMap();
        source.put("serialNumber",loginPhoneNumber);
        String userAreaCode = (String)rspData.get("USER_AREA_CODE");
        source.put("areaCode",provinceAreaCode(userAreaCode));
        String staffNo = (String)rspData.get("STAFF_ID");
        source.put("staffNo",staffNo);
        String staffName = (String)rspData.get("STAFF_NAME");
        source.put("staffName",staffName);
        addHeader(request,source);
        userInfoAdd(userInfo,source);
        redisTemplate.opsForValue().set("H5session:"+sessionId, JSONObject.toJSONString(source), 2, TimeUnit.HOURS);
        /*String userInfoBase64="";
        // 非省份管理员
        userInfoBase64 = "eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTIzMDUwNiIsInN0YWZmTm8iOiJKU0lEMDAwMiIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjMiLCJhcmVhQ29kZSI6IjQ0MCIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTEyMzA1MDYiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5bi45bee6ZmG5bu66IuxIiwidXNlclBpZCI6IjMyMDQwMjE5NzkwMTEwMzEyMSIsImRlcGFydENvZGUiOiIzNDEwNDcyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6IuW4uOW3nuW4guWIhuWFrOWPuCJ9==";
        //省份管理员
//         userInfoBase64 = "eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTYxMDI4OSIsInN0YWZmTm8iOiJaMDAwTEpYMSIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjQiLCJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTE2MTAyODkiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5YiY6YeR6ZGrIiwidXNlclBpZCI6IjQxMjcyODE5OTEwODEwMDAzMiIsImRlcGFydENvZGUiOiIzNDU2NzAyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuaxn+iLj+ecgeWIhuWFrOWPuOS/oeaBr+WMlumDqCJ9==";
        userInfo = com.jsunicom.oms.common.Utils.getUserInfo(userInfoBase64);
        UserInfo newInfo=new UserInfo();
        newInfo.setStaffName(userInfo.getStaffName());
        newInfo.setStaffNo(userInfo.getStaffNo());
        newInfo.setSerialNumber(userInfo.getSerialNumber());
        newInfo.setAreaCode(userInfo.getAreaCode());
        String s = JSONObject.toJSONString(newInfo);
        HashMap source=JSONObject.parseObject(s,HashMap.class);
        addHeader(request,source);
        userInfoAdd(userInfo,source);*/
        return userInfo;

    }

    @Override
    public JSONObject blackListQue(HashMap hashMap) {
        log.info("进入黑名单查询blackListQue方法："+JSONObject.toJSONString(hashMap));
        String resultString="";
        try {
            // 创建Httpclient对象
            CloseableHttpClient httpclient = HttpClients.createDefault();
            CloseableHttpResponse response = null;
//            String blackListQueUrl="http://10.124.150.230:8000/api/microservice/credits/getcbssblacklistNew/v1?";
            String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String transId = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) + ((int)((Math.random()*9+1)*100000));
            String orginParam = "APP_ID"+appId+"TIMESTAMP"+timeStamp+"TRANS_ID"+transId+appSecret;
            String token = MD5Util.encryptionMd5(orginParam).toLowerCase();
            String blackUrl=blackListQueUrl+"APP_ID="+appId+"&TIMESTAMP="+timeStamp+ "&TRANS_ID="+transId+"&TOKEN="+token
                    +"&PROVINCE_CODE=34"
                    +"&PSPT_ID="+(String)hashMap.get("psptId")
                    +"&PSPT_TYPE_CODE=1&DATA_SOURCE="+hashMap.get("dataSource");
            URIBuilder builder = new URIBuilder(blackUrl);
            URI uri = builder.build();
            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            response = httpclient.execute(httpGet);
            log.info("blackListQue ===url="+blackUrl+"===Code="+response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() != 200) {
                //    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                return null;
            }
            resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("黑名单信息返回："+resultString);
        } catch (Exception e) {
            log.info("黑名单查询blackListQue方法 error:"+e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(resultString);
        JSONObject body = (JSONObject)jsonObject.get("UNI_BSS_BODY");
        JSONObject rspBlack = (JSONObject)body.get("GETCBSSBLACKLIST_RSP");
        return rspBlack;
    }

    private void userInfoAdd(UserInfo userInfo, Map<String, String> headerMap) {
        userInfo.setSerialNumber(headerMap.get("serialNumber"));
        userInfo.setAreaCode(headerMap.get("areaCode"));
        userInfo.setStaffNo(headerMap.get("staffNo"));
        userInfo.setStaffName(headerMap.get("staffName"));
    }
    private void addHeader(HttpServletRequest request, Map<String, String> headerMap) {
        if (headerMap==null||headerMap.isEmpty()){
            return;
        }

        Class<? extends HttpServletRequest> c=request.getClass();
        //System.out.println(c.getName());
        System.out.println("request实现类="+c.getName());
        try{
            Field requestField=c.getDeclaredField("request");
            requestField.setAccessible(true);

            Object o=requestField.get(request);
            Field coyoteRequest=o.getClass().getDeclaredField("coyoteRequest");
            coyoteRequest.setAccessible(true);

            Object o2=coyoteRequest.get(o);
            System.out.println("coyoteRequest实现类="+o2.getClass().getName());
            Field headers=o2.getClass().getDeclaredField("headers");
            headers.setAccessible(true);

            MimeHeaders mimeHeaders=(MimeHeaders) headers.get(o2);
            for (Map.Entry<String,String> entry:headerMap.entrySet()){
                mimeHeaders.removeHeader(entry.getKey());
                mimeHeaders.addValue(entry.getKey()).setString(entry.getValue());
            }

        }catch (Exception e){
//            e.printStackTrace();
            // NoSuchFieldException处理逻辑
            log.error("NoSuchFieldException：{}",e.getMessage());
        }
    }
    public String provinceAreaCode(String zbAreaCode){
        switch (zbAreaCode)
        {
            case "340":
                return "0025";
            case "430":
                return "0514";
            case "330":
                return "0510";
            case "343":
                return "0511";
            case "450":
                return "0512";
            case "358":
                return "0513";
            case "348":
                return "0515";
            case "350":
                return "0516";
            case "354":
                return "0517";
            case "346":
                return "0518";
            case "440":
                return "0519";
            case "445":
                return "0523";
            case "349":
                return "0527";
            default:
                return "";
        }
    }
}
