package com.jsunicom.oms.service;

import com.jsunicom.oms.po.WoSchoolCampusMarketingInfo;
import com.jsunicom.oms.po.WoSchoolCampusNetworkInfo;

import java.util.List;

/**
 * Project:CampusMarketingInfoService
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
public interface CampusNetworkInfoService {

    public List<WoSchoolCampusNetworkInfo> getCollegeList(WoSchoolCampusNetworkInfo record);

    public int saveCollegeExtendInfo(WoSchoolCampusNetworkInfo record);

    public int updateCollegeInfo(WoSchoolCampusNetworkInfo record);

    public int deleteCollegeInfo(Long collegeId);
}
