package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.po.DataMonitor;
import com.jsunicom.oms.po.DataMonitorDict;
import com.jsunicom.oms.service.DataMonitorDictService;
import com.jsunicom.oms.service.DataMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.controller
 * @ClassName: DataMonitorController
 * @Author: zhaowang
 * @CreateTime: 2023-03-16  19:25
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/dataMonitor",name = "运营端-数据监控")
public class DataMonitorController extends AbstractSimpleController {
    @Resource
    private DataMonitorDictService dataMonitorDictService;

    @Resource
    private DataMonitorService dataMonitorService;

    @GetMapping(value = "/find",name = "查询数据监控")
    public CustomResult getDataMonitorByPage(DataMonitor dataMonitor, Integer pageNumber, Integer pageSize) {
        try {
            log.info("查询数据监控start... ...");
            PageInfo<DataMonitor> pagedResult = dataMonitorService.find(dataMonitor, pageNumber, pageSize);
            log.info("查询数据监控成功,total:{}", pagedResult.getTotal());
            JSONObject result = new JSONObject();
            result.put("list", pagedResult);
            result.put("totalNumber", pagedResult.getTotal());
            log.info("查询数据监控end... ...");
            return ResultUtil.success(result);
        } catch (Exception e) {
            log.info("查询数据监控异常："+e.getMessage());
            return ResultUtil.error("9999",e.getMessage());
        }
    }

    @GetMapping(value = "/findDict",name = "查询数据监控字典")
    public CustomResult getDataMonitorDictByPage(Integer pageNumber, Integer pageSize) {
        try {
            log.info("查询数据监控字典start... ...");
            List<DataMonitorDict> pagedResult = dataMonitorDictService.findAll();
            log.info("查询数据监控字典成功,total:{}", pagedResult.size());
            JSONObject result = new JSONObject();
            result.put("list", pagedResult);
            result.put("totalNumber", pagedResult.size());
            log.info("查询数据监控字典end... ...");
            return ResultUtil.success(result);
        } catch (Exception e) {
            log.info("查询数据监控字典异常："+e.getMessage());
            return ResultUtil.error("9999",e.getMessage());
        }
    }
}
