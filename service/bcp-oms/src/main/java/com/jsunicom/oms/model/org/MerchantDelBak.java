package com.jsunicom.oms.model.org;


import com.jsunicom.oms.model.base.BasicModel;

import java.sql.Timestamp;

public class MerchantDelBak extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;   //主键
    private String orgCode;   //所属机构编码
    private String orgName;   //所属机构名称【废弃】
    private String merchantType;   //
    private String merchantName;   //商户名称
    private String busLicense;   //工商执照注册号
    private String legRepName;   //负责人姓名
    private String legRepPhone;   //负责人电话
    private String provCode;   //负责人地址省编码
    private String provName;   //负责人地址省名称
    private String cityCode;   //负责人地址市编码
    private String cityName;   //负责人地址市名称
    private String areaCode;   //负责人地址区编码
    private String areaName;   //负责人地址区名称
    private String address;   //地址详细地址
    private String legRepCertNo;   //负责人身份号
    private String legRepPhoto1;   //负责人身份证正面【废弃】
    private String legRepPhoto2;   //负责人身份证反面【废弃】
    private String busLicensePhoto;   //营业执照
    private String busLicenseType;  //营业执照类型
    private String busLicenseName; //营业执照名称
    private String industry;   //所属行业
    private String intentBusCode;   //意向业务或产品
    private String intentBusName;   //意向业务或产品名称【废弃】
    private String state;   //
    private String createBy;   //创建人
    private Timestamp createTime;   //创建时间
    private String updateBy;   //更新人
    private Timestamp updateTime;   //更新时间
    private Long pid;   //上级商户id
    private String contactPhone;   //联系人电话
    private String busPermitPhoto;   //营业许可证照片
    private String headOffice;   //所属总店
    private String wxEntpDid;   //企业号部门id
    private String wxEntpPdid;   //企业号上级部门id
    private String commisFlag;   //是否是佣金商户, 0-否,默认值; 1-佣金商户
    private String rightsFlag;   //是否是权限商户, 0-否,默认值; 1-权益商户
    private String qrcodeType;   //商户的二维码类型 0-临时 1-长期
    private String logoImgUrl;   //商户logo图片url

    private String whitelistAbility;// 商户白名单是否可修改 0：不可修改 1：可修改
    private String operateBy; //删除时操作人
    private Timestamp operateTime;//删除时执行时间

    public String getWhitelistAbility() {
        return whitelistAbility;
    }

    public void setWhitelistAbility(String whitelistAbility) {
        this.whitelistAbility = whitelistAbility;
    }

    public String getOperateBy() {
        return operateBy;
    }

    public void setOperateBy(String operateBy) {
        this.operateBy = operateBy;
    }

    public Timestamp getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Timestamp operateTime) {
        this.operateTime = operateTime;
    }

    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public String getOrgCode() {
        return orgCode;
   }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
   }

    public String getOrgName() {
        return orgName;
   }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
   }

    public String getMerchantType() {
        return merchantType;
   }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
   }

    public String getMerchantName() {
        return merchantName;
   }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
   }

    public String getBusLicense() {
        return busLicense;
   }

    public void setBusLicense(String busLicense) {
        this.busLicense = busLicense;
   }

    public String getLegRepName() {
        return legRepName;
   }

    public void setLegRepName(String legRepName) {
        this.legRepName = legRepName;
   }

    public String getLegRepPhone() {
        return legRepPhone;
   }

    public void setLegRepPhone(String legRepPhone) {
        this.legRepPhone = legRepPhone;
   }

    public String getProvCode() {
        return provCode;
   }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
   }

    public String getProvName() {
        return provName;
   }

    public void setProvName(String provName) {
        this.provName = provName;
   }

    public String getCityCode() {
        return cityCode;
   }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
   }

    public String getCityName() {
        return cityName;
   }

    public void setCityName(String cityName) {
        this.cityName = cityName;
   }

    public String getAreaCode() {
        return areaCode;
   }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
   }

    public String getAreaName() {
        return areaName;
   }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
   }

    public String getAddress() {
        return address;
   }

    public void setAddress(String address) {
        this.address = address;
   }

    public String getLegRepCertNo() {
        return legRepCertNo;
   }

    public void setLegRepCertNo(String legRepCertNo) {
        this.legRepCertNo = legRepCertNo;
   }

    public String getLegRepPhoto1() {
        return legRepPhoto1;
   }

    public void setLegRepPhoto1(String legRepPhoto1) {
        this.legRepPhoto1 = legRepPhoto1;
   }

    public String getLegRepPhoto2() {
        return legRepPhoto2;
   }

    public void setLegRepPhoto2(String legRepPhoto2) {
        this.legRepPhoto2 = legRepPhoto2;
   }

    public String getBusLicensePhoto() {
        return busLicensePhoto;
   }

    public void setBusLicensePhoto(String busLicensePhoto) {
        this.busLicensePhoto = busLicensePhoto;
   }

    public String getIndustry() {
        return industry;
   }

    public void setIndustry(String industry) {
        this.industry = industry;
   }

    public String getIntentBusCode() {
        return intentBusCode;
   }

    public void setIntentBusCode(String intentBusCode) {
        this.intentBusCode = intentBusCode;
   }

    public String getIntentBusName() {
        return intentBusName;
   }

    public void setIntentBusName(String intentBusName) {
        this.intentBusName = intentBusName;
   }

    public String getState() {
        return state;
   }

    public void setState(String state) {
        this.state = state;
   }

    public String getCreateBy() {
        return createBy;
   }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
   }

    public Timestamp getCreateTime() {
        return createTime;
   }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
   }

    public String getUpdateBy() {
        return updateBy;
   }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
   }

    public Timestamp getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
   }

    public Long getPid() {
        return pid;
   }

    public void setPid(Long pid) {
        this.pid = pid;
   }

    public String getContactPhone() {
        return contactPhone;
   }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
   }

    public String getBusPermitPhoto() {
        return busPermitPhoto;
   }

    public void setBusPermitPhoto(String busPermitPhoto) {
        this.busPermitPhoto = busPermitPhoto;
   }

    public String getHeadOffice() {
        return headOffice;
   }

    public void setHeadOffice(String headOffice) {
        this.headOffice = headOffice;
   }

    public String getWxEntpDid() {
        return wxEntpDid;
   }

    public void setWxEntpDid(String wxEntpDid) {
        this.wxEntpDid = wxEntpDid;
   }

    public String getWxEntpPdid() {
        return wxEntpPdid;
   }

    public void setWxEntpPdid(String wxEntpPdid) {
        this.wxEntpPdid = wxEntpPdid;
   }

    public String getCommisFlag() {
        return commisFlag;
   }

    public void setCommisFlag(String commisFlag) {
        this.commisFlag = commisFlag;
   }

    public String getRightsFlag() {
        return rightsFlag;
   }

    public void setRightsFlag(String rightsFlag) {
        this.rightsFlag = rightsFlag;
   }

    public String getQrcodeType() {
        return qrcodeType;
   }

    public void setQrcodeType(String qrcodeType) {
        this.qrcodeType = qrcodeType;
   }

    public String getLogoImgUrl() {
        return logoImgUrl;
   }

    public void setLogoImgUrl(String logoImgUrl) {
        this.logoImgUrl = logoImgUrl;
   }

    public String getBusLicenseType() {
        return busLicenseType;
    }

    public void setBusLicenseType(String busLicenseType) {
        this.busLicenseType = busLicenseType;
    }

    public String getBusLicenseName() {
        return busLicenseName;
    }

    public void setBusLicenseName(String busLicenseName) {
        this.busLicenseName = busLicenseName;
    }
}

