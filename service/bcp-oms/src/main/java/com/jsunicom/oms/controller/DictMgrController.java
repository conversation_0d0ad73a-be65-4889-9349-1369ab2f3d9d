package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.service.DataMonitorService;
import com.jsunicom.oms.service.DictFacade;
import com.jsunicom.oms.service.DictService;
import com.jsunicom.oms.po.Dict;
import com.jsunicom.oms.service.impl.DataMonitorServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-16-14:17
 */
@RestController
@RequestMapping(value = "/operator", name = "运营端-数据字典")
@Slf4j
public class DictMgrController extends AbstractSimpleController {
    @Autowired
    private DictService dictService;
    @Autowired
    private DataMonitorService dataMonitorService;

    @RequestMapping(value = "/dict/condition", method = RequestMethod.GET, name = "查询字典")
    public CustomResult getDictByPage(Dict dict, Integer pageNumber, Integer pageSize) {
            log.info("分页查询字典信息,kind:" + dict.getKind() + ",kindDesc:" + dict.getKindDesc() + ",code:" + dict.getCode() + ",name:" + dict.getName() + ",createBy:" + dict.getCreateBy() + ",updateBy:" + dict.getUpdateBy() + ",pageNumber:" + pageNumber + ",pageSize:" + pageSize);
            PageInfo<Dict> pagedResult = dictService.findDict(dict.getKind(), dict.getKindDesc(), dict.getCode(), dict.getName(),pageNumber,pageSize);
            log.info("查询字典信息成功,total:{}", pagedResult.getTotal());

            JSONObject result = new JSONObject();
            result.put("list", pagedResult.getList());
            result.put("totalNumber", pagedResult.getTotal());
            return ResultUtil.success(result);
    }
    @RequestMapping(value = "/dict", method = RequestMethod.POST, name = "新增或者修改字典")
    public CustomResult addOrUpdateDict(@RequestBody Dict dict, HttpServletRequest request) {
        log.info("进入DictMgrCtl.addOrUpdateDict 方法");
            String currAcctNo = request.getHeader("staffNo");
            if (dict.getId() != null) {
                Dict dictBefore=dictService.findById(dict.getId());
                dict.setUpdateBy(currAcctNo);
                int updateCount=dictService.update(dict);
                Dict dictAfter=dictService.findById(dict.getId());
                if(updateCount>0){
                    dataMonitorService.save("dict_config","update",dict.getId().toString(), currAcctNo,
                            dictAfter,dictBefore);
                }
            } else {
                dict.setCreateBy(currAcctNo);
                Dict dict1=dictService.save(dict);
                if(null!=dict1){
                    dataMonitorService.save("dict_config","add",dict1.getId().toString(), currAcctNo,
                            dict1.toString());
                }
            }
            JSONObject result = new JSONObject();
            result.put("code","success");
            return ResultUtil.success(result);
    }
    @RequestMapping(value = "/dict/id", method = RequestMethod.POST, name = "删除字典")
    public CustomResult deleteDictById(@RequestParam Long id,HttpServletRequest request) throws Exception {
            log.info("删除字典,id:{}", id);
            Dict dict=dictService.findById(id);
            String currAcctNo = request.getHeader("staffNo");
            int deleteCount=dictService.deleteById(id,dict);
            if(deleteCount>0){
                dataMonitorService.save("dict_config","delete",dict.getId().toString(), currAcctNo,
                        dict.toString());
            }
            JSONObject result = new JSONObject();
            result.put("code","success");
            return ResultUtil.success(result);
    }
    @RequestMapping(value="/list")
    public CustomResult getDict(String kind){
            log.info("进入DictMgrController.getDict方法");
            JSONObject result = new JSONObject();
            List<Dict> list = dictService.getDictsByKind(kind);
            result.put("ret", "0");
            result.put("errMsg", "");
            result.put("errCode", "");
            result.put("data",list);
            log.info("调用DictMgrController.getDict方法  成功");
            return ResultUtil.success(result);
    }

    @GetMapping("/getDictsByKindNew")
    public CustomResult getDictsByKindNew(@RequestParam(value = "kind") String kind){
        return ResultUtil.success(dictService.getDictsByKind(kind));
    }


    @GetMapping("/queryDictsByKind")
    public CustomResult queryDictsByKind(@RequestParam(value = "kind") String kind){

        try {
            log.info("DictMgrController.queryDictsByKind：字典查询，kind：{}",kind);
            return ResultUtil.success(dictService.queryDictsByKind(kind));

         }catch (Exception e){
            e.printStackTrace();
            JSONObject object = new JSONObject();
            return ResultUtil.errorByJSONObject(object);
        }



    }

    @GetMapping("/querySwitchStateByCode")
    public CustomResult querySwitchStateByCode(@RequestParam(value = "code") String code){
        log.info("DictMgrController.querySwitchStateByCode：开关查询请求，code：{}",code);
        String switchName = dictService.getNameByKey("switch", code);
        log.info("DictMgrController.querySwitchStateByCode：开关查询结果，name：{}",switchName);
        if (switchName.equals("1")){
            return ResultUtil.success(true);
        }else{
            return ResultUtil.success(false);
        }
    }

    @GetMapping("/queryDictsByKindAndCode")
    public CustomResult queryDictsByKindAndCode(@RequestParam(value = "kind") String kind,@RequestParam(value = "code") String code){
        log.info("DictMgrController.queryDictsByKindAndCode：字典查询，kind：{},code：{}",kind,code);
        String switchName = dictService.getNameByKey(kind, code);
        log.info("DictMgrController.queryDictsByKindAndCode：查询结果，name：{}",switchName);
        return ResultUtil.success(switchName);
    }
}
