package com.jsunicom.oms.service;

import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.dto.view.CampusViewDto;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-08-29-16:25
 */
public interface SchoolViewService {
    CustomResult querySchoolInfos(String orgCode, String campusId, String dateTime);

    CustomResult queryCityAndSchoolInfos(String orgCode,String serialNumber);

    CustomResult querySchoolStockAndDevelopUserInfos(CampusViewDto campusViewDto);
}
