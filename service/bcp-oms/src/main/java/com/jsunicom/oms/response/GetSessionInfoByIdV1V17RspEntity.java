/**
 * Copyright (c) 2016-Now http://open.chinaunicom.cn All rights reserved.
 */
package com.jsunicom.oms.response;


import java.io.Serializable;
import java.util.List;

public class GetSessionInfoByIdV1V17RspEntity implements Serializable {


    /**
     * 描述： , 长度限制：
     */
     public Rsp rsp;

    /**
     * 描述： , 长度限制：
     */
     public Rsp getRsp() {
        return rsp;
     }

     public Rsp setRsp(Rsp rsp) {
        this.rsp = rsp;
        return this.rsp;


     }

public static class Rsp implements Serializable {

    /**
     * 描述：返回代码。 0000 成功  8888失败 , 长度限制：
     */
     public String respCode;

    /**
     * 描述：返回代码。 0000 成功  8888失败 , 长度限制：
     */
     public String getRespCode() {
        return respCode;
     }

     public Rsp setRespCode(String respCode) {
        this.respCode = respCode;
        return this;


     }

    /**
     * 描述：返回描述 , 长度限制：
     */
     public String respMsg;

    /**
     * 描述：返回描述 , 长度限制：
     */
     public String getRespMsg() {
        return respMsg;
     }

     public Rsp setRespMsg(String respMsg) {
        this.respMsg = respMsg;
        return this;


     }

    /**
     * 描述：Session信息 , 长度限制：
     */
     public RespData respData;

    /**
     * 描述：Session信息 , 长度限制：
     */
     public RespData getRespData() {
        return respData;
     }

     public Rsp setRespData(RespData respData) {
        this.respData = respData;
        return this;


     }
}
public static class RespData implements Serializable {

    /**
     * 描述：登陆关系信息节点 , 长度限制：
     */
     public LoginRelationData loginRelationData;

    /**
     * 描述：登陆关系信息节点 , 长度限制：
     */
     public LoginRelationData getLoginRelationData() {
        return loginRelationData;
     }

     public RespData setLoginRelationData(LoginRelationData loginRelationData) {
        this.loginRelationData = loginRelationData;
        return this;


     }

    /**
     * 描述：能人类型（0-店铺， 1-社会能人，2-京东小店，3-苏宁人员，4-晨光文具，5-中国邮政） , 长度限制：
     */
     public String ableType;

    /**
     * 描述：能人类型（0-店铺， 1-社会能人，2-京东小店，3-苏宁人员，4-晨光文具，5-中国邮政） , 长度限制：
     */
     public String getAbleType() {
        return ableType;
     }

     public RespData setAbleType(String ableType) {
        this.ableType = ableType;
        return this;


     }

    /**
     * 描述：代理人信息 , 长度限制：
     */
     public AgentInfo agentInfo;

    /**
     * 描述：代理人信息 , 长度限制：
     */
     public AgentInfo getAgentInfo() {
        return agentInfo;
     }

     public RespData setAgentInfo(AgentInfo agentInfo) {
        this.agentInfo = agentInfo;
        return this;


     }

    /**
     * 描述：能人编码 , 长度限制：
     */
     public String mstaffIdMain;

    /**
     * 描述：能人编码 , 长度限制：
     */
     public String getMstaffIdMain() {
        return mstaffIdMain;
     }

     public RespData setMstaffIdMain(String mstaffIdMain) {
        this.mstaffIdMain = mstaffIdMain;
        return this;


     }

    /**
     * 描述：输入归集群 , 长度限制：
     */
     public String groupId;

    /**
     * 描述：输入归集群 , 长度限制：
     */
     public String getGroupId() {
        return groupId;
     }

     public RespData setGroupId(String groupId) {
        this.groupId = groupId;
        return this;


     }

    /**
     * 描述：1-手机号标识，2-是否实名认证标识 , 长度限制：
     */
     public String operFlag;

    /**
     * 描述：1-手机号标识，2-是否实名认证标识 , 长度限制：
     */
     public String getOperFlag() {
        return operFlag;
     }

     public RespData setOperFlag(String operFlag) {
        this.operFlag = operFlag;
        return this;


     }

    /**
     * 描述：登陆结果节点 , 长度限制：
     */
     public CheckLoginResult checkLoginResult;

    /**
     * 描述：登陆结果节点 , 长度限制：
     */
     public CheckLoginResult getCheckLoginResult() {
        return checkLoginResult;
     }

     public RespData setCheckLoginResult(CheckLoginResult checkLoginResult) {
        this.checkLoginResult = checkLoginResult;
        return this;


     }

    /**
     * 描述：地市编码 , 长度限制：
     */
     public String eparchyCode;

    /**
     * 描述：地市编码 , 长度限制：
     */
     public String getEparchyCode() {
        return eparchyCode;
     }

     public RespData setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
        return this;


     }

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String groupSerialNumber;

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String getGroupSerialNumber() {
        return groupSerialNumber;
     }

     public RespData setGroupSerialNumber(String groupSerialNumber) {
        this.groupSerialNumber = groupSerialNumber;
        return this;


     }

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String provinceCode;

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String getProvinceCode() {
        return provinceCode;
     }

     public RespData setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;


     }

    /**
     * 描述：能人编码 , 长度限制：
     */
     public String mstaffId;

    /**
     * 描述：能人编码 , 长度限制：
     */
     public String getMstaffId() {
        return mstaffId;
     }

     public RespData setMstaffId(String mstaffId) {
        this.mstaffId = mstaffId;
        return this;


     }

    /**
     * 描述：部门编码 , 长度限制：
     */
     public String departCode;

    /**
     * 描述：部门编码 , 长度限制：
     */
     public String getDepartCode() {
        return departCode;
     }

     public RespData setDepartCode(String departCode) {
        this.departCode = departCode;
        return this;


     }

    /**
     * 描述：能人姓名 , 长度限制：
     */
     public String mstaffNameMain;

    /**
     * 描述：能人姓名 , 长度限制：
     */
     public String getMstaffNameMain() {
        return mstaffNameMain;
     }

     public RespData setMstaffNameMain(String mstaffNameMain) {
        this.mstaffNameMain = mstaffNameMain;
        return this;


     }

    /**
     * 描述：发展人信息 , 长度限制：
     */
     public DevelopManInfo developManInfo;

    /**
     * 描述：发展人信息 , 长度限制：
     */
     public DevelopManInfo getDevelopManInfo() {
        return developManInfo;
     }

     public RespData setDevelopManInfo(DevelopManInfo developManInfo) {
        this.developManInfo = developManInfo;
        return this;


     }

    /**
     * 描述：渠道类型编码 , 长度限制：
     */
     public String departKindCode;

    /**
     * 描述：渠道类型编码 , 长度限制：
     */
     public String getDepartKindCode() {
        return departKindCode;
     }

     public RespData setDepartKindCode(String departKindCode) {
        this.departKindCode = departKindCode;
        return this;


     }

    /**
     * 描述：2i信息 , 长度限制：
     */
     public String staff2iinfo;

    /**
     * 描述：2i信息 , 长度限制：
     */
     public String getStaff2iinfo() {
        return staff2iinfo;
     }

     public RespData setStaff2iinfo(String staff2iinfo) {
        this.staff2iinfo = staff2iinfo;
        return this;


     }

    /**
     * 描述：码上购返回数据信息 , 长度限制：
     */
     public MsgData msgData;

    /**
     * 描述：码上购返回数据信息 , 长度限制：
     */
     public MsgData getMsgData() {
        return msgData;
     }

     public RespData setMsgData(MsgData msgData) {
        this.msgData = msgData;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String tradeSource;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getTradeSource() {
        return tradeSource;
     }

     public RespData setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
        return this;


     }

    /**
     * 描述：登陆来源 a：沃行销b：沃云购 c：沃行销沃云购已绑定 d:绑定沃行销工号未生效 , 长度限制：
     */

     public String staffResource;

    /**
     * 描述：登陆来源 a：沃行销b：沃云购 c：沃行销沃云购已绑定 d:绑定沃行销工号未生效 , 长度限制：
     */
     public String getStaffResource() {
        return staffResource;
     }

     public RespData setStaffResource(String staffResource) {
        this.staffResource = staffResource;
        return this;


     }

    /**
     * 描述：部门id , 长度限制：
     */

     public String departId;

    /**
     * 描述：部门id , 长度限制：
     */
     public String getDepartId() {
        return departId;
     }

     public RespData setDepartId(String departId) {
        this.departId = departId;
        return this;


     }

    /**
     * 描述：中台人员信息 , 长度限制：
     */

     public MidStaffInfo midStaffInfo;

    /**
     * 描述：中台人员信息 , 长度限制：
     */
     public MidStaffInfo getMidStaffInfo() {
        return midStaffInfo;
     }

     public RespData setMidStaffInfo(MidStaffInfo midStaffInfo) {
        this.midStaffInfo = midStaffInfo;
        return this;


     }

    /**
     * 描述：员工类型 , 长度限制：
     */

     public String staffType;

    /**
     * 描述：员工类型 , 长度限制：
     */
     public String getStaffType() {
        return staffType;
     }

     public RespData setStaffType(String staffType) {
        this.staffType = staffType;
        return this;


     }

    /**
     * 描述：能人编码 , 长度限制：
     */

     public String mstaffName;

    /**
     * 描述：能人编码 , 长度限制：
     */
     public String getMstaffName() {
        return mstaffName;
     }

     public RespData setMstaffName(String mstaffName) {
        this.mstaffName = mstaffName;
        return this;


     }

    /**
     * 描述：登陆号码 , 长度限制：
     */

     public String loginPhoneNumber;

    /**
     * 描述：登陆号码 , 长度限制：
     */
     public String getLoginPhoneNumber() {
        return loginPhoneNumber;
     }

     public RespData setLoginPhoneNumber(String loginPhoneNumber) {
        this.loginPhoneNumber = loginPhoneNumber;
        return this;


     }

    /**
     * 描述：地市名称 , 长度限制：
     */

     public String eparchyName;

    /**
     * 描述：地市名称 , 长度限制：
     */
     public String getEparchyName() {
        return eparchyName;
     }

     public RespData setEparchyName(String eparchyName) {
        this.eparchyName = eparchyName;
        return this;


     }

    /**
     * 描述：是否app , 长度限制：
     */

     public String isApp;

    /**
     * 描述：是否app , 长度限制：
     */
     public String getIsApp() {
        return isApp;
     }

     public RespData setIsApp(String isApp) {
        this.isApp = isApp;
        return this;


     }

    /**
     * 描述：是否登录成功 , 长度限制：
     */

     public boolean loginSuccess;

    /**
     * 描述：是否登录成功 , 长度限制：
     */
     public boolean getLoginSuccess() {
        return loginSuccess;
     }

     public RespData setLoginSuccess(boolean loginSuccess) {
        this.loginSuccess = loginSuccess;
        return this;


     }

    /**
     * 描述：用户姓名 , 长度限制：
     */

     public String userName;

    /**
     * 描述：用户姓名 , 长度限制：
     */
     public String getUserName() {
        return userName;
     }

     public RespData setUserName(String userName) {
        this.userName = userName;
        return this;


     }

    /**
     * 描述：当前登陆工号id , 长度限制：
     */

     public String currentStaffId;

    /**
     * 描述：当前登陆工号id , 长度限制：
     */
     public String getCurrentStaffId() {
        return currentStaffId;
     }

     public RespData setCurrentStaffId(String currentStaffId) {
        this.currentStaffId = currentStaffId;
        return this;


     }

    /**
     * 描述：省份名称 , 长度限制：
     */

     public String provinceName;

    /**
     * 描述：省份名称 , 长度限制：
     */
     public String getProvinceName() {
        return provinceName;
     }

     public RespData setProvinceName(String provinceName) {
        this.provinceName = provinceName;
        return this;


     }

    /**
     * 描述：设备信息 , 长度限制：
     */

     public DeviceInfo deviceInfo;

    /**
     * 描述：设备信息 , 长度限制：
     */
     public DeviceInfo getDeviceInfo() {
        return deviceInfo;
     }

     public RespData setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
        return this;


     }

    /**
     * 描述：是否行销标志（1-沃行销） , 长度限制：
     */

     public String isMkt;

    /**
     * 描述：是否行销标志（1-沃行销） , 长度限制：
     */
     public String getIsMkt() {
        return isMkt;
     }

     public RespData setIsMkt(String isMkt) {
        this.isMkt = isMkt;
        return this;


     }

    /**
     * 描述：沃云购节点 , 长度限制：
     */

     public YunSaleInfoMap yunSaleInfoMap;

    /**
     * 描述：沃云购节点 , 长度限制：
     */
     public YunSaleInfoMap getYunSaleInfoMap() {
        return yunSaleInfoMap;
     }

     public RespData setYunSaleInfoMap(YunSaleInfoMap yunSaleInfoMap) {
        this.yunSaleInfoMap = yunSaleInfoMap;
        return this;


     }

    /**
     * 描述：用户星级 , 长度限制：
     */

     public String starLevel;

    /**
     * 描述：用户星级 , 长度限制：
     */
     public String getStarLevel() {
        return starLevel;
     }

     public RespData setStarLevel(String starLevel) {
        this.starLevel = starLevel;
        return this;


     }

    /**
     * 描述：登录设备真实ip , 长度限制：
     */

     public String photoIp;

    /**
     * 描述：登录设备真实ip , 长度限制：
     */
     public String getPhotoIp() {
        return photoIp;
     }

     public RespData setPhotoIp(String photoIp) {
        this.photoIp = photoIp;
        return this;


     }

    /**
     * 描述：3位地市编码 , 长度限制：
     */

     public String userAreaCode;

    /**
     * 描述：3位地市编码 , 长度限制：
     */
     public String getUserAreaCode() {
        return userAreaCode;
     }

     public RespData setUserAreaCode(String userAreaCode) {
        this.userAreaCode = userAreaCode;
        return this;


     }

    /**
     * 描述：发展人信息 , 长度限制：
     */

     public DeveloperInfo developerInfo;

    /**
     * 描述：发展人信息 , 长度限制：
     */
     public DeveloperInfo getDeveloperInfo() {
        return developerInfo;
     }

     public RespData setDeveloperInfo(DeveloperInfo developerInfo) {
        this.developerInfo = developerInfo;
        return this;


     }

    /**
     * 描述：省份编码 , 长度限制：
     */

     public String realProvinceCode;

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String getRealProvinceCode() {
        return realProvinceCode;
     }

     public RespData setRealProvinceCode(String realProvinceCode) {
        this.realProvinceCode = realProvinceCode;
        return this;


     }

    /**
     * 描述：父部门编码 , 长度限制：
     */

     public String parentDepartId;

    /**
     * 描述：父部门编码 , 长度限制：
     */
     public String getParentDepartId() {
        return parentDepartId;
     }

     public RespData setParentDepartId(String parentDepartId) {
        this.parentDepartId = parentDepartId;
        return this;


     }

    /**
     * 描述：结束时间 , 长度限制：
     */

     public String endDate;

    /**
     * 描述：结束时间 , 长度限制：
     */
     public String getEndDate() {
        return endDate;
     }

     public RespData setEndDate(String endDate) {
        this.endDate = endDate;
        return this;


     }

    /**
     * 描述：员工id , 长度限制：
     */

     public String staffId;

    /**
     * 描述：员工id , 长度限制：
     */
     public String getStaffId() {
        return staffId;
     }

     public RespData setStaffId(String staffId) {
        this.staffId = staffId;
        return this;


     }

    /**
     * 描述：交付模式 , 长度限制：
     */

     public String deliveryModel;

    /**
     * 描述：交付模式 , 长度限制：
     */
     public String getDeliveryModel() {
        return deliveryModel;
     }

     public RespData setDeliveryModel(String deliveryModel) {
        this.deliveryModel = deliveryModel;
        return this;


     }

    /**
     * 描述：区域编码 , 长度限制：
     */

     public String areaCode;

    /**
     * 描述：区域编码 , 长度限制：
     */
     public String getAreaCode() {
        return areaCode;
     }

     public RespData setAreaCode(String areaCode) {
        this.areaCode = areaCode;
        return this;


     }

    /**
     * 描述：激励模式 , 长度限制：
     */

     public String motivationModel;

    /**
     * 描述：激励模式 , 长度限制：
     */
     public String getMotivationModel() {
        return motivationModel;
     }

     public RespData setMotivationModel(String motivationModel) {
        this.motivationModel = motivationModel;
        return this;


     }

    /**
     * 描述：生产部门编码 , 长度限制：
     */

     public String prodDepartid;

    /**
     * 描述：生产部门编码 , 长度限制：
     */
     public String getProdDepartid() {
        return prodDepartid;
     }

     public RespData setProdDepartid(String prodDepartid) {
        this.prodDepartid = prodDepartid;
        return this;


     }

    /**
     * 描述：员工姓名 , 长度限制：
     */

     public String staffName;

    /**
     * 描述：员工姓名 , 长度限制：
     */
     public String getStaffName() {
        return staffName;
     }

     public RespData setStaffName(String staffName) {
        this.staffName = staffName;
        return this;


     }

    /**
     * 描述：菜单权限信息 , 长度限制：
     */

     public List<RightInfo> rightInfo;

    /**
     * 描述：菜单权限信息 , 长度限制：
     */
     public List<RightInfo> getRightInfo() {
        return rightInfo;
     }

     public RespData setRightInfo(List<RightInfo> rightInfo) {
        this.rightInfo = rightInfo;
        return this;


     }

    /**
     * 描述：员工角色类型 , 长度限制：
     */

     public String authType;

    /**
     * 描述：员工角色类型 , 长度限制：
     */
     public String getAuthType() {
        return authType;
     }

     public RespData setAuthType(String authType) {
        this.authType = authType;
        return this;


     }

    /**
     * 描述：区县编码 , 长度限制：
     */

     public String cityCode;

    /**
     * 描述：区县编码 , 长度限制：
     */
     public String getCityCode() {
        return cityCode;
     }

     public RespData setCityCode(String cityCode) {
        this.cityCode = cityCode;
        return this;


     }

    /**
     * 描述： , 长度限制：
     */

     public String managerId;

    /**
     * 描述： , 长度限制：
     */
     public String getManagerId() {
        return managerId;
     }

     public RespData setManagerId(String managerId) {
        this.managerId = managerId;
        return this;


     }

    /**
     * 描述：部门串 , 长度限制：
     */

     public String departFrame;

    /**
     * 描述：部门串 , 长度限制：
     */
     public String getDepartFrame() {
        return departFrame;
     }

     public RespData setDepartFrame(String departFrame) {
        this.departFrame = departFrame;
        return this;


     }

    /**
     * 描述：地市名称 , 长度限制：
     */

     public String cityName;

    /**
     * 描述：地市名称 , 长度限制：
     */
     public String getCityName() {
        return cityName;
     }

     public RespData setCityName(String cityName) {
        this.cityName = cityName;
        return this;


     }

    /**
     * 描述：地市编码 , 长度限制：
     */

     public String realEparchyCode;

    /**
     * 描述：地市编码 , 长度限制：
     */
     public String getRealEparchyCode() {
        return realEparchyCode;
     }

     public RespData setRealEparchyCode(String realEparchyCode) {
        this.realEparchyCode = realEparchyCode;
        return this;


     }

    /**
     * 描述：有效标识（1-有效，2-锁定，3-冻结，4-失效） , 长度限制：
     */

     public String validflag;

    /**
     * 描述：有效标识（1-有效，2-锁定，3-冻结，4-失效） , 长度限制：
     */
     public String getValidflag() {
        return validflag;
     }

     public RespData setValidflag(String validflag) {
        this.validflag = validflag;
        return this;


     }

    /**
     * 描述：异业商户标记 , 长度限制：
     */

     public String mertag;

    /**
     * 描述：异业商户标记 , 长度限制：
     */
     public String getMertag() {
        return mertag;
     }

     public RespData setMertag(String mertag) {
        this.mertag = mertag;
        return this;


     }

    /**
     * 描述：显示顺序号 , 长度限制：
     */

     public String orderNo;

    /**
     * 描述：显示顺序号 , 长度限制：
     */
     public String getOrderNo() {
        return orderNo;
     }

     public RespData setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;


     }

    /**
     * 描述：生失效状态（0：有效） , 长度限制：
     */

     public String dimissionTag;

    /**
     * 描述：生失效状态（0：有效） , 长度限制：
     */
     public String getDimissionTag() {
        return dimissionTag;
     }

     public RespData setDimissionTag(String dimissionTag) {
        this.dimissionTag = dimissionTag;
        return this;


     }

    /**
     * 描述：联系方式 , 长度限制：
     */

     public String serialNumber;

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String getSerialNumber() {
        return serialNumber;
     }

     public RespData setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;


     }

    /**
     * 描述：对应渠道类型 , 长度限制：
     */

     public String codeTypeCode;

    /**
     * 描述：对应渠道类型 , 长度限制：
     */
     public String getCodeTypeCode() {
        return codeTypeCode;
     }

     public RespData setCodeTypeCode(String codeTypeCode) {
        this.codeTypeCode = codeTypeCode;
        return this;


     }

    /**
     * 描述：能人经理id , 长度限制：
     */

     public String managerStaffId;

    /**
     * 描述：能人经理id , 长度限制：
     */
     public String getManagerStaffId() {
        return managerStaffId;
     }

     public RespData setManagerStaffId(String managerStaffId) {
        this.managerStaffId = managerStaffId;
        return this;


     }

    /**
     * 描述：部门级别 , 长度限制：
     */

     public String departLevel;

    /**
     * 描述：部门级别 , 长度限制：
     */
     public String getDepartLevel() {
        return departLevel;
     }

     public RespData setDepartLevel(String departLevel) {
        this.departLevel = departLevel;
        return this;


     }

    /**
     * 描述：开始时间 , 长度限制：
     */

     public String startDate;

    /**
     * 描述：开始时间 , 长度限制：
     */
     public String getStartDate() {
        return startDate;
     }

     public RespData setStartDate(String startDate) {
        this.startDate = startDate;
        return this;


     }

    /**
     * 描述：生产部门名称 , 长度限制：
     */

     public String prodDepartname;

    /**
     * 描述：生产部门名称 , 长度限制：
     */
     public String getProdDepartname() {
        return prodDepartname;
     }

     public RespData setProdDepartname(String prodDepartname) {
        this.prodDepartname = prodDepartname;
        return this;


     }

    /**
     * 描述：渠道类型 , 长度限制：
     */

     public String standardKindCode;

    /**
     * 描述：渠道类型 , 长度限制：
     */
     public String getStandardKindCode() {
        return standardKindCode;
     }

     public RespData setStandardKindCode(String standardKindCode) {
        this.standardKindCode = standardKindCode;
        return this;


     }

    /**
     * 描述：部门名称 , 长度限制：
     */

     public String departName;

    /**
     * 描述：部门名称 , 长度限制：
     */
     public String getDepartName() {
        return departName;
     }

     public RespData setDepartName(String departName) {
        this.departName = departName;
        return this;


     }

    /**
     * 描述：员工信息 , 长度限制：
     */

     public StaffInfo staffInfo;

    /**
     * 描述：员工信息 , 长度限制：
     */
     public StaffInfo getStaffInfo() {
        return staffInfo;
     }

     public RespData setStaffInfo(StaffInfo staffInfo) {
        this.staffInfo = staffInfo;
        return this;


     }

    /**
     * 描述：经度 , 长度限制：
     */

     public String longitude;

    /**
     * 描述：经度 , 长度限制：
     */
     public String getLongitude() {
        return longitude;
     }

     public RespData setLongitude(String longitude) {
        this.longitude = longitude;
        return this;


     }

    /**
     * 描述：纬度 , 长度限制：
     */

     public String latitude;

    /**
     * 描述：纬度 , 长度限制：
     */
     public String getLatitude() {
        return latitude;
     }

     public RespData setLatitude(String latitude) {
        this.latitude = latitude;
        return this;


     }
}
public static class StaffInfo {

    /**
     * 描述：是否集中生产 , 长度限制：
     */

     public String isCenterProdPlace;

    /**
     * 描述：是否集中生产 , 长度限制：
     */
     public String getIsCenterProdPlace() {
        return isCenterProdPlace;
     }

     public StaffInfo setIsCenterProdPlace(String isCenterProdPlace) {
        this.isCenterProdPlace = isCenterProdPlace;
        return this;


     }

    /**
     * 描述：结束时间 , 长度限制：
     */

     public Integer endDate;

    /**
     * 描述：结束时间 , 长度限制：
     */
     public Integer getEndDate() {
        return endDate;
     }

     public StaffInfo setEndDate(Integer endDate) {
        this.endDate = endDate;
        return this;


     }

    /**
     * 描述：员工级别 , 长度限制：
     */

     public String rightClass;

    /**
     * 描述：员工级别 , 长度限制：
     */
     public String getRightClass() {
        return rightClass;
     }

     public StaffInfo setRightClass(String rightClass) {
        this.rightClass = rightClass;
        return this;


     }

    /**
     * 描述：输入归集群 , 长度限制：
     */

     public String groupId;

    /**
     * 描述：输入归集群 , 长度限制：
     */
     public String getGroupId() {
        return groupId;
     }

     public StaffInfo setGroupId(String groupId) {
        this.groupId = groupId;
        return this;


     }

    /**
     * 描述：是否物流点(0:否,1:是)' , 长度限制：
     */

     public String isLogistics;

    /**
     * 描述：是否物流点(0:否,1:是)' , 长度限制：
     */
     public String getIsLogistics() {
        return isLogistics;
     }

     public StaffInfo setIsLogistics(String isLogistics) {
        this.isLogistics = isLogistics;
        return this;


     }

    /**
     * 描述：渠道类型编码 , 长度限制：
     */

     public String departKindCode;

    /**
     * 描述：渠道类型编码 , 长度限制：
     */
     public String getDepartKindCode() {
        return departKindCode;
     }

     public StaffInfo setDepartKindCode(String departKindCode) {
        this.departKindCode = departKindCode;
        return this;


     }

    /**
     * 描述：联系方式 , 长度限制：
     */

     public String groupSerialNumber;

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String getGroupSerialNumber() {
        return groupSerialNumber;
     }

     public StaffInfo setGroupSerialNumber(String groupSerialNumber) {
        this.groupSerialNumber = groupSerialNumber;
        return this;


     }

    /**
     * 描述：部门级别 , 长度限制：
     */

     public Integer departLevel;

    /**
     * 描述：部门级别 , 长度限制：
     */
     public Integer getDepartLevel() {
        return departLevel;
     }

     public StaffInfo setDepartLevel(Integer departLevel) {
        this.departLevel = departLevel;
        return this;


     }

    /**
     * 描述：行政管理上级部门 , 长度限制：
     */

     public String areaLevel;

    /**
     * 描述：行政管理上级部门 , 长度限制：
     */
     public String getAreaLevel() {
        return areaLevel;
     }

     public StaffInfo setAreaLevel(String areaLevel) {
        this.areaLevel = areaLevel;
        return this;


     }

    /**
     * 描述：地市名称 , 长度限制：
     */

     public String eparchyName;

    /**
     * 描述：地市名称 , 长度限制：
     */
     public String getEparchyName() {
        return eparchyName;
     }

     public StaffInfo setEparchyName(String eparchyName) {
        this.eparchyName = eparchyName;
        return this;


     }

    /**
     * 描述：部门失生效 , 长度限制：
     */

     public String departValidTag;

    /**
     * 描述：部门失生效 , 长度限制：
     */
     public String getDepartValidTag() {
        return departValidTag;
     }

     public StaffInfo setDepartValidTag(String departValidTag) {
        this.departValidTag = departValidTag;
        return this;


     }

    /**
     * 描述：父部门编码 , 长度限制：
     */

     public String parentDepartId;

    /**
     * 描述：父部门编码 , 长度限制：
     */
     public String getParentDepartId() {
        return parentDepartId;
     }

     public StaffInfo setParentDepartId(String parentDepartId) {
        this.parentDepartId = parentDepartId;
        return this;


     }

    /**
     * 描述：发展人渠道类型 , 长度限制：
     */

     public String devStandardKindCode;

    /**
     * 描述：发展人渠道类型 , 长度限制：
     */
     public String getDevStandardKindCode() {
        return devStandardKindCode;
     }

     public StaffInfo setDevStandardKindCode(String devStandardKindCode) {
        this.devStandardKindCode = devStandardKindCode;
        return this;


     }

    /**
     * 描述：渠道名称 , 长度限制：
     */

     public String departName;

    /**
     * 描述：渠道名称 , 长度限制：
     */
     public String getDepartName() {
        return departName;
     }

     public StaffInfo setDepartName(String departName) {
        this.departName = departName;
        return this;


     }

    /**
     * 描述：银行账户 , 长度限制：
     */

     public String bankAccount;

    /**
     * 描述：银行账户 , 长度限制：
     */
     public String getBankAccount() {
        return bankAccount;
     }

     public StaffInfo setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
        return this;


     }

    /**
     * 描述：联系方式 , 长度限制：
     */

     public String serialNumber;

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String getSerialNumber() {
        return serialNumber;
     }

     public StaffInfo setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;


     }

    /**
     * 描述：显示顺序号 , 长度限制：
     */

     public Integer orderNo;

    /**
     * 描述：显示顺序号 , 长度限制：
     */
     public Integer getOrderNo() {
        return orderNo;
     }

     public StaffInfo setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
        return this;


     }

    /**
     * 描述：交付模式 , 长度限制：
     */

     public String deliveryModel;

    /**
     * 描述：交付模式 , 长度限制：
     */
     public String getDeliveryModel() {
        return deliveryModel;
     }

     public StaffInfo setDeliveryModel(String deliveryModel) {
        this.deliveryModel = deliveryModel;
        return this;


     }

    /**
     * 描述：更新人员 , 长度限制：
     */

     public String updateStaffId;

    /**
     * 描述：更新人员 , 长度限制：
     */
     public String getUpdateStaffId() {
        return updateStaffId;
     }

     public StaffInfo setUpdateStaffId(String updateStaffId) {
        this.updateStaffId = updateStaffId;
        return this;


     }

    /**
     * 描述：生失效状态（0：有效） , 长度限制：
     */

     public String demissionTag;

    /**
     * 描述：生失效状态（0：有效） , 长度限制：
     */
     public String getDemissionTag() {
        return demissionTag;
     }

     public StaffInfo setDemissionTag(String demissionTag) {
        this.demissionTag = demissionTag;
        return this;


     }

    /**
     * 描述：生失效 , 长度限制：
     */

     public String validTag;

    /**
     * 描述：生失效 , 长度限制：
     */
     public String getValidTag() {
        return validTag;
     }

     public StaffInfo setValidTag(String validTag) {
        this.validTag = validTag;
        return this;


     }

    /**
     * 描述：省份名称 , 长度限制：
     */

     public String provinceName;

    /**
     * 描述：省份名称 , 长度限制：
     */
     public String getProvinceName() {
        return provinceName;
     }

     public StaffInfo setProvinceName(String provinceName) {
        this.provinceName = provinceName;
        return this;


     }

    /**
     * 描述：开始时间 , 长度限制：
     */

     public Integer startDate;

    /**
     * 描述：开始时间 , 长度限制：
     */
     public Integer getStartDate() {
        return startDate;
     }

     public StaffInfo setStartDate(Integer startDate) {
        this.startDate = startDate;
        return this;


     }

    /**
     * 描述：生日 , 长度限制：
     */

     public Integer birthday;

    /**
     * 描述：生日 , 长度限制：
     */
     public Integer getBirthday() {
        return birthday;
     }

     public StaffInfo setBirthday(Integer birthday) {
        this.birthday = birthday;
        return this;


     }

    /**
     * 描述：联系方式 , 长度限制：
     */

     public String linkPhone;

    /**
     * 描述：联系方式 , 长度限制：
     */
     public String getLinkPhone() {
        return linkPhone;
     }

     public StaffInfo setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
        return this;


     }

    /**
     * 描述：能人类型（0-店铺， 1-社会能人，2-京东小店，3-苏宁人员，4-晨光文具，5-中国邮政） , 长度限制：
     */

     public String ableType;

    /**
     * 描述：能人类型（0-店铺， 1-社会能人，2-京东小店，3-苏宁人员，4-晨光文具，5-中国邮政） , 长度限制：
     */
     public String getAbleType() {
        return ableType;
     }

     public StaffInfo setAbleType(String ableType) {
        this.ableType = ableType;
        return this;


     }

    /**
     * 描述：部门串 , 长度限制：
     */

     public String departFrame;

    /**
     * 描述：部门串 , 长度限制：
     */
     public String getDepartFrame() {
        return departFrame;
     }

     public StaffInfo setDepartFrame(String departFrame) {
        this.departFrame = departFrame;
        return this;


     }

    /**
     * 描述：区县编码 , 长度限制：
     */

     public String cityCode;

    /**
     * 描述：区县编码 , 长度限制：
     */
     public String getCityCode() {
        return cityCode;
     }

     public StaffInfo setCityCode(String cityCode) {
        this.cityCode = cityCode;
        return this;


     }

    /**
     * 描述：更新部门编码 , 长度限制：
     */

     public String updateDepartId;

    /**
     * 描述：更新部门编码 , 长度限制：
     */
     public String getUpdateDepartId() {
        return updateDepartId;
     }

     public StaffInfo setUpdateDepartId(String updateDepartId) {
        this.updateDepartId = updateDepartId;
        return this;


     }

    /**
     * 描述：员工id , 长度限制：
     */

     public String mstaffId;

    /**
     * 描述：员工id , 长度限制：
     */
     public String getMstaffId() {
        return mstaffId;
     }

     public StaffInfo setMstaffId(String mstaffId) {
        this.mstaffId = mstaffId;
        return this;


     }

    /**
     * 描述：是否自提点 , 长度限制：
     */

     public String isSelfPickUp;

    /**
     * 描述：是否自提点 , 长度限制：
     */
     public String getIsSelfPickUp() {
        return isSelfPickUp;
     }

     public StaffInfo setIsSelfPickUp(String isSelfPickUp) {
        this.isSelfPickUp = isSelfPickUp;
        return this;


     }

    /**
     * 描述：生产部门编码 , 长度限制：
     */

     public String prodDepartId;

    /**
     * 描述：生产部门编码 , 长度限制：
     */
     public String getProdDepartId() {
        return prodDepartId;
     }

     public StaffInfo setProdDepartId(String prodDepartId) {
        this.prodDepartId = prodDepartId;
        return this;


     }

    /**
     * 描述：部门名称 , 长度限制：
     */

     public String departKind;

    /**
     * 描述：部门名称 , 长度限制：
     */
     public String getDepartKind() {
        return departKind;
     }

     public StaffInfo setDepartKind(String departKind) {
        this.departKind = departKind;
        return this;


     }

    /**
     * 描述：员工姓名 , 长度限制：
     */

     public String staffName;

    /**
     * 描述：员工姓名 , 长度限制：
     */
     public String getStaffName() {
        return staffName;
     }

     public StaffInfo setStaffName(String staffName) {
        this.staffName = staffName;
        return this;


     }

    /**
     * 描述：生产部门名称 , 长度限制：
     */

     public String prodDepartName;

    /**
     * 描述：生产部门名称 , 长度限制：
     */
     public String getProdDepartName() {
        return prodDepartName;
     }

     public StaffInfo setProdDepartName(String prodDepartName) {
        this.prodDepartName = prodDepartName;
        return this;


     }

    /**
     * 描述：部门编码 , 长度限制：
     */

     public String departId;

    /**
     * 描述：部门编码 , 长度限制：
     */
     public String getDepartId() {
        return departId;
     }

     public StaffInfo setDepartId(String departId) {
        this.departId = departId;
        return this;


     }

    /**
     * 描述：激励模式 , 长度限制：
     */

     public String motivationModel;

    /**
     * 描述：激励模式 , 长度限制：
     */
     public String getMotivationModel() {
        return motivationModel;
     }

     public StaffInfo setMotivationModel(String motivationModel) {
        this.motivationModel = motivationModel;
        return this;


     }

    /**
     * 描述：对应渠道类型 , 长度限制：
     */

     public String codeTypeCode;

    /**
     * 描述：对应渠道类型 , 长度限制：
     */
     public String getCodeTypeCode() {
        return codeTypeCode;
     }

     public StaffInfo setCodeTypeCode(String codeTypeCode) {
        this.codeTypeCode = codeTypeCode;
        return this;


     }

    /**
     * 描述：员工角色类型 , 长度限制：
     */

     public String authType;

    /**
     * 描述：员工角色类型 , 长度限制：
     */
     public String getAuthType() {
        return authType;
     }

     public StaffInfo setAuthType(String authType) {
        this.authType = authType;
        return this;


     }

    /**
     * 描述：电子邮箱地址 , 长度限制：
     */

     public String email;

    /**
     * 描述：电子邮箱地址 , 长度限制：
     */
     public String getEmail() {
        return email;
     }

     public StaffInfo setEmail(String email) {
        this.email = email;
        return this;


     }

    /**
     * 描述：是否直销渠道 , 长度限制：
     */

     public String isSellDepart;

    /**
     * 描述：是否直销渠道 , 长度限制：
     */
     public String getIsSellDepart() {
        return isSellDepart;
     }

     public StaffInfo setIsSellDepart(String isSellDepart) {
        this.isSellDepart = isSellDepart;
        return this;


     }

    /**
     * 描述：省份编码 , 长度限制：
     */

     public String provinceCode;

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String getProvinceCode() {
        return provinceCode;
     }

     public StaffInfo setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;


     }

    /**
     * 描述：3位地市编码 , 长度限制：
     */

     public String userAreaCode;

    /**
     * 描述：3位地市编码 , 长度限制：
     */
     public String getUserAreaCode() {
        return userAreaCode;
     }

     public StaffInfo setUserAreaCode(String userAreaCode) {
        this.userAreaCode = userAreaCode;
        return this;


     }

    /**
     * 描述：性别 , 长度限制：
     */

     public String sex;

    /**
     * 描述：性别 , 长度限制：
     */
     public String getSex() {
        return sex;
     }

     public StaffInfo setSex(String sex) {
        this.sex = sex;
        return this;


     }

    /**
     * 描述：更新时间 , 长度限制：
     */

     public Integer updateTime;

    /**
     * 描述：更新时间 , 长度限制：
     */
     public Integer getUpdateTime() {
        return updateTime;
     }

     public StaffInfo setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
        return this;


     }

    /**
     * 描述：员工类型 , 长度限制：
     */

     public String staffType;

    /**
     * 描述：员工类型 , 长度限制：
     */
     public String getStaffType() {
        return staffType;
     }

     public StaffInfo setStaffType(String staffType) {
        this.staffType = staffType;
        return this;


     }

    /**
     * 描述：是否阅读注册协议 , 长度限制：
     */

     public String isRegistrationProtocol;

    /**
     * 描述：是否阅读注册协议 , 长度限制：
     */
     public String getIsRegistrationProtocol() {
        return isRegistrationProtocol;
     }

     public StaffInfo setIsRegistrationProtocol(String isRegistrationProtocol) {
        this.isRegistrationProtocol = isRegistrationProtocol;
        return this;


     }

    /**
     * 描述：身份证号 , 长度限制：
     */

     public String userPid;

    /**
     * 描述：身份证号 , 长度限制：
     */
     public String getUserPid() {
        return userPid;
     }

     public StaffInfo setUserPid(String userPid) {
        this.userPid = userPid;
        return this;


     }

    /**
     * 描述：部门编码 , 长度限制：
     */

     public String departCode;

    /**
     * 描述：部门编码 , 长度限制：
     */
     public String getDepartCode() {
        return departCode;
     }

     public StaffInfo setDepartCode(String departCode) {
        this.departCode = departCode;
        return this;


     }

    /**
     * 描述：父级区域编码 , 长度限制：
     */

     public String parentAreaCode;

    /**
     * 描述：父级区域编码 , 长度限制：
     */
     public String getParentAreaCode() {
        return parentAreaCode;
     }

     public StaffInfo setParentAreaCode(String parentAreaCode) {
        this.parentAreaCode = parentAreaCode;
        return this;


     }

    /**
     * 描述：员工id , 长度限制：
     */

     public String staffId;

    /**
     * 描述：员工id , 长度限制：
     */
     public String getStaffId() {
        return staffId;
     }

     public StaffInfo setStaffId(String staffId) {
        this.staffId = staffId;
        return this;


     }

    /**
     * 描述：区域串 , 长度限制：
     */

     public String areaFrame;

    /**
     * 描述：区域串 , 长度限制：
     */
     public String getAreaFrame() {
        return areaFrame;
     }

     public StaffInfo setAreaFrame(String areaFrame) {
        this.areaFrame = areaFrame;
        return this;


     }

    /**
     * 描述：渠道类型 , 长度限制：
     */

     public String standardKindCode;

    /**
     * 描述：渠道类型 , 长度限制：
     */
     public String getStandardKindCode() {
        return standardKindCode;
     }

     public StaffInfo setStandardKindCode(String standardKindCode) {
        this.standardKindCode = standardKindCode;
        return this;


     }

    /**
     * 描述：地市编码 , 长度限制：
     */

     public String eparchyCode;

    /**
     * 描述：地市编码 , 长度限制：
     */
     public String getEparchyCode() {
        return eparchyCode;
     }

     public StaffInfo setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
        return this;


     }
}
public static class RightInfo {

    /**
     * 描述：权限图标顺序 , 长度限制：
     */

     public String imgIndex;

    /**
     * 描述：权限图标顺序 , 长度限制：
     */
     public String getImgIndex() {
        return imgIndex;
     }

     public RightInfo setImgIndex(String imgIndex) {
        this.imgIndex = imgIndex;
        return this;


     }

    /**
     * 描述：父菜单id , 长度限制：
     */

     public String parentMenuId;

    /**
     * 描述：父菜单id , 长度限制：
     */
     public String getParentMenuId() {
        return parentMenuId;
     }

     public RightInfo setParentMenuId(String parentMenuId) {
        this.parentMenuId = parentMenuId;
        return this;


     }

    /**
     * 描述：权限id , 长度限制：
     */

     public String dataId;

    /**
     * 描述：权限id , 长度限制：
     */
     public String getDataId() {
        return dataId;
     }

     public RightInfo setDataId(String dataId) {
        this.dataId = dataId;
        return this;


     }

    /**
     * 描述：菜单链接 , 长度限制：
     */

     public String menuUrl;

    /**
     * 描述：菜单链接 , 长度限制：
     */
     public String getMenuUrl() {
        return menuUrl;
     }

     public RightInfo setMenuUrl(String menuUrl) {
        this.menuUrl = menuUrl;
        return this;


     }

    /**
     * 描述：菜单来源 , 长度限制：
     */

     public String resource;

    /**
     * 描述：菜单来源 , 长度限制：
     */
     public String getResource() {
        return resource;
     }

     public RightInfo setResource(String resource) {
        this.resource = resource;
        return this;


     }

    /**
     * 描述：权限类型 , 长度限制：
     */

     public String dataType;

    /**
     * 描述：权限类型 , 长度限制：
     */
     public String getDataType() {
        return dataType;
     }

     public RightInfo setDataType(String dataType) {
        this.dataType = dataType;
        return this;


     }

    /**
     * 描述：菜单级别 , 长度限制：
     */

     public Integer menuLevel;

    /**
     * 描述：菜单级别 , 长度限制：
     */
     public Integer getMenuLevel() {
        return menuLevel;
     }

     public RightInfo setMenuLevel(Integer menuLevel) {
        this.menuLevel = menuLevel;
        return this;


     }

    /**
     * 描述：顺序 , 长度限制：
     */

     public Integer showOrder;

    /**
     * 描述：顺序 , 长度限制：
     */
     public Integer getShowOrder() {
        return showOrder;
     }

     public RightInfo setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
        return this;


     }

    /**
     * 描述：权限名称 , 长度限制：
     */

     public String dataName;

    /**
     * 描述：权限名称 , 长度限制：
     */
     public String getDataName() {
        return dataName;
     }

     public RightInfo setDataName(String dataName) {
        this.dataName = dataName;
        return this;


     }

    /**
     * 描述：权限编码 , 长度限制：
     */

     public String dataCode;

    /**
     * 描述：权限编码 , 长度限制：
     */
     public String getDataCode() {
        return dataCode;
     }

     public RightInfo setDataCode(String dataCode) {
        this.dataCode = dataCode;
        return this;


     }
}
public static class DeveloperInfo {

    /**
     * 描述：发展人地市编码 , 长度限制：
     */

     public String developEparchyCode;

    /**
     * 描述：发展人地市编码 , 长度限制：
     */
     public String getDevelopEparchyCode() {
        return developEparchyCode;
     }

     public DeveloperInfo setDevelopEparchyCode(String developEparchyCode) {
        this.developEparchyCode = developEparchyCode;
        return this;


     }

    /**
     * 描述：发展人部门名称 , 长度限制：
     */

     public String developDepartName;

    /**
     * 描述：发展人部门名称 , 长度限制：
     */
     public String getDevelopDepartName() {
        return developDepartName;
     }

     public DeveloperInfo setDevelopDepartName(String developDepartName) {
        this.developDepartName = developDepartName;
        return this;


     }

    /**
     * 描述：发展人员工编码 , 长度限制：
     */

     public String developStaffId;

    /**
     * 描述：发展人员工编码 , 长度限制：
     */
     public String getDevelopStaffId() {
        return developStaffId;
     }

     public DeveloperInfo setDevelopStaffId(String developStaffId) {
        this.developStaffId = developStaffId;
        return this;


     }

    /**
     * 描述：发展人员工名称 , 长度限制：
     */

     public String developStaffName;

    /**
     * 描述：发展人员工名称 , 长度限制：
     */
     public String getDevelopStaffName() {
        return developStaffName;
     }

     public DeveloperInfo setDevelopStaffName(String developStaffName) {
        this.developStaffName = developStaffName;
        return this;


     }

    /**
     * 描述：发展人部门编码 , 长度限制：
     */

     public String developDepartId;

    /**
     * 描述：发展人部门编码 , 长度限制：
     */
     public String getDevelopDepartId() {
        return developDepartId;
     }

     public DeveloperInfo setDevelopDepartId(String developDepartId) {
        this.developDepartId = developDepartId;
        return this;


     }

    /**
     * 描述：发展人创建日期 , 长度限制：
     */

     public String developDate;

    /**
     * 描述：发展人创建日期 , 长度限制：
     */
     public String getDevelopDate() {
        return developDate;
     }

     public DeveloperInfo setDevelopDate(String developDate) {
        this.developDate = developDate;
        return this;


     }

    /**
     * 描述：发展人渠道类型 , 长度限制：
     */

     public String devStandardKindCode;

    /**
     * 描述：发展人渠道类型 , 长度限制：
     */
     public String getDevStandardKindCode() {
        return devStandardKindCode;
     }

     public DeveloperInfo setDevStandardKindCode(String devStandardKindCode) {
        this.devStandardKindCode = devStandardKindCode;
        return this;


     }

    /**
     * 描述：发展人联系方式 , 长度限制：
     */

     public String developContact;

    /**
     * 描述：发展人联系方式 , 长度限制：
     */
     public String getDevelopContact() {
        return developContact;
     }

     public DeveloperInfo setDevelopContact(String developContact) {
        this.developContact = developContact;
        return this;


     }
}
public static class YunSaleInfoMap {
}
public static class DeviceInfo {

    /**
     * 描述：设备ip , 长度限制：
     */

     public String ip;

    /**
     * 描述：设备ip , 长度限制：
     */
     public String getIp() {
        return ip;
     }

     public DeviceInfo setIp(String ip) {
        this.ip = ip;
        return this;


     }

    /**
     * 描述：Imsi , 长度限制：
     */

     public String imsi;

    /**
     * 描述：Imsi , 长度限制：
     */
     public String getImsi() {
        return imsi;
     }

     public DeviceInfo setImsi(String imsi) {
        this.imsi = imsi;
        return this;


     }

    /**
     * 描述：设备标识 , 长度限制：
     */

     public String uuid;

    /**
     * 描述：设备标识 , 长度限制：
     */
     public String getUuid() {
        return uuid;
     }

     public DeviceInfo setUuid(String uuid) {
        this.uuid = uuid;
        return this;


     }

    /**
     * 描述：终端类型 , 长度限制：
     */

     public String platform;

    /**
     * 描述：终端类型 , 长度限制：
     */
     public String getPlatform() {
        return platform;
     }

     public DeviceInfo setPlatform(String platform) {
        this.platform = platform;
        return this;


     }

    /**
     * 描述：Mac地址 , 长度限制：
     */

     public String mac;

    /**
     * 描述：Mac地址 , 长度限制：
     */
     public String getMac() {
        return mac;
     }

     public DeviceInfo setMac(String mac) {
        this.mac = mac;
        return this;


     }

    /**
     * 描述：生产商 , 长度限制：
     */

     public String manufacturer;

    /**
     * 描述：生产商 , 长度限制：
     */
     public String getManufacturer() {
        return manufacturer;
     }

     public DeviceInfo setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;


     }

    /**
     * 描述：Sdk版本 , 长度限制：
     */

     public String sdkversion;

    /**
     * 描述：Sdk版本 , 长度限制：
     */
     public String getSdkversion() {
        return sdkversion;
     }

     public DeviceInfo setSdkversion(String sdkversion) {
        this.sdkversion = sdkversion;
        return this;


     }

    /**
     * 描述：Imsi , 长度限制：
     */

     public String imei;

    /**
     * 描述：Imsi , 长度限制：
     */
     public String getImei() {
        return imei;
     }

     public DeviceInfo setImei(String imei) {
        this.imei = imei;
        return this;


     }

    /**
     * 描述：Sim卡号 , 长度限制：
     */

     public String simnumber;

    /**
     * 描述：Sim卡号 , 长度限制：
     */
     public String getSimnumber() {
        return simnumber;
     }

     public DeviceInfo setSimnumber(String simnumber) {
        this.simnumber = simnumber;
        return this;


     }

    /**
     * 描述：产品名称 , 长度限制：
     */

     public String productname;

    /**
     * 描述：产品名称 , 长度限制：
     */
     public String getProductname() {
        return productname;
     }

     public DeviceInfo setProductname(String productname) {
        this.productname = productname;
        return this;


     }

    /**
     * 描述：型号 , 长度限制：
     */

     public String model;

    /**
     * 描述：型号 , 长度限制：
     */
     public String getModel() {
        return model;
     }

     public DeviceInfo setModel(String model) {
        this.model = model;
        return this;


     }

    /**
     * 描述：操作系统版本 , 长度限制：
     */

     public String osversion;

    /**
     * 描述：操作系统版本 , 长度限制：
     */
     public String getOsversion() {
        return osversion;
     }

     public DeviceInfo setOsversion(String osversion) {
        this.osversion = osversion;
        return this;


     }

    /**
     * 描述：品牌 , 长度限制：
     */

     public String brand;

    /**
     * 描述：品牌 , 长度限制：
     */
     public String getBrand() {
        return brand;
     }

     public DeviceInfo setBrand(String brand) {
        this.brand = brand;
        return this;


     }
}
public static class MidStaffInfo {

    /**
     * 描述：是否为中台人员 , 长度限制：
     */

     public String isMidStaff;

    /**
     * 描述：是否为中台人员 , 长度限制：
     */
     public String getIsMidStaff() {
        return isMidStaff;
     }

     public MidStaffInfo setIsMidStaff(String isMidStaff) {
        this.isMidStaff = isMidStaff;
        return this;


     }
}
public static class MsgData {

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String origin;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getOrigin() {
        return origin;
     }

     public MsgData setOrigin(String origin) {
        this.origin = origin;
        return this;


     }

    /**
     * 描述：登录方式: passwd密码 captcha 验证码 , 长度限制：
     */

     public String loginBy;

    /**
     * 描述：登录方式: passwd密码 captcha 验证码 , 长度限制：
     */
     public String getLoginBy() {
        return loginBy;
     }

     public MsgData setLoginBy(String loginBy) {
        this.loginBy = loginBy;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String loginFrom;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getLoginFrom() {
        return loginFrom;
     }

     public MsgData setLoginFrom(String loginFrom) {
        this.loginFrom = loginFrom;
        return this;


     }

    /**
     * 描述：定位开关，1:强制打开定位 , 长度限制：
     */

     public String locationSwitch;

    /**
     * 描述：定位开关，1:强制打开定位 , 长度限制：
     */
     public String getLocationSwitch() {
        return locationSwitch;
     }

     public MsgData setLocationSwitch(String locationSwitch) {
        this.locationSwitch = locationSwitch;
        return this;


     }

    /**
     * 描述：用户手机号码 , 长度限制：
     */

     public String phoneNo;

    /**
     * 描述：用户手机号码 , 长度限制：
     */
     public String getPhoneNo() {
        return phoneNo;
     }

     public MsgData setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
        return this;


     }

    /**
     * 描述：发展人手机号 , 长度限制：
     */

     public String devPhoneNo;

    /**
     * 描述：发展人手机号 , 长度限制：
     */
     public String getDevPhoneNo() {
        return devPhoneNo;
     }

     public MsgData setDevPhoneNo(String devPhoneNo) {
        this.devPhoneNo = devPhoneNo;
        return this;


     }

    /**
     * 描述：用户id , 长度限制：
     */

     public String uid;

    /**
     * 描述：用户id , 长度限制：
     */
     public String getUid() {
        return uid;
     }

     public MsgData setUid(String uid) {
        this.uid = uid;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String channelMainType;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getChannelMainType() {
        return channelMainType;
     }

     public MsgData setChannelMainType(String channelMainType) {
        this.channelMainType = channelMainType;
        return this;


     }

    /**
     * 描述：登陆时间 , 长度限制：
     */

     public String loginTime;

    /**
     * 描述：登陆时间 , 长度限制：
     */
     public String getLoginTime() {
        return loginTime;
     }

     public MsgData setLoginTime(String loginTime) {
        this.loginTime = loginTime;
        return this;


     }

    /**
     * 描述：是否绑定发展人0-未绑定1-已绑定 , 长度限制：
     */

     public String devBound;

    /**
     * 描述：是否绑定发展人0-未绑定1-已绑定 , 长度限制：
     */
     public String getDevBound() {
        return devBound;
     }

     public MsgData setDevBound(String devBound) {
        this.devBound = devBound;
        return this;


     }

    /**
     * 描述：验证码 , 长度限制：
     */

     public String captcha;

    /**
     * 描述：验证码 , 长度限制：
     */
     public String getCaptcha() {
        return captcha;
     }

     public MsgData setCaptcha(String captcha) {
        this.captcha = captcha;
        return this;


     }

    /**
     * 描述：部门id , 长度限制：
     */

     public String departId;

    /**
     * 描述：部门id , 长度限制：
     */
     public String getDepartId() {
        return departId;
     }

     public MsgData setDepartId(String departId) {
        this.departId = departId;
        return this;


     }

    /**
     * 描述：地市名称 , 长度限制：
     */

     public String eparchyName;

    /**
     * 描述：地市名称 , 长度限制：
     */
     public String getEparchyName() {
        return eparchyName;
     }

     public MsgData setEparchyName(String eparchyName) {
        this.eparchyName = eparchyName;
        return this;


     }

    /**
     * 描述：渠道id , 长度限制：
     */

     public String channelId;

    /**
     * 描述：渠道id , 长度限制：
     */
     public String getChannelId() {
        return channelId;
     }

     public MsgData setChannelId(String channelId) {
        this.channelId = channelId;
        return this;


     }

    /**
     * 描述：渠道名称 , 长度限制：
     */

     public String departName;

    /**
     * 描述：渠道名称 , 长度限制：
     */
     public String getDepartName() {
        return departName;
     }

     public MsgData setDepartName(String departName) {
        this.departName = departName;
        return this;


     }

    /**
     * 描述：发展人id , 长度限制：
     */

     public String devId;

    /**
     * 描述：发展人id , 长度限制：
     */
     public String getDevId() {
        return devId;
     }

     public MsgData setDevId(String devId) {
        this.devId = devId;
        return this;


     }

    /**
     * 描述：设备操作系统 i:iOS a:安卓 , 长度限制：
     */

     public String deviceType;

    /**
     * 描述：设备操作系统 i:iOS a:安卓 , 长度限制：
     */
     public String getDeviceType() {
        return deviceType;
     }

     public MsgData setDeviceType(String deviceType) {
        this.deviceType = deviceType;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String boundDevBeforeLogin;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getBoundDevBeforeLogin() {
        return boundDevBeforeLogin;
     }

     public MsgData setBoundDevBeforeLogin(String boundDevBeforeLogin) {
        this.boundDevBeforeLogin = boundDevBeforeLogin;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String userSource;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getUserSource() {
        return userSource;
     }

     public MsgData setUserSource(String userSource) {
        this.userSource = userSource;
        return this;


     }

    /**
     * 描述：省份编码 , 长度限制：
     */

     public String provinceCode;

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String getProvinceCode() {
        return provinceCode;
     }

     public MsgData setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;


     }

    /**
     * 描述：权限 , 长度限制：
     */

     public List<FunRightInfo> funRightInfo;

    /**
     * 描述：权限 , 长度限制：
     */
     public List<FunRightInfo> getFunRightInfo() {
        return funRightInfo;
     }

     public MsgData setFunRightInfo(List<FunRightInfo> funRightInfo) {
        this.funRightInfo = funRightInfo;
        return this;


     }

    /**
     * 描述：net取号开关，1:需要net取号， 0:不需要net取号 , 长度限制：
     */

     public String netIdSwitch;

    /**
     * 描述：net取号开关，1:需要net取号， 0:不需要net取号 , 长度限制：
     */
     public String getNetIdSwitch() {
        return netIdSwitch;
     }

     public MsgData setNetIdSwitch(String netIdSwitch) {
        this.netIdSwitch = netIdSwitch;
        return this;


     }

    /**
     * 描述：发展人姓名 , 长度限制：
     */

     public String devName;

    /**
     * 描述：发展人姓名 , 长度限制：
     */
     public String getDevName() {
        return devName;
     }

     public MsgData setDevName(String devName) {
        this.devName = devName;
        return this;


     }

    /**
     * 描述：收入归集id , 长度限制：
     */

     public String userId;

    /**
     * 描述：收入归集id , 长度限制：
     */
     public String getUserId() {
        return userId;
     }

     public MsgData setUserId(String userId) {
        this.userId = userId;
        return this;


     }

    /**
     * 描述：版本号 , 长度限制：
     */

     public String version;

    /**
     * 描述：版本号 , 长度限制：
     */
     public String getVersion() {
        return version;
     }

     public MsgData setVersion(String version) {
        this.version = version;
        return this;


     }

    /**
     * 描述：登陆令牌 , 长度限制：
     */

     public String token;

    /**
     * 描述：登陆令牌 , 长度限制：
     */
     public String getToken() {
        return token;
     }

     public MsgData setToken(String token) {
        this.token = token;
        return this;


     }

    /**
     * 描述：net取号失败后是否忽略处理开关，1:忽略失败, 0：不忽略失败 , 长度限制：
     */

     public String netIdIgnoreFailSwitch;

    /**
     * 描述：net取号失败后是否忽略处理开关，1:忽略失败, 0：不忽略失败 , 长度限制：
     */
     public String getNetIdIgnoreFailSwitch() {
        return netIdIgnoreFailSwitch;
     }

     public MsgData setNetIdIgnoreFailSwitch(String netIdIgnoreFailSwitch) {
        this.netIdIgnoreFailSwitch = netIdIgnoreFailSwitch;
        return this;


     }

    /**
     * 描述：登录ip , 长度限制：
     */

     public String userIp;

    /**
     * 描述：登录ip , 长度限制：
     */
     public String getUserIp() {
        return userIp;
     }

     public MsgData setUserIp(String userIp) {
        this.userIp = userIp;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String msgoUserAgent;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getMsgoUserAgent() {
        return msgoUserAgent;
     }

     public MsgData setMsgoUserAgent(String msgoUserAgent) {
        this.msgoUserAgent = msgoUserAgent;
        return this;


     }

    /**
     * 描述：部门编码 , 长度限制：
     */

     public String departCode;

    /**
     * 描述：部门编码 , 长度限制：
     */
     public String getDepartCode() {
        return departCode;
     }

     public MsgData setDepartCode(String departCode) {
        this.departCode = departCode;
        return this;


     }

    /**
     * 描述：渠道名称 , 长度限制：
     */

     public String channelName;

    /**
     * 描述：渠道名称 , 长度限制：
     */
     public String getChannelName() {
        return channelName;
     }

     public MsgData setChannelName(String channelName) {
        this.channelName = channelName;
        return this;


     }

    /**
     * 描述： , 长度限制：
     */

     public String deviceModel;

    /**
     * 描述： , 长度限制：
     */
     public String getDeviceModel() {
        return deviceModel;
     }

     public MsgData setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
        return this;


     }

    /**
     * 描述：码上购专用节点 , 长度限制：
     */

     public String userType;

    /**
     * 描述：码上购专用节点 , 长度限制：
     */
     public String getUserType() {
        return userType;
     }

     public MsgData setUserType(String userType) {
        this.userType = userType;
        return this;


     }

    /**
     * 描述：省份名称 , 长度限制：
     */

     public String provinceName;

    /**
     * 描述：省份名称 , 长度限制：
     */
     public String getProvinceName() {
        return provinceName;
     }

     public MsgData setProvinceName(String provinceName) {
        this.provinceName = provinceName;
        return this;


     }

    /**
     * 描述：地市名称 , 长度限制：
     */

     public String eparchyCode;

    /**
     * 描述：地市名称 , 长度限制：
     */
     public String getEparchyCode() {
        return eparchyCode;
     }

     public MsgData setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
        return this;


     }
}
public static class FunRightInfo {

    /**
     * 描述：菜单链接 , 长度限制：
     */

     public String menuUrl;

    /**
     * 描述：菜单链接 , 长度限制：
     */
     public String getMenuUrl() {
        return menuUrl;
     }

     public FunRightInfo setMenuUrl(String menuUrl) {
        this.menuUrl = menuUrl;
        return this;


     }

    /**
     * 描述：排序 , 长度限制：
     */

     public String rankNum;

    /**
     * 描述：排序 , 长度限制：
     */
     public String getRankNum() {
        return rankNum;
     }

     public FunRightInfo setRankNum(String rankNum) {
        this.rankNum = rankNum;
        return this;


     }

    /**
     * 描述：是否可编辑 0:不可编辑 1:可以编辑 , 长度限制：
     */

     public String candEdit;

    /**
     * 描述：是否可编辑 0:不可编辑 1:可以编辑 , 长度限制：
     */
     public String getCandEdit() {
        return candEdit;
     }

     public FunRightInfo setCandEdit(String candEdit) {
        this.candEdit = candEdit;
        return this;


     }

    /**
     * 描述：菜单名字 , 长度限制：
     */

     public String menuName;

    /**
     * 描述：菜单名字 , 长度限制：
     */
     public String getMenuName() {
        return menuName;
     }

     public FunRightInfo setMenuName(String menuName) {
        this.menuName = menuName;
        return this;


     }

    /**
     * 描述：图标地址 , 长度限制：
     */

     public String iconUrl;

    /**
     * 描述：图标地址 , 长度限制：
     */
     public String getIconUrl() {
        return iconUrl;
     }

     public FunRightInfo setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
        return this;


     }

    /**
     * 描述：菜单id , 长度限制：
     */

     public String dataCode;

    /**
     * 描述：菜单id , 长度限制：
     */
     public String getDataCode() {
        return dataCode;
     }

     public FunRightInfo setDataCode(String dataCode) {
        this.dataCode = dataCode;
        return this;


     }
}
public static class DevelopManInfo {

    /**
     * 描述：渠道编码 , 长度限制：
     */

     public String dapartId;

    /**
     * 描述：渠道编码 , 长度限制：
     */
     public String getDapartId() {
        return dapartId;
     }

     public DevelopManInfo setDapartId(String dapartId) {
        this.dapartId = dapartId;
        return this;


     }

    /**
     * 描述：员工姓名 , 长度限制：
     */

     public String staffName;

    /**
     * 描述：员工姓名 , 长度限制：
     */
     public String getStaffName() {
        return staffName;
     }

     public DevelopManInfo setStaffName(String staffName) {
        this.staffName = staffName;
        return this;


     }

    /**
     * 描述：员工id , 长度限制：
     */

     public String staffId;

    /**
     * 描述：员工id , 长度限制：
     */
     public String getStaffId() {
        return staffId;
     }

     public DevelopManInfo setStaffId(String staffId) {
        this.staffId = staffId;
        return this;


     }
}
public static class CheckLoginResult {

    /**
     * 描述：实名照片OSS存储路径 , 长度限制：
     */

     public String realNamePicUrl;

    /**
     * 描述：实名照片OSS存储路径 , 长度限制：
     */
     public String getRealNamePicUrl() {
        return realNamePicUrl;
     }

     public CheckLoginResult setRealNamePicUrl(String realNamePicUrl) {
        this.realNamePicUrl = realNamePicUrl;
        return this;


     }

    /**
     * 描述：是否是名 , 长度限制：
     */

     public String realName;

    /**
     * 描述：是否是名 , 长度限制：
     */
     public String getRealName() {
        return realName;
     }

     public CheckLoginResult setRealName(String realName) {
        this.realName = realName;
        return this;


     }

    /**
     * 描述：登陆账号 , 长度限制：
     */

     public String phoneNumber;

    /**
     * 描述：登陆账号 , 长度限制：
     */
     public String getPhoneNumber() {
        return phoneNumber;
     }

     public CheckLoginResult setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;


     }

    /**
     * 描述：身份证号码 , 长度限制：
     */

     public String psptId;

    /**
     * 描述：身份证号码 , 长度限制：
     */
     public String getPsptId() {
        return psptId;
     }

     public CheckLoginResult setPsptId(String psptId) {
        this.psptId = psptId;
        return this;


     }

    /**
     * 描述：省份编码 , 长度限制：
     */

     public String provinceCode;

    /**
     * 描述：省份编码 , 长度限制：
     */
     public String getProvinceCode() {
        return provinceCode;
     }

     public CheckLoginResult setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;


     }

    /**
     * 描述：登陆账号名称 , 长度限制：
     */

     public String name;

    /**
     * 描述：登陆账号名称 , 长度限制：
     */
     public String getName() {
        return name;
     }

     public CheckLoginResult setName(String name) {
        this.name = name;
        return this;


     }

    /**
     * 描述：地市编码 , 长度限制：
     */

     public String eparchyCode;

    /**
     * 描述：地市编码 , 长度限制：
     */
     public String getEparchyCode() {
        return eparchyCode;
     }

     public CheckLoginResult setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
        return this;


     }
}
public static class AgentInfo {
}
public static class LoginRelationData {

    /**
     * 描述：实名标记1-已实命；0-未实名 , 长度限制：
     */

     public String realName;

    /**
     * 描述：实名标记1-已实命；0-未实名 , 长度限制：
     */
     public String getRealName() {
        return realName;
     }

     public LoginRelationData setRealName(String realName) {
        this.realName = realName;
        return this;


     }

    /**
     * 描述：登录手机号码 , 长度限制：
     */

     public String phoneNumber;

    /**
     * 描述：登录手机号码 , 长度限制：
     */
     public String getPhoneNumber() {
        return phoneNumber;
     }

     public LoginRelationData setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;


     }

    /**
     * 描述：绑定发展人编码 , 长度限制：
     */

     public String developStaffId;

    /**
     * 描述：绑定发展人编码 , 长度限制：
     */
     public String getDevelopStaffId() {
        return developStaffId;
     }

     public LoginRelationData setDevelopStaffId(String developStaffId) {
        this.developStaffId = developStaffId;
        return this;


     }

    /**
     * 描述：登陆账号名称 , 长度限制：
     */

     public String name;

    /**
     * 描述：登陆账号名称 , 长度限制：
     */
     public String getName() {
        return name;
     }

     public LoginRelationData setName(String name) {
        this.name = name;
        return this;


     }

    /**
     * 描述：审核状态:0-无需审核、1-审核中、2-审核完成、3-审核失败、4-注销 , 长度限制：
     */

     public String verifyState;

    /**
     * 描述：审核状态:0-无需审核、1-审核中、2-审核完成、3-审核失败、4-注销 , 长度限制：
     */
     public String getVerifyState() {
        return verifyState;
     }

     public LoginRelationData setVerifyState(String verifyState) {
        this.verifyState = verifyState;
        return this;


     }
  }
}
