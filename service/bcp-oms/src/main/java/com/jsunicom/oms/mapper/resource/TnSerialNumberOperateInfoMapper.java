package com.jsunicom.oms.mapper.resource;

import com.jsunicom.oms.po.resource.TnlSerialStateOperateInfo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface TnSerialNumberOperateInfoMapper {

    /**
     * <p>添加号码操作轨迹</p>
     * @param tnlSerialStateOperateInfo 记录
     * @return int 更新行数
     * */
    int addOperateInfo(TnlSerialStateOperateInfo tnlSerialStateOperateInfo);

    /**
     * <p>根据号码查询号码编辑记录</p>
     * @param map 号码,操作开始时间，操作结束时间，操作人
     * @return List 编辑记录列表
     * */
    List<TnlSerialStateOperateInfo> queryOperateInfoList(Map<String, Object> map);

}
