package com.jsunicom.oms.common.dto;


import com.jsunicom.oms.model.base.BasicModel;

/**
 * Created by xuyj108 on 2021/8/12.
 */
public class WoAppletSchoolInfo extends BasicModel {

    private Long id;

    private String orgCode;

    private String appletSchoolName;

    private String appletSchoolLogo;

    private String develId;

    private String quickEntrance;

    private String appletCreateTime;

    private String createBy;

    private String updateTime;

    private String updateBy;

    private Integer pageNum;

    private Integer pageSize;

    private Integer partnerId;

    private Integer schoolId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getAppletSchoolName() {
        return appletSchoolName;
    }

    public void setAppletSchoolName(String appletSchoolName) {
        this.appletSchoolName = appletSchoolName;
    }

    public String getDevelId() {
        return develId;
    }

    public void setDevelId(String develId) {
        this.develId = develId;
    }

    public String getAppletCreateTime() {
        return appletCreateTime;
    }

    public void setAppletCreateTime(String appletCreateTime) {
        this.appletCreateTime = appletCreateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getAppletSchoolLogo() {
        return appletSchoolLogo;
    }

    public void setAppletSchoolLogo(String appletSchoolLogo) {
        this.appletSchoolLogo = appletSchoolLogo;
    }

    public String getQuickEntrance() {
        return quickEntrance;
    }

    public void setQuickEntrance(String quickEntrance) {
        this.quickEntrance = quickEntrance;
    }

    public Integer getSchoolId() {
        return schoolId;
    }

    public void setSchoolId(Integer schoolId) {
        this.schoolId = schoolId;
    }
}
