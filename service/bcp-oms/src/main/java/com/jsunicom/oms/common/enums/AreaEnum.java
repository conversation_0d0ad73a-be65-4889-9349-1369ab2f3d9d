package com.jsunicom.oms.common.enums;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2021/9/11 17:20
 * @Since: V1.00
 */
public enum AreaEnum {
        NANJING("340", "0025"),
        SUZHOU("450", "0512"),
        YANCHENG("348", "0515"),
        LIANYUNGANG("346", "0518"),
        XUZHOU("350", "0516"),
        WU<PERSON><PERSON>("330", "0510"),
        ZHENJIANG("343", "0511"),
        NANTONG("358", "0513"),
        YANGZHOU("430", "0514"),
        HUAIAN("354", "0517"),
        CHANGZHOU("440", "0519"),
        TAIZHOU("445", "0523"),
        SUQIAN("349", "0527");

        private String cityName;
        private String cityValue;

        private AreaEnum(String cityName, String cityValue) {
            this.cityName = cityName;
            this.cityValue = cityValue;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public String getCityValue() {
            return cityValue;
        }

        public void setCityValue(String cityValue) {
            this.cityValue = cityValue;
        }

        public static String getCityName(String cityValue) {
            AreaEnum[] areaEnums = AreaEnum.values();
            for (int index = 0; index < areaEnums.length; index++) {
                AreaEnum areaEnum = areaEnums[index];
                if (areaEnum.getCityValue().equals(cityValue)){
                    return areaEnum.getCityName();
                }
            }
            return null;
        }


}
