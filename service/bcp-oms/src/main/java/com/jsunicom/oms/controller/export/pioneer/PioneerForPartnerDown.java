package com.jsunicom.oms.controller.export.pioneer;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PioneerForPartnerDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 1,value = "地市名称")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 2,value = "校区ID")
    @JSONField(name = "campusId")
    private String campusId;
    @ExcelProperty(index = 3,value = "校区名称")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 4,value = "宿舍名称")
    @JSONField(name = "dormitoryName")
    private String dormitoryName;
    @ExcelProperty(index = 5,value = "是否覆盖宽带先锋")
    @JSONField(name = "isPioneerVal")
    private String isPioneerVal;
    @ExcelProperty(index = 6,value = "青创社成员数量")
    @JSONField(name = "memberNum")
    private String memberNum;
    @ExcelProperty(index = 7,value = "宽带先锋数量")
    @JSONField(name = "pioneerNum")
    private String pioneerNum;
    @ExcelProperty(index = 8,value = "宽带先锋覆盖率")
    @JSONField(name = "coverageRate")
    private String coverageRate;
}
