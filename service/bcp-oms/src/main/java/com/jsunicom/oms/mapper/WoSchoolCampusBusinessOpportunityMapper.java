package com.jsunicom.oms.mapper;

import com.jsunicom.oms.po.WoSchoolCampusBusinessOpportunity;
import com.jsunicom.oms.po.WoSchoolCampusBusinessOpportunityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WoSchoolCampusBusinessOpportunityMapper {
    long countByExample(WoSchoolCampusBusinessOpportunityExample example);

    int deleteByExample(WoSchoolCampusBusinessOpportunityExample example);

    int deleteByPrimaryKey(@Param("campusId") Long campusId, @Param("projectName") String projectName);

    int insert(WoSchoolCampusBusinessOpportunity record);

    int insertSelective(WoSchoolCampusBusinessOpportunity record);

    List<WoSchoolCampusBusinessOpportunity> selectByExample(WoSchoolCampusBusinessOpportunityExample example);

    WoSchoolCampusBusinessOpportunity selectByPrimaryKey(@Param("campusId") Long campusId, @Param("projectName") String projectName);

    int updateByExampleSelective(@Param("record") WoSchoolCampusBusinessOpportunity record, @Param("example") WoSchoolCampusBusinessOpportunityExample example);

    int updateByExample(@Param("record") WoSchoolCampusBusinessOpportunity record, @Param("example") WoSchoolCampusBusinessOpportunityExample example);

    int updateByPrimaryKeySelective(WoSchoolCampusBusinessOpportunity record);

    int updateByPrimaryKey(WoSchoolCampusBusinessOpportunity record);
}