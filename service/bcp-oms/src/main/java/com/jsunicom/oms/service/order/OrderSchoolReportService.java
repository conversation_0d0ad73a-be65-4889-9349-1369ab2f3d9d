package com.jsunicom.oms.service.order;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.oms.common.result.CustomResult;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

public interface OrderSchoolReportService {
    PageInfo<Map<String, Object>> qryOrdSchoolTotal(JSONObject param, HttpServletRequest request) throws Exception;

    PageInfo<Map<String, Object>> qryOrdSchoolTotalHistory(JSONObject param, HttpServletRequest request) throws Exception;

    //定时任务，定期更新校园历史报表数据
    int SynSchoolReportHistory();

    int importSendStatusExcelFile(String filename, MultipartFile file) throws Exception;

    void export(JSONObject param, SXSSFWorkbook swb, OutputStream os,HttpServletRequest request) throws Exception;

    String qryUpdateTime();

    CustomResult getOrderNumByCondition(Map<String, Object> param);

    boolean queryExportAuth(String staffNo);

    List<Map<String, Object>> exportEasyExcel(JSONObject param, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 查询工号是否有校区绑定关系
     * @param staffNo 当前登录工号
     * @return 有校区绑定关系返回true，没有返回false
     * */
    boolean queryCampusBindRelation(String staffNo);

}
