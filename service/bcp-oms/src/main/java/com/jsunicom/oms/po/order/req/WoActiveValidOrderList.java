package com.jsunicom.oms.po.order.req;

import lombok.Data;

import java.io.Serializable;
@Data
public class WoActiveValidOrderList implements Serializable {
    private static final long serialVersionUID = -2919688958096281893L;

    private String orderCenterId;
    private String goodsOrderId;
    private String goodsState;
    private String productOrderId;
    private String custName;
    private String psptId;
    private String serialNumber;
    private String packageBrand;
    private String activeState;
    private String activeDate;
    private String activeTime;
    private String lastActiveTime;
    private String eparchyCode;
    private String areaCode;

    private String psptAddress;//证件地址
    private String psptValidity;//证件有效期
    private String linkPhone;//联系电话
    private String linkPerson;//联系人
    private String isRongHe;//是否融合
    private String staffId;//工号
    private String channelId;//工号
}
