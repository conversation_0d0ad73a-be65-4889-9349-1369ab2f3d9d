package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.po.Goods;
import com.jsunicom.oms.service.DataMonitorService;
import com.jsunicom.oms.service.GoodsService;
import com.jsunicom.oms.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-17-17:10
 */
@RestController
@RequestMapping(value = "/goods", name = "运营端-数据字典")
@Slf4j
public class GoodsController extends AbstractSimpleController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private DataMonitorService dataMonitorService;
    @Autowired
    private RedisUtil redisUtil;
    //商品信息redis缓存key
    public static final String GROUP_PURCHASE_GOODS = "bcp.group_purchase_goods";

    // 分页查询商品信息
    @RequestMapping(value = "/findByPage", method = RequestMethod.GET, name = "查询商品信息")
    public CustomResult findByPage(Goods info, HttpServletRequest request, Integer pageNo, Integer pageSize) {
        log.info("进入分页查询商品信息方法 GoodsController.findByPage");
        if (pageNo == null || pageNo < 1 || pageSize == null || pageSize < 1) {
            throw new RuntimeException("分页参数格式错误！");
        }
            if(StringUtils.isNotEmpty(info.getName())){
                info.setName("%"+info.getName()+"%");
            }
            String goodsArea=request.getHeader("areaCode");
            if (StringUtils.isNotEmpty(info.getGoodsArea())) {
                goodsArea = info.getGoodsArea();
            }
            if ("root".equals(goodsArea)) {
                goodsArea = "";
            }
            info.setGoodsArea(goodsArea);
            //TODO 待确定
            info.setBusLine("20");
            PageInfo<Goods> pageResult = goodsService.findByPage(info, pageNo, pageSize);
            log.info("方法状态"+info.getState());
            log.info("商品类型"+info.getGoodsType());
            log.info("商品地市"+info.getGoodsArea());
            HashMap res=new HashMap();
            res.put("rsList",pageResult.getList());
            res.put("totalCount",pageResult.getTotal());
            log.info("分页查询商品信息方法 GoodsController.findByPage成功);");
            return ResultUtil.success(res);
    }

    @RequestMapping(value = "/mod", method = RequestMethod.POST, name = "编辑商品")
    public CustomResult mod(@RequestBody Goods info,HttpServletRequest request) {
        log.info("进入编辑商品方法 GoodsController.mod");
            JSONObject result = new JSONObject();
            Goods oldGoods = goodsService.findByCode(info.getCode());
            if(null != oldGoods && oldGoods.getId().longValue() != info.getId().longValue()) {
                log.info("调用编辑商品方法 GoodsController.mod 失败：商品编码已存在, 请检查！");
                return ResultUtil.error("9999","商品编码已存在, 请检查！");
            }
            info.setUpdateBy(request.getHeader("staffNo"));
            info.setUpdateTime(new Date());
            if(StringUtils.isEmpty(info.getBusLine())){
                info.setBusLine("20");
            }
            goodsService.update(info);
            redisUtil.del(GROUP_PURCHASE_GOODS);
            dataMonitorService.save("goods","update",info.getId().toString(),request.getHeader("staffNo"),info,oldGoods);
            result.put("ret", "0");
            result.put("errMsg", "");
            result.put("errCode", "");
            log.info("调用编辑商品方法 GoodsController.mod 成功");
            return ResultUtil.success(result);
    }
    // 查询商品是否上架
    @RequestMapping(value = "/countPutAwayGoods", method = RequestMethod.GET, name = "查询")
    public CustomResult countPutAwayGoods(Goods info, HttpServletRequest request) {
        log.info("进入查询商品是否上架方法 GoodsController.countPutAwayGoods");
            Boolean res=goodsService.countPutAwayGoods(info);
            log.info("调用查询商品是否上架 GoodsController.countPutAwayGoods 成功");
            return ResultUtil.success(res);
    }
    /**
     * 预约下架商品
     * @param preGoodsInfo
     * @param preEndTime 预约下架时间
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/preDeleteGoods.json")
    public JSONObject preDeleteGoods(Goods preGoodsInfo,Timestamp preEndTime,HttpServletRequest request) throws Exception {
        log.info("{},预约下架商品--开始---入参：{}", request.getHeader("staffNo"),preGoodsInfo);
        //操作前商品记录信息
        Goods oldGoodsInfo=new Goods();
        BeanUtils.copyProperties(preGoodsInfo,oldGoodsInfo);
        JSONObject result = new JSONObject();

        preGoodsInfo.setEndTime(preEndTime);
        preGoodsInfo.setUpdateBy(request.getHeader("staffNo"));
        preGoodsInfo.setUpdateTime(new Date());
        preGoodsInfo.setPreState("1");

        if(!checkPreGoodsOperaterRight(preGoodsInfo,request.getHeader("staffNo"),request.getHeader("areaCode"))) {
            result.put("ret", "1");
            result.put("errMsg", "没有权限预约下架该商品！");
            log.info("{}预约下架商品--结束--无操作权限---入参：{}", request.getHeader("staffNo"), preGoodsInfo);
            return result;
        }

        int res=goodsService.batchPreDeleteGoods(preGoodsInfo);
        if(res>0){
            //TODO 待确定
//            promotActvFacade.batchUpdatePromotEndTimeByGoodsId(preGoodsInfo);
            dataMonitorService.save("goods", "preDelete", preGoodsInfo.getId().toString(),request.getHeader("staffNo"), preGoodsInfo.toString(),oldGoodsInfo.toString());
        }else{
            result.put("ret", "1");
            result.put("errMsg", "没有符合预约下架条件的商品！");
            log.info("{}预约下架商品--结束--预约下架商品失败---入参：{}", request.getHeader("staffNo"), preGoodsInfo);
            return result;
        }
        result.put("ret", "0");
        log.info("{}批量预约下架商品--结束---入参：{}", request.getHeader("staffNo"), preGoodsInfo);
        return result;
    }
    //校验执行权限
    private boolean checkPreGoodsOperaterRight(Goods goods,String staffNo,String userOrgCode) {
        if(null == goods) {
            return false;
        }
        if(StringUtils.isEmpty(staffNo)) {
            return false;
        }
        if(null != userOrgCode && !"root".equals(userOrgCode) && !"".equals(userOrgCode)) {
            if(!goods.getGoodsArea().equals(userOrgCode)) {
                return false;
            }
        }
        return true;
    }
    //批量预约下架
    @RequestMapping(value = "/batchPreDeleteGoods.json", method = RequestMethod.POST)
    public CustomResult batchPreDeleteGoods( @RequestBody JSONObject preGoodsInfo,HttpServletRequest request) throws Exception {
        log.info("{},批量预约下架商品--开始---入参：{}", request.getHeader("staffNo"),preGoodsInfo);

            JSONObject result = new JSONObject();
//        JSONObject goodsInfoJson=JSONObject.parseObject(preGoodsInfo);
            String goodsInfoStr=preGoodsInfo.getString("preGoodsInfo");
            Timestamp endTime= Timestamp.valueOf(preGoodsInfo.getString("endTime"));
            JSONArray jsonArray = (JSONArray)JSONArray.parse(goodsInfoStr);
            JSONObject jsonObject;
            List<Goods> goodsList = new ArrayList<>();

            for(int i=0;i<jsonArray.size();i++){
                jsonObject=jsonArray.getJSONObject(i);
                Goods goodsInfo=JSONObject.toJavaObject(jsonObject,Goods.class);
                goodsInfo.setEndTime(endTime);
                goodsInfo.setUpdateBy(request.getHeader("staffNo"));
                goodsInfo.setUpdateTime(new Date());
                goodsList.add(goodsInfo);
            }
            if(!checkPreGoodsOperaterRight(goodsList, request.getHeader("staffNo"), request.getHeader("areaCode"))) {
                log.info("{}批量预约下架商品--结束--无操作权限---入参：{}", request.getHeader("staffNo"), goodsList);
                return ResultUtil.error("9999","所选批量预约下架商品中包含无权限的地市商品！");
            }
            for(Goods goods1:goodsList){
                int res=goodsService.batchPreDeleteGoods(goods1);
                if(res>0){
                    //TODO 待确定
//                    promotActvFacade.batchUpdatePromotEndTimeByGoodsId(goods1);
                    dataMonitorService.save("goods", "batchPreDelete", goods1.getId().toString(), request.getHeader("staffNo"), goods1.toString());
                }

            }
            result.put("ret", "0");
            log.info("{}批量预约下架商品--结束---入参：{}", request.getHeader("staffNo"), goodsList);
            return ResultUtil.success(result);
    }
    //校验预约下架商品执行权限
    private boolean checkPreGoodsOperaterRight(List<Goods> goodsList,String staffNo,String userOrgCode) {
        if(CollectionUtils.isEmpty(goodsList)) {
            return false;
        }
        if(StringUtils.isEmpty(staffNo)) {
            return false;
        }
        for(Goods goods:goodsList) {
            if (null != userOrgCode && !"root".equals(userOrgCode) && !"".equals(userOrgCode)) {
                if (!goods.getGoodsArea().equals(userOrgCode)) {
                    return false;
                }
            }
        }
        return true;
    }
    // 删除商品
    @RequestMapping(value = "/del", method = RequestMethod.GET, name = "删除商品")
    public CustomResult del(Goods info, HttpServletRequest request) {
        log.info("进入上下架方法 成功");
            if("Y".equals(info.getState())) {//下架操作
                goodsService.delete(info,request.getHeader("staffNo"));
                //删除商品成功，删除中间表数据
                goodsService.deletePutAwayGoods(info);
                redisUtil.del(GROUP_PURCHASE_GOODS);
                dataMonitorService.save("goods", "delete", info.getId().toString(), request.getHeader("staffNo"), "下架商品:"+info);
                //TODO 待确定
                /*PromotActv promotActv=new PromotActv();
                promotActv.setGoodsId(info.getCode());
                promotActv.setState(PromoteState.OVERDUESTATE);
                promotActv.setUpdateBy(userInfo.getAcctNo());
                //推广活动延期
                promotActvFacade.updateByGoodsId(promotActv);

                List<DataMonitorDescVo> list=promotActvFacade.getPromotActvByGoodsId(info.getCode());
                if(list.size()>0){
                    HashMap map=new HashMap();
                    map.put("monitorModules","promot_operator_config");
                    map.put("monitorType","overdue");
                    map.put("acctNo",request.getHeader("staffNo"));
                    map.put("list",list);
                    dataMonitorService.saveBatchPromotActvMonitor(map);
                }*/
            }else if("N".equals(info.getState())) {//上架操作
                goodsService.updateState(info,request.getHeader("staffNO"));
                //商品上架时清除预约推广活动预约过期状态
                //TODO 待确定
    //            promotActvFacade.updatePreStateByGoodsId(info.getCode());
                redisUtil.del(GROUP_PURCHASE_GOODS);
                dataMonitorService.save("goods", "delete", info.getId().toString(), request.getHeader("staffNo"), "上架商品");
            }
            log.info("调用上下架方法 成功");
            return ResultUtil.success();
    }
    // 新增商品
    @RequestMapping(value = "/add", method = RequestMethod.POST, name = "添加商品")
    @ResponseBody
    public CustomResult add(@RequestBody Goods info,HttpServletRequest request) {
        log.info("进入添加商品方法");
            JSONObject result = new JSONObject();
            Goods dbGoods = goodsService.findByCode(info.getCode());
            if(null != dbGoods) {
                log.info("调用添加商品方法 异常 商品编码已存在, 请检查！");
                return ResultUtil.error("9999","商品编码已存在, 请检查！");
            }
            info.setCreateBy(request.getHeader("staffNo"));
            info.setCreateTime(new Date());
            info.setUpdateBy(request.getHeader("staffNo"));
            info.setUpdateTime(new Date());
            String orgCode = info.getGoodsArea();
            orgCode = orgCode.replaceAll("\"", "");
            orgCode = orgCode.replaceAll("\\[|\\]", "");
            info.setGoodsArea(orgCode);
            if(StringUtils.isEmpty(info.getBusLine())){
                info.setBusLine("20");
            }
            Map<String,Goods> goodsMap = goodsService.insert(info);
            info = goodsMap.get("goods");
            redisUtil.del(GROUP_PURCHASE_GOODS);
            dataMonitorService.save("goods","add",info.getId().toString(),request.getHeader("staffNo"),info);
            result.put("ret", "0");
            result.put("errMsg", "");
            result.put("errCode", "");
            log.info("调用添加商品方法 success");
            return ResultUtil.success(result);
    }
    @RequestMapping(value = "/batchDirectShelves.json", method = RequestMethod.POST)
    public CustomResult batchOperate(@RequestBody String goodsInfo,HttpServletRequest request) throws Exception {
        log.info("{}操作批量下架商品--开始---入参：{}", request.getHeader("staffNo"),goodsInfo);
        JSONObject result = new JSONObject();
        JSONObject goodsInfoJson=JSONObject.parseObject(goodsInfo);
        String goodsInfoStr=goodsInfoJson.getString("goodsInfo");
        JSONArray jsonArray = (JSONArray)JSONArray.parse(goodsInfoStr);
        JSONObject jsonObject;
        List<Goods> goodsList = new ArrayList<>();
        for(int i=0;i<jsonArray.size();i++){
            jsonObject=jsonArray.getJSONObject(i);
            Goods goods=JSONObject.toJavaObject(jsonObject,Goods.class);
            goods.setUpdateBy(request.getHeader("staffNo"));
            goods.setUpdateTime(new Date());
            goodsList.add(goods);
        }

        if(!checkOperaterRight(goodsList,request.getHeader("staffNo"),request.getHeader("areaCode"))) {
            log.info("{}操作批量下架商品--结束--无操作权限---入参：{}", request.getHeader("staffNo"), goodsInfo);
            return ResultUtil.error("9999","所选数据中包含无权限的地市");
        }


            for(Goods goodsTemp:goodsList){
                goodsService.delete(goodsTemp,request.getHeader("staffNo"));
                //删除商品成功，删除中间表数据
                goodsService.deletePutAwayGoods(goodsTemp);
                //TODO 待确定
                /*PromotActv promotActv=new PromotActv();
                promotActv.setGoodsId(goodsTemp.getCode());
                promotActv.setState(PromoteState.OVERDUESTATE);
                promotActv.setUpdateBy(userInfoDto.getAcctNo());
                //推广活动延期
                promotActvFacade.updateByGoodsId(promotActv);
                List<DataMonitorDescVo> list=promotActvFacade.getPromotActvByGoodsId(goodsTemp.getCode());
                if(list.size()>0){
                    HashMap map=new HashMap();
                    map.put("monitorModules","promot_operator_config");
                    map.put("monitorType","overdue");
                    map.put("acctNo",userInfoDto.getAcctNo());
                    map.put("list",list);
                    dataMonitorService.saveBatchPromotActvMonitor(map);
                }*/
            }
            if(goodsList.size()>0){
                HashMap map=new HashMap();
                map.put("monitorModules","goods");
                map.put("monitorType","delete");
                map.put("acctNo",request.getHeader("staffNo"));
                map.put("list",goodsList);
                dataMonitorService.saveBatchGoodsMonitor(map);
            }

            redisUtil.del(GROUP_PURCHASE_GOODS);

        result.put("ret", "0");
        log.info("{}批量下架商品--结束---入参：{}", request.getHeader("staffNo"), goodsInfo);
        return ResultUtil.success(result);
    }
    //校验执行权限
    private boolean checkOperaterRight(List<Goods> goodsList,String staffNo,String userOrgCode) {
        if(CollectionUtils.isEmpty(goodsList)) {
            return false;
        }
        if(StringUtils.isEmpty(staffNo)) {
            return false;
        }
        for(Goods goods:goodsList) {
            if (null != userOrgCode && !"root".equals(userOrgCode) && !"".equals(userOrgCode)) {
                if (!goods.getGoodsArea().equals(userOrgCode)) {
                    return false;
                }
            }
        }
        return true;
    }

}
