package com.jsunicom.oms.entity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 
* @TableName activities_campus_relation
*/
@Data
public class ActivitiesCampusRelation implements Serializable {

    /**
    * id
    */

    private Integer id;
    /**
    * 活动通知id
    */

    private String activitiesNotifyId;
    /**
    * 校园id
    */

    private String campusId;
    /**
    * 校园名称
    */

    private String campusName;
    /**
    * 是否确定
    */

    private Integer hasRead;
    /**
    * 更新时间
    */

    private String updateTime;
    /**
    * 更新人
    */

    private String updateUser;


}
