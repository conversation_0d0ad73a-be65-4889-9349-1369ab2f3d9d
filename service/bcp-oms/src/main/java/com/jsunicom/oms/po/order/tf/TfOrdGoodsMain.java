package com.jsunicom.oms.po.order.tf;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单产商品表
 * @TableName tf_ord_goods_main
 */
@Data
public class TfOrdGoodsMain implements Serializable {
    /**
     *
     */
    private String goodsOrderId;

    /**
     *
     */
    private String orderId;

    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 生成时间
     */
    private Date createDate;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     *
     */
    private String productDesc;

    /**
     * 产品类型
     */
    private String productType;

    private String goodsType;

    /**
     * 主号码
     */
    private String mainNumber;

    /**
     * 更新时间
     */
    private Date updateDate;

    //private String goodsType;

    private static final long serialVersionUID = 1L;
}
