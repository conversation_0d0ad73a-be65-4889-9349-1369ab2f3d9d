package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/12 19:45
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TfWscContractPdf implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    private String orderId;
    private String jParty;
    private String yParty;
    private String jPhone;
    private String contractType;
    private String executeState;
    private String createStaff;
    private Date createDate;
    private String reserve1;
    private String reserve2;
    private String reserve3;

}
