package com.jsunicom.oms.service;

import com.jsunicom.oms.po.WoSchoolCampusMarketingInfo;

import java.util.List;

/**
 * Project:CampusMarketingInfoService
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
public interface CampusMarketingInfoService {

    public List<WoSchoolCampusMarketingInfo> getCollegeList(WoSchoolCampusMarketingInfo record);

    public int saveCollegeExtendInfo(WoSchoolCampusMarketingInfo record);

    public List<WoSchoolCampusMarketingInfo> getCollegeAdd(WoSchoolCampusMarketingInfo record);

    public int updateCollegeInfo(WoSchoolCampusMarketingInfo record);

    public int deleteCollegeInfo(Long collegeId,String marketingType,String marketingYear);
}
