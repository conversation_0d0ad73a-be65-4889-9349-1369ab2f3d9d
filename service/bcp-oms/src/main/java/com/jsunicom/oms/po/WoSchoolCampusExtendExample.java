package com.jsunicom.oms.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WoSchoolCampusExtendExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoSchoolCampusExtendExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCampusIdIsNull() {
            addCriterion("CAMPUS_ID is null");
            return (Criteria) this;
        }

        public Criteria andCampusIdIsNotNull() {
            addCriterion("CAMPUS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCampusIdEqualTo(Long value) {
            addCriterion("CAMPUS_ID =", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotEqualTo(Long value) {
            addCriterion("CAMPUS_ID <>", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThan(Long value) {
            addCriterion("CAMPUS_ID >", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID >=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThan(Long value) {
            addCriterion("CAMPUS_ID <", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID <=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdIn(List<Long> values) {
            addCriterion("CAMPUS_ID in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotIn(List<Long> values) {
            addCriterion("CAMPUS_ID not in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID not between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressIsNull() {
            addCriterion("BI_CAMPUS_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressIsNotNull() {
            addCriterion("BI_CAMPUS_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressEqualTo(String value) {
            addCriterion("BI_CAMPUS_ADDRESS =", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressNotEqualTo(String value) {
            addCriterion("BI_CAMPUS_ADDRESS <>", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressGreaterThan(String value) {
            addCriterion("BI_CAMPUS_ADDRESS >", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressGreaterThanOrEqualTo(String value) {
            addCriterion("BI_CAMPUS_ADDRESS >=", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressLessThan(String value) {
            addCriterion("BI_CAMPUS_ADDRESS <", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressLessThanOrEqualTo(String value) {
            addCriterion("BI_CAMPUS_ADDRESS <=", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressLike(String value) {
            addCriterion("BI_CAMPUS_ADDRESS like", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressNotLike(String value) {
            addCriterion("BI_CAMPUS_ADDRESS not like", value, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressIn(List<String> values) {
            addCriterion("BI_CAMPUS_ADDRESS in", values, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressNotIn(List<String> values) {
            addCriterion("BI_CAMPUS_ADDRESS not in", values, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressBetween(String value1, String value2) {
            addCriterion("BI_CAMPUS_ADDRESS between", value1, value2, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiCampusAddressNotBetween(String value1, String value2) {
            addCriterion("BI_CAMPUS_ADDRESS not between", value1, value2, "biCampusAddress");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureIsNull() {
            addCriterion("BI_EDUCATIONAL_NATURE is null");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureIsNotNull() {
            addCriterion("BI_EDUCATIONAL_NATURE is not null");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE =", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureNotEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE <>", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureGreaterThan(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE >", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureGreaterThanOrEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE >=", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureLessThan(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE <", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureLessThanOrEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE <=", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureLike(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE like", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureNotLike(String value) {
            addCriterion("BI_EDUCATIONAL_NATURE not like", value, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureIn(List<String> values) {
            addCriterion("BI_EDUCATIONAL_NATURE in", values, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureNotIn(List<String> values) {
            addCriterion("BI_EDUCATIONAL_NATURE not in", values, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureBetween(String value1, String value2) {
            addCriterion("BI_EDUCATIONAL_NATURE between", value1, value2, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalNatureNotBetween(String value1, String value2) {
            addCriterion("BI_EDUCATIONAL_NATURE not between", value1, value2, "biEducationalNature");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelIsNull() {
            addCriterion("BI_EDUCATIONAL_LEVEL is null");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelIsNotNull() {
            addCriterion("BI_EDUCATIONAL_LEVEL is not null");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL =", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelNotEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL <>", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelGreaterThan(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL >", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelGreaterThanOrEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL >=", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelLessThan(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL <", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelLessThanOrEqualTo(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL <=", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelLike(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL like", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelNotLike(String value) {
            addCriterion("BI_EDUCATIONAL_LEVEL not like", value, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelIn(List<String> values) {
            addCriterion("BI_EDUCATIONAL_LEVEL in", values, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelNotIn(List<String> values) {
            addCriterion("BI_EDUCATIONAL_LEVEL not in", values, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelBetween(String value1, String value2) {
            addCriterion("BI_EDUCATIONAL_LEVEL between", value1, value2, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andBiEducationalLevelNotBetween(String value1, String value2) {
            addCriterion("BI_EDUCATIONAL_LEVEL not between", value1, value2, "biEducationalLevel");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitIsNull() {
            addCriterion("SUPERVISORY_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitIsNotNull() {
            addCriterion("SUPERVISORY_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitEqualTo(String value) {
            addCriterion("SUPERVISORY_UNIT =", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitNotEqualTo(String value) {
            addCriterion("SUPERVISORY_UNIT <>", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitGreaterThan(String value) {
            addCriterion("SUPERVISORY_UNIT >", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitGreaterThanOrEqualTo(String value) {
            addCriterion("SUPERVISORY_UNIT >=", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitLessThan(String value) {
            addCriterion("SUPERVISORY_UNIT <", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitLessThanOrEqualTo(String value) {
            addCriterion("SUPERVISORY_UNIT <=", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitLike(String value) {
            addCriterion("SUPERVISORY_UNIT like", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitNotLike(String value) {
            addCriterion("SUPERVISORY_UNIT not like", value, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitIn(List<String> values) {
            addCriterion("SUPERVISORY_UNIT in", values, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitNotIn(List<String> values) {
            addCriterion("SUPERVISORY_UNIT not in", values, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitBetween(String value1, String value2) {
            addCriterion("SUPERVISORY_UNIT between", value1, value2, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andSupervisoryUnitNotBetween(String value1, String value2) {
            addCriterion("SUPERVISORY_UNIT not between", value1, value2, "supervisoryUnit");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyIsNull() {
            addCriterion("CR_LEADING_COMPANY is null");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyIsNotNull() {
            addCriterion("CR_LEADING_COMPANY is not null");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyEqualTo(String value) {
            addCriterion("CR_LEADING_COMPANY =", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyNotEqualTo(String value) {
            addCriterion("CR_LEADING_COMPANY <>", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyGreaterThan(String value) {
            addCriterion("CR_LEADING_COMPANY >", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("CR_LEADING_COMPANY >=", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyLessThan(String value) {
            addCriterion("CR_LEADING_COMPANY <", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyLessThanOrEqualTo(String value) {
            addCriterion("CR_LEADING_COMPANY <=", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyLike(String value) {
            addCriterion("CR_LEADING_COMPANY like", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyNotLike(String value) {
            addCriterion("CR_LEADING_COMPANY not like", value, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyIn(List<String> values) {
            addCriterion("CR_LEADING_COMPANY in", values, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyNotIn(List<String> values) {
            addCriterion("CR_LEADING_COMPANY not in", values, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyBetween(String value1, String value2) {
            addCriterion("CR_LEADING_COMPANY between", value1, value2, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingCompanyNotBetween(String value1, String value2) {
            addCriterion("CR_LEADING_COMPANY not between", value1, value2, "crLeadingCompany");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentIsNull() {
            addCriterion("CR_LEADING_DEPARTMENT is null");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentIsNotNull() {
            addCriterion("CR_LEADING_DEPARTMENT is not null");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentEqualTo(String value) {
            addCriterion("CR_LEADING_DEPARTMENT =", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentNotEqualTo(String value) {
            addCriterion("CR_LEADING_DEPARTMENT <>", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentGreaterThan(String value) {
            addCriterion("CR_LEADING_DEPARTMENT >", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("CR_LEADING_DEPARTMENT >=", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentLessThan(String value) {
            addCriterion("CR_LEADING_DEPARTMENT <", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentLessThanOrEqualTo(String value) {
            addCriterion("CR_LEADING_DEPARTMENT <=", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentLike(String value) {
            addCriterion("CR_LEADING_DEPARTMENT like", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentNotLike(String value) {
            addCriterion("CR_LEADING_DEPARTMENT not like", value, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentIn(List<String> values) {
            addCriterion("CR_LEADING_DEPARTMENT in", values, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentNotIn(List<String> values) {
            addCriterion("CR_LEADING_DEPARTMENT not in", values, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentBetween(String value1, String value2) {
            addCriterion("CR_LEADING_DEPARTMENT between", value1, value2, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrLeadingDepartmentNotBetween(String value1, String value2) {
            addCriterion("CR_LEADING_DEPARTMENT not between", value1, value2, "crLeadingDepartment");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeIsNull() {
            addCriterion("CR_COMPETITION_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeIsNotNull() {
            addCriterion("CR_COMPETITION_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeEqualTo(String value) {
            addCriterion("CR_COMPETITION_TYPE =", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeNotEqualTo(String value) {
            addCriterion("CR_COMPETITION_TYPE <>", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeGreaterThan(String value) {
            addCriterion("CR_COMPETITION_TYPE >", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("CR_COMPETITION_TYPE >=", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeLessThan(String value) {
            addCriterion("CR_COMPETITION_TYPE <", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeLessThanOrEqualTo(String value) {
            addCriterion("CR_COMPETITION_TYPE <=", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeLike(String value) {
            addCriterion("CR_COMPETITION_TYPE like", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeNotLike(String value) {
            addCriterion("CR_COMPETITION_TYPE not like", value, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeIn(List<String> values) {
            addCriterion("CR_COMPETITION_TYPE in", values, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeNotIn(List<String> values) {
            addCriterion("CR_COMPETITION_TYPE not in", values, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeBetween(String value1, String value2) {
            addCriterion("CR_COMPETITION_TYPE between", value1, value2, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrCompetitionTypeNotBetween(String value1, String value2) {
            addCriterion("CR_COMPETITION_TYPE not between", value1, value2, "crCompetitionType");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameIsNull() {
            addCriterion("CR_KEY_PEOPLE_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameIsNotNull() {
            addCriterion("CR_KEY_PEOPLE_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameEqualTo(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME =", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameNotEqualTo(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME <>", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameGreaterThan(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME >", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameGreaterThanOrEqualTo(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME >=", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameLessThan(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME <", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameLessThanOrEqualTo(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME <=", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameLike(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME like", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameNotLike(String value) {
            addCriterion("CR_KEY_PEOPLE_NAME not like", value, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameIn(List<String> values) {
            addCriterion("CR_KEY_PEOPLE_NAME in", values, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameNotIn(List<String> values) {
            addCriterion("CR_KEY_PEOPLE_NAME not in", values, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameBetween(String value1, String value2) {
            addCriterion("CR_KEY_PEOPLE_NAME between", value1, value2, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrKeyPeopleNameNotBetween(String value1, String value2) {
            addCriterion("CR_KEY_PEOPLE_NAME not between", value1, value2, "crKeyPeopleName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeIsNull() {
            addCriterion("CR_ASCRIPTION_GRID_CODE is null");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeIsNotNull() {
            addCriterion("CR_ASCRIPTION_GRID_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE =", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeNotEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE <>", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeGreaterThan(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE >", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeGreaterThanOrEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE >=", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeLessThan(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE <", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeLessThanOrEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE <=", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeLike(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE like", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeNotLike(String value) {
            addCriterion("CR_ASCRIPTION_GRID_CODE not like", value, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeIn(List<String> values) {
            addCriterion("CR_ASCRIPTION_GRID_CODE in", values, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeNotIn(List<String> values) {
            addCriterion("CR_ASCRIPTION_GRID_CODE not in", values, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeBetween(String value1, String value2) {
            addCriterion("CR_ASCRIPTION_GRID_CODE between", value1, value2, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridCodeNotBetween(String value1, String value2) {
            addCriterion("CR_ASCRIPTION_GRID_CODE not between", value1, value2, "crAscriptionGridCode");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameIsNull() {
            addCriterion("CR_ASCRIPTION_GRID_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameIsNotNull() {
            addCriterion("CR_ASCRIPTION_GRID_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME =", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameNotEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME <>", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameGreaterThan(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME >", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameGreaterThanOrEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME >=", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameLessThan(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME <", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameLessThanOrEqualTo(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME <=", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameLike(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME like", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameNotLike(String value) {
            addCriterion("CR_ASCRIPTION_GRID_NAME not like", value, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameIn(List<String> values) {
            addCriterion("CR_ASCRIPTION_GRID_NAME in", values, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameNotIn(List<String> values) {
            addCriterion("CR_ASCRIPTION_GRID_NAME not in", values, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameBetween(String value1, String value2) {
            addCriterion("CR_ASCRIPTION_GRID_NAME between", value1, value2, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrAscriptionGridNameNotBetween(String value1, String value2) {
            addCriterion("CR_ASCRIPTION_GRID_NAME not between", value1, value2, "crAscriptionGridName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameIsNull() {
            addCriterion("CR_BAOZHUA_LEADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameIsNotNull() {
            addCriterion("CR_BAOZHUA_LEADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME =", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameNotEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME <>", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameGreaterThan(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME >", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameGreaterThanOrEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME >=", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameLessThan(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME <", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameLessThanOrEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME <=", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameLike(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME like", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameNotLike(String value) {
            addCriterion("CR_BAOZHUA_LEADER_NAME not like", value, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameIn(List<String> values) {
            addCriterion("CR_BAOZHUA_LEADER_NAME in", values, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameNotIn(List<String> values) {
            addCriterion("CR_BAOZHUA_LEADER_NAME not in", values, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameBetween(String value1, String value2) {
            addCriterion("CR_BAOZHUA_LEADER_NAME between", value1, value2, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderNameNotBetween(String value1, String value2) {
            addCriterion("CR_BAOZHUA_LEADER_NAME not between", value1, value2, "crBaozhuaLeaderName");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneIsNull() {
            addCriterion("CR_BAOZHUA_LEADER_PHONE is null");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneIsNotNull() {
            addCriterion("CR_BAOZHUA_LEADER_PHONE is not null");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE =", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneNotEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE <>", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneGreaterThan(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE >", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE >=", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneLessThan(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE <", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneLessThanOrEqualTo(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE <=", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneLike(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE like", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneNotLike(String value) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE not like", value, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneIn(List<String> values) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE in", values, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneNotIn(List<String> values) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE not in", values, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneBetween(String value1, String value2) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE between", value1, value2, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andCrBaozhuaLeaderPhoneNotBetween(String value1, String value2) {
            addCriterion("CR_BAOZHUA_LEADER_PHONE not between", value1, value2, "crBaozhuaLeaderPhone");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberIsNull() {
            addCriterion("TS_TEACHER_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberIsNotNull() {
            addCriterion("TS_TEACHER_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberEqualTo(Integer value) {
            addCriterion("TS_TEACHER_NUMBER =", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberNotEqualTo(Integer value) {
            addCriterion("TS_TEACHER_NUMBER <>", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberGreaterThan(Integer value) {
            addCriterion("TS_TEACHER_NUMBER >", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_TEACHER_NUMBER >=", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberLessThan(Integer value) {
            addCriterion("TS_TEACHER_NUMBER <", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_TEACHER_NUMBER <=", value, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberIn(List<Integer> values) {
            addCriterion("TS_TEACHER_NUMBER in", values, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberNotIn(List<Integer> values) {
            addCriterion("TS_TEACHER_NUMBER not in", values, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_TEACHER_NUMBER between", value1, value2, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsTeacherNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_TEACHER_NUMBER not between", value1, value2, "tsTeacherNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberIsNull() {
            addCriterion("TS_STUDENT_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberIsNotNull() {
            addCriterion("TS_STUDENT_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberEqualTo(Integer value) {
            addCriterion("TS_STUDENT_NUMBER =", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberNotEqualTo(Integer value) {
            addCriterion("TS_STUDENT_NUMBER <>", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberGreaterThan(Integer value) {
            addCriterion("TS_STUDENT_NUMBER >", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_STUDENT_NUMBER >=", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberLessThan(Integer value) {
            addCriterion("TS_STUDENT_NUMBER <", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_STUDENT_NUMBER <=", value, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberIn(List<Integer> values) {
            addCriterion("TS_STUDENT_NUMBER in", values, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberNotIn(List<Integer> values) {
            addCriterion("TS_STUDENT_NUMBER not in", values, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_STUDENT_NUMBER between", value1, value2, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsStudentNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_STUDENT_NUMBER not between", value1, value2, "tsStudentNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberIsNull() {
            addCriterion("TS_FIRST_GRADE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberIsNotNull() {
            addCriterion("TS_FIRST_GRADE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberEqualTo(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER =", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberNotEqualTo(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER <>", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberGreaterThan(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER >", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER >=", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberLessThan(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER <", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_FIRST_GRADE_NUMBER <=", value, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberIn(List<Integer> values) {
            addCriterion("TS_FIRST_GRADE_NUMBER in", values, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberNotIn(List<Integer> values) {
            addCriterion("TS_FIRST_GRADE_NUMBER not in", values, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_FIRST_GRADE_NUMBER between", value1, value2, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFirstGradeNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_FIRST_GRADE_NUMBER not between", value1, value2, "tsFirstGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberIsNull() {
            addCriterion("TS_SECOND_GRADE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberIsNotNull() {
            addCriterion("TS_SECOND_GRADE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberEqualTo(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER =", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberNotEqualTo(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER <>", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberGreaterThan(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER >", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER >=", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberLessThan(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER <", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_SECOND_GRADE_NUMBER <=", value, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberIn(List<Integer> values) {
            addCriterion("TS_SECOND_GRADE_NUMBER in", values, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberNotIn(List<Integer> values) {
            addCriterion("TS_SECOND_GRADE_NUMBER not in", values, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_SECOND_GRADE_NUMBER between", value1, value2, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsSecondGradeNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_SECOND_GRADE_NUMBER not between", value1, value2, "tsSecondGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberIsNull() {
            addCriterion("TS_THIRD_GRADE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberIsNotNull() {
            addCriterion("TS_THIRD_GRADE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberEqualTo(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER =", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberNotEqualTo(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER <>", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberGreaterThan(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER >", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER >=", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberLessThan(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER <", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_THIRD_GRADE_NUMBER <=", value, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberIn(List<Integer> values) {
            addCriterion("TS_THIRD_GRADE_NUMBER in", values, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberNotIn(List<Integer> values) {
            addCriterion("TS_THIRD_GRADE_NUMBER not in", values, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_THIRD_GRADE_NUMBER between", value1, value2, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsThirdGradeNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_THIRD_GRADE_NUMBER not between", value1, value2, "tsThirdGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberIsNull() {
            addCriterion("TS_FOURTH_GRADE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberIsNotNull() {
            addCriterion("TS_FOURTH_GRADE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberEqualTo(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER =", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberNotEqualTo(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER <>", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberGreaterThan(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER >", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER >=", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberLessThan(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER <", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberLessThanOrEqualTo(Integer value) {
            addCriterion("TS_FOURTH_GRADE_NUMBER <=", value, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberIn(List<Integer> values) {
            addCriterion("TS_FOURTH_GRADE_NUMBER in", values, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberNotIn(List<Integer> values) {
            addCriterion("TS_FOURTH_GRADE_NUMBER not in", values, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberBetween(Integer value1, Integer value2) {
            addCriterion("TS_FOURTH_GRADE_NUMBER between", value1, value2, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsFourthGradeNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_FOURTH_GRADE_NUMBER not between", value1, value2, "tsFourthGradeNumber");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentIsNull() {
            addCriterion("TS_NEW_JU_STUDENT is null");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentIsNotNull() {
            addCriterion("TS_NEW_JU_STUDENT is not null");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentEqualTo(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT =", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentNotEqualTo(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT <>", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentGreaterThan(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT >", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT >=", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentLessThan(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT <", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentLessThanOrEqualTo(Integer value) {
            addCriterion("TS_NEW_JU_STUDENT <=", value, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentIn(List<Integer> values) {
            addCriterion("TS_NEW_JU_STUDENT in", values, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentNotIn(List<Integer> values) {
            addCriterion("TS_NEW_JU_STUDENT not in", values, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentBetween(Integer value1, Integer value2) {
            addCriterion("TS_NEW_JU_STUDENT between", value1, value2, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsNewJuStudentNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_NEW_JU_STUDENT not between", value1, value2, "tsNewJuStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentIsNull() {
            addCriterion("TS_MD_STUDENT is null");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentIsNotNull() {
            addCriterion("TS_MD_STUDENT is not null");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentEqualTo(Integer value) {
            addCriterion("TS_MD_STUDENT =", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentNotEqualTo(Integer value) {
            addCriterion("TS_MD_STUDENT <>", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentGreaterThan(Integer value) {
            addCriterion("TS_MD_STUDENT >", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentGreaterThanOrEqualTo(Integer value) {
            addCriterion("TS_MD_STUDENT >=", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentLessThan(Integer value) {
            addCriterion("TS_MD_STUDENT <", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentLessThanOrEqualTo(Integer value) {
            addCriterion("TS_MD_STUDENT <=", value, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentIn(List<Integer> values) {
            addCriterion("TS_MD_STUDENT in", values, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentNotIn(List<Integer> values) {
            addCriterion("TS_MD_STUDENT not in", values, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentBetween(Integer value1, Integer value2) {
            addCriterion("TS_MD_STUDENT between", value1, value2, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andTsMdStudentNotBetween(Integer value1, Integer value2) {
            addCriterion("TS_MD_STUDENT not between", value1, value2, "tsMdStudent");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementIsNull() {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT is null");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementIsNotNull() {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT is not null");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementEqualTo(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT =", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementNotEqualTo(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT <>", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementGreaterThan(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT >", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementGreaterThanOrEqualTo(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT >=", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementLessThan(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT <", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementLessThanOrEqualTo(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT <=", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementLike(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT like", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementNotLike(String value) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT not like", value, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementIn(List<String> values) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT in", values, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementNotIn(List<String> values) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT not in", values, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementBetween(String value1, String value2) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT between", value1, value2, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andIsSignCooperationAgreementNotBetween(String value1, String value2) {
            addCriterion("IS_SIGN_COOPERATION_AGREEMENT not between", value1, value2, "isSignCooperationAgreement");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateIsNull() {
            addCriterion("AGREEMENT_START_DATE is null");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateIsNotNull() {
            addCriterion("AGREEMENT_START_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateEqualTo(Date value) {
            addCriterion("AGREEMENT_START_DATE =", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateNotEqualTo(Date value) {
            addCriterion("AGREEMENT_START_DATE <>", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateGreaterThan(Date value) {
            addCriterion("AGREEMENT_START_DATE >", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_START_DATE >=", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateLessThan(Date value) {
            addCriterion("AGREEMENT_START_DATE <", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateLessThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_START_DATE <=", value, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateIn(List<Date> values) {
            addCriterion("AGREEMENT_START_DATE in", values, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateNotIn(List<Date> values) {
            addCriterion("AGREEMENT_START_DATE not in", values, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateBetween(Date value1, Date value2) {
            addCriterion("AGREEMENT_START_DATE between", value1, value2, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementStartDateNotBetween(Date value1, Date value2) {
            addCriterion("AGREEMENT_START_DATE not between", value1, value2, "agreementStartDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1IsNull() {
            addCriterion("AGREEMENT_END_DATE_1 is null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1IsNotNull() {
            addCriterion("AGREEMENT_END_DATE_1 is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1EqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 =", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1NotEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 <>", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1GreaterThan(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 >", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1GreaterThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 >=", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1LessThan(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 <", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1LessThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE_1 <=", value, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1In(List<Date> values) {
            addCriterion("AGREEMENT_END_DATE_1 in", values, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1NotIn(List<Date> values) {
            addCriterion("AGREEMENT_END_DATE_1 not in", values, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1Between(Date value1, Date value2) {
            addCriterion("AGREEMENT_END_DATE_1 between", value1, value2, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDate1NotBetween(Date value1, Date value2) {
            addCriterion("AGREEMENT_END_DATE_1 not between", value1, value2, "agreementEndDate1");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("CREATE_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("CREATE_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("CREATE_BY =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("CREATE_BY <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("CREATE_BY >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATE_BY >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("CREATE_BY <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("CREATE_BY <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("CREATE_BY like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("CREATE_BY not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("CREATE_BY in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("CREATE_BY not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("CREATE_BY between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("CREATE_BY not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("CREATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("CREATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("CREATE_TIME =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("CREATE_TIME <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("CREATE_TIME >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("CREATE_TIME <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("CREATE_TIME in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("CREATE_TIME not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("UPDATE_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("UPDATE_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("UPDATE_BY =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("UPDATE_BY <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("UPDATE_BY >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATE_BY >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("UPDATE_BY <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("UPDATE_BY <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("UPDATE_BY like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("UPDATE_BY not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("UPDATE_BY in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("UPDATE_BY not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("UPDATE_BY between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("UPDATE_BY not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("UPDATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("UPDATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("UPDATE_TIME =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("UPDATE_TIME <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("UPDATE_TIME >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATE_TIME >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("UPDATE_TIME <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATE_TIME <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("UPDATE_TIME in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("UPDATE_TIME not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATE_TIME between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATE_TIME not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNull() {
            addCriterion("RESERVE1 is null");
            return (Criteria) this;
        }

        public Criteria andReserve1IsNotNull() {
            addCriterion("RESERVE1 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve1EqualTo(String value) {
            addCriterion("RESERVE1 =", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotEqualTo(String value) {
            addCriterion("RESERVE1 <>", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThan(String value) {
            addCriterion("RESERVE1 >", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE1 >=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThan(String value) {
            addCriterion("RESERVE1 <", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1LessThanOrEqualTo(String value) {
            addCriterion("RESERVE1 <=", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Like(String value) {
            addCriterion("RESERVE1 like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotLike(String value) {
            addCriterion("RESERVE1 not like", value, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1In(List<String> values) {
            addCriterion("RESERVE1 in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotIn(List<String> values) {
            addCriterion("RESERVE1 not in", values, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1Between(String value1, String value2) {
            addCriterion("RESERVE1 between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve1NotBetween(String value1, String value2) {
            addCriterion("RESERVE1 not between", value1, value2, "reserve1");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNull() {
            addCriterion("RESERVE2 is null");
            return (Criteria) this;
        }

        public Criteria andReserve2IsNotNull() {
            addCriterion("RESERVE2 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve2EqualTo(String value) {
            addCriterion("RESERVE2 =", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotEqualTo(String value) {
            addCriterion("RESERVE2 <>", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThan(String value) {
            addCriterion("RESERVE2 >", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE2 >=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThan(String value) {
            addCriterion("RESERVE2 <", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2LessThanOrEqualTo(String value) {
            addCriterion("RESERVE2 <=", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Like(String value) {
            addCriterion("RESERVE2 like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotLike(String value) {
            addCriterion("RESERVE2 not like", value, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2In(List<String> values) {
            addCriterion("RESERVE2 in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotIn(List<String> values) {
            addCriterion("RESERVE2 not in", values, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2Between(String value1, String value2) {
            addCriterion("RESERVE2 between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve2NotBetween(String value1, String value2) {
            addCriterion("RESERVE2 not between", value1, value2, "reserve2");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNull() {
            addCriterion("RESERVE3 is null");
            return (Criteria) this;
        }

        public Criteria andReserve3IsNotNull() {
            addCriterion("RESERVE3 is not null");
            return (Criteria) this;
        }

        public Criteria andReserve3EqualTo(String value) {
            addCriterion("RESERVE3 =", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotEqualTo(String value) {
            addCriterion("RESERVE3 <>", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThan(String value) {
            addCriterion("RESERVE3 >", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3GreaterThanOrEqualTo(String value) {
            addCriterion("RESERVE3 >=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThan(String value) {
            addCriterion("RESERVE3 <", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3LessThanOrEqualTo(String value) {
            addCriterion("RESERVE3 <=", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Like(String value) {
            addCriterion("RESERVE3 like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotLike(String value) {
            addCriterion("RESERVE3 not like", value, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3In(List<String> values) {
            addCriterion("RESERVE3 in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotIn(List<String> values) {
            addCriterion("RESERVE3 not in", values, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3Between(String value1, String value2) {
            addCriterion("RESERVE3 between", value1, value2, "reserve3");
            return (Criteria) this;
        }

        public Criteria andReserve3NotBetween(String value1, String value2) {
            addCriterion("RESERVE3 not between", value1, value2, "reserve3");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateIsNull() {
            addCriterion("AGREEMENT_END_DATE is null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateIsNotNull() {
            addCriterion("AGREEMENT_END_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE =", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateNotEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE <>", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateGreaterThan(Date value) {
            addCriterion("AGREEMENT_END_DATE >", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE >=", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateLessThan(Date value) {
            addCriterion("AGREEMENT_END_DATE <", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateLessThanOrEqualTo(Date value) {
            addCriterion("AGREEMENT_END_DATE <=", value, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateIn(List<Date> values) {
            addCriterion("AGREEMENT_END_DATE in", values, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateNotIn(List<Date> values) {
            addCriterion("AGREEMENT_END_DATE not in", values, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateBetween(Date value1, Date value2) {
            addCriterion("AGREEMENT_END_DATE between", value1, value2, "agreementEndDate");
            return (Criteria) this;
        }

        public Criteria andAgreementEndDateNotBetween(Date value1, Date value2) {
            addCriterion("AGREEMENT_END_DATE not between", value1, value2, "agreementEndDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}