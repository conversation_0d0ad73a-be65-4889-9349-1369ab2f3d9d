package com.jsunicom.oms.po.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsunicom.oms.po.order.tf.TfOrdCall;
import com.jsunicom.oms.po.order.tf.TfOrdSvcnumActiveRes;
import com.jsunicom.oms.po.order.tf.TfOrderItem;
import com.jsunicom.oms.po.order.tl.TlOrdOperatorLog;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ToString
public class OrderDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    //订单号
    private String orderId;
    //下单时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;
    //订单金额
    private BigDecimal orderAmount;
    private String orderState;
    private String inModeCode;
    private String extOrderId;

    //地市
    private String cityCode;
    private String cityName;
    //订单环节状态
    private String orderNodeName;
    private String orderNodeCode;
    //业务类型
    private String busiType;
    //商品名称
    private String goodsName;
    private String goodsDesc;
    //下单人工号
    private String staffId;
    //下单人姓名
    private String staffName;
    //下单人联系方式
    private String staffPhone;

    private String channelId;

    List<TlOrdOperatorLog> ordOperatorList;
    //客户姓名
    private String custName;
    //证件号码
    private String psptId;
    //证件地址
    private String psptAddresss;
    //三张照片
    private List<Map<String,String>> photoInfo;
    //业务号码
    private String mainNumber;
    //iccid
    private String iccid;
    private String gztCheckState;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gztCheckTime;
    private String gztCheckRemark;
    private String hmdCheckState;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date hmdCheckTime;
    private String hmdCheckRemark;

    //分配员工ID
    private String dealStaffId;
    //校区编码
    private String schoolId;
    //校区名称
    private String schoolName;
    private String developerId;
    //校区经理编码
    private String schoolManagerPid;
    private String receiveName;
    //配送方式
    private String deliveryType;
    //联系电话
    private String phone;
    //配送地址
    private String address;
    private String PROVINCE_CODE ;
    private String CITY_CODE ;
    private String COUNTY_CODE ;

    private String frontState;
    private String frontRemark;
    private String backState;
    private String backRemark;
    private String similarity;
    private String faceState;
    private String faceRemark;

    // private String payAgent;
    // private String payMethod;
    private String payResult;
    // private String payTransactionId;
    private List<PayInfoVo> payInfoVoList;

    //外呼信息
    List<TfOrdCall> ordCallList;
    List<TfOrderItem> orderItemList;

    List<TfOrderItem> prodItemList;
    //订单备注
    private String remark;

    private String goodsId;
    private String eparchyCode;
    //本异网标识
    private String numStatus;
}
