package com.jsunicom.oms.service;

import com.jsunicom.oms.model.partner.PartnerDevelChannelCode;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service
 * @ClassName: PartnerDevelChannelCodeFacade
 * @Author: z<PERSON><PERSON>
 * @CreateTime: 2023-03-18  20:05
 * @Description: TODO
 * @Version: 1.0
 */
public interface PartnerDevelChannelCodeFacade {
    List<PartnerDevelChannelCode> findMappingPointByName(String channelName, String orgCode);
    PartnerDevelChannelCode findByChannelCode(String channelCode);

}
