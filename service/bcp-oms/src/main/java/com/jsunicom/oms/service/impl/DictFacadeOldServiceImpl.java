package com.jsunicom.oms.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jsunicom.oms.common.RedisKeys;
import com.jsunicom.oms.mapper.base.DictDao;
import com.jsunicom.oms.model.base.Dict;
import com.jsunicom.oms.service.DictFacade;
import com.jsunicom.oms.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: DictFacadeOldServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-18  21:23
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@Slf4j
public class DictFacadeOldServiceImpl implements DictFacade {
    @Resource
    private RedisUtil redisUtils;
    @Resource
    private DictCache dictCache;

    @Autowired
    private DictDao dictDao;

    @Override
    public ArrayList<Dict> getDictsByKind(String kind) {
        return dictCache.getDictByKind(kind);
    }

    @Override
    public String getNameByKey(String kind, String code) {
        String name = "";
        try {
            String dictListKey = RedisKeys.keyForDict(kind);
            log.info("--dictListKey=="+dictListKey+"  code:"+code);
            Object json = redisUtils.hget(dictListKey, code);
            log.info("--json"+JSONObject.toJSONString(json));
            if (json==null) {
                Dict dict = dictDao.findByUK(kind, code);
                if (dict != null) {
                    redisUtils.hset(dictListKey, dict.getCode(), JSON.toJSONString(dict));
                    name = dict.getName();
                }
            } else {
                JSONObject jsonObject = JSONObject.parseObject(json.toString());
                Dict dict = new ObjectMapper().convertValue(jsonObject, Dict.class);
                name = dict.getName();
            }
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
        return name;
    }

    @Override
    public Map<Object, Object> getDictMapByKind(String kind) {
        return dictCache.getDictMapByKind(kind);
    }
    @Override
    public String findNameByKeyNotCache(String kind, String code) {
        Dict dict = dictDao.findByUK(kind, code);
        return dict == null ? StringUtils.EMPTY : StringUtils.defaultString(dict.getName());
    }

    @Override
    public Dict findDictByKeyNotCache(String kind, String code) {
        return dictDao.findByUK(kind, code);
    }

    @Override
    public String getNameByKeyBack(String kind, String code) {
        String name = "";
        try {
            String dictListKey = RedisKeys.keyForDict(kind+"_back");
            log.info("--dictListKey=="+dictListKey+"  code:"+code);
            Object json = redisUtils.hget(dictListKey, code);
            log.info("--json"+JSONObject.toJSONString(json));
            if (json==null) {
                Dict dict = dictDao.findByUK(kind, code);
                if (dict != null) {
                    redisUtils.hset(dictListKey, dict.getCode(), JSON.toJSONString(dict));
                    name = dict.getName();
                }
            } else {
                JSONObject jsonObject = JSONObject.parseObject(json.toString());
                Dict dict = new ObjectMapper().convertValue(jsonObject, Dict.class);
                name = dict.getName();
            }
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
        return name;
    }

}
