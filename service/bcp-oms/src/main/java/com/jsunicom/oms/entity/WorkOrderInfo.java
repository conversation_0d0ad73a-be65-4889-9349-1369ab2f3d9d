package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2022/6/28 15:10
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private String orderType;
    private String busiNumber;
    private String custName;
    private String custPhone;
    private String salesclerkName;
    private String salesclerkPhone;
    private List<Map> pictures;

}
