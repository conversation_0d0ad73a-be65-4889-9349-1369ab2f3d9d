package com.jsunicom.oms.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/8.
 */
public class SmsUtil {

    private static final Pattern p = Pattern.compile("^1([358][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8}$");

    /**
     * 验证手机号码
     *
     * @param mobile
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }

        boolean flag = false;
        try {
            Matcher m = p.matcher(mobile);
            flag = m.matches();
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }

    /**
     * 生成4位随机码
     *
     * @return
     */
    public static String generateVerifyCode() {
        return String.valueOf(new Random().nextInt(899999) + 100000);
    }

}
