package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.service.GoodsService;
import com.jsunicom.oms.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-24-16:46
 */
@RestController
@RequestMapping(value = "/redisController")
@Slf4j
public class TestController extends AbstractSimpleController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private RedisUtil redisUtil;
    //查看redis信息
    @GetMapping("/gethInfo")
    public Map<Object, Object> gethInfo(String key,String keyk){
        Map<Object, Object> hmget = redisUtil.hmget(key);
        return hmget;
    }
    @GetMapping("/getInfo")
    public String getInfo(String key){
        Object o = redisUtil.get(key);
        String s = JSONObject.toJSONString(o);
        return s;
    }
    @GetMapping("/deletekey")
    public String deletekey(String key){
        redisUtil.del(key);
        return "success";
    }
    @GetMapping("/deleteHkey")
    public String deleteHkey(String key,String keyk){
        redisUtil.hdel(key,keyk);
        return "success";
    }
}
