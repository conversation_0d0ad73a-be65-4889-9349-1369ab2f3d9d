package com.jsunicom.oms.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.po.Goods;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-19-22:42
 */
public interface GoodsService {
    PageInfo<Goods> findByPage(Goods info, Integer pageNo, Integer pageSize);

    Goods findByCode(String code);

    void update(Goods info);

    Boolean countPutAwayGoods(Goods info);

    int batchPreDeleteGoods(Goods preGoodsInfo);

    void delete(Goods info,String staffNo);

    void deletePutAwayGoods(Goods info);

    void updateState(Goods info,String staffNo);

    Map<String, Goods> insert(Goods info);
}
