package com.jsunicom.oms.po.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 搬运自老校园orderreceive
 * <AUTHOR>
 * */

@ApiModel(description = "订单详情查询请求报文配送信息")
@Data
public class PostInfoRsp implements Serializable {

	private static final long serialVersionUID = 1L;

//	@ApiModelProperty(value = "物流单号", required = true, position = 1)
//	private String postNo;

//	@ApiModelProperty(value = "配送方式", required = true, position = 2)
//	private String postType;

//	@ApiModelProperty(value = "配送费用", required = true, position = 3)
//	private String postFee;

//	@ApiModelProperty(value = "物流公司", required = true, position = 4)
//	private String postCompany;

//	@ApiModelProperty(value = "发货时间", required = true, position = 5)
//	private String postDate;
//
//	@ApiModelProperty(value = "签收时间", required = true, position = 6)
//	private String signDate;

}
