package com.jsunicom.oms.mapper;

import com.jsunicom.oms.po.WoSchoolCampusExtend;
import com.jsunicom.oms.po.WoSchoolCampusExtendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WoSchoolCampusExtendMapper {
    long countByExample(WoSchoolCampusExtendExample example);

    int deleteByExample(WoSchoolCampusExtendExample example);

    int deleteByPrimaryKey(Long campusId);

    int insert(WoSchoolCampusExtend record);

    int insertSelective(WoSchoolCampusExtend record);

    List<WoSchoolCampusExtend> selectByExample(WoSchoolCampusExtendExample example);

    WoSchoolCampusExtend selectByPrimaryKey(Long campusId);

    int updateByExampleSelective(@Param("record") WoSchoolCampusExtend record, @Param("example") WoSchoolCampusExtendExample example);

    int updateByExample(@Param("record") WoSchoolCampusExtend record, @Param("example") WoSchoolCampusExtendExample example);

    int updateByPrimaryKeySelective(WoSchoolCampusExtend record);

    int updateByPrimaryKey(WoSchoolCampusExtend record);
}