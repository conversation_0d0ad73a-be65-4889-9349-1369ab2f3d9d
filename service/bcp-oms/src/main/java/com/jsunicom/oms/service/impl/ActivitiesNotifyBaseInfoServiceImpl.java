package com.jsunicom.oms.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.enums.HasReadEnum;
import com.jsunicom.oms.dto.campus.*;
import com.jsunicom.oms.entity.ActivitiesCampusRelation;
import com.jsunicom.oms.entity.ActivitiesNotifyBaseInfo;
import com.jsunicom.oms.mapper.campus.ActivitiesCampusRelationMapper;
import com.jsunicom.oms.mapper.campus.ActivitiesNotifyBaseInfoMapper;
import com.jsunicom.oms.model.campus.Campus;
import com.jsunicom.oms.service.ActivitiesNotifyBaseInfoService;
import com.jsunicom.oms.utils.DateUtils;
import com.jsunicom.oms.utils.StringUtil;
import com.jsunicom.oms.utils.UserContext;
import org.osgi.dto.DTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【activities_notify_base_info】的数据库操作Service实现
 * @createDate 2025-03-18 10:21:04
 */
@Service
public class ActivitiesNotifyBaseInfoServiceImpl extends ServiceImpl<ActivitiesNotifyBaseInfoMapper, ActivitiesNotifyBaseInfo> implements ActivitiesNotifyBaseInfoService {


    @Resource
    private ActivitiesNotifyBaseInfoMapper activitiesNotifyBaseInfoMapper;

    @Resource
    private ActivitiesCampusRelationMapper activitiesCampusRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertActivitiesNotifyBaseInfo(ActivitiesNotifyCampusDto activitiesNotifyCampusDto) {

        //插入通知基本信息
        activitiesNotifyCampusDto.setUpdateUser(UserContext.getUser().getStaffName());
        activitiesNotifyCampusDto.setUpdateTime(DateUtils.getDateString());
        activitiesNotifyCampusDto.setActivitiesStatus(0);
        int insertRow = activitiesNotifyBaseInfoMapper.insert(activitiesNotifyCampusDto);
        //插入对应的学校
        for (ActivitiesCampusRelation activitiesCampusRelation : activitiesNotifyCampusDto.getActivitiesCampusRelationList()) {
            activitiesCampusRelation.setActivitiesNotifyId(activitiesNotifyCampusDto.getActivitiesNotifyId().toString());
            activitiesCampusRelation.setHasRead(Integer.parseInt(HasReadEnum.UNREAD.getStatus()));
            activitiesCampusRelation.setUpdateTime(DateUtils.getDateString());
        }
        activitiesCampusRelationMapper.batchInsertActivitiesCampus(activitiesNotifyCampusDto.getActivitiesCampusRelationList());
        return insertRow;
    }

    @Override
    public PageInfo<ActivitiesNotifyBaseInfoExtendVO> getActivitiesNotifyBaseInfoList(ActivitiesNotifyListMessageVO activitiesNotifyListMessageVO) {

//        LambdaQueryWrapper<ActivitiesNotifyBaseInfo> activitiesNotifyBaseInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(activitiesNotifyListMessageVO != null) {
//            activitiesNotifyBaseInfoLambdaQueryWrapper.like(StringUtil.isNotEmpty(activitiesNotifyListMessageVO.getActivityNotifyName()), ActivitiesNotifyBaseInfo::getActivityNotifyName, activitiesNotifyListMessageVO.getActivityNotifyName());
//            activitiesNotifyBaseInfoLambdaQueryWrapper.eq(StringUtil.isNotEmpty(activitiesNotifyListMessageVO.getActivityAttributes()), ActivitiesNotifyBaseInfo::getActivityAttributes, activitiesNotifyListMessageVO.getActivityAttributes());
            // 日期条件 todo
            if(activitiesNotifyListMessageVO.getPageInfo() != null){ //兼容导出功能
                PageHelper.startPage(activitiesNotifyListMessageVO.getPageInfo().getPageNumber(), activitiesNotifyListMessageVO.getPageInfo().getPageSize());

            }
        }
//        List<ActivitiesNotifyBaseInfo> activitiesNotifyBaseInfos = activitiesNotifyBaseInfoMapper.selectList(activitiesNotifyBaseInfoLambdaQueryWrapper);
        List<ActivitiesNotifyBaseInfoExtendVO> activitiesNotifyBaseInfoExtendVOS = activitiesNotifyBaseInfoMapper.getActivitiesNotifyBaseInfoList(activitiesNotifyListMessageVO);
        return new PageInfo(activitiesNotifyBaseInfoExtendVOS);
    }

    @Override
    public PageInfo<ActivitiesNotifyCompusVo> getActivitiesNotifyDetails( CampusListVo campusListVo) {
        //查询通知基本信息
//        ActivitiesNotifyBaseInfo activitiesNotifyBaseInfoDatabase = activitiesNotifyBaseInfoMapper.selectById(campusListVo.getActivitiesNotifyId());
        //查询关联院系信息
        PageHelper.startPage(campusListVo.getPageInfo().getPageNumber(), campusListVo.getPageInfo().getPageSize());
        List<ActivitiesNotifyCompusVo> activitiesNotifyCompusVos = activitiesCampusRelationMapper.selectActivitiesNotifyCompus(campusListVo);
//        PageInfo pageInfo = new PageInfo(activitiesNotifyCompusVos);
//
//        ActivitiesNotifyDetailsDto activitiesNotifyDetailsDto = new ActivitiesNotifyDetailsDto();
//        activitiesNotifyDetailsDto.setActivitiesNotifyBaseInfo(activitiesNotifyBaseInfoDatabase);
//        activitiesNotifyDetailsDto.setPageInfo(pageInfo);
        return new PageInfo(activitiesNotifyCompusVos);
    }

    @Override
    public int updateActivitiesNotifyBaseInfo(ActivitiesNotifyBaseInfo activitiesNotifyBaseInfo) {
        int updateRows = activitiesNotifyBaseInfoMapper.updateById(activitiesNotifyBaseInfo);
        return updateRows;
    }

    @Override
    public PageInfo<CampusChooseDto> getCampusList(CampusListVo campusListVo) {
        PageHelper.startPage(campusListVo.getPageInfo().getPageNumber(), campusListVo.getPageInfo().getPageSize());
        List<CampusChooseDto> campusList =  activitiesCampusRelationMapper.getCampusList(campusListVo);

        return new PageInfo<>(campusList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteActivitiesNotifyBaseInfo(Integer activitiesNotifyId) {
        //删除活动通知基本信息
        int deleteRow = activitiesNotifyBaseInfoMapper.deleteById(activitiesNotifyId);

        //删除下发对应的学校信息
        LambdaUpdateWrapper<ActivitiesCampusRelation> activitiesCampusRelationLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        activitiesCampusRelationLambdaUpdateWrapper.eq(ActivitiesCampusRelation::getActivitiesNotifyId, activitiesNotifyId);
        activitiesCampusRelationMapper.delete(activitiesCampusRelationLambdaUpdateWrapper);
        return deleteRow;
    }
}




