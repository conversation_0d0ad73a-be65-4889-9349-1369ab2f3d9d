package com.jsunicom.oms.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.po.DataMonitor;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-16-17:40
 */
public interface DataMonitorService {
    DataMonitor save(DataMonitor dataMonitor);

    DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc);

    DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc,String preOperateDesc);

    DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, Object model);

    DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, Object model,Object previousModel);

    PageInfo<DataMonitor> find(DataMonitor dataMonitor, int pageNumber, int pageSize);

    void saveBatchGoodsMonitor(HashMap map);
}
