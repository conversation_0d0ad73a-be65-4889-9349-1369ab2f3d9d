package com.jsunicom.oms.utils;

import cn.hutool.core.codec.Base64;
import com.jsunicom.api.common.utils.SM3Utils;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.Key;
import java.security.SecureRandom;

/**
 * @PackageName: com.mucfc.bcp.api.utils
 * @ClassName DesHelper
 * @Description des非对称加密
 * @Date 2023/2/23 16:16
 * @Created by yangyj3
 */
@Slf4j
public class DesHelper {
    Key key;

    public DesHelper() {

    }

    public DesHelper(String str) {
        setKey(str); // 生成密匙
    }

    public Key getKey() {
        return key;
    }

    public void setKey(Key key) {
        this.key = key;
    }

    /**
     * 根据参数生成 KEY
     */
    public void setKey(String strKey) {
        try {
            KeyGenerator _generator = KeyGenerator.getInstance("DES");
            //防止linux下 随机生成key
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(strKey.getBytes());

            _generator.init(56, secureRandom);
            this.key = _generator.generateKey();
            _generator = null;
        } catch (Exception e) {
            throw new RuntimeException(
                    "Error initializing SqlMap class. Cause: " + e);
        }
    }

    /**
     * 加密 String 明文输入 ,String 密文输出
     */
    public String encryptStr(String strMing) {
        byte[] byteMi = null;
        byte[] byteMing = null;
        String strMi = "";
        try {
            byteMing = strMing.getBytes("utf-8");
            byteMi = this.encryptByte(byteMing);
            strMi = Base64.encode(byteMi);
        } catch (Exception e) {
            throw new RuntimeException(
                    "Error initializing SqlMap class. Cause: " + e);
        } finally {
            byteMing = null;
            byteMi = null;
        }
        return strMi;
    }

    /**
     * 解密 以 String 密文输入 ,String 明文输出
     *
     * @param strMi
     * @return
     */
    public String decryptStr(String strMi) {
        byte[] byteMing = null;
        byte[] byteMi = null;
        String strMing = "";
        try {
            byteMi = Base64.decode(strMi);
            byteMing = this.decryptByte(byteMi);
            strMing = new String(byteMing, "utf-8");
        } catch (Exception e) {
            throw new RuntimeException(
                    "Error initializing SqlMap class. Cause: " + e);
        } finally {
            byteMing = null;
            byteMi = null;
        }
        return strMing;
    }

    /**
     * 加密以 byte[] 明文输入 ,byte[] 密文输出
     *
     * @param byteS
     * @return
     */
    private byte[] encryptByte(byte[] byteS) {
        byte[] byteFina = null;
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byteFina = cipher.doFinal(byteS);
        } catch (Exception e) {
            throw new RuntimeException(
                    "Error initializing SqlMap class. Cause: " + e);
        } finally {
            cipher = null;
        }
        return byteFina;
    }

    /**
     * 解密以 byte[] 密文输入 , 以 byte[] 明文输出
     *
     * @param byteD
     * @return
     */
    private byte[] decryptByte(byte[] byteD) {
        Cipher cipher;
        byte[] byteFina = null;
        try {
            cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byteFina = cipher.doFinal(byteD);
        } catch (Exception e) {
            throw new RuntimeException(
                    "Error initializing SqlMap class. Cause: " + e);
        } finally {
            cipher = null;
        }
        return byteFina;
    }


    /***************************  3DES加密解密  ******************************************/

    /**
     * ECB加密,不要IV
     *
     * @param key     密钥
     * @param message 明文
     * @return Base64编码的密文
     * @throws Exception
     */
    public static String des3EncodeECB(String key, String message)
            throws Exception {


        byte[] data = message.getBytes("UTF-8");
        byte[] bytekeys = Base64.decode(key);
        DESedeKeySpec spec = new DESedeKeySpec(bytekeys);
        SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
        Key deskey = keyfactory.generateSecret(spec);

        Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");

        cipher.init(Cipher.ENCRYPT_MODE, deskey);
        byte[] bOut = cipher.doFinal(data);
        return Base64.encode(bOut);
//        return bOut;
    }

    /**
     * ECB解密,不要IV
     *
     * @param key     密钥
     * @param message 密文
     * @return 明文
     * @throws Exception
     */
    public static String ees3DecodeECB(String key, String message)
            throws Exception {

        byte[] bytekeys = Base64.decode(key);
        DESedeKeySpec spec = new DESedeKeySpec(bytekeys);
        SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
        Key deskey = keyfactory.generateSecret(spec);

        byte[] data = Base64.decode(message);
        Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, deskey);
        byte[] bOut = cipher.doFinal(data);
        return new String(bOut, "UTF-8");
    }

    /***************************  TripleDES加密解密  ******************************************/

    /**
     * ECB加密,不要IV
     *
     * @param key     密钥, 真实密钥其实是key的mds5值
     * @param message 明文
     * @return Base64编码的密文
     * @throws Exception
     */
    public static String tripleDesEncodeECB(String key, String message) throws Exception {
        byte[] data = message.getBytes("UTF-8");
        DESedeKeySpec spec = new DESedeKeySpec(MD5Util.MD5(key).getBytes());
        SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
        Key deskey = keyfactory.generateSecret(spec);

        Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");

        cipher.init(Cipher.ENCRYPT_MODE, deskey);
        byte[] bOut = cipher.doFinal(data);
        return Base64.encode(bOut);
    }

    /**
     * ECB解密,不要IV
     *
     * @param key     密钥, 真实密钥其实是key的mds5值
     * @param message 密文
     * @return 明文
     * @throws Exception
     */
    public static String tripleDesDecodeECB(String key, String message) throws Exception {
        log.info(SM3Utils.encrypt(key));
//        DESedeKeySpec spec = new DESedeKeySpec(MD5Util.MD5(key).getBytes());
        DESedeKeySpec spec = new DESedeKeySpec(SM3Utils.encrypt(key).getBytes());
        SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
        Key deskey = keyfactory.generateSecret(spec);

        byte[] data = Base64.decode(message);
        Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, deskey);
        byte[] bOut = cipher.doFinal(data);
        return new String(bOut, "UTF-8");
    }

    public static void main(String[] args) throws Exception {
        System.out.println("phoneNumber："+tripleDesEncodeECB("D24k81#$Wa9x","13011228867"));

//        System.out.println("phoneNumber："+tripleDesDecodeECB("D24k81#$Wa9x","/Lb7ukn 2Lz3jcnpzzY4Cg=="));
    }
}
