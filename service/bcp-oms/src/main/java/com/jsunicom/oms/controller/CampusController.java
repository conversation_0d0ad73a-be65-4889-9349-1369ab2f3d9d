package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.dto.CampusQuery;
import com.jsunicom.oms.common.exception.BCPBusinessException;
import com.jsunicom.oms.common.listener.CampusExcelListener;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.dto.campus.WoSchoolCampusExtendDto;
import com.jsunicom.oms.entity.CampusExcel;
import com.jsunicom.oms.mapper.UserRoleInfoMapper;
import com.jsunicom.oms.model.campus.Campus;
import com.jsunicom.oms.po.UserRoleInfo;
import com.jsunicom.oms.po.UserRoleInfoExample;
import com.jsunicom.oms.po.WoSchoolCampus;
import com.jsunicom.oms.service.CampusService;
import com.jsunicom.oms.service.OrgInfoService;
import com.jsunicom.oms.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-23-17:08
 */
@Slf4j
@RestController
@RequestMapping(value = "/campus")
public class CampusController extends AbstractSimpleController {
    @Autowired
    private CampusService campusService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Resource
    private UserRoleInfoMapper userRoleInfoMapper;

    @RequestMapping(value = "/queryByCondition", method = RequestMethod.POST, name = "查询校区")
    public CustomResult query(@RequestBody CampusQuery campusQuery, HttpServletRequest request) {
        log.info("Entry Method: queryByCondition() ");
        log.debug("campusQuery:{}",campusQuery);
        String LoginSerialNumber=request.getHeader("serialNumber");
        String LoginStaffNo=request.getHeader("staffNo");

        PageInfo<Map<String,Object>> campuses = null;
        campusQuery.setUserOrgCode(request.getHeader("areaCode"));
        campuses = campusService.queryByCondition(campusQuery,LoginSerialNumber,LoginStaffNo);
        log.info("campusQuery:{}",campuses);
        log.info("Exit Method: queryByCondition() ");
        HashMap res=new HashMap();
        res.put("rsList",campuses.getList());
        res.put("totalCount",campuses.getTotal());
        return ResultUtil.success(res);
    }
    @RequestMapping(value = "/modify", method = RequestMethod.POST, name = "编辑校区")
    public CustomResult modify(@RequestBody WoSchoolCampusExtendDto campus, HttpServletRequest request) throws Exception{
        log.info("Entry Method: modify() ");
        log.info("操作员信息：工号【{}】,手机号【{}】",request.getHeader("staffNo"),request.getHeader("serialNumber"));
        log.info("campus:{}",campus);
        //先校验是否是省管理员，如果是非省管理员，在判断有没有修改过学校名称、校区名称，修改的话拦截，提示
        boolean checkResult = campusService.checkLoginUserRole(campus,request.getHeader("staffNo"));
        if (!checkResult){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"非省管理员禁止修改学校名称、校区名称！");
        }
        campusService.modify(campus,request.getHeader("staffNo"));
        log.info("Exit Method: modify() ");
        return ResultUtil.success();
    }
    @RequestMapping(value = "/uploadCampusSave", method = RequestMethod.POST, name = "导入校园")
    public JSONObject uploadCampusSave(MultipartFile file,HttpServletRequest request) {
        log.info("Entry Method: uploadCampusSave() ");
        log.info("操作员信息：工号【{}】,手机号【{}】",request.getHeader("staffNo"),request.getHeader("serialNumber"));
        JSONObject jsonObject = new JSONObject();
        try {

            //判断操作员是否是校区经理
            String serialNumber=request.getHeader("serialNumber");
            String phone="";
            UserRoleInfoExample example=new UserRoleInfoExample();
            UserRoleInfoExample.Criteria criteria = example.createCriteria();
            criteria.andSerialNumberEqualTo(serialNumber);
            List<UserRoleInfo> userRoleInfos = userRoleInfoMapper.selectByExample(example);
            UserRoleInfo userRoleInfo = userRoleInfos.get(0);
            if (!"1".equals(userRoleInfo.getRoleCode())){//非省管理员没有导入权限
                List<String> errorList = Collections.singletonList("抱歉，非省管理员没有导入权限！");
                jsonObject.put("errorInfo",errorList);
                jsonObject.put("num",1);
                return jsonObject;
            }

            CampusExcelListener campusExcelListener =
                    new CampusExcelListener(campusService, orgInfoService);

            Map<String, Object> dataMap = ExcelUtil.readExcel(file, new CampusExcel(),campusExcelListener);
            List<String> errorList = (List<String>) dataMap.get("errorMsg");
            if (errorList.size() > 0) {
                jsonObject.put("errorInfo",errorList);
            }
            List<CampusExcel> numList = (List<CampusExcel>) dataMap.get("data");
            List<WoSchoolCampus>  campuses = new ArrayList<>();
            WoSchoolCampus campus = null;
            if(null != numList && numList.size() > 0){
                for(CampusExcel campusExcel : numList){
                    campus = new WoSchoolCampus();
                    org.springframework.beans.BeanUtils.copyProperties(campusExcel,
                            campus);
                    campuses.add(campus);
                }
            }
            if(campuses.size() > 0)
                campusService.save(campuses,request.getHeader("staffNo"));

            jsonObject.put("num",campuses.size());

        }catch (Exception e){
            log.error("uploadCampusSave:",e);
            throw e;
        }
        log.debug("jsonObject :{}",jsonObject);
        log.info("Exit Method: uploadCampusSave() ");
        return jsonObject;
    }
    @RequestMapping(value = "/getAllCampus", method = RequestMethod.GET, name = "所有小区集合")
    public JSONObject getAllCampus() {
        log.info("Entry Method: getAllCampus() ");
        JSONObject result = new JSONObject();
        List<Campus> campusList;
        try {
            campusList = campusService.getAllCampus();
        }catch (BCPBusinessException bcpE){
            log.error("getAllCampus:",bcpE);
            throw bcpE;
        }catch (Exception e){
            log.error("getAllCampus:",e);
            throw e;
        }
        result.put("campusList", campusList);
        return result;
    }


    @RequestMapping(value = "/upLoadSaleManager", method = RequestMethod.POST, name = "导入销售经理")
    public CustomResult upLoadSaleManager(MultipartFile file, HttpServletRequest request) {
        log.info("进入{}方法", "upLoadSaleManager");
        log.info("操作员信息：工号【{}】,手机号【{}】", request.getHeader("staffNo"), request.getHeader("serialNumber"));
        
        // 判断操作员是否是省管理员
        String serialNumber = request.getHeader("serialNumber");
        UserRoleInfoExample example = new UserRoleInfoExample();
        UserRoleInfoExample.Criteria criteria = example.createCriteria();
        criteria.andSerialNumberEqualTo(serialNumber);
        List<UserRoleInfo> userRoleInfos = userRoleInfoMapper.selectByExample(example);
        
        if (userRoleInfos.isEmpty() || !"1".equals(userRoleInfos.get(0).getRoleCode())) {
            return new CustomResult("9999", "非省管理员没有导入权限！", null);
        }

        return campusService.upLoadSaleManager(file);
    }

    /**
     * 下载销售经理导入模板
     */
    @GetMapping(value = "/downloadSaleManagerTemplate",name = "下载导入校区经理模板")
    public void downloadSaleManagerTemplate(HttpServletResponse response) {
        log.info("进入{}方法{}", "downloadSaleManagerTemplate","下载导入校区经理模板");
        try {
            campusService.generateSalesManagerTemplate(response);
        } catch (Exception e) {
            log.error("下载销售经理导入模板失败", e);
            throw new RuntimeException("下载销售经理导入模板失败");
        }
    }
}
