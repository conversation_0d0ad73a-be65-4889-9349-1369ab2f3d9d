package com.jsunicom.oms.mapper.woschool;

import com.jsunicom.oms.po.WoSchoolCampusSchemeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WoSchoolCampusSchemeInfoMapper {
    int deleteByPrimaryKey(@Param("schemeId") Long schemeId, @Param("dictKind") String dictKind, @Param("dictKindCode") String dictKindCode);

    int insert(WoSchoolCampusSchemeInfo record);

    Long insertSelective(WoSchoolCampusSchemeInfo record);

    WoSchoolCampusSchemeInfo selectByPrimaryKey(@Param("schemeId") Long schemeId, @Param("dictKind") String dictKind, @Param("dictKindCode") String dictKindCode);

    int updateByPrimaryKeySelective(WoSchoolCampusSchemeInfo record);

    int updateByPrimaryKey(WoSchoolCampusSchemeInfo record);

    List<WoSchoolCampusSchemeInfo> selectList(@Param("schemeId") Long schemeId, @Param("dictKind") String dictKind, @Param("dictKindCode") String dictKindCode);
}