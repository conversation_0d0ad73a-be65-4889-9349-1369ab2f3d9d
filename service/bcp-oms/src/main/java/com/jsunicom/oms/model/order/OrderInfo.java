package com.jsunicom.oms.model.order;


import com.jsunicom.oms.model.base.BasicModel;

import java.sql.Timestamp;
import java.util.List;

public class OrderInfo extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long partnerId;
    private String develId;
    private String orgCode;
    private String orderNo;
    private String custName;
    private String custPhone;
    private String custCertNo;
    private String custExtend;
    private java.math.BigDecimal amount;
    private String remark;
    private String state;
    private String extState;
    private String orderBy;
    private java.sql.Timestamp orderTime;
    private java.sql.Timestamp finishTime;
    private java.sql.Timestamp retreatTime;
    private String orderExtend;
    private String createBy;
    private java.sql.Timestamp createTime;
    private String updateBy;
    private java.sql.Timestamp updateTime;
    private String payType;
    private String payState;
    private String deliveryType;
    private List<OrderGoods> orderGoods;
    private String provName;
    private String cityName;
    private String areaName;
    private String address;

    // 以下属性来自OrderGoods
    private String goodsOrderNo;
    private String goodsOrderState;
    private String goodsCode;
    private String goodsName;
    private String acIsIntoAcct;//该条订单激活是否已经插入佣金明细表
    private String fcIsIntoAcct;//该条订单首充是否已经插入佣金明细表
    private String goodsMsisdn;
    private String level;
    private String goodsType;

    private String acctNo;
    private String count;

    private String orderProgressName;//订单进度名称
    private String orderProgressTime;//订单进度时间

    private String outTradeNo;//金融业务流水号

    private String busiType;

    public OrderInfo(){

    }

    public OrderInfo(String orderProgressName, String orderProgressTime){
        this.orderProgressName = orderProgressName;
        this.orderProgressTime=orderProgressTime;
    }


    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    private List<String> orgCodes;

    private String partnerName;

    public String getProvName() {
        return provName;
    }

    public void setProvName(String provName) {
        this.provName = provName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getGoodsMsisdn() {
        return goodsMsisdn;
    }

    public void setGoodsMsisdn(String goodsMsisdn) {
        this.goodsMsisdn = goodsMsisdn;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public Long getPartnerId() {
        return partnerId;
   }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
   }

    public String getDevelId() {
        return develId;
   }

    public void setDevelId(String develId) {
        this.develId = develId;
   }

    public String getOrgCode() {
        return orgCode;
   }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
   }


    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCustName() {
        return custName;
   }

    public void setCustName(String custName) {
        this.custName = custName;
   }

    public String getCustPhone() {
        return custPhone;
   }

    public void setCustPhone(String custPhone) {
        this.custPhone = custPhone;
   }

    public String getCustCertNo() {
        return custCertNo;
   }

    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
   }

    public String getCustExtend() {
        return custExtend;
   }

    public void setCustExtend(String custExtend) {
        this.custExtend = custExtend;
   }

    public java.math.BigDecimal getAmount() {
        return amount;
   }

    public void setAmount(java.math.BigDecimal amount) {
        this.amount = amount;
   }

    public String getRemark() {
        return remark;
   }

    public void setRemark(String remark) {
        this.remark = remark;
   }

    public String getState() {
        return state;
   }

    public void setState(String state) {
        this.state = state;
   }

    public java.sql.Timestamp getFinishTime() {
        return finishTime;
   }

    public void setFinishTime(java.sql.Timestamp finishTime) {
        this.finishTime = finishTime;
   }

    public java.sql.Timestamp getRetreatTime() {
        return retreatTime;
   }

    public void setRetreatTime(java.sql.Timestamp retreatTime) {
        this.retreatTime = retreatTime;
   }

    public String getOrderExtend() {
        return orderExtend;
   }

    public void setOrderExtend(String orderExtend) {
        this.orderExtend = orderExtend;
   }

    public String getCreateBy() {
        return createBy;
   }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
   }

    public java.sql.Timestamp getCreateTime() {
        return createTime;
   }

    public void setCreateTime(java.sql.Timestamp createTime) {
        this.createTime = createTime;
   }

    public String getUpdateBy() {
        return updateBy;
   }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
   }

    public java.sql.Timestamp getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(java.sql.Timestamp updateTime) {
        this.updateTime = updateTime;
   }

    public String getExtState() {
        return extState;
   }

    public void setExtState(String extState) {
        this.extState = extState;
   }


    public List<OrderGoods> getOrderGoods() {
        return orderGoods;
    }

    public void setOrderGoods(List<OrderGoods> orderGoods) {
        this.orderGoods = orderGoods;
    }

    public Timestamp getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Timestamp orderTime) {
        this.orderTime = orderTime;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsOrderState() {
        return goodsOrderState;
    }

    public void setGoodsOrderState(String goodsOrderState) {
        this.goodsOrderState = goodsOrderState;
    }

    public String getGoodsOrderNo() {
        return goodsOrderNo;
    }

    public void setGoodsOrderNo(String goodsOrderNo) {
        this.goodsOrderNo = goodsOrderNo;
    }

    public List<String> getOrgCodes() {
        return orgCodes;
    }

    public void setOrgCodes(List<String> orgCodes) {
        this.orgCodes = orgCodes;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getFcIsIntoAcct() {
        return fcIsIntoAcct;
    }

    public void setFcIsIntoAcct(String fcIsIntoAcct) {
        this.fcIsIntoAcct = fcIsIntoAcct;
    }

    public String getAcIsIntoAcct() {
        return acIsIntoAcct;
    }

    public void setAcIsIntoAcct(String acIsIntoAcct) {
        this.acIsIntoAcct = acIsIntoAcct;
    }

    public String getOrderProgressName() {
        return orderProgressName;
    }

    public void setOrderProgressName(String orderProgressName) {
        this.orderProgressName = orderProgressName;
    }

    public String getOrderProgressTime() {
        return orderProgressTime;
    }

    public void setOrderProgressTime(String orderProgressTime) {
        this.orderProgressTime = orderProgressTime;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }
}

/*List columns as follows:
"id", "partner_id", "devel_id", "org_code", "ext_order_no", "cust_name", "cust_phone", 
"cust_cert_no", "cust_extend", "amount", "remark", "state", "finish_time", "retreat_time", 
"order_extend", "create_by", "create_time", "update_by", "update_time", "ext_state"
*/