package com.jsunicom.oms.po;

import java.io.Serializable;

public class WoInnovateListInfo implements Serializable {

    private Long partnerId;

    private Long societyId;

    private String societyName;

    private Long campusId;

    private String societyLogoUrl;

    private String societyIntroduction;

    private String societyRemark;

    private String partnerCertNo;

    private String mblNbr;

    private String partnerName;

    private String logStr;

    private String isMerAdmin;

    private long members;

    public long getMembers() {
        return members;
    }

    public void setMembers(long members) {
        this.members = members;
    }

    public String getIsMerAdmin() {
        return isMerAdmin;
    }

    public void setIsMerAdmin(String isMerAdmin) {
        this.isMerAdmin = isMerAdmin;
    }

    public String getLogStr() {
        return logStr;
    }

    public void setLogStr(String logStr) {
        this.logStr = logStr;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public Long getSocietyId() {
        return societyId;
    }

    public void setSocietyId(Long societyId) {
        this.societyId = societyId;
    }

    public String getSocietyName() {
        return societyName;
    }

    public void setSocietyName(String societyName) {
        this.societyName = societyName;
    }

    public Long getCampusId() {
        return campusId;
    }

    public void setCampusId(Long campusId) {
        this.campusId = campusId;
    }

    public String getSocietyLogoUrl() {
        return societyLogoUrl;
    }

    public void setSocietyLogoUrl(String societyLogoUrl) {
        this.societyLogoUrl = societyLogoUrl;
    }

    public String getSocietyIntroduction() {
        return societyIntroduction;
    }

    public void setSocietyIntroduction(String societyIntroduction) {
        this.societyIntroduction = societyIntroduction;
    }

    public String getSocietyRemark() {
        return societyRemark;
    }

    public void setSocietyRemark(String societyRemark) {
        this.societyRemark = societyRemark;
    }

    public String getPartnerCertNo() {
        return partnerCertNo;
    }

    public void setPartnerCertNo(String partnerCertNo) {
        this.partnerCertNo = partnerCertNo;
    }

    public String getMblNbr() {
        return mblNbr;
    }

    public void setMblNbr(String mblNbr) {
        this.mblNbr = mblNbr;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
}
