package com.jsunicom.oms.service;

import com.jsunicom.oms.entity.TdOrdSysDict;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @PackageName: com.jsunicom.oms.service
 * @ClassName PhotoProcessService
 * @Description 照片相关操作
 * @Date 2023/6/2 20:36
 * @Created by yangyj3
 */
public interface PhotoProcessService {
    TdOrdSysDict queryDict(String paramType, String paramKey);
    List<Map<String, String>> querySynPhotoRecords();

    List<List<Map<String, String>>> divList(List<Map<String,String>> sourceList,int avgSize);
    void synThread(CountDownLatch count, List<Map<String, String>> list);

    List<TdOrdSysDict> queryDictList(String paramType);

    List<Map<String, String>> synPhotoInfos(String sourceSystem);

    void synPhotoThread(CountDownLatch count,int index,int fileCount, List<Map<String, String>> list,TdOrdSysDict dict);

    int saveRspPhotoInfoZRR(HashMap<String,String> record);

    int updateRspRecordSucess();


    List<Map<String, String>> queryRspNoDealFail();

    void updateRspFail(HashMap<String,String> param);
}
