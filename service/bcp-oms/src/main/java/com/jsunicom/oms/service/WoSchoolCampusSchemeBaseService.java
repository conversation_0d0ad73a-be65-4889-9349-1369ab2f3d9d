package com.jsunicom.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.dto.scheme.CheckSchemeProcessApproveVo;
import com.jsunicom.oms.dto.scheme.SchemeProcessApprove;
import com.jsunicom.oms.dto.scheme.SchoolCampusSchemeDto;
import org.springframework.web.multipart.MultipartFile;

public interface WoSchoolCampusSchemeBaseService{


    Long deleteByPrimaryKey(Long schemeId);

    Long insert(SchoolCampusSchemeDto schemeDto);

    PageInfo selectPage(String campusId, String orgCode, Integer pageNo, Integer pageSize);

    SchoolCampusSchemeDto selectByPrimaryKey(Long schemeId);


    Long updateByPrimaryKey(SchoolCampusSchemeDto schemeDto);

    PageInfo queryUnbindTeam(String campusId, String schemeYear, String marketingType, Integer pageNo, Integer pageSize);

    JSONObject processApprove(SchemeProcessApprove schemeProcessApprove, String serialNumber);

    JSONObject getApprovalProgressDetail(CheckSchemeProcessApproveVo checkSchemeProcessApproveVo);

    JSONObject uploadPdf(MultipartFile file);

}
