package com.jsunicom.oms.service.impl.api;

import com.jsunicom.oms.factory.FactoryCreator;
import com.jsunicom.oms.request.BaseRequest;
import com.jsunicom.oms.response.BaseResponse;
import com.jsunicom.oms.service.api.ApiMemberService;
import com.lz.lsf.exception.ServiceException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("apiMemberServiceImpl")
public class ApiMemberServiceImpl implements ApiMemberService {

//    @Resource
//    private FactoryCreator factoryCreator;

    @Override
    public <T extends BaseResponse> T getDeptMemberDetails(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T createMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T updateMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T deleteMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T batchDeleteMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T getMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T inviteMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

    @Override
    public <T extends BaseResponse> T inviteBatchMember(BaseRequest<T> request) throws ServiceException {
        return null;
    }

//    @Override
//    public <T extends BaseResponse> T getDeptMemberDetails(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().getDeptMemberDetails(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T createMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().createMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T updateMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().updateMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T deleteMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().deleteMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T batchDeleteMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().batchDeleteMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T getMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().getMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T inviteMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().inviteMember(request);
//    }
//
//    @Override
//    public <T extends BaseResponse> T inviteBatchMember(BaseRequest<T> request) throws ServiceException {
//        return factoryCreator.getServiceFactory(request).getMemberService().inviteBatchMember(request);
//    }
}
