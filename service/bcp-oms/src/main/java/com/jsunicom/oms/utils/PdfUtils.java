package com.jsunicom.oms.utils;

import com.alibaba.fastjson.JSONObject;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.jsunicom.oms.config.SpringUtils;
import com.jsunicom.oms.model.partner.UserAgreementPdf;
import com.jsunicom.oms.service.DictFacade;
import com.jsunicom.oms.service.PartnerFacade;
import com.lz.lsf.framework.spring.SpringContextHolder;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.Base64;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import java.io.*;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class PdfUtils {
	private static final org.slf4j.Logger logger = LoggerFactory.getLogger(PdfUtils.class);

	/*public static final String NATURETEXT = "src/main/resources/pdf/nature.txt";
	public static final String MERCHANTTEXT = "src/main/resources/pdf/merchant.txt";

	private static Map <String,String> merchantDictMap = new HashMap<String,String>();
	static {
		Date now=new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
		String dateStr = sdf.format(now);

		merchantDictMap.put("{partAname}", "江苏联通");
		merchantDictMap.put("{partBname}", "宙斯连锁店");
		merchantDictMap.put("{businessLicence}", "***********");
		merchantDictMap.put("{address}", "南京市玄武区XX路XX号");
		merchantDictMap.put("{chargeName}", "阿波罗");
		merchantDictMap.put("{chargeIdentity}", "320903198801014356");
		merchantDictMap.put("{chargeNumber}", "***********");

		merchantDictMap.put("{contactName}", "雅典娜");
		merchantDictMap.put("{contactIdentity}", "320903198801011234");
		merchantDictMap.put("{contactNumber}", "***********");

		merchantDictMap.put("{date}", dateStr);
	}*/

	public static boolean createPdf(Map<String, String> natureDictMap, UserAgreementPdf userAgreementPdf, String fileType) throws IOException {
		StringBuffer tmpName = new StringBuffer();
		String resp = "";
		boolean result = true;

		long now = Calendar.getInstance().getTimeInMillis();
		tmpName.append(fileType);
		tmpName.append("_1");
		tmpName.append("_" + natureDictMap.get("{identity}"));
		tmpName.append("_" + now);
		//tmpName.append(".pdf");

		String pdfFileName = tmpName.toString();

		String templateTxtPath = PdfUtils.class.getResource("/").toString() + "pdf/";

		String os = System.getProperties().getProperty("os.name");
        if (os.startsWith("win") || os.startsWith("Win")) {
        	templateTxtPath = templateTxtPath.substring(6, templateTxtPath.length());
        } else {
        	templateTxtPath = templateTxtPath.substring(5, templateTxtPath.length());
        }
        String templateTxt = "/tmp/" + fileType + ".txt";
        logger.info("templateTxt:" + templateTxt);
        File pdfFile = File.createTempFile(pdfFileName, ".pdf");
		logger.info("pdfFile文件absolutePath路径:{}",pdfFile.getAbsolutePath());
//        File txtFile = new File(templateTxt);
		File txtFile = File.createTempFile(fileType, ".txt");
		logger.info("pdfFile文件canonicalPath路径:" + pdfFile.getCanonicalPath());
		logger.info("txtFile文件canonicalPath路径:{}",txtFile.getCanonicalPath());

		try {

			generatePdf(txtFile, pdfFile, natureDictMap);

		} catch (IOException e) {
			result = false;
			logger.error("创建PDF文件异常:", e);
		}
		logger.info("===uploadFile===");
		ApplicationContext context = SpringUtils.getApplicationContext();
		DictFacade dictFacade = context.getBean(DictFacade.class);
		String switchName = dictFacade.getNameByKey("switch", "switch_image_upload");
		PartnerFacade partnerService =context.getBean(PartnerFacade.class);
		if(StringUtils.equals("1",switchName)){
			logger.info("走商户中心接口上传pdf文件");
			//走商户中心接口上传图片
			byte[] bytes= IOUtils.toByteArray(new FileInputStream(pdfFile));
			String imageBase64= Base64.encodeBase64String(bytes);
			JSONObject jsonObject=partnerService.getImageUrlByShzx(imageBase64,pdfFileName + ".pdf");
			if(StringUtils.equals("0",jsonObject.getString("ret"))){
				resp=jsonObject.getString("url");
				logger.info("走商户中心接口上传pdf文件,返回的文件链接地址:{}",resp);
			}else{
				String errMsg=jsonObject.getString("errMsg");
				result = false;
				logger.error("走商户中心文件上传接口创建PDF文件异常:", errMsg);
			}
		}else{
			logger.info("走cfile上传文件模式上传pdf文件");
			//Cfile模式上传
			resp = CfileUtils.uploadFile(pdfFileName + ".pdf", new FileInputStream(pdfFile));
		}
		//商户中心接口上传
		logger.info("resp:" + resp);

		userAgreementPdf.setFileType(fileType);
		userAgreementPdf.setFileName(pdfFileName + ".pdf");
		userAgreementPdf.setCfilePath(resp);
		userAgreementPdf.setFileSize(pdfFile.length());
		userAgreementPdf.setCreateTime(new Timestamp(now));

		pdfFile.deleteOnExit();
		return result;
	}

	private static void generatePdf(File src, File dest, Map<String, String> dictMap) throws IOException {

		boolean title = true, first = true;
		//String prefixFont = "";
		BufferedReader br = null;
		Font bold = null, normal= null;

		Document document = new Document();
		try {
			PdfWriter.getInstance(document, new FileOutputStream(dest));
		} catch (FileNotFoundException e) {
			logger.error("文件异常：", e);
		} catch (DocumentException e) {
			logger.error("文件异常：", e);
		}
		document.open();

       /* String os = System.getProperties().getProperty("os.name");
        if (os.startsWith("win") || os.startsWith("Win")) {
            prefixFont = "C:/Windows/Fonts" + File.separator;
        } else {
            prefixFont = "/usr/share/fonts/chinese" + File.separator;
        }*/

		try {
			//BaseFont baseFont = BaseFont.createFont(prefixFont + "SIMYOU.TTF", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
			BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

			bold = new Font(baseFont);
			normal = new Font(baseFont);
		} catch (DocumentException e) {
			logger.error("文件异常：", e);
		} catch (IOException e) {
			logger.error("文件异常：", e);
			throw new IOException("文件没找到：", e);
		}

		bold.setStyle(Font.BOLD);

		/*try {
			br = new BufferedReader(new FileReader(src));
			String line;
			while ((line = br.readLine()) != null) {
				Paragraph p;
				//logger.info("line:" + line);

				for (Map.Entry<String, String> entry : dictMap.entrySet()) {
					if (line.contains(entry.getKey())){
						line = line.replace(entry.getKey(), entry.getValue() != null ? entry.getValue() : "");
					}
				}

				if(first) {
					Font titleBold = new Font(BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED));
					titleBold.setSize(16);
					titleBold.setStyle(Font.BOLD);

					p = new Paragraph(line, titleBold );
					p.setAlignment(Element.ALIGN_CENTER);

					first = false;
				} else {

					p = new Paragraph(line, title ? bold : normal);
					p.setAlignment(Element.ALIGN_JUSTIFIED);
				}

				title = line.isEmpty();
				document.add(p);
			}
		} catch (IOException e) {
			logger.error("文件异常：", e);
		} catch (DocumentException e) {
			logger.error("文件异常：", e);
		} finally {
			logger.info("first:" + first);
			if( br != null){
				try {
					br.close();
				} catch (IOException e) {
					logger.error("IO异常：", e);
				}
			}

			if(document != null){
				document.close();
			}
		}*/

	}

	public static void downloadPdf(String pdfUrl, String fileName, File zipFile) {
		byte[] stream = null;

		if (pdfUrl != null && pdfUrl.trim().length() > 1) {
			logger.info("pdfUrl:" + pdfUrl + ",fileName" + fileName);

            if(pdfUrl.indexOf("imageId") >=0) {
                //之前cfile上传
                logger.info("PdfUtils中的downloadPdf走cfile上传的地址获取文件信息url:{}",pdfUrl);
                stream = HttpRequestUtils.sendGet(pdfUrl);
            }else{
				logger.info("PdfUtils中的downloadPdf走商户中心文件上传的地址获取文件信息url:{}",pdfUrl);
                ApplicationContext context = SpringContextHolder.getApplicationContext();
                PartnerFacade partnerService =(PartnerFacade)context.getBean("partnerService");
                //走商户中心图片上传接口返回的url
                String base64 = partnerService.getBase64ImageStr(pdfUrl);
                logger.info("走商户中心下载pdf获取的base64:{}" ,base64);
                stream= Base64.decodeBase64(base64);
            }

			File tempFile;
			OutputStream os = null;
			try {
				tempFile = File.createTempFile(fileName.replace(".pdf", ""), ".pdf", zipFile);
				logger.info("tempFile:" + tempFile.getAbsolutePath());
				os = new FileOutputStream(tempFile);
				os.write(stream);
			} catch (IOException e) {
				logger.error("创建文件失败:" + e.toString());
			} finally {
				if (os != null) {
					try {
						os.close();
					} catch (IOException e) {
						logger.error(e.toString());
					}
				}
			}

		} else {
			logger.error("url为空");
		}
	}

	public static void main(String[] args) {
		System.out.println("=====main()======");
		Map<String, String> natureDictMap = new HashMap<String, String>();
		Date now=new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
		String dateStr = sdf.format(now);

		natureDictMap.put("{partAname}", "江苏联通");
		natureDictMap.put("{partBname}", "阿波罗");
		natureDictMap.put("{name}", "阿波罗");
		natureDictMap.put("{identity}", "320903198801014356");
		natureDictMap.put("{address}", "南京市玄武区XX路XX号");
		natureDictMap.put("{number}", "***********");
		natureDictMap.put("{date}", dateStr);
		System.out.println(natureDictMap.get("{identity}"));

		//createPdf(natureDictMap);
	}
}
