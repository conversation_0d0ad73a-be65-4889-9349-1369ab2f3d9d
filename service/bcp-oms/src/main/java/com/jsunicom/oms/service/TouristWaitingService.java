package com.jsunicom.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.entity.PartnerInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface TouristWaitingService {
    HashMap<String, Object> qryPageTouristInfo(JSONObject jsonObject);

    HashMap<String, Object> qrySocietyInfoBySchoolId(JSONObject jsonObject);

    int deleteTouristWaiting(JSONObject jsonObject);

    long countOrByExample(PartnerInfo record);

    int insertPartnerInfo(PartnerInfo record);

    int upDateTouristWaiting(HashMap jsonObject);

    List<Map> queryMemberIListById(JSONObject params);
}
