package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.mapper.campus.CampusBindDao;
import com.jsunicom.oms.mapper.ext.ExtMapper;
import com.jsunicom.oms.mapper.partner.PartnerDao;
import com.jsunicom.oms.model.campus.CampusBind;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.model.woshcool.SchoolInfo;
import com.jsunicom.oms.model.woshcool.SchoolListInfo;
import com.jsunicom.oms.po.WoScYouthInnovateBase;
import com.jsunicom.oms.po.WoScYouthInnovateBaseExample;
import com.jsunicom.oms.service.CampusBindFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: CampusBindServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-21  21:57
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class CampusBindServiceImpl implements CampusBindFacade {
    @Autowired
    private CampusBindDao campusBindDao;
    @Autowired
    private ExtMapper extMapper;
    @Autowired
    private PartnerServiceImpl partnerService;
    @Autowired
    private PartnerDao partnerDao;

    @Override
    public void removeBy(CampusBind campusBind) {
        campusBindDao.deleteByBindIdAndType(campusBind);
    }

    @Override
    public void save(CampusBind campusBind) {
        campusBind.init();
        campusBindDao.save(campusBind);
    }

    @Override
    public List<SchoolInfo> querySchoolByCId(Long campusId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("campusId",campusId);

        return extMapper.findBySchool(paramMap);
    }
    @Override
    public void modifySchoolManager(Long campusId, Long pId) {
        CampusBind campusBind = new CampusBind();
        campusBind.setCampusId(campusId);
        campusBind.setType(CampusBind.TYPE_1);
        campusBindDao.deleteByCampusIdAndType(campusBind);

        campusBind.setBindId(pId);
        campusBind.init();
        campusBindDao.save(campusBind);
    }

    @Override
    public CustomResult removeSchoolManagerByCampusId(Long campusId, String mblNbr) {
        //校验校区下是否有创建团队
        List<SchoolListInfo>  schoolListInfos = extMapper.getSchoolInfoList(campusId);
        if (schoolListInfos.size()>0){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"该校区下已创建团队，不能解除校区经理与校区关系，可以通过更换校区经理操作变更校区经理！");
        }
        //

        CampusBind campusBind = new CampusBind();
        campusBind.setCampusId(campusId);
        campusBind.setType(CampusBind.TYPE_1);
        campusBindDao.deleteByCampusIdAndType(campusBind);

        return ResultUtil.success();
    }
}
