package com.jsunicom.oms.service.api;

import com.jsunicom.oms.request.BaseRequest;
import com.jsunicom.oms.response.BaseResponse;
import com.lz.lsf.exception.ServiceException;

/**
 * 成员管理服务，目前只有微信企业号使用
 * 
 * <AUTHOR>
 *
 */
public interface ApiMemberService {

    /**
     * 获取部门成员(详情)
     * 
     * @param request
     *            请求
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T getDeptMemberDetails(BaseRequest<T> request) throws ServiceException;

    /**
     * 创建成员
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T createMember(BaseRequest<T> request) throws ServiceException;

    /**
     * 更新成员
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T updateMember(BaseRequest<T> request) throws ServiceException;

    /**
     * 删除成员
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T deleteMember(BaseRequest<T> request) throws ServiceException;

    /**
     * 批量删除成员
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T batchDeleteMember(BaseRequest<T> request) throws ServiceException;

    /**
     * 获取成员
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    <T extends BaseResponse> T getMember(BaseRequest<T> request) throws ServiceException;

    /**
     * 邀请成员关注企业号
     * 
     * @param request
     * @return
     * @throws ServiceException
     */
    @Deprecated
    <T extends BaseResponse> T inviteMember(BaseRequest<T> request) throws ServiceException;


    <T extends BaseResponse> T inviteBatchMember(BaseRequest<T> request) throws ServiceException;
}
