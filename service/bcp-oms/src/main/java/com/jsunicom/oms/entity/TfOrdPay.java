package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TfOrdPay {

    private static final long serialVersionUID = 1L;

    private String payId;

    private String orderId;

    private String payType;

    private String paySeq;

    private BigDecimal oldFee;

    private BigDecimal fee;

    private BigDecimal discntFee;

    private String payChannel;

    private String payState;

    private Date createDate;

    private Date updateDate;

    private BigDecimal payMoney;

    private String tradeType;

    private String poundAge;

    private String tradeStatus;

    private String wslPayTag;

}
