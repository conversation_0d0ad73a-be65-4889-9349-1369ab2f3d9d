package com.jsunicom.oms.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SchoolCcpShopTableExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SchoolCcpShopTableExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andMonthIdIsNull() {
            addCriterion("month_id is null");
            return (Criteria) this;
        }

        public Criteria andMonthIdIsNotNull() {
            addCriterion("month_id is not null");
            return (Criteria) this;
        }

        public Criteria andMonthIdEqualTo(String value) {
            addCriterion("month_id =", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdNotEqualTo(String value) {
            addCriterion("month_id <>", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdGreaterThan(String value) {
            addCriterion("month_id >", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdGreaterThanOrEqualTo(String value) {
            addCriterion("month_id >=", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdLessThan(String value) {
            addCriterion("month_id <", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdLessThanOrEqualTo(String value) {
            addCriterion("month_id <=", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdLike(String value) {
            addCriterion("month_id like", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdNotLike(String value) {
            addCriterion("month_id not like", value, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdIn(List<String> values) {
            addCriterion("month_id in", values, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdNotIn(List<String> values) {
            addCriterion("month_id not in", values, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdBetween(String value1, String value2) {
            addCriterion("month_id between", value1, value2, "monthId");
            return (Criteria) this;
        }

        public Criteria andMonthIdNotBetween(String value1, String value2) {
            addCriterion("month_id not between", value1, value2, "monthId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdEqualTo(String value) {
            addCriterion("merchant_id =", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotEqualTo(String value) {
            addCriterion("merchant_id <>", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThan(String value) {
            addCriterion("merchant_id >", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_id >=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThan(String value) {
            addCriterion("merchant_id <", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThanOrEqualTo(String value) {
            addCriterion("merchant_id <=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLike(String value) {
            addCriterion("merchant_id like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotLike(String value) {
            addCriterion("merchant_id not like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIn(List<String> values) {
            addCriterion("merchant_id in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotIn(List<String> values) {
            addCriterion("merchant_id not in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdBetween(String value1, String value2) {
            addCriterion("merchant_id between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotBetween(String value1, String value2) {
            addCriterion("merchant_id not between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andProvIdIsNull() {
            addCriterion("prov_id is null");
            return (Criteria) this;
        }

        public Criteria andProvIdIsNotNull() {
            addCriterion("prov_id is not null");
            return (Criteria) this;
        }

        public Criteria andProvIdEqualTo(String value) {
            addCriterion("prov_id =", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdNotEqualTo(String value) {
            addCriterion("prov_id <>", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdGreaterThan(String value) {
            addCriterion("prov_id >", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdGreaterThanOrEqualTo(String value) {
            addCriterion("prov_id >=", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdLessThan(String value) {
            addCriterion("prov_id <", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdLessThanOrEqualTo(String value) {
            addCriterion("prov_id <=", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdLike(String value) {
            addCriterion("prov_id like", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdNotLike(String value) {
            addCriterion("prov_id not like", value, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdIn(List<String> values) {
            addCriterion("prov_id in", values, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdNotIn(List<String> values) {
            addCriterion("prov_id not in", values, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdBetween(String value1, String value2) {
            addCriterion("prov_id between", value1, value2, "provId");
            return (Criteria) this;
        }

        public Criteria andProvIdNotBetween(String value1, String value2) {
            addCriterion("prov_id not between", value1, value2, "provId");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeIsNull() {
            addCriterion("org_prov_code is null");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeIsNotNull() {
            addCriterion("org_prov_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeEqualTo(String value) {
            addCriterion("org_prov_code =", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeNotEqualTo(String value) {
            addCriterion("org_prov_code <>", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeGreaterThan(String value) {
            addCriterion("org_prov_code >", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeGreaterThanOrEqualTo(String value) {
            addCriterion("org_prov_code >=", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeLessThan(String value) {
            addCriterion("org_prov_code <", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeLessThanOrEqualTo(String value) {
            addCriterion("org_prov_code <=", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeLike(String value) {
            addCriterion("org_prov_code like", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeNotLike(String value) {
            addCriterion("org_prov_code not like", value, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeIn(List<String> values) {
            addCriterion("org_prov_code in", values, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeNotIn(List<String> values) {
            addCriterion("org_prov_code not in", values, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeBetween(String value1, String value2) {
            addCriterion("org_prov_code between", value1, value2, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvCodeNotBetween(String value1, String value2) {
            addCriterion("org_prov_code not between", value1, value2, "orgProvCode");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameIsNull() {
            addCriterion("org_prov_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameIsNotNull() {
            addCriterion("org_prov_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameEqualTo(String value) {
            addCriterion("org_prov_name =", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameNotEqualTo(String value) {
            addCriterion("org_prov_name <>", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameGreaterThan(String value) {
            addCriterion("org_prov_name >", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_prov_name >=", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameLessThan(String value) {
            addCriterion("org_prov_name <", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameLessThanOrEqualTo(String value) {
            addCriterion("org_prov_name <=", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameLike(String value) {
            addCriterion("org_prov_name like", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameNotLike(String value) {
            addCriterion("org_prov_name not like", value, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameIn(List<String> values) {
            addCriterion("org_prov_name in", values, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameNotIn(List<String> values) {
            addCriterion("org_prov_name not in", values, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameBetween(String value1, String value2) {
            addCriterion("org_prov_name between", value1, value2, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgProvNameNotBetween(String value1, String value2) {
            addCriterion("org_prov_name not between", value1, value2, "orgProvName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeIsNull() {
            addCriterion("org_area_code is null");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeIsNotNull() {
            addCriterion("org_area_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeEqualTo(String value) {
            addCriterion("org_area_code =", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeNotEqualTo(String value) {
            addCriterion("org_area_code <>", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeGreaterThan(String value) {
            addCriterion("org_area_code >", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("org_area_code >=", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeLessThan(String value) {
            addCriterion("org_area_code <", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeLessThanOrEqualTo(String value) {
            addCriterion("org_area_code <=", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeLike(String value) {
            addCriterion("org_area_code like", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeNotLike(String value) {
            addCriterion("org_area_code not like", value, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeIn(List<String> values) {
            addCriterion("org_area_code in", values, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeNotIn(List<String> values) {
            addCriterion("org_area_code not in", values, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeBetween(String value1, String value2) {
            addCriterion("org_area_code between", value1, value2, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaCodeNotBetween(String value1, String value2) {
            addCriterion("org_area_code not between", value1, value2, "orgAreaCode");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameIsNull() {
            addCriterion("org_area_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameIsNotNull() {
            addCriterion("org_area_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameEqualTo(String value) {
            addCriterion("org_area_name =", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameNotEqualTo(String value) {
            addCriterion("org_area_name <>", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameGreaterThan(String value) {
            addCriterion("org_area_name >", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_area_name >=", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameLessThan(String value) {
            addCriterion("org_area_name <", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameLessThanOrEqualTo(String value) {
            addCriterion("org_area_name <=", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameLike(String value) {
            addCriterion("org_area_name like", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameNotLike(String value) {
            addCriterion("org_area_name not like", value, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameIn(List<String> values) {
            addCriterion("org_area_name in", values, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameNotIn(List<String> values) {
            addCriterion("org_area_name not in", values, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameBetween(String value1, String value2) {
            addCriterion("org_area_name between", value1, value2, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgAreaNameNotBetween(String value1, String value2) {
            addCriterion("org_area_name not between", value1, value2, "orgAreaName");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeIsNull() {
            addCriterion("org_city_code is null");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeIsNotNull() {
            addCriterion("org_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeEqualTo(String value) {
            addCriterion("org_city_code =", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeNotEqualTo(String value) {
            addCriterion("org_city_code <>", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeGreaterThan(String value) {
            addCriterion("org_city_code >", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("org_city_code >=", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeLessThan(String value) {
            addCriterion("org_city_code <", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeLessThanOrEqualTo(String value) {
            addCriterion("org_city_code <=", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeLike(String value) {
            addCriterion("org_city_code like", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeNotLike(String value) {
            addCriterion("org_city_code not like", value, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeIn(List<String> values) {
            addCriterion("org_city_code in", values, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeNotIn(List<String> values) {
            addCriterion("org_city_code not in", values, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeBetween(String value1, String value2) {
            addCriterion("org_city_code between", value1, value2, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityCodeNotBetween(String value1, String value2) {
            addCriterion("org_city_code not between", value1, value2, "orgCityCode");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameIsNull() {
            addCriterion("org_city_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameIsNotNull() {
            addCriterion("org_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameEqualTo(String value) {
            addCriterion("org_city_name =", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameNotEqualTo(String value) {
            addCriterion("org_city_name <>", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameGreaterThan(String value) {
            addCriterion("org_city_name >", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_city_name >=", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameLessThan(String value) {
            addCriterion("org_city_name <", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameLessThanOrEqualTo(String value) {
            addCriterion("org_city_name <=", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameLike(String value) {
            addCriterion("org_city_name like", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameNotLike(String value) {
            addCriterion("org_city_name not like", value, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameIn(List<String> values) {
            addCriterion("org_city_name in", values, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameNotIn(List<String> values) {
            addCriterion("org_city_name not in", values, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameBetween(String value1, String value2) {
            addCriterion("org_city_name between", value1, value2, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgCityNameNotBetween(String value1, String value2) {
            addCriterion("org_city_name not between", value1, value2, "orgCityName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeIsNull() {
            addCriterion("org_street_code is null");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeIsNotNull() {
            addCriterion("org_street_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeEqualTo(String value) {
            addCriterion("org_street_code =", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeNotEqualTo(String value) {
            addCriterion("org_street_code <>", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeGreaterThan(String value) {
            addCriterion("org_street_code >", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeGreaterThanOrEqualTo(String value) {
            addCriterion("org_street_code >=", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeLessThan(String value) {
            addCriterion("org_street_code <", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeLessThanOrEqualTo(String value) {
            addCriterion("org_street_code <=", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeLike(String value) {
            addCriterion("org_street_code like", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeNotLike(String value) {
            addCriterion("org_street_code not like", value, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeIn(List<String> values) {
            addCriterion("org_street_code in", values, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeNotIn(List<String> values) {
            addCriterion("org_street_code not in", values, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeBetween(String value1, String value2) {
            addCriterion("org_street_code between", value1, value2, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetCodeNotBetween(String value1, String value2) {
            addCriterion("org_street_code not between", value1, value2, "orgStreetCode");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameIsNull() {
            addCriterion("org_street_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameIsNotNull() {
            addCriterion("org_street_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameEqualTo(String value) {
            addCriterion("org_street_name =", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameNotEqualTo(String value) {
            addCriterion("org_street_name <>", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameGreaterThan(String value) {
            addCriterion("org_street_name >", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_street_name >=", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameLessThan(String value) {
            addCriterion("org_street_name <", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameLessThanOrEqualTo(String value) {
            addCriterion("org_street_name <=", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameLike(String value) {
            addCriterion("org_street_name like", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameNotLike(String value) {
            addCriterion("org_street_name not like", value, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameIn(List<String> values) {
            addCriterion("org_street_name in", values, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameNotIn(List<String> values) {
            addCriterion("org_street_name not in", values, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameBetween(String value1, String value2) {
            addCriterion("org_street_name between", value1, value2, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andOrgStreetNameNotBetween(String value1, String value2) {
            addCriterion("org_street_name not between", value1, value2, "orgStreetName");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeIsNull() {
            addCriterion("merchant_type is null");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeIsNotNull() {
            addCriterion("merchant_type is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeEqualTo(String value) {
            addCriterion("merchant_type =", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeNotEqualTo(String value) {
            addCriterion("merchant_type <>", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeGreaterThan(String value) {
            addCriterion("merchant_type >", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_type >=", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeLessThan(String value) {
            addCriterion("merchant_type <", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeLessThanOrEqualTo(String value) {
            addCriterion("merchant_type <=", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeLike(String value) {
            addCriterion("merchant_type like", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeNotLike(String value) {
            addCriterion("merchant_type not like", value, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeIn(List<String> values) {
            addCriterion("merchant_type in", values, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeNotIn(List<String> values) {
            addCriterion("merchant_type not in", values, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeBetween(String value1, String value2) {
            addCriterion("merchant_type between", value1, value2, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantTypeNotBetween(String value1, String value2) {
            addCriterion("merchant_type not between", value1, value2, "merchantType");
            return (Criteria) this;
        }

        public Criteria andMerchantNameIsNull() {
            addCriterion("merchant_name is null");
            return (Criteria) this;
        }

        public Criteria andMerchantNameIsNotNull() {
            addCriterion("merchant_name is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantNameEqualTo(String value) {
            addCriterion("merchant_name =", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameNotEqualTo(String value) {
            addCriterion("merchant_name <>", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameGreaterThan(String value) {
            addCriterion("merchant_name >", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_name >=", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameLessThan(String value) {
            addCriterion("merchant_name <", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameLessThanOrEqualTo(String value) {
            addCriterion("merchant_name <=", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameLike(String value) {
            addCriterion("merchant_name like", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameNotLike(String value) {
            addCriterion("merchant_name not like", value, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameIn(List<String> values) {
            addCriterion("merchant_name in", values, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameNotIn(List<String> values) {
            addCriterion("merchant_name not in", values, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameBetween(String value1, String value2) {
            addCriterion("merchant_name between", value1, value2, "merchantName");
            return (Criteria) this;
        }

        public Criteria andMerchantNameNotBetween(String value1, String value2) {
            addCriterion("merchant_name not between", value1, value2, "merchantName");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseIsNull() {
            addCriterion("busi_license is null");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseIsNotNull() {
            addCriterion("busi_license is not null");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseEqualTo(String value) {
            addCriterion("busi_license =", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseNotEqualTo(String value) {
            addCriterion("busi_license <>", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseGreaterThan(String value) {
            addCriterion("busi_license >", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("busi_license >=", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseLessThan(String value) {
            addCriterion("busi_license <", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseLessThanOrEqualTo(String value) {
            addCriterion("busi_license <=", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseLike(String value) {
            addCriterion("busi_license like", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseNotLike(String value) {
            addCriterion("busi_license not like", value, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseIn(List<String> values) {
            addCriterion("busi_license in", values, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseNotIn(List<String> values) {
            addCriterion("busi_license not in", values, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseBetween(String value1, String value2) {
            addCriterion("busi_license between", value1, value2, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andBusiLicenseNotBetween(String value1, String value2) {
            addCriterion("busi_license not between", value1, value2, "busiLicense");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameIsNull() {
            addCriterion("principal_name is null");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameIsNotNull() {
            addCriterion("principal_name is not null");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameEqualTo(String value) {
            addCriterion("principal_name =", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameNotEqualTo(String value) {
            addCriterion("principal_name <>", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameGreaterThan(String value) {
            addCriterion("principal_name >", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameGreaterThanOrEqualTo(String value) {
            addCriterion("principal_name >=", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameLessThan(String value) {
            addCriterion("principal_name <", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameLessThanOrEqualTo(String value) {
            addCriterion("principal_name <=", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameLike(String value) {
            addCriterion("principal_name like", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameNotLike(String value) {
            addCriterion("principal_name not like", value, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameIn(List<String> values) {
            addCriterion("principal_name in", values, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameNotIn(List<String> values) {
            addCriterion("principal_name not in", values, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameBetween(String value1, String value2) {
            addCriterion("principal_name between", value1, value2, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalNameNotBetween(String value1, String value2) {
            addCriterion("principal_name not between", value1, value2, "principalName");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneIsNull() {
            addCriterion("principal_phone is null");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneIsNotNull() {
            addCriterion("principal_phone is not null");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneEqualTo(String value) {
            addCriterion("principal_phone =", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneNotEqualTo(String value) {
            addCriterion("principal_phone <>", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneGreaterThan(String value) {
            addCriterion("principal_phone >", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("principal_phone >=", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneLessThan(String value) {
            addCriterion("principal_phone <", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneLessThanOrEqualTo(String value) {
            addCriterion("principal_phone <=", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneLike(String value) {
            addCriterion("principal_phone like", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneNotLike(String value) {
            addCriterion("principal_phone not like", value, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneIn(List<String> values) {
            addCriterion("principal_phone in", values, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneNotIn(List<String> values) {
            addCriterion("principal_phone not in", values, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneBetween(String value1, String value2) {
            addCriterion("principal_phone between", value1, value2, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andPrincipalPhoneNotBetween(String value1, String value2) {
            addCriterion("principal_phone not between", value1, value2, "principalPhone");
            return (Criteria) this;
        }

        public Criteria andRoadNameIsNull() {
            addCriterion("road_name is null");
            return (Criteria) this;
        }

        public Criteria andRoadNameIsNotNull() {
            addCriterion("road_name is not null");
            return (Criteria) this;
        }

        public Criteria andRoadNameEqualTo(String value) {
            addCriterion("road_name =", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameNotEqualTo(String value) {
            addCriterion("road_name <>", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameGreaterThan(String value) {
            addCriterion("road_name >", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameGreaterThanOrEqualTo(String value) {
            addCriterion("road_name >=", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameLessThan(String value) {
            addCriterion("road_name <", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameLessThanOrEqualTo(String value) {
            addCriterion("road_name <=", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameLike(String value) {
            addCriterion("road_name like", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameNotLike(String value) {
            addCriterion("road_name not like", value, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameIn(List<String> values) {
            addCriterion("road_name in", values, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameNotIn(List<String> values) {
            addCriterion("road_name not in", values, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameBetween(String value1, String value2) {
            addCriterion("road_name between", value1, value2, "roadName");
            return (Criteria) this;
        }

        public Criteria andRoadNameNotBetween(String value1, String value2) {
            addCriterion("road_name not between", value1, value2, "roadName");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeIsNull() {
            addCriterion("merchant_scope is null");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeIsNotNull() {
            addCriterion("merchant_scope is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeEqualTo(String value) {
            addCriterion("merchant_scope =", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeNotEqualTo(String value) {
            addCriterion("merchant_scope <>", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeGreaterThan(String value) {
            addCriterion("merchant_scope >", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_scope >=", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeLessThan(String value) {
            addCriterion("merchant_scope <", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeLessThanOrEqualTo(String value) {
            addCriterion("merchant_scope <=", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeLike(String value) {
            addCriterion("merchant_scope like", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeNotLike(String value) {
            addCriterion("merchant_scope not like", value, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeIn(List<String> values) {
            addCriterion("merchant_scope in", values, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeNotIn(List<String> values) {
            addCriterion("merchant_scope not in", values, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeBetween(String value1, String value2) {
            addCriterion("merchant_scope between", value1, value2, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantScopeNotBetween(String value1, String value2) {
            addCriterion("merchant_scope not between", value1, value2, "merchantScope");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressIsNull() {
            addCriterion("merchant_address is null");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressIsNotNull() {
            addCriterion("merchant_address is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressEqualTo(String value) {
            addCriterion("merchant_address =", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressNotEqualTo(String value) {
            addCriterion("merchant_address <>", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressGreaterThan(String value) {
            addCriterion("merchant_address >", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_address >=", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressLessThan(String value) {
            addCriterion("merchant_address <", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressLessThanOrEqualTo(String value) {
            addCriterion("merchant_address <=", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressLike(String value) {
            addCriterion("merchant_address like", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressNotLike(String value) {
            addCriterion("merchant_address not like", value, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressIn(List<String> values) {
            addCriterion("merchant_address in", values, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressNotIn(List<String> values) {
            addCriterion("merchant_address not in", values, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressBetween(String value1, String value2) {
            addCriterion("merchant_address between", value1, value2, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andMerchantAddressNotBetween(String value1, String value2) {
            addCriterion("merchant_address not between", value1, value2, "merchantAddress");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationIsNull() {
            addCriterion("customer_relation is null");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationIsNotNull() {
            addCriterion("customer_relation is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationEqualTo(String value) {
            addCriterion("customer_relation =", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationNotEqualTo(String value) {
            addCriterion("customer_relation <>", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationGreaterThan(String value) {
            addCriterion("customer_relation >", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationGreaterThanOrEqualTo(String value) {
            addCriterion("customer_relation >=", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationLessThan(String value) {
            addCriterion("customer_relation <", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationLessThanOrEqualTo(String value) {
            addCriterion("customer_relation <=", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationLike(String value) {
            addCriterion("customer_relation like", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationNotLike(String value) {
            addCriterion("customer_relation not like", value, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationIn(List<String> values) {
            addCriterion("customer_relation in", values, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationNotIn(List<String> values) {
            addCriterion("customer_relation not in", values, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationBetween(String value1, String value2) {
            addCriterion("customer_relation between", value1, value2, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andCustomerRelationNotBetween(String value1, String value2) {
            addCriterion("customer_relation not between", value1, value2, "customerRelation");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandIsNull() {
            addCriterion("is_has_wideband is null");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandIsNotNull() {
            addCriterion("is_has_wideband is not null");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandEqualTo(String value) {
            addCriterion("is_has_wideband =", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandNotEqualTo(String value) {
            addCriterion("is_has_wideband <>", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandGreaterThan(String value) {
            addCriterion("is_has_wideband >", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandGreaterThanOrEqualTo(String value) {
            addCriterion("is_has_wideband >=", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandLessThan(String value) {
            addCriterion("is_has_wideband <", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandLessThanOrEqualTo(String value) {
            addCriterion("is_has_wideband <=", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandLike(String value) {
            addCriterion("is_has_wideband like", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandNotLike(String value) {
            addCriterion("is_has_wideband not like", value, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandIn(List<String> values) {
            addCriterion("is_has_wideband in", values, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandNotIn(List<String> values) {
            addCriterion("is_has_wideband not in", values, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandBetween(String value1, String value2) {
            addCriterion("is_has_wideband between", value1, value2, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andIsHasWidebandNotBetween(String value1, String value2) {
            addCriterion("is_has_wideband not between", value1, value2, "isHasWideband");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeIsNull() {
            addCriterion("wideband_type is null");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeIsNotNull() {
            addCriterion("wideband_type is not null");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeEqualTo(String value) {
            addCriterion("wideband_type =", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeNotEqualTo(String value) {
            addCriterion("wideband_type <>", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeGreaterThan(String value) {
            addCriterion("wideband_type >", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeGreaterThanOrEqualTo(String value) {
            addCriterion("wideband_type >=", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeLessThan(String value) {
            addCriterion("wideband_type <", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeLessThanOrEqualTo(String value) {
            addCriterion("wideband_type <=", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeLike(String value) {
            addCriterion("wideband_type like", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeNotLike(String value) {
            addCriterion("wideband_type not like", value, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeIn(List<String> values) {
            addCriterion("wideband_type in", values, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeNotIn(List<String> values) {
            addCriterion("wideband_type not in", values, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeBetween(String value1, String value2) {
            addCriterion("wideband_type between", value1, value2, "widebandType");
            return (Criteria) this;
        }

        public Criteria andWidebandTypeNotBetween(String value1, String value2) {
            addCriterion("wideband_type not between", value1, value2, "widebandType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeIsNull() {
            addCriterion("phone_type is null");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeIsNotNull() {
            addCriterion("phone_type is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeEqualTo(String value) {
            addCriterion("phone_type =", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeNotEqualTo(String value) {
            addCriterion("phone_type <>", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeGreaterThan(String value) {
            addCriterion("phone_type >", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeGreaterThanOrEqualTo(String value) {
            addCriterion("phone_type >=", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeLessThan(String value) {
            addCriterion("phone_type <", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeLessThanOrEqualTo(String value) {
            addCriterion("phone_type <=", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeLike(String value) {
            addCriterion("phone_type like", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeNotLike(String value) {
            addCriterion("phone_type not like", value, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeIn(List<String> values) {
            addCriterion("phone_type in", values, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeNotIn(List<String> values) {
            addCriterion("phone_type not in", values, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeBetween(String value1, String value2) {
            addCriterion("phone_type between", value1, value2, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneTypeNotBetween(String value1, String value2) {
            addCriterion("phone_type not between", value1, value2, "phoneType");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalIsNull() {
            addCriterion("phone_signal is null");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalIsNotNull() {
            addCriterion("phone_signal is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalEqualTo(String value) {
            addCriterion("phone_signal =", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalNotEqualTo(String value) {
            addCriterion("phone_signal <>", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalGreaterThan(String value) {
            addCriterion("phone_signal >", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalGreaterThanOrEqualTo(String value) {
            addCriterion("phone_signal >=", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalLessThan(String value) {
            addCriterion("phone_signal <", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalLessThanOrEqualTo(String value) {
            addCriterion("phone_signal <=", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalLike(String value) {
            addCriterion("phone_signal like", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalNotLike(String value) {
            addCriterion("phone_signal not like", value, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalIn(List<String> values) {
            addCriterion("phone_signal in", values, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalNotIn(List<String> values) {
            addCriterion("phone_signal not in", values, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalBetween(String value1, String value2) {
            addCriterion("phone_signal between", value1, value2, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andPhoneSignalNotBetween(String value1, String value2) {
            addCriterion("phone_signal not between", value1, value2, "phoneSignal");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneIsNull() {
            addCriterion("street_manager_phone is null");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneIsNotNull() {
            addCriterion("street_manager_phone is not null");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneEqualTo(String value) {
            addCriterion("street_manager_phone =", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneNotEqualTo(String value) {
            addCriterion("street_manager_phone <>", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneGreaterThan(String value) {
            addCriterion("street_manager_phone >", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("street_manager_phone >=", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneLessThan(String value) {
            addCriterion("street_manager_phone <", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneLessThanOrEqualTo(String value) {
            addCriterion("street_manager_phone <=", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneLike(String value) {
            addCriterion("street_manager_phone like", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneNotLike(String value) {
            addCriterion("street_manager_phone not like", value, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneIn(List<String> values) {
            addCriterion("street_manager_phone in", values, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneNotIn(List<String> values) {
            addCriterion("street_manager_phone not in", values, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneBetween(String value1, String value2) {
            addCriterion("street_manager_phone between", value1, value2, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerPhoneNotBetween(String value1, String value2) {
            addCriterion("street_manager_phone not between", value1, value2, "streetManagerPhone");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameIsNull() {
            addCriterion("street_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameIsNotNull() {
            addCriterion("street_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameEqualTo(String value) {
            addCriterion("street_manager_name =", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameNotEqualTo(String value) {
            addCriterion("street_manager_name <>", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameGreaterThan(String value) {
            addCriterion("street_manager_name >", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("street_manager_name >=", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameLessThan(String value) {
            addCriterion("street_manager_name <", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameLessThanOrEqualTo(String value) {
            addCriterion("street_manager_name <=", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameLike(String value) {
            addCriterion("street_manager_name like", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameNotLike(String value) {
            addCriterion("street_manager_name not like", value, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameIn(List<String> values) {
            addCriterion("street_manager_name in", values, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameNotIn(List<String> values) {
            addCriterion("street_manager_name not in", values, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameBetween(String value1, String value2) {
            addCriterion("street_manager_name between", value1, value2, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andStreetManagerNameNotBetween(String value1, String value2) {
            addCriterion("street_manager_name not between", value1, value2, "streetManagerName");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationIsNull() {
            addCriterion("busi_classification is null");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationIsNotNull() {
            addCriterion("busi_classification is not null");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationEqualTo(String value) {
            addCriterion("busi_classification =", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationNotEqualTo(String value) {
            addCriterion("busi_classification <>", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationGreaterThan(String value) {
            addCriterion("busi_classification >", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("busi_classification >=", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationLessThan(String value) {
            addCriterion("busi_classification <", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationLessThanOrEqualTo(String value) {
            addCriterion("busi_classification <=", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationLike(String value) {
            addCriterion("busi_classification like", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationNotLike(String value) {
            addCriterion("busi_classification not like", value, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationIn(List<String> values) {
            addCriterion("busi_classification in", values, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationNotIn(List<String> values) {
            addCriterion("busi_classification not in", values, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationBetween(String value1, String value2) {
            addCriterion("busi_classification between", value1, value2, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andBusiClassificationNotBetween(String value1, String value2) {
            addCriterion("busi_classification not between", value1, value2, "busiClassification");
            return (Criteria) this;
        }

        public Criteria andDevelopIdIsNull() {
            addCriterion("develop_id is null");
            return (Criteria) this;
        }

        public Criteria andDevelopIdIsNotNull() {
            addCriterion("develop_id is not null");
            return (Criteria) this;
        }

        public Criteria andDevelopIdEqualTo(String value) {
            addCriterion("develop_id =", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdNotEqualTo(String value) {
            addCriterion("develop_id <>", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdGreaterThan(String value) {
            addCriterion("develop_id >", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdGreaterThanOrEqualTo(String value) {
            addCriterion("develop_id >=", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdLessThan(String value) {
            addCriterion("develop_id <", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdLessThanOrEqualTo(String value) {
            addCriterion("develop_id <=", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdLike(String value) {
            addCriterion("develop_id like", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdNotLike(String value) {
            addCriterion("develop_id not like", value, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdIn(List<String> values) {
            addCriterion("develop_id in", values, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdNotIn(List<String> values) {
            addCriterion("develop_id not in", values, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdBetween(String value1, String value2) {
            addCriterion("develop_id between", value1, value2, "developId");
            return (Criteria) this;
        }

        public Criteria andDevelopIdNotBetween(String value1, String value2) {
            addCriterion("develop_id not between", value1, value2, "developId");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNull() {
            addCriterion("longitude is null");
            return (Criteria) this;
        }

        public Criteria andLongitudeIsNotNull() {
            addCriterion("longitude is not null");
            return (Criteria) this;
        }

        public Criteria andLongitudeEqualTo(String value) {
            addCriterion("longitude =", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotEqualTo(String value) {
            addCriterion("longitude <>", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThan(String value) {
            addCriterion("longitude >", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeGreaterThanOrEqualTo(String value) {
            addCriterion("longitude >=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThan(String value) {
            addCriterion("longitude <", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLessThanOrEqualTo(String value) {
            addCriterion("longitude <=", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeLike(String value) {
            addCriterion("longitude like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotLike(String value) {
            addCriterion("longitude not like", value, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeIn(List<String> values) {
            addCriterion("longitude in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotIn(List<String> values) {
            addCriterion("longitude not in", values, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeBetween(String value1, String value2) {
            addCriterion("longitude between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLongitudeNotBetween(String value1, String value2) {
            addCriterion("longitude not between", value1, value2, "longitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNull() {
            addCriterion("latitude is null");
            return (Criteria) this;
        }

        public Criteria andLatitudeIsNotNull() {
            addCriterion("latitude is not null");
            return (Criteria) this;
        }

        public Criteria andLatitudeEqualTo(String value) {
            addCriterion("latitude =", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotEqualTo(String value) {
            addCriterion("latitude <>", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThan(String value) {
            addCriterion("latitude >", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeGreaterThanOrEqualTo(String value) {
            addCriterion("latitude >=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThan(String value) {
            addCriterion("latitude <", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLessThanOrEqualTo(String value) {
            addCriterion("latitude <=", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeLike(String value) {
            addCriterion("latitude like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotLike(String value) {
            addCriterion("latitude not like", value, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeIn(List<String> values) {
            addCriterion("latitude in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotIn(List<String> values) {
            addCriterion("latitude not in", values, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeBetween(String value1, String value2) {
            addCriterion("latitude between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andLatitudeNotBetween(String value1, String value2) {
            addCriterion("latitude not between", value1, value2, "latitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeIsNull() {
            addCriterion("baidu_longitude is null");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeIsNotNull() {
            addCriterion("baidu_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeEqualTo(String value) {
            addCriterion("baidu_longitude =", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeNotEqualTo(String value) {
            addCriterion("baidu_longitude <>", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeGreaterThan(String value) {
            addCriterion("baidu_longitude >", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeGreaterThanOrEqualTo(String value) {
            addCriterion("baidu_longitude >=", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeLessThan(String value) {
            addCriterion("baidu_longitude <", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeLessThanOrEqualTo(String value) {
            addCriterion("baidu_longitude <=", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeLike(String value) {
            addCriterion("baidu_longitude like", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeNotLike(String value) {
            addCriterion("baidu_longitude not like", value, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeIn(List<String> values) {
            addCriterion("baidu_longitude in", values, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeNotIn(List<String> values) {
            addCriterion("baidu_longitude not in", values, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeBetween(String value1, String value2) {
            addCriterion("baidu_longitude between", value1, value2, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLongitudeNotBetween(String value1, String value2) {
            addCriterion("baidu_longitude not between", value1, value2, "baiduLongitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeIsNull() {
            addCriterion("baidu_latitude is null");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeIsNotNull() {
            addCriterion("baidu_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeEqualTo(String value) {
            addCriterion("baidu_latitude =", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeNotEqualTo(String value) {
            addCriterion("baidu_latitude <>", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeGreaterThan(String value) {
            addCriterion("baidu_latitude >", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeGreaterThanOrEqualTo(String value) {
            addCriterion("baidu_latitude >=", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeLessThan(String value) {
            addCriterion("baidu_latitude <", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeLessThanOrEqualTo(String value) {
            addCriterion("baidu_latitude <=", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeLike(String value) {
            addCriterion("baidu_latitude like", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeNotLike(String value) {
            addCriterion("baidu_latitude not like", value, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeIn(List<String> values) {
            addCriterion("baidu_latitude in", values, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeNotIn(List<String> values) {
            addCriterion("baidu_latitude not in", values, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeBetween(String value1, String value2) {
            addCriterion("baidu_latitude between", value1, value2, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andBaiduLatitudeNotBetween(String value1, String value2) {
            addCriterion("baidu_latitude not between", value1, value2, "baiduLatitude");
            return (Criteria) this;
        }

        public Criteria andRemarkDescIsNull() {
            addCriterion("remark_desc is null");
            return (Criteria) this;
        }

        public Criteria andRemarkDescIsNotNull() {
            addCriterion("remark_desc is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkDescEqualTo(String value) {
            addCriterion("remark_desc =", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescNotEqualTo(String value) {
            addCriterion("remark_desc <>", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescGreaterThan(String value) {
            addCriterion("remark_desc >", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescGreaterThanOrEqualTo(String value) {
            addCriterion("remark_desc >=", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescLessThan(String value) {
            addCriterion("remark_desc <", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescLessThanOrEqualTo(String value) {
            addCriterion("remark_desc <=", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescLike(String value) {
            addCriterion("remark_desc like", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescNotLike(String value) {
            addCriterion("remark_desc not like", value, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescIn(List<String> values) {
            addCriterion("remark_desc in", values, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescNotIn(List<String> values) {
            addCriterion("remark_desc not in", values, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescBetween(String value1, String value2) {
            addCriterion("remark_desc between", value1, value2, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andRemarkDescNotBetween(String value1, String value2) {
            addCriterion("remark_desc not between", value1, value2, "remarkDesc");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(String value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(String value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(String value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(String value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(String value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(String value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLike(String value) {
            addCriterion("delete_flag like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotLike(String value) {
            addCriterion("delete_flag not like", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<String> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<String> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(String value1, String value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(String value1, String value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneIsNull() {
            addCriterion("org_strt_manag_phone is null");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneIsNotNull() {
            addCriterion("org_strt_manag_phone is not null");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneEqualTo(String value) {
            addCriterion("org_strt_manag_phone =", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneNotEqualTo(String value) {
            addCriterion("org_strt_manag_phone <>", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneGreaterThan(String value) {
            addCriterion("org_strt_manag_phone >", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("org_strt_manag_phone >=", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneLessThan(String value) {
            addCriterion("org_strt_manag_phone <", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneLessThanOrEqualTo(String value) {
            addCriterion("org_strt_manag_phone <=", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneLike(String value) {
            addCriterion("org_strt_manag_phone like", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneNotLike(String value) {
            addCriterion("org_strt_manag_phone not like", value, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneIn(List<String> values) {
            addCriterion("org_strt_manag_phone in", values, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneNotIn(List<String> values) {
            addCriterion("org_strt_manag_phone not in", values, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneBetween(String value1, String value2) {
            addCriterion("org_strt_manag_phone between", value1, value2, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagPhoneNotBetween(String value1, String value2) {
            addCriterion("org_strt_manag_phone not between", value1, value2, "orgStrtManagPhone");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameIsNull() {
            addCriterion("org_strt_manag_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameIsNotNull() {
            addCriterion("org_strt_manag_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameEqualTo(String value) {
            addCriterion("org_strt_manag_name =", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameNotEqualTo(String value) {
            addCriterion("org_strt_manag_name <>", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameGreaterThan(String value) {
            addCriterion("org_strt_manag_name >", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_strt_manag_name >=", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameLessThan(String value) {
            addCriterion("org_strt_manag_name <", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameLessThanOrEqualTo(String value) {
            addCriterion("org_strt_manag_name <=", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameLike(String value) {
            addCriterion("org_strt_manag_name like", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameNotLike(String value) {
            addCriterion("org_strt_manag_name not like", value, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameIn(List<String> values) {
            addCriterion("org_strt_manag_name in", values, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameNotIn(List<String> values) {
            addCriterion("org_strt_manag_name not in", values, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameBetween(String value1, String value2) {
            addCriterion("org_strt_manag_name between", value1, value2, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andOrgStrtManagNameNotBetween(String value1, String value2) {
            addCriterion("org_strt_manag_name not between", value1, value2, "orgStrtManagName");
            return (Criteria) this;
        }

        public Criteria andDistriTimeIsNull() {
            addCriterion("distri_time is null");
            return (Criteria) this;
        }

        public Criteria andDistriTimeIsNotNull() {
            addCriterion("distri_time is not null");
            return (Criteria) this;
        }

        public Criteria andDistriTimeEqualTo(String value) {
            addCriterion("distri_time =", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeNotEqualTo(String value) {
            addCriterion("distri_time <>", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeGreaterThan(String value) {
            addCriterion("distri_time >", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeGreaterThanOrEqualTo(String value) {
            addCriterion("distri_time >=", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeLessThan(String value) {
            addCriterion("distri_time <", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeLessThanOrEqualTo(String value) {
            addCriterion("distri_time <=", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeLike(String value) {
            addCriterion("distri_time like", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeNotLike(String value) {
            addCriterion("distri_time not like", value, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeIn(List<String> values) {
            addCriterion("distri_time in", values, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeNotIn(List<String> values) {
            addCriterion("distri_time not in", values, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeBetween(String value1, String value2) {
            addCriterion("distri_time between", value1, value2, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriTimeNotBetween(String value1, String value2) {
            addCriterion("distri_time not between", value1, value2, "distriTime");
            return (Criteria) this;
        }

        public Criteria andDistriNameIsNull() {
            addCriterion("distri_name is null");
            return (Criteria) this;
        }

        public Criteria andDistriNameIsNotNull() {
            addCriterion("distri_name is not null");
            return (Criteria) this;
        }

        public Criteria andDistriNameEqualTo(String value) {
            addCriterion("distri_name =", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameNotEqualTo(String value) {
            addCriterion("distri_name <>", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameGreaterThan(String value) {
            addCriterion("distri_name >", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameGreaterThanOrEqualTo(String value) {
            addCriterion("distri_name >=", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameLessThan(String value) {
            addCriterion("distri_name <", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameLessThanOrEqualTo(String value) {
            addCriterion("distri_name <=", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameLike(String value) {
            addCriterion("distri_name like", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameNotLike(String value) {
            addCriterion("distri_name not like", value, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameIn(List<String> values) {
            addCriterion("distri_name in", values, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameNotIn(List<String> values) {
            addCriterion("distri_name not in", values, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameBetween(String value1, String value2) {
            addCriterion("distri_name between", value1, value2, "distriName");
            return (Criteria) this;
        }

        public Criteria andDistriNameNotBetween(String value1, String value2) {
            addCriterion("distri_name not between", value1, value2, "distriName");
            return (Criteria) this;
        }

        public Criteria andOriginTypeIsNull() {
            addCriterion("origin_type is null");
            return (Criteria) this;
        }

        public Criteria andOriginTypeIsNotNull() {
            addCriterion("origin_type is not null");
            return (Criteria) this;
        }

        public Criteria andOriginTypeEqualTo(String value) {
            addCriterion("origin_type =", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeNotEqualTo(String value) {
            addCriterion("origin_type <>", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeGreaterThan(String value) {
            addCriterion("origin_type >", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeGreaterThanOrEqualTo(String value) {
            addCriterion("origin_type >=", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeLessThan(String value) {
            addCriterion("origin_type <", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeLessThanOrEqualTo(String value) {
            addCriterion("origin_type <=", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeLike(String value) {
            addCriterion("origin_type like", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeNotLike(String value) {
            addCriterion("origin_type not like", value, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeIn(List<String> values) {
            addCriterion("origin_type in", values, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeNotIn(List<String> values) {
            addCriterion("origin_type not in", values, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeBetween(String value1, String value2) {
            addCriterion("origin_type between", value1, value2, "originType");
            return (Criteria) this;
        }

        public Criteria andOriginTypeNotBetween(String value1, String value2) {
            addCriterion("origin_type not between", value1, value2, "originType");
            return (Criteria) this;
        }

        public Criteria andMerchantStateIsNull() {
            addCriterion("merchant_state is null");
            return (Criteria) this;
        }

        public Criteria andMerchantStateIsNotNull() {
            addCriterion("merchant_state is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantStateEqualTo(String value) {
            addCriterion("merchant_state =", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateNotEqualTo(String value) {
            addCriterion("merchant_state <>", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateGreaterThan(String value) {
            addCriterion("merchant_state >", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_state >=", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateLessThan(String value) {
            addCriterion("merchant_state <", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateLessThanOrEqualTo(String value) {
            addCriterion("merchant_state <=", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateLike(String value) {
            addCriterion("merchant_state like", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateNotLike(String value) {
            addCriterion("merchant_state not like", value, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateIn(List<String> values) {
            addCriterion("merchant_state in", values, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateNotIn(List<String> values) {
            addCriterion("merchant_state not in", values, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateBetween(String value1, String value2) {
            addCriterion("merchant_state between", value1, value2, "merchantState");
            return (Criteria) this;
        }

        public Criteria andMerchantStateNotBetween(String value1, String value2) {
            addCriterion("merchant_state not between", value1, value2, "merchantState");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineIsNull() {
            addCriterion("wideband_deadline is null");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineIsNotNull() {
            addCriterion("wideband_deadline is not null");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineEqualTo(String value) {
            addCriterion("wideband_deadline =", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineNotEqualTo(String value) {
            addCriterion("wideband_deadline <>", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineGreaterThan(String value) {
            addCriterion("wideband_deadline >", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineGreaterThanOrEqualTo(String value) {
            addCriterion("wideband_deadline >=", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineLessThan(String value) {
            addCriterion("wideband_deadline <", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineLessThanOrEqualTo(String value) {
            addCriterion("wideband_deadline <=", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineLike(String value) {
            addCriterion("wideband_deadline like", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineNotLike(String value) {
            addCriterion("wideband_deadline not like", value, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineIn(List<String> values) {
            addCriterion("wideband_deadline in", values, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineNotIn(List<String> values) {
            addCriterion("wideband_deadline not in", values, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineBetween(String value1, String value2) {
            addCriterion("wideband_deadline between", value1, value2, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andWidebandDeadlineNotBetween(String value1, String value2) {
            addCriterion("wideband_deadline not between", value1, value2, "widebandDeadline");
            return (Criteria) this;
        }

        public Criteria andIsConvertIsNull() {
            addCriterion("is_convert is null");
            return (Criteria) this;
        }

        public Criteria andIsConvertIsNotNull() {
            addCriterion("is_convert is not null");
            return (Criteria) this;
        }

        public Criteria andIsConvertEqualTo(String value) {
            addCriterion("is_convert =", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertNotEqualTo(String value) {
            addCriterion("is_convert <>", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertGreaterThan(String value) {
            addCriterion("is_convert >", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertGreaterThanOrEqualTo(String value) {
            addCriterion("is_convert >=", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertLessThan(String value) {
            addCriterion("is_convert <", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertLessThanOrEqualTo(String value) {
            addCriterion("is_convert <=", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertLike(String value) {
            addCriterion("is_convert like", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertNotLike(String value) {
            addCriterion("is_convert not like", value, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertIn(List<String> values) {
            addCriterion("is_convert in", values, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertNotIn(List<String> values) {
            addCriterion("is_convert not in", values, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertBetween(String value1, String value2) {
            addCriterion("is_convert between", value1, value2, "isConvert");
            return (Criteria) this;
        }

        public Criteria andIsConvertNotBetween(String value1, String value2) {
            addCriterion("is_convert not between", value1, value2, "isConvert");
            return (Criteria) this;
        }

        public Criteria andMonthCostIsNull() {
            addCriterion("month_cost is null");
            return (Criteria) this;
        }

        public Criteria andMonthCostIsNotNull() {
            addCriterion("month_cost is not null");
            return (Criteria) this;
        }

        public Criteria andMonthCostEqualTo(String value) {
            addCriterion("month_cost =", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotEqualTo(String value) {
            addCriterion("month_cost <>", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostGreaterThan(String value) {
            addCriterion("month_cost >", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostGreaterThanOrEqualTo(String value) {
            addCriterion("month_cost >=", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostLessThan(String value) {
            addCriterion("month_cost <", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostLessThanOrEqualTo(String value) {
            addCriterion("month_cost <=", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostLike(String value) {
            addCriterion("month_cost like", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotLike(String value) {
            addCriterion("month_cost not like", value, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostIn(List<String> values) {
            addCriterion("month_cost in", values, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotIn(List<String> values) {
            addCriterion("month_cost not in", values, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostBetween(String value1, String value2) {
            addCriterion("month_cost between", value1, value2, "monthCost");
            return (Criteria) this;
        }

        public Criteria andMonthCostNotBetween(String value1, String value2) {
            addCriterion("month_cost not between", value1, value2, "monthCost");
            return (Criteria) this;
        }

        public Criteria andTellPhoneIsNull() {
            addCriterion("tell_phone is null");
            return (Criteria) this;
        }

        public Criteria andTellPhoneIsNotNull() {
            addCriterion("tell_phone is not null");
            return (Criteria) this;
        }

        public Criteria andTellPhoneEqualTo(String value) {
            addCriterion("tell_phone =", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneNotEqualTo(String value) {
            addCriterion("tell_phone <>", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneGreaterThan(String value) {
            addCriterion("tell_phone >", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("tell_phone >=", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneLessThan(String value) {
            addCriterion("tell_phone <", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneLessThanOrEqualTo(String value) {
            addCriterion("tell_phone <=", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneLike(String value) {
            addCriterion("tell_phone like", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneNotLike(String value) {
            addCriterion("tell_phone not like", value, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneIn(List<String> values) {
            addCriterion("tell_phone in", values, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneNotIn(List<String> values) {
            addCriterion("tell_phone not in", values, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneBetween(String value1, String value2) {
            addCriterion("tell_phone between", value1, value2, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andTellPhoneNotBetween(String value1, String value2) {
            addCriterion("tell_phone not between", value1, value2, "tellPhone");
            return (Criteria) this;
        }

        public Criteria andSignIdIsNull() {
            addCriterion("sign_id is null");
            return (Criteria) this;
        }

        public Criteria andSignIdIsNotNull() {
            addCriterion("sign_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignIdEqualTo(String value) {
            addCriterion("sign_id =", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdNotEqualTo(String value) {
            addCriterion("sign_id <>", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdGreaterThan(String value) {
            addCriterion("sign_id >", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdGreaterThanOrEqualTo(String value) {
            addCriterion("sign_id >=", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdLessThan(String value) {
            addCriterion("sign_id <", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdLessThanOrEqualTo(String value) {
            addCriterion("sign_id <=", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdLike(String value) {
            addCriterion("sign_id like", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdNotLike(String value) {
            addCriterion("sign_id not like", value, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdIn(List<String> values) {
            addCriterion("sign_id in", values, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdNotIn(List<String> values) {
            addCriterion("sign_id not in", values, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdBetween(String value1, String value2) {
            addCriterion("sign_id between", value1, value2, "signId");
            return (Criteria) this;
        }

        public Criteria andSignIdNotBetween(String value1, String value2) {
            addCriterion("sign_id not between", value1, value2, "signId");
            return (Criteria) this;
        }

        public Criteria andSignTypeIsNull() {
            addCriterion("sign_type is null");
            return (Criteria) this;
        }

        public Criteria andSignTypeIsNotNull() {
            addCriterion("sign_type is not null");
            return (Criteria) this;
        }

        public Criteria andSignTypeEqualTo(String value) {
            addCriterion("sign_type =", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeNotEqualTo(String value) {
            addCriterion("sign_type <>", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeGreaterThan(String value) {
            addCriterion("sign_type >", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sign_type >=", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeLessThan(String value) {
            addCriterion("sign_type <", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeLessThanOrEqualTo(String value) {
            addCriterion("sign_type <=", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeLike(String value) {
            addCriterion("sign_type like", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeNotLike(String value) {
            addCriterion("sign_type not like", value, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeIn(List<String> values) {
            addCriterion("sign_type in", values, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeNotIn(List<String> values) {
            addCriterion("sign_type not in", values, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeBetween(String value1, String value2) {
            addCriterion("sign_type between", value1, value2, "signType");
            return (Criteria) this;
        }

        public Criteria andSignTypeNotBetween(String value1, String value2) {
            addCriterion("sign_type not between", value1, value2, "signType");
            return (Criteria) this;
        }

        public Criteria andIsFeelingIsNull() {
            addCriterion("is_feeling is null");
            return (Criteria) this;
        }

        public Criteria andIsFeelingIsNotNull() {
            addCriterion("is_feeling is not null");
            return (Criteria) this;
        }

        public Criteria andIsFeelingEqualTo(String value) {
            addCriterion("is_feeling =", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingNotEqualTo(String value) {
            addCriterion("is_feeling <>", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingGreaterThan(String value) {
            addCriterion("is_feeling >", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingGreaterThanOrEqualTo(String value) {
            addCriterion("is_feeling >=", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingLessThan(String value) {
            addCriterion("is_feeling <", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingLessThanOrEqualTo(String value) {
            addCriterion("is_feeling <=", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingLike(String value) {
            addCriterion("is_feeling like", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingNotLike(String value) {
            addCriterion("is_feeling not like", value, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingIn(List<String> values) {
            addCriterion("is_feeling in", values, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingNotIn(List<String> values) {
            addCriterion("is_feeling not in", values, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingBetween(String value1, String value2) {
            addCriterion("is_feeling between", value1, value2, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andIsFeelingNotBetween(String value1, String value2) {
            addCriterion("is_feeling not between", value1, value2, "isFeeling");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagIsNull() {
            addCriterion("network_flag is null");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagIsNotNull() {
            addCriterion("network_flag is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagEqualTo(String value) {
            addCriterion("network_flag =", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagNotEqualTo(String value) {
            addCriterion("network_flag <>", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagGreaterThan(String value) {
            addCriterion("network_flag >", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagGreaterThanOrEqualTo(String value) {
            addCriterion("network_flag >=", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagLessThan(String value) {
            addCriterion("network_flag <", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagLessThanOrEqualTo(String value) {
            addCriterion("network_flag <=", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagLike(String value) {
            addCriterion("network_flag like", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagNotLike(String value) {
            addCriterion("network_flag not like", value, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagIn(List<String> values) {
            addCriterion("network_flag in", values, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagNotIn(List<String> values) {
            addCriterion("network_flag not in", values, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagBetween(String value1, String value2) {
            addCriterion("network_flag between", value1, value2, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andNetworkFlagNotBetween(String value1, String value2) {
            addCriterion("network_flag not between", value1, value2, "networkFlag");
            return (Criteria) this;
        }

        public Criteria andPartIdIsNull() {
            addCriterion("part_id is null");
            return (Criteria) this;
        }

        public Criteria andPartIdIsNotNull() {
            addCriterion("part_id is not null");
            return (Criteria) this;
        }

        public Criteria andPartIdEqualTo(String value) {
            addCriterion("part_id =", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdNotEqualTo(String value) {
            addCriterion("part_id <>", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdGreaterThan(String value) {
            addCriterion("part_id >", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdGreaterThanOrEqualTo(String value) {
            addCriterion("part_id >=", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdLessThan(String value) {
            addCriterion("part_id <", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdLessThanOrEqualTo(String value) {
            addCriterion("part_id <=", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdLike(String value) {
            addCriterion("part_id like", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdNotLike(String value) {
            addCriterion("part_id not like", value, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdIn(List<String> values) {
            addCriterion("part_id in", values, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdNotIn(List<String> values) {
            addCriterion("part_id not in", values, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdBetween(String value1, String value2) {
            addCriterion("part_id between", value1, value2, "partId");
            return (Criteria) this;
        }

        public Criteria andPartIdNotBetween(String value1, String value2) {
            addCriterion("part_id not between", value1, value2, "partId");
            return (Criteria) this;
        }

        public Criteria andDayIdIsNull() {
            addCriterion("day_id is null");
            return (Criteria) this;
        }

        public Criteria andDayIdIsNotNull() {
            addCriterion("day_id is not null");
            return (Criteria) this;
        }

        public Criteria andDayIdEqualTo(String value) {
            addCriterion("day_id =", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdNotEqualTo(String value) {
            addCriterion("day_id <>", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdGreaterThan(String value) {
            addCriterion("day_id >", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdGreaterThanOrEqualTo(String value) {
            addCriterion("day_id >=", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdLessThan(String value) {
            addCriterion("day_id <", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdLessThanOrEqualTo(String value) {
            addCriterion("day_id <=", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdLike(String value) {
            addCriterion("day_id like", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdNotLike(String value) {
            addCriterion("day_id not like", value, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdIn(List<String> values) {
            addCriterion("day_id in", values, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdNotIn(List<String> values) {
            addCriterion("day_id not in", values, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdBetween(String value1, String value2) {
            addCriterion("day_id between", value1, value2, "dayId");
            return (Criteria) this;
        }

        public Criteria andDayIdNotBetween(String value1, String value2) {
            addCriterion("day_id not between", value1, value2, "dayId");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("state like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("state not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNull() {
            addCriterion("file_name is null");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNotNull() {
            addCriterion("file_name is not null");
            return (Criteria) this;
        }

        public Criteria andFileNameEqualTo(String value) {
            addCriterion("file_name =", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualTo(String value) {
            addCriterion("file_name <>", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThan(String value) {
            addCriterion("file_name >", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("file_name >=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThan(String value) {
            addCriterion("file_name <", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualTo(String value) {
            addCriterion("file_name <=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLike(String value) {
            addCriterion("file_name like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotLike(String value) {
            addCriterion("file_name not like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameIn(List<String> values) {
            addCriterion("file_name in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotIn(List<String> values) {
            addCriterion("file_name not in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameBetween(String value1, String value2) {
            addCriterion("file_name between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetween(String value1, String value2) {
            addCriterion("file_name not between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeIsNull() {
            addCriterion("info_insert_time is null");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeIsNotNull() {
            addCriterion("info_insert_time is not null");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeEqualTo(Date value) {
            addCriterion("info_insert_time =", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeNotEqualTo(Date value) {
            addCriterion("info_insert_time <>", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeGreaterThan(Date value) {
            addCriterion("info_insert_time >", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("info_insert_time >=", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeLessThan(Date value) {
            addCriterion("info_insert_time <", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeLessThanOrEqualTo(Date value) {
            addCriterion("info_insert_time <=", value, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeIn(List<Date> values) {
            addCriterion("info_insert_time in", values, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeNotIn(List<Date> values) {
            addCriterion("info_insert_time not in", values, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeBetween(Date value1, Date value2) {
            addCriterion("info_insert_time between", value1, value2, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoInsertTimeNotBetween(Date value1, Date value2) {
            addCriterion("info_insert_time not between", value1, value2, "infoInsertTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeIsNull() {
            addCriterion("info_update_time is null");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeIsNotNull() {
            addCriterion("info_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeEqualTo(Date value) {
            addCriterion("info_update_time =", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeNotEqualTo(Date value) {
            addCriterion("info_update_time <>", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeGreaterThan(Date value) {
            addCriterion("info_update_time >", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("info_update_time >=", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeLessThan(Date value) {
            addCriterion("info_update_time <", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("info_update_time <=", value, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeIn(List<Date> values) {
            addCriterion("info_update_time in", values, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeNotIn(List<Date> values) {
            addCriterion("info_update_time not in", values, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("info_update_time between", value1, value2, "infoUpdateTime");
            return (Criteria) this;
        }

        public Criteria andInfoUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("info_update_time not between", value1, value2, "infoUpdateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}