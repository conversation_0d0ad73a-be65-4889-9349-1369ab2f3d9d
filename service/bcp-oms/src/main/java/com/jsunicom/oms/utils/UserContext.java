package com.jsunicom.oms.utils;

import com.jsunicom.oms.dto.scheme.UserVo;
import com.jsunicom.oms.po.UserRoleInfo;

public class UserContext {

    private static final ThreadLocal<UserRoleInfo> userThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> tokenThreadLocal = new ThreadLocal<>();

    public static void setUser(UserRoleInfo user) {
        userThreadLocal.set(user);
    }

    public static UserRoleInfo getUser() {
        return userThreadLocal.get();
    }

    public static void setToken(String token) {
        tokenThreadLocal.set(token);
    }

    public static String getToken() {
        return tokenThreadLocal.get();
    }

    public static void clear() {
        userThreadLocal.remove();
    }
}
