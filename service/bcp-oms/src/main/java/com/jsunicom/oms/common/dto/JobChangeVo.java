package com.jsunicom.oms.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.vo.approvalManagement
 * @ClassName: JobChangeVo
 * @Author: zhaowang
 * @CreateTime: 2023-10-24  15:30
 * @Description: TODO
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobChangeVo implements Serializable {
    /**
     * 操作类型：1 主席，2 部长
     */
    private String operationType;
    /**
     * 老成员ID
     */
    private Long oldMemberId;
    /**
     * 新成员ID
     */
    private Long memberId;
    /**
     * 手机号
     */
    private String acctNo;

    /**
     * 人员名称
     */
    private String memberName;

    /**
     * 职位汉字描述
     */
    private String position;

    /**
     * 成员角色类型
     */
    private String roleType;
}
