//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.jsunicom.oms.utils;


import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

public class SftpClientUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(SftpClientUtil.class);
    private String host;
    private int port;
    private String username;
    private String password;
    private String directory;
    private Session session;
    private ChannelSftp channel;
    private static final int CONNECT_TIMEOUT = 30000;


    public SftpClientUtil() {
        this(false);
    }

    public SftpClientUtil(boolean useEmptyConfig) {
       /* if (!useEmptyConfig) {
            boolean getSftpConfigFromDBSuccess = this.getSftpConfigFromDB();
            if (!getSftpConfigFromDBSuccess) {
                this.getSftpConfigFromFile();
            }
        }*/

    }

    public SftpClientUtil(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

   /* private boolean getSftpConfigFromFile() {
        boolean flag = false;

        try {
            Properties proper = new Properties();
            proper.load(FtpUtil.class.getResourceAsStream("/ftp.properties"));
            this.host = proper.getProperty("leaf.ftp.hostip");
            this.port = Integer.parseInt(proper.getProperty("leaf.ftp.hostport"));
            this.username = proper.getProperty("leaf.ftp.loginuser");
            this.password = proper.getProperty("leaf.ftp.loginpwd");
            this.directory = proper.getProperty("leaf.ftp.directory");
        } catch (IOException var3) {
            LOGGER.error(var3.getMessage(), var3);
        }

        return flag;
    }*/

   /* private boolean getSftpConfigFromDB() {
        boolean flag = false;

        try {
            TdOrdSysDict dict = SysDictUtil.getSysDictByTypeAndKey("ftpConfig", "ftpConfig");
            if (dict != null) {
                this.host = dict.getParam1();
                String portStr = dict.getParam2();
                if (StringUtils.isNotEmpty(portStr)) {
                    try {
                        this.port = Integer.valueOf(portStr);
                    } catch (Exception var5) {
                        this.port = 22;
                    }
                }

                this.username = dict.getParam3();
                this.password = dict.getParam4();
                this.directory = dict.getParam5();
                flag = true;
            }
        } catch (Exception var6) {
            LOGGER.error(var6.getMessage(), var6);
        }

        return flag;
    }*/

    private void connect() throws Exception {
        JSch jsch = new JSch();
        if (StringUtils.isEmpty(this.host)) {
            throw new Exception("主机ip为空");
        } else if (!StringUtils.isEmpty(this.username) && !StringUtils.isEmpty(this.password)) {
            if (this.session != null) {
                this.session.disconnect();
                this.session = null;
            }

            this.session = jsch.getSession(this.username, this.host, this.port);
            this.session.setPassword(this.password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            this.session.setConfig(sshConfig);
            this.session.connect(30000);
            if (this.channel != null) {
                this.channel.disconnect();
                this.channel = null;
            }

            this.channel = (ChannelSftp)this.session.openChannel("sftp");
            this.channel.connect();
        } else {
            throw new Exception("用户名或密码为空");
        }
    }

    public void disconnect() {
        if (this.channel != null) {
            try {
                this.channel.disconnect();
            } catch (Exception var3) {
                LOGGER.error("关闭sftp通道异常:" + var3.getMessage());
            }
        }

        if (this.session != null) {
            try {
                this.session.disconnect();
            } catch (Exception var2) {
                LOGGER.error("关闭sftp会话异常:" + var2.getMessage());
            }
        }

    }

    public String uploadFile(Map<String, String> uploadParam, File uploadFile) throws Exception {
        try {
            if (uploadFile == null) {
                throw new Exception("上传文件为空");
            }

            this.setUserInfo(uploadParam);
            this.connect();

            try {
                this.channel.cd(this.directory);
            } catch (SftpException var8) {
                this.channel.mkdir(this.directory);
            }

            this.channel.cd(this.directory);
            this.channel.put(new FileInputStream(uploadFile), uploadFile.getName());
        } catch (Exception var9) {
            throw new Exception("上传文件失败!" + var9.getMessage());
        } finally {
            this.disconnect();
        }

        return this.directory + File.separator + uploadFile.getName();
    }

    public String uploadFileStream(Map<String, String> uploadParam, List<LinkedHashMap<String, String>> list, String fileName) throws Exception {
        ByteArrayInputStream inStream = null;

        try {
            if (list == null || StringUtils.isEmpty(fileName)) {
                throw new Exception("上传数据或文件名为空");
            } else {
                this.setUserInfo(uploadParam);
                this.connect();

                try {
                    this.channel.cd(this.directory);
                } catch (SftpException var15) {
                    this.channel.mkdir(this.directory);
                }

                this.channel.cd(this.directory);
                StringBuilder builder = new StringBuilder();
                Iterator var7 = list.iterator();

                while(var7.hasNext()) {
                    LinkedHashMap<String, String> map = (LinkedHashMap)var7.next();
                    Set<String> keys = map.keySet();
                    Iterator var10 = keys.iterator();

                    while(var10.hasNext()) {
                        String key = (String)var10.next();
                        builder.append(((String)map.get(key)).toString());
                        builder.append("|");
                    }

                    builder.append("\r\n");
                }

                inStream = new ByteArrayInputStream(builder.toString().getBytes());
                this.channel.put(inStream, fileName);
                return this.directory + File.separator + fileName;
            }
        } catch (Exception var16) {
            throw new Exception("上传文件失败!" + var16.getMessage());
        } finally {
            this.disconnect();
            if (inStream != null) {
                inStream.close();
            }

        }
    }

    private void setUserInfo(Map<String, String> uploadParam) throws Exception {
        if (uploadParam == null) {
            throw new Exception("SFTP用户参数MAP为空");
        } else {
            String username = (String)uploadParam.get("username");
            String password = (String)uploadParam.get("password");
            if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(password)) {
                String host = (String)uploadParam.get("host");
                String port = (String)uploadParam.get("port");
                if (!StringUtils.isEmpty(host) && !StringUtils.isEmpty(port)) {
                    boolean var6 = true;

                    int portNum;
                    try {
                        portNum = Integer.parseInt(port);
                    } catch (Exception var8) {
                        portNum = 22;
                    }

                    String directory = (String)uploadParam.get("dir");
                    if (StringUtils.isEmpty(directory)) {
                        throw new Exception("文件存储目录为空");
                    } else {
                        this.setHost(host);
                        this.setPort(portNum);
                        this.setUsername(username);
                        this.setPassword(password);
                        this.setDirectory(directory);
                    }
                } else {
                    throw new Exception("主机或端口为空");
                }
            } else {
                throw new Exception("用户名或密码为空");
            }
        }
    }

    public String download(String directory, String saveDirectory) throws Exception {
        String saveFile = null;

        try {
            int index = directory.lastIndexOf("/");
            String directory1 = directory.substring(0, index);
            String downloadFile = directory.substring(index + 1);
            saveFile = saveDirectory + "/" + downloadFile;
            this.connect();
            this.channel.cd(directory1);
            File file = new File(saveDirectory);
            file.mkdirs();
            if (!file.exists()) {
                throw new Exception("创建本地下载文件夹" + file + "失败");
            }

            File stofile = new File(saveFile);
            this.channel.get(downloadFile, new FileOutputStream(stofile));
        } catch (Exception var11) {
            throw var11;
        } finally {
            this.disconnect();
        }

        return saveFile;
    }

    public File downloadFile(String directory, String downloadFile) throws Exception {
        File file = null;

        try {
            String tmpPath = System.getProperty("user.dir");
            String saveFile = tmpPath + File.separator + downloadFile;
            this.connect();
            this.channel.cd(directory);
            file = new File(saveFile);
            if (!file.exists()) {
                file.createNewFile();
            }

            this.channel.get(downloadFile, new FileOutputStream(file));
        } catch (Exception var9) {
            LOGGER.error("下载文件至本地失败," + var9.getMessage());
        } finally {
            this.disconnect();
        }

        return file;
    }

    public InputStream downloadFile2Stream(String path, String fileName, String prefix) {
        InputStream is = null;
        InputStream finalIs = null;

        try {
            if (!StringUtils.isEmpty(path)) {
                this.connect();
                this.channel.cd(path);

                try {
                    is = this.channel.get(fileName);
                } catch (Exception var17) {
                    is = this.getLastSortedFileOfDir(path, prefix);
                }

                finalIs = this.copyStream(is);
                return finalIs;
            }
        } catch (Exception var18) {
            LOGGER.error(var18.getMessage(), var18);
            return finalIs;
        } finally {
            this.disconnect();
            if (is != null) {
                try {
                    is.close();
                } catch (IOException var16) {
                    LOGGER.error(var16.getMessage());
                }
            }

        }

        return null;
    }

    private InputStream getLastSortedFileOfDir(String dir, String prefix) throws SftpException {
        if (StringUtils.isEmpty(prefix)) {
            return null;
        } else {
            InputStream is = null;
            List<String> fileNameList = new ArrayList();
            Vector<ChannelSftp.LsEntry> fileList = this.channel.ls(dir);
            Iterator iter = fileList.iterator();

            while(iter.hasNext()) {
                ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry)iter.next();
                String eachFileName = lsEntry.getFilename();
                if (eachFileName.startsWith(prefix)) {
                    fileNameList.add(eachFileName);
                }
            }

            Collections.sort(fileNameList);
            String lastFileName = null;
            if (!fileNameList.isEmpty()) {
                lastFileName = (String)fileNameList.get(fileNameList.size() - 1);
            }

            if (StringUtils.isNotEmpty(lastFileName)) {
                is = this.channel.get(lastFileName);
            }

            return is;
        }
    }

    public String getLastSortedFileOfDirByTime(String dir, String prefix ,String suffix) throws SftpException {
        String lastFileName = null;
        try{
            this.connect();
            LOGGER.info("--连接sftp成功--");
            if (StringUtils.isEmpty(prefix)) {
                return null;
            } else {
                InputStream is = null;
                List<String> fileNameList = new ArrayList();
                Vector<ChannelSftp.LsEntry> fileList = this.channel.ls(dir);
                Iterator iter = fileList.iterator();

                while(iter.hasNext()) {
                    ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry)iter.next();
                    String eachFileName = lsEntry.getFilename();
                    if (eachFileName.startsWith(prefix)&& eachFileName.endsWith(suffix)) {
                        fileNameList.add(eachFileName);
                    }
                }

                Collections.sort(fileNameList);

                if (!fileNameList.isEmpty()) {
                    lastFileName = (String)fileNameList.get(fileNameList.size() - 1);
                }

                return lastFileName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            this.disconnect();
        }

        return lastFileName;
    }

    private InputStream copyStream(InputStream srcStream) {
        if (srcStream == null) {
            return null;
        } else {
            ByteArrayInputStream newStream = null;

            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];

                int len;
                while((len = srcStream.read(buffer)) > -1) {
                    baos.write(buffer, 0, len);
                }

                baos.flush();
                newStream = new ByteArrayInputStream(baos.toByteArray());
            } catch (Exception var6) {
                LOGGER.error(var6.getMessage());
            }

            return newStream;
        }
    }

    public String getHost() {
        return this.host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDirectory() {
        return this.directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public List<String> getFileOfDir(String dir, String prefix, String postfix) throws SftpException {
        if (StringUtils.isEmpty(prefix)) {
            return null;
        } else {
            try {
                this.connect();
            }catch (Exception var19){

            }
             List<String> fileNameList = new ArrayList();
            Vector<ChannelSftp.LsEntry> fileList = this.channel.ls(dir);
            Iterator iter = fileList.iterator();

            while(iter.hasNext()) {
                ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry)iter.next();
                String eachFileName = lsEntry.getFilename();
                if (eachFileName.startsWith(prefix) && eachFileName.endsWith(postfix)) {
                    fileNameList.add(eachFileName);
                }
            }
            return fileNameList;
        }
    }

    public InputStream downloadFile2Stream(String path, String fileName) {
        InputStream is = null;
        InputStream finalIs = null;

        Object var6;
        try {
            if (!StringUtils.isEmpty(path)) {
                this.connect();
                this.channel.cd(path);
                try {
                    is = this.channel.get(fileName);
                } catch (Exception var18) {
                    LOGGER.info(var18.getMessage());
                }
                finalIs = this.copyStream(is);
                return finalIs;
            }
            var6 = null;
        } catch (Exception var19) {
            LOGGER.info(var19.getMessage());
            return finalIs;
        } finally {
            this.disconnect();
            if (is != null) {
                try {
                    is.close();
                } catch (IOException var17) {
                    LOGGER.info(var17.getMessage());
                }
            }
        }
        return (InputStream)var6;
    }
}
