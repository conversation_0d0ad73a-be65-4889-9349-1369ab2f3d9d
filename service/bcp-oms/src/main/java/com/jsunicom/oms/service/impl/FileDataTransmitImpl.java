package com.jsunicom.oms.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jsunicom.oms.entity.DimPubScbSchoolUser;
import com.jsunicom.oms.entity.WoSchoolCampusResLevelSevenAddr;
import com.jsunicom.oms.service.CampusBaseInfoService;
import com.jsunicom.oms.service.DimPubScbSchoolUserService;
import com.jsunicom.oms.utils.SftpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class FileDataTransmitImpl {

    @Value("${datasource.sftp.username}")
    private String sftpUsername;

    @Value("${datasource.sftp.password}")
    private String sftpPassword;

    @Value("${datasource.sftp.port}")
    private Integer sftpPort;

    @Value("${datasource.sftp.host}")
    private String sftpHost;

    @Value("${datasource.sftp.remotePath}")
    private String remotePath;

    @Resource
    private CampusBaseInfoService campusBaseInfoService;

    @Resource
    private DimPubScbSchoolUserService dimPubScbSchoolUserService;

    /**
     * 上传文件到行云库
     *
     * @param fileName
     * @param remoteFileName
     */
    public void uploadFile(String fileName, String remoteFileName, InputStream input) {
        if (fileName == null || remoteFileName == null) {
            log.error("文件路径或远程文件名不能为空");
            return; // 文件路径或远程文件名无效，直接返回
        }

        SftpUtil sftpUtil = new SftpUtil(sftpUsername, sftpPassword, sftpHost, sftpPort);
        try {
            sftpUtil.login();
            sftpUtil.upload(remotePath, fileName, input);
        } catch (Exception e) {
            log.error("数据上传到sftp失败，失败原因：", e);
        } finally {
            sftpUtil.logout();
        }
    }

    /**
     * 查询wo_school_campus_res_level_seven_addr表上传到服务器
     */
    public void uploadTspXjValidDivision() {
        String fileName = "wo_school_campus_res_level_seven_addr_" + getDay(0) + ".txt";
//        List<TspXjValidDivision> list = campusBaseInfoService.getWoSchoolCampusResLevelSevenAddrList();
        List<WoSchoolCampusResLevelSevenAddr> list = campusBaseInfoService.getWoSchoolCampusResLevelSevenAddrList();
        // 使用StringBuilder构建字符串
        StringBuilder sb = new StringBuilder();
        for (WoSchoolCampusResLevelSevenAddr woSchoolCampusResLevelSevenAddr : list) {
            sb.append(woSchoolCampusResLevelSevenAddr.toSeparatedString());
        }
        // 将字符串转换为字节数组
        byte[] byteArray;
        byteArray = sb.toString().getBytes(StandardCharsets.UTF_8);
        // 使用字节数组创建InputStream
        try (InputStream inputStream = new ByteArrayInputStream(byteArray)) {
            uploadFile(fileName, remotePath, inputStream);
        } catch (Exception e) {
            log.error("上传wo_school_campus_res_level_seven_addr失败，失败原因：", e);
        }
    }

    /**
     * 拉取国信返回的围栏数据
     */
    public void insertdimPubScbSchoolUser() {
        List<DimPubScbSchoolUser> list = new ArrayList<>();
       // String fileName = "dim_pub_scb_school_user_" + getDay(0) + ".csv";
        String fileName = "dim_pub_scb_school_user_20250513.csv";
        log.info("开始解析行国信返回围栏地址文件{}：" + fileName);
        SftpUtil sftpUtil = null;
        try {
            sftpUtil = new SftpUtil(sftpUsername, sftpPassword, sftpHost, sftpPort);
            sftpUtil.login();
            log.info("登录成功=============");
            try (InputStream inputStream = sftpUtil.getStream(remotePath, fileName);
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split("\\|", -1);
                    if (parts.length > 0) { // 确保分割后的数组至少有一个元素
                        DimPubScbSchoolUser dimPubScbSchoolUser = new DimPubScbSchoolUser();
                        dimPubScbSchoolUser.setDayId(Integer.parseInt(parts[0]));
                        dimPubScbSchoolUser.setAddrId(parts[1]);
                        dimPubScbSchoolUser.setUserId(parts[2]);
                        dimPubScbSchoolUser.setStayingMinute(parts[3]);
                        list.add(dimPubScbSchoolUser);
                    }
                }
            }
            dimPubScbSchoolUserService.remove(Wrappers.<DimPubScbSchoolUser>lambdaUpdate()
                    .eq(DimPubScbSchoolUser::getDayId, getDay(1) + getDay(2)));
            dimPubScbSchoolUserService.saveBatch(list, 1000);
        } catch (Exception e) {
            log.error("处理国信返回围栏数据失败，失败原因：", e);
        } finally {
            assert sftpUtil != null;
            sftpUtil.logout();
        }
    }


    /**
     * 获取前一天的日期
     *
     * @param type 1：年月，2：日
     * @return
     */
    public static String getDay(int type) {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate previousDay = today.minusDays(1); // 减去一天，得到前一天的日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd"); // 定义格式化模式
        if (type == 1) {
            formatter = DateTimeFormatter.ofPattern("yyyyMM"); // 定义格式化模式
        } else if (type == 2) {
            formatter = DateTimeFormatter.ofPattern("dd"); // 定义格式化模式
        }
        return previousDay.format(formatter);//将LocalDate格式化为字符串
    }

    /**
     * 获取前当天的日期
     *
     * @param type 1：年月，2：日
     * @return
     */
    public static String getDay1(int type) {
        LocalDate today = LocalDate.now(); // 获取当前日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd"); // 定义格式化模式
        if (type == 1) {
            formatter = DateTimeFormatter.ofPattern("yyyyMM"); // 定义格式化模式
        } else if (type == 2) {
            formatter = DateTimeFormatter.ofPattern("dd"); // 定义格式化模式
        }
        return today.format(formatter);//将LocalDate格式化为字符串
    }

    public static void main(String[] args) {
        FileDataTransmitImpl fileDataTransmit = new FileDataTransmitImpl();
        fileDataTransmit.uploadTspXjValidDivision();
    }
}
