package com.jsunicom.oms.service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service
 * @ClassName: PartnerClassificationCodeFacade
 * @Author: z<PERSON>wang
 * @CreateTime: 2023-03-18  20:05
 * @Description: TODO
 * @Version: 1.0
 */
public interface PartnerClassificationCodeFacade {
    /**
     *
     * @Title: addClassificationPtner
     * @Description: 增加分类合伙人
     * @param classificationId 分类id
     * @param partnerId 合伙人id
     * @param createBy 创建人
     * @throws
     */
    void addClassificationPtner(Long classificationId, Long partnerId, Long classificationFirstId,String createBy);
}
