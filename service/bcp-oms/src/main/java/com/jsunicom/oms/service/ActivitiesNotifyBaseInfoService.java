package com.jsunicom.oms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.dto.campus.*;
import com.jsunicom.oms.entity.ActivitiesNotifyBaseInfo;
import com.jsunicom.oms.model.campus.Campus;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【activities_notify_base_info】的数据库操作Service
* @createDate 2025-03-18 10:21:04
*/
public interface ActivitiesNotifyBaseInfoService extends IService<ActivitiesNotifyBaseInfo> {

    int insertActivitiesNotifyBaseInfo(ActivitiesNotifyCampusDto activitiesNotifyCampusDto);

    int deleteActivitiesNotifyBaseInfo(Integer activitiesNotifyId);

    PageInfo<ActivitiesNotifyBaseInfoExtendVO> getActivitiesNotifyBaseInfoList(ActivitiesNotifyListMessageVO activitiesNotifyListMessageVO);

    PageInfo<ActivitiesNotifyCompusVo> getActivitiesNotifyDetails( CampusListVo campusListVo);

    PageInfo<CampusChooseDto> getCampusList(CampusListVo campusListVo);

    int updateActivitiesNotifyBaseInfo(ActivitiesNotifyBaseInfo activitiesNotifyBaseInfo);
}

