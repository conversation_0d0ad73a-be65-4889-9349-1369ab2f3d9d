package com.jsunicom.oms.mapper;

import com.jsunicom.oms.po.DataMonitor;
import com.jsunicom.oms.po.DataMonitorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataMonitorMapper {
    long countByExample(DataMonitorExample example);

    int deleteByExample(DataMonitorExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DataMonitor record);

    int insertSelective(DataMonitor record);

    List<DataMonitor> selectByExample(DataMonitorExample example);

    DataMonitor selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DataMonitor record, @Param("example") DataMonitorExample example);

    int updateByExample(@Param("record") DataMonitor record, @Param("example") DataMonitorExample example);

    int updateByPrimaryKeySelective(DataMonitor record);

    int updateByPrimaryKey(DataMonitor record);
}