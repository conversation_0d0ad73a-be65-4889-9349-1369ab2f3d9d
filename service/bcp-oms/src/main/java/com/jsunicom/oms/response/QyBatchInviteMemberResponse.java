package com.jsunicom.oms.response;


import com.jsunicom.oms.common.annotation.ApiField;

/**
 * Created by wentao on 2018/3/6.
 **/
public class QyBatchInviteMemberResponse extends BaseResponse{

    @ApiField("errcode")
    private String errCode;

    @ApiField("errmsg")
    private String errMsg;

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    @Override
    public String getErrCode() {
        return errCode;
    }

    @Override
    public String getErrMsg() {
        return errMsg;
    }
}
