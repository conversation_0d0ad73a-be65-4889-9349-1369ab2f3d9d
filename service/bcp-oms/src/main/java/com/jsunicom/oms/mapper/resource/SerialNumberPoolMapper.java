package com.jsunicom.oms.mapper.resource;

import com.jsunicom.oms.po.resource.TnSerialNumberPool;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

@Mapper
public interface SerialNumberPoolMapper {

    int insertTnSerialNumberPool(TnSerialNumberPool tnSerialNumberPool);

    int updateTnSerialNumberPoolById(TnSerialNumberPool tnSerialNumberPool);

    TnSerialNumberPool selectTnSerialNumberPoolByPoolCode(String poolCode);


    List<TnSerialNumberPool> querySerialPoolList(TnSerialNumberPool tnSerialNumberPool);

    List<TnSerialNumberPool> querySerialPoolNameList(Map paramMap);

    List<Map<Object,String>> qryTeamBySchoolId(Map paramMap);

    int deleteSerialPoolByPoolCode(String poolCode);

    List<TnSerialNumberPool> qryPoolInfo(Map<String, Object> param);

}
