package com.jsunicom.oms.controller.tourist;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.BaseController;
import com.jsunicom.oms.entity.PartnerInfo;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.service.PartnerFacade;
import com.jsunicom.oms.service.TouristWaitingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述  待选池
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/26 17:33:20
 */
@RestController
@RequestMapping(value = "/tourist")
@Slf4j
public class TouristController extends BaseController {

    @Autowired
    private TouristWaitingService touristWaitingService;

    @Autowired
    private PartnerFacade partnerFacade;


    @RequestMapping(value = "/qryPageTouristInfo", method = RequestMethod.POST, name = "查询分页待选池")
    public CustomResult qryPageTouristInfo(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        log.info("Entry Method: queryByCondition() ");
        HashMap<String, Object> tourists = null;
        tourists = touristWaitingService.qryPageTouristInfo(jsonObject);
        return ResultUtil.success(tourists);
    }

    @RequestMapping(value = "/qrySocietyInfoBySchoolId", method = RequestMethod.POST, name = "查询校园社团结构")
    public CustomResult qrySocietyInfoBySchoolId(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        HashMap<String, Object> maps = null;
        maps = touristWaitingService.qrySocietyInfoBySchoolId(jsonObject);
        return ResultUtil.success(maps);
    }

    @RequestMapping(value = "/deleteTouristWaiting", method = RequestMethod.POST, name = "删除游客")
    public CustomResult deleteTouristWaiting(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        HashMap<String, Object> maps = null;
        touristWaitingService.deleteTouristWaiting(jsonObject);
        return ResultUtil.success();
    }


    @RequestMapping(value = "/createMerchantMember", method = RequestMethod.POST, name = "待选池加入队员")
    public CustomResult createMerchantMember(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createMerchantMember方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {
            if(!hashMap.containsKey("partnerCertNo") || !hashMap.containsKey("createBy") || !hashMap.containsKey("mblNbr")
                    || !hashMap.containsKey("partnerName") ||  !hashMap.containsKey("merchantId")
                    || !hashMap.containsKey("campusId") ||  !hashMap.containsKey("collegeId") ||  !hashMap.containsKey("societyId")
                    ){
                error = "缺少必传字段";
                return ResultUtil.error("8888",error);
            }
            PartnerInfo partnerRecord = JSONObject.parseObject(JSONObject.toJSONString(hashMap), PartnerInfo.class);
            // d)校验注册人员信息是否在表中待审核、已审核状态存在，如果已存在给出提示
            PartnerInfo record = new PartnerInfo();
            record.setMblNbr(partnerRecord.getMblNbr());
            record.setState("0|1");
            record.setPartnerCertNo(partnerRecord.getPartnerCertNo());
            long partnerCount = touristWaitingService.countOrByExample(record);
            if(partnerCount>0){
                return ResultUtil.error("8888","该用户已存在");
            }

            Partner partner = getCurrentUser(request);
            if(null != partner){
                partnerRecord.setCreateBy(partner.getMblNbr());
                partnerRecord.setOrgCode(partner.getOrgCode());
            }

            partnerRecord.setState("0");
            partnerRecord.setPartnerType("2");
            partnerRecord.setIsMerAdmin("0");
            partnerRecord.setAcctNo(partnerRecord.getMblNbr());
            partnerRecord.setCreateTime(new Date());
            partnerRecord.setBusLine("20");
            partnerRecord.setReserve1("0");
            touristWaitingService.insertPartnerInfo(partnerRecord);
            //修改表状态
            touristWaitingService.upDateTouristWaiting(hashMap);
            return ResultUtil.success("0000","加入成功");
        }catch (Exception e){
            log.error("createMerchantMember方法异常："+e.getMessage());
            // error = "加入异常";
            return ResultUtil.error("8888","："+e.getMessage());
        }
    }

    @RequestMapping(value = "/getPartnerBySocietyId", method = RequestMethod.POST, name = "待选池-社员列表查询接口")
    public CustomResult getPartnerBySocietyId(@RequestBody JSONObject params, HttpServletRequest request) {
        log.info("进入getPartnerBySocietyId方法："+ JSONObject.toJSONString(params));
        try {

            int pageNum = 1;
            int pageSize = 500;
            if(params.containsKey("pageNum")){
                pageNum = (int) params.get("pageNum");
            }
            if(params.containsKey("pageSize")){
                pageSize = (int) params.get("pageSize");
            }
            PageHelper.startPage(pageNum, pageSize);

            if(!params.containsKey("societyId") || !params.containsKey("merchantId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }
            List<Map> resultList  = touristWaitingService.queryMemberIListById(params);


            PageInfo<Map> pageInfo = new PageInfo<>(resultList);


            JSONObject object = new JSONObject();
            object.put("data", resultList);
            object.put("totalCount", pageInfo.getTotal());

            return ResultUtil.successByJSONObject(object);

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"查询异常！");
        }

    }
}
