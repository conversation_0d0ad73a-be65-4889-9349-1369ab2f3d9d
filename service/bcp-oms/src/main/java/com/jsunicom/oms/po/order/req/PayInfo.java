package com.jsunicom.oms.po.order.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class PayInfo implements Serializable {
    private String payMoney;//支付金额

    private String paySeq;//支付单流水号

    private String payOrderId;//支付单流水号

    private String oldFee;//应收金额（元），默认0，精确到分

    private String fee;//实收金额（元），默认0，精确到分

    private String payState;//支付状态

    private String discnt_fee;

    private String old_fee;

    private String pay_channel;

    private String pay_money;

    private String pay_seq;

    private String payType;

    private String pay_state;
}
