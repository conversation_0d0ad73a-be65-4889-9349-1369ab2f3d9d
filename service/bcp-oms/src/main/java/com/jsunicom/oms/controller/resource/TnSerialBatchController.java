package com.jsunicom.oms.controller.resource;

import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.common.cos.PrivateCosClientUtil;
import com.jsunicom.common.cos.PublicCosClientUtil;
import com.jsunicom.oms.common.Utils;
import com.jsunicom.oms.common.annotation.NotNeedResponseCover;
import com.jsunicom.oms.common.result.PageResult;
import com.jsunicom.oms.po.resource.SerialImportModel;
import com.jsunicom.oms.po.resource.TnSerialBatch;
import com.jsunicom.oms.service.resource.TnSerialBatchService;
import com.jsunicom.oms.utils.DateUtils;
import com.jsunicom.oms.utils.PageUtils;
import com.jsunicom.oms.utils.S3Util;
import com.jsunicom.oms.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("tnSerialBatch")
@Slf4j
@NotNeedResponseCover
public class TnSerialBatchController {

    @Autowired
    private TnSerialBatchService tnSerialBatchService;

    @Value("${s3.bucket}")
    private String bucketName;

    @ApiModelProperty("批次列表")
    @PostMapping("qrySerialBatchList")
    public PageResult qryTnSerialBatchList(@RequestBody TnSerialBatch tnSerialBatch, HttpServletRequest request) {
        log.info("qryTnSerialBatchList reqParam:{}", tnSerialBatch.toString());
        try {
            UserInfo userInfo = com.jsunicom.oms.utils.Utils.getUserInfo(request.getHeader("User-Info"));
            return PageUtils.getPageResult(tnSerialBatchService.qryTnSerialBatchInfo(tnSerialBatch, userInfo));
        } catch (Exception e) {
            PageResult resultFail = new PageResult();
            resultFail.setCode("500");
            resultFail.setMsg("批次列表查询失败!");
            log.error("批次列表查询失败：{}", e.getMessage());
            return resultFail;
        }
    }

    @ApiModelProperty("批量导入")
    @PostMapping("batchImport")
    public Result batchImport(@RequestParam("fileName") MultipartFile file, @RequestParam("batchName") String batchName,
                              @RequestParam("poolCode") String poolCode, HttpServletRequest request) {
        log.info("batchImport reqParam file:{}  batchName:{}   poolCode:{}", file, batchName, poolCode);
        Result resultSuc = new Result(200L, "", true, "批量导入成功", "批量导入成功", 0);
        Result resultFail = new Result(500L, "", false, "批量导入失败", "批量导入失败", 0);
        if (file.isEmpty()) {
            resultFail.setMsg("未上传任何文件，不做导入处理！");
            return resultFail;
        }
        String fileName = StringUtils.defaultIfBlank(file.getOriginalFilename(), "");
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            resultFail.setMsg("文件格式不支持！");
            return resultFail;
        }
        try {
            InputStream inputStream = file.getInputStream();
            XSSFWorkbook book = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = book.getSheetAt(0);
            int dataSize = sheet.getLastRowNum();
            if (dataSize == 0) {
                resultFail.setMsg("空文件，不做导入处理！");
                return resultFail;
            } else if (dataSize >= 5001) {
                resultFail.setMsg("单次号码导入超过5000，请分批导入！");
                return resultFail;
            }
            //将目标文件上传至服务器
            String fileDir = "serialPoolBatchExcel/" + DateUtils.getDay().replaceAll("-","") + "/";
            if (tnSerialBatchService.upload(file, fileDir)) {
                //将所有数据读进内存
                List<SerialImportModel> serialNoList = new ArrayList<>();
                //存放已读取的数据判断是否有重复数据
                List<String> listSerialNo = new ArrayList<>();
                List<String> listIccid = new ArrayList<>();
                List<String> listPsptId = new ArrayList<>();

                for (Row row : sheet) {
                    if (row.getRowNum() == 0) {
                        continue;
                    }
                    SerialImportModel serialImportModel = new SerialImportModel();
                    serialImportModel.setRowIndex(row.getRowNum());
                    for (Cell c : row) {
                        c.setCellType(1);
                        String cellValue = StringUtil.ifNullGetDefualt(c.getStringCellValue(), "");
                        //取出空串
                        if (StringUtils.isNotBlank(cellValue)) {
                            if (c.getColumnIndex() == 0) {
                                serialImportModel.setSerialNo(cellValue);
                            }
                            if (c.getColumnIndex() == 1) {
                                serialImportModel.setIccid(cellValue);
                            }
                            if (c.getColumnIndex() == 2) {
                                serialImportModel.setPsptId(cellValue);
                            }
                        }
                    }
                    if (listSerialNo.contains(serialImportModel.getSerialNo())) {
                        resultFail.setMsg("文件中存在重复号码：" + serialImportModel.getSerialNo() + ",请处理后重新上传");
                        return resultFail;
                    }
                    if (listIccid.contains(serialImportModel.getIccid())) {
                        resultFail.setMsg("文件中存在重复iccid：" + serialImportModel.getSerialNo() + ",请处理后重新上传");
                        return resultFail;
                    }
                    if (StringUtils.isNotBlank(serialImportModel.getPsptId()) && listPsptId.contains(serialImportModel.getPsptId())) {
                        resultFail.setMsg("文件中存在重复psptId：" + serialImportModel.getSerialNo() + ",请处理后重新上传");
                        return resultFail;
                    }
                    if (StringUtils.isNotBlank(serialImportModel.getIccid()) && serialImportModel.getIccid().length() < 20) {
                        resultFail.setMsg("文件中存在不符合规范的ICCID,长度须大于等于20位：" + serialImportModel.getSerialNo() + ",请处理后重新上传");
                        return resultFail;
                    }
                    if (StringUtils.isNoneBlank(serialImportModel.getIccid(), serialImportModel.getSerialNo())) {
                        listPsptId.add(serialImportModel.getPsptId());
                        listIccid.add(serialImportModel.getIccid());
                        listSerialNo.add(serialImportModel.getSerialNo());
                        serialNoList.add(serialImportModel);
                    }
                }
                UserInfo userInfo = getUserInfo(request);
                log.info("待插入表 读取Excel经过处理后的数据：{}", serialNoList);
                return tnSerialBatchService.importData(serialNoList, batchName, poolCode, fileDir, userInfo);
            } else {
                resultFail.setMsg("文件上传失败");
                return resultFail;
            }
        } catch (Exception e) {
            log.error("批量导入失败：{}", e);
            resultFail.setMsg("批量导入失败");
            return resultFail;
        }
    }

    @ApiModelProperty("号池导入结果文件下载")
    @GetMapping("batchResultDownload/{fileName}/{type}")
    public void batchResultDownload(@PathVariable("fileName") String fileName, @PathVariable("type") String type, HttpServletResponse response) {
        log.info("入号池导入结果文件下载参数:{} type :{}", fileName,type);
        String fileDir = "";
        if ("serialPool".equals(type)) {
            fileDir = "serialPoolBatchExcel/" + fileName.substring(0,8) + "/";
        } else {
            fileDir = "serialBatchExcel/" + fileName.substring(0,8) + "/";
        }
        try {
            S3Util.downFileFromCOS(fileName,fileDir,bucketName,response);
            log.info("成功下载文件,filePath: {}", fileDir + fileName);
        } catch (Exception e) {
            log.error("文件下载失败：{}", e);
        }
    }

    private UserInfo getUserInfo(HttpServletRequest request) throws Exception {
        String userInfoBase64 = request.getHeader("User-Info");
        if (StringUtils.isBlank(userInfoBase64)) {
            log.error("UserInfoInterceptor - [{}]未获取到用户信息，userInfo为空", request.getPathInfo());
            throw new Exception("未获取到用户信息，请求头User-Info为空");
            // userInfoBase64 = "eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTYxMDI4OSIsInN0YWZmTm8iOiJaMDAwTEpYMSIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjQiLCJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTE2MTAyODkiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5YiY6YeR6ZGrIiwidXNlclBpZCI6IjQxMjcyODE5OTEwODEwMDAzMiIsImRlcGFydENvZGUiOiIzNDU2NzAyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuaxn+iLj+ecgeWIhuWFrOWPuOS/oeaBr+WMlumDqCJ9==";
        }
        UserInfo userInfo = Utils.getUserInfo(userInfoBase64);
        if (null == userInfo) {
            log.error("queryStaffInfo - userInfo is empty!");
            throw new Exception("userInfo is empty");
        }
        log.info("===userInfo===" + userInfo);
        String staffNo = userInfo.getStaffNo();
        if (Strings.isBlank(staffNo)) {
            log.error("queryStaffInfo - The staffNo cannot be found!");
            throw new Exception("The staffNo cannot be found!");
        }
        return userInfo;
    }
}
