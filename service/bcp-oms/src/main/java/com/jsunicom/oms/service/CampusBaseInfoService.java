package com.jsunicom.oms.service;

import com.jsunicom.oms.entity.WoSchoolCampusResLevelSevenAddr;
import com.jsunicom.oms.po.CampusBaseResult;
import com.jsunicom.oms.po.CampusInfo;
import com.jsunicom.oms.po.CampusInfoExtend;
import com.jsunicom.oms.po.PartnerResult;

import java.util.List;

public interface CampusBaseInfoService {
    public CampusBaseResult selectBaseInfo(Long campusId);

    public int saveOrUpdate(CampusInfoExtend campusInfoExtend);


    public List<CampusInfo> selectByExample(CampusInfo record);

    public List<CampusInfo>  selectByExampleGroup(CampusInfo record);

    public List<PartnerResult> selectPartnerName(long campusId);

    public WoSchoolCampusResLevelSevenAddr selectCampusAddrByCampusId(Long campusId);

    List<WoSchoolCampusResLevelSevenAddr> getWoSchoolCampusResLevelSevenAddrList();
}
