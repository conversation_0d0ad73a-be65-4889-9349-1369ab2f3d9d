package com.jsunicom.oms.controller.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class InnovateTaskDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "地市")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 1,value = "学校校区名称")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 2,value = "青创社名称")
    @JSONField(name = "societyName")
    private String societyName;
    @ExcelProperty(index = 3,value = "任务标识")
    @JSONField(name = "taskId")
    private Long taskId;   //null
    @ExcelProperty(index = 4,value = "任务类型")
    @JSONField(name = "taskTemplateId")
    private String taskTemplateId;
    @ExcelProperty(index = 5,value = "任务名称")
    @JSONField(name = "taskName")
    private String taskName;
    @ExcelProperty(index = 6,value = "任务开始时间")
    @JSONField(name = "startTime")
    private String startTime;
    @ExcelProperty(index = 7,value = "任务结束时间")
    @JSONField(name = "endTime")
    private String endTime;
    @ExcelProperty(index = 8,value = "任务状态")
    @JSONField(name = "taskState")
    private String taskState;
    @ExcelProperty(index = 9,value = "流程状态")
    @JSONField(name = "orderState")
    private String orderState;
    @ExcelProperty(index = 10,value = "任务地点")
    @JSONField(name = "location")
    private String location;
    @ExcelProperty(index = 11,value = "创建时间")
    @JSONField(name = "createdTime")
    private String createdTime;
    @ExcelProperty(index = 12,value = "创建人")
    @JSONField(name = "memberName")
    private String memberName;
    @ExcelProperty(index = 13,value = "手机号")
    @JSONField(name = "mblNbr")
    private String mblNbr;
}
