package com.jsunicom.oms.timer.resource;

import com.jsunicom.oms.service.WoSchoolService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-07-05-14:46
 */
@Slf4j
@Component
public class SendMsgJob {
    @Autowired
    private WoSchoolService woSchoolService;
    @XxlJob("sendMsgByTime")
    public ReturnT<String> sendMsgByTime(String param){
        log.info("sendMsgByTime start");
        woSchoolService.sendMsgByTime();
        log.info("sendMsgByTime end");
        return ReturnT.SUCCESS;
    }
}
