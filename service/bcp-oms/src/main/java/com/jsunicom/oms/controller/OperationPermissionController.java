package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.entity.PermInfosRsp;
import com.jsunicom.oms.mapper.order.OrderQueryMapper;
import com.jsunicom.oms.mapper.order.TfOrdCustinfoMapper;
import com.jsunicom.oms.model.base.Dict;
import com.jsunicom.oms.po.order.tf.TfOrdCustinfo;
import com.jsunicom.oms.service.DictFacade;
import com.jsunicom.oms.service.OperationPermissionService;
import com.jsunicom.oms.service.SessionQueService;
import com.jsunicom.oms.utils.DesensitizeUtil;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.controller
 * @ClassName: OperationPermissionController
 * @Author: zhaowang
 * @CreateTime: 2024-01-24  20:54
 * @Description: TODO 操作权限校验
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/operationPermission")
public class OperationPermissionController {
    @Autowired
    private OperationPermissionService operationPermissionService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private SessionQueService sessionQueService;
    @Autowired
    private DictFacade dictService;
    @Autowired
    private TfOrdCustinfoMapper tfOrdCustinfoMapper;
    @Autowired
    private OrderQueryMapper queryMapper;
    /**
     * 获取菜单权限 redis key值 ：security_perminfo_工号
     */
    private final static String PERMINFO_PREFIX = "security_perminfo_";
    /**
    *@autor: lkm
    *@time: 2024/7/30 18:00
    *@description: 黑名单查询
    *@param:
    *@return: 0000成功  9999失败
    */
    @PostMapping("/checkBlack")
    public CustomResult checkBlack(@RequestBody HashMap hashMap){
        log.info("进入黑名单校验checkBlack方法："+JSONObject.toJSONString(hashMap));
        List<HashMap> res=new ArrayList<>();
        String blackShow = dictService.findNameByKeyNotCache("blackShow", "1");
        if ("0".equals(blackShow)){
            log.info("进入黑名单校验checkBlack方法： 开关关闭");
            hashMap.put("dataSource",0);
        }else {
            hashMap.put("dataSource", 1);
        }
        try {
            String orderId = (String)hashMap.get("orderId");
            TfOrdCustinfo tfOrdCustinfo = tfOrdCustinfoMapper.qryTfOrdCustinfoByOrderId(orderId);
            hashMap.put("psptId",tfOrdCustinfo.getPsptId());
            JSONObject jsonObject = sessionQueService.blackListQue(hashMap);
            if ("0000".equals(jsonObject.get("STATUS"))){
                JSONObject rsp = (JSONObject)jsonObject.get("RSP");
                if ("0000".equals(rsp.get("RSP_CODE"))){
                    JSONArray data=(JSONArray)rsp.get("DATA");
                    //查询黑名单展示那些信息
                    String blackShowInfo = dictService.findNameByKeyNotCache("blackShowInfo", "1");
                    List<String> blackShowList=new ArrayList<>();
                    if (!StringUtil.isEmpty(blackShowInfo)) {
                        blackShowList=Arrays.asList(blackShowInfo.split(","));
                    }
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject dataI = (JSONObject)data.get(i);
                        String source=(String)dataI.get("LIST_TYPE");
                        if (CollectionUtils.isEmpty(blackShowList)||!blackShowList.contains(source)){
                            continue;
                        }
                        HashMap temp=new HashMap();
//                        OrgInfo province_code = orgInfoService.getOrgInfo((String) dataI.get("PROVINCE_CODE"));
                        String province_code = queryMapper.getCodeName((String) dataI.get("PROVINCE_CODE"));
                        temp.put("PROVINCE_NAME",StringUtil.isEmpty(province_code)?"":province_code);
//                        OrgInfo eparchy_code = orgInfoService.getOrgInfo((String) dataI.get("EPARCHY_CODE"));
                        temp.put("EPARCHY_CODE_NAME",(String) dataI.get("EPARCHY_CODE"));
                        String LIST_TYPE_NAME="";
                        String listType=(String)dataI.get("LIST_TYPE");
                        LIST_TYPE_NAME = dictService.findNameByKeyNotCache("blackShowName", listType);
                        temp.put("LIST_TYPE_NAME",LIST_TYPE_NAME);
                        temp.put("SERIAL_NUMBER", DesensitizeUtil.mobilePhone((String)dataI.get("SERIAL_NUMBER")));
                        temp.put("JOIN_DATE",dataI.get("JOIN_DATE"));
                        String SOURCE_NAME="";
                        switch ((String)dataI.get("SOURCE")){
                            case "0" : SOURCE_NAME="cb黑名单"; break;
                            case "1" : SOURCE_NAME="省份黑名单"; break;
                            case "2" : SOURCE_NAME="特殊黑名单"; break;
                        }
                        temp.put("SOURCE_NAME",SOURCE_NAME);
                        temp.put("JOIN_CAUSE",dataI.get("JOIN_CAUSE"));
                        res.add(temp);
                    }
                }
            }
            log.info("进入黑名单校验checkBlack方法： success");
            return ResultUtil.success(res);
        } catch (Exception e) {
            log.info("进入黑名单校验checkBlack方法： error "+e.getMessage());
            return ResultUtil.error("9999","黑名单查询异常");
        }
    }

    @PostMapping("/checkBlackH5")
    public CustomResult checkBlackH5(@RequestBody String sig,HttpServletRequest request){
        log.info("进入黑名单校验checkBlackH5方法："+sig);
        try {
            List resList=new ArrayList();
            HashMap<String,Integer> res=new HashMap<>();
            UserInfo userInfo = sessionQueService.getSessionInfo(request);
            if (userInfo==null){
                return ResultUtil.error("9999","请求异常");
            }
            String decryptInfo = sessionQueService.decryptInfo(sig);
            HashMap hashMap = JSONObject.parseObject(decryptInfo, HashMap.class);
            String blackShow = dictService.findNameByKeyNotCache("blackShow", "1");
            if ("0".equals(blackShow)){
                log.info("进入黑名单校验checkBlack方法： 开关关闭");
                hashMap.put("dataSource",0);
            }else {
                hashMap.put("dataSource", 1);
            }
            String orderId = (String)hashMap.get("orderId");
            TfOrdCustinfo tfOrdCustinfo = tfOrdCustinfoMapper.qryTfOrdCustinfoByOrderId(orderId);
            hashMap.put("psptId",tfOrdCustinfo.getPsptId());
            JSONObject jsonObject = sessionQueService.blackListQue(hashMap);
            if ("0000".equals(jsonObject.get("STATUS"))){
                JSONObject rsp = (JSONObject)jsonObject.get("RSP");
                if ("0000".equals(rsp.get("RSP_CODE"))){
                    //查询黑名单展示那些信息
                    String blackShowInfo = dictService.findNameByKeyNotCache("blackShowInfo", "1");
                    List<String> blackShowList=new ArrayList<>();
                    if (!StringUtil.isEmpty(blackShowInfo)) {
                        String[] split = blackShowInfo.split(",");
                        for (int i = 0; i < split.length; i++) {
                            res.put(split[i],0);
                            blackShowList.add(split[i]);
                        }
                    }
                    JSONArray data=(JSONArray)rsp.get("DATA");
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject dataI = (JSONObject)data.get(i);
                        String source=(String)dataI.get("LIST_TYPE");
                        if (CollectionUtils.isEmpty(blackShowList)||!blackShowList.contains(source)){
                            continue;
                        }
                        res.put(source,res.get(source)+1);
                    }
                }
            }
            res.forEach((k,v)->{
               HashMap temp=new HashMap();
               String blackShowName = dictService.findNameByKeyNotCache("blackShowName", k);
               temp.put("name",blackShowName);
               temp.put("num",v);
               resList.add(temp);
            });
            log.info("进入黑名单校验checkBlackH5方法： success："+JSONObject.toJSONString(resList));
            return ResultUtil.success(resList);
        } catch (Exception e) {
            log.info("进入黑名单校验checkBlackH5方法： error " +e.getMessage());
            return ResultUtil.error("9999","黑名单查询异常");
        }
    }

    @GetMapping(value = "/checkLoginUser", name = "登录用户操作权限校验")
    public Boolean checkLoginUser(HttpServletRequest request){
        log.info("进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser");
        try {
//            String path = URLDecoder.decode(request.getQueryString(),"UTF-8");
            String queryString = request.getQueryString();
            String path = "";
            // 检查是否存在查询字符串
            if (queryString != null) {
                // 拆分查询字符串参数
                String[] params = queryString.split("&");
                // 遍历并输出每个参数
                for (String param : params) {
                    String[] pair = param.split("=");
                    String name = pair[0];
                    String value = pair.length > 1 ? pair[1] : "";
                    if (name.equals("path")){
                        path = value;
                    }
                   log.info("参数名: " + name + ", 参数值: " + value);
                }
            }
            String loginSerialNumber=request.getHeader("serialNumber");
            String loginStaffNo=request.getHeader("staffNo");
            log.info("staffNo:"+loginStaffNo);
            try {
            String pathNew = URLDecoder.decode(path,"UTF-8");
            List<PermInfosRsp> permInfoList;
            String permInfos = redisTemplate.opsForValue().get(PERMINFO_PREFIX+loginStaffNo);
            String sign = "1";//默认不包含
            if(permInfos != null && permInfos.length() > 0) {
                permInfoList = JSONArray.parseArray(permInfos, PermInfosRsp.class);
                for (PermInfosRsp permInfosRsp : permInfoList) {
                    if (permInfosRsp.getMenuUrl().contains(pathNew)) {
                        sign = "2";
                        break;
                    }
                }
            }
            log.info("sign:"+sign);
//            if ("1".equals(sign)) {//不包含相关页面权限
//                Dict dictDemo = dictService.findDictByKeyNotCache("allow_url_list", pathNew);
//                if (dictDemo == null) {
//                    return false;
//                }
//            }

            } catch (Exception e){
                log.info("OperationPermissionController error :"+e.getMessage());
            }
            return operationPermissionService.getLoginUserPermission(loginSerialNumber,loginStaffNo,path);
        }catch (Exception e) {
            log.info("进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser  fail:"+e.getMessage());
            return false;
        }
    }


    @GetMapping(value = "/checkLoginUserH5", name = "H5登录用户操作权限校验")
    public Boolean checkLoginUserH5(HttpServletRequest request){
        log.info("进入H5登录用户操作权限校验接口 OperationPermissionController.checkLoginUserH5");
        try {
            UserInfo userInfo = sessionQueService.getSessionInfo(request);
            if (userInfo==null){
                return false;
            }
            return operationPermissionService.getLoginUserPermissionH5(userInfo.getSerialNumber());
        }catch (Exception e) {
            log.info("进入H5登录用户操作权限校验接口 OperationPermissionController.checkLoginUserH5  fail:"+e.getMessage());
            return false;
        }
    }
}
