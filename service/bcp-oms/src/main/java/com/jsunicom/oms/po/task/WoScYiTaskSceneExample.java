package com.jsunicom.oms.po.task;

import java.util.ArrayList;
import java.util.List;

public class WoScYiTaskSceneExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYiTaskSceneExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSceneIdIsNull() {
            addCriterion("SCENE_ID is null");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNotNull() {
            addCriterion("SCENE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSceneIdEqualTo(Long value) {
            addCriterion("SCENE_ID =", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotEqualTo(Long value) {
            addCriterion("SCENE_ID <>", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThan(Long value) {
            addCriterion("SCENE_ID >", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SCENE_ID >=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThan(Long value) {
            addCriterion("SCENE_ID <", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanOrEqualTo(Long value) {
            addCriterion("SCENE_ID <=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdIn(List<Long> values) {
            addCriterion("SCENE_ID in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotIn(List<Long> values) {
            addCriterion("SCENE_ID not in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdBetween(Long value1, Long value2) {
            addCriterion("SCENE_ID between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotBetween(Long value1, Long value2) {
            addCriterion("SCENE_ID not between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("TASK_ID is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("TASK_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("TASK_ID =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("TASK_ID <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("TASK_ID >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("TASK_ID >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("TASK_ID <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("TASK_ID <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("TASK_ID in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("TASK_ID not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("TASK_ID between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("TASK_ID not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeIsNull() {
            addCriterion("TASK_SCENE_TYPE_CODE is null");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeIsNotNull() {
            addCriterion("TASK_SCENE_TYPE_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE =", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <>", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeGreaterThan(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE >", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE >=", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLessThan(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <=", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLike(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE like", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotLike(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE not like", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeIn(List<String> values) {
            addCriterion("TASK_SCENE_TYPE_CODE in", values, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotIn(List<String> values) {
            addCriterion("TASK_SCENE_TYPE_CODE not in", values, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeBetween(String value1, String value2) {
            addCriterion("TASK_SCENE_TYPE_CODE between", value1, value2, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotBetween(String value1, String value2) {
            addCriterion("TASK_SCENE_TYPE_CODE not between", value1, value2, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andField1IsNull() {
            addCriterion("FIELD1 is null");
            return (Criteria) this;
        }

        public Criteria andField1IsNotNull() {
            addCriterion("FIELD1 is not null");
            return (Criteria) this;
        }

        public Criteria andField1EqualTo(String value) {
            addCriterion("FIELD1 =", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1NotEqualTo(String value) {
            addCriterion("FIELD1 <>", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1GreaterThan(String value) {
            addCriterion("FIELD1 >", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD1 >=", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1LessThan(String value) {
            addCriterion("FIELD1 <", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1LessThanOrEqualTo(String value) {
            addCriterion("FIELD1 <=", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1Like(String value) {
            addCriterion("FIELD1 like", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1NotLike(String value) {
            addCriterion("FIELD1 not like", value, "field1");
            return (Criteria) this;
        }

        public Criteria andField1In(List<String> values) {
            addCriterion("FIELD1 in", values, "field1");
            return (Criteria) this;
        }

        public Criteria andField1NotIn(List<String> values) {
            addCriterion("FIELD1 not in", values, "field1");
            return (Criteria) this;
        }

        public Criteria andField1Between(String value1, String value2) {
            addCriterion("FIELD1 between", value1, value2, "field1");
            return (Criteria) this;
        }

        public Criteria andField1NotBetween(String value1, String value2) {
            addCriterion("FIELD1 not between", value1, value2, "field1");
            return (Criteria) this;
        }

        public Criteria andField2IsNull() {
            addCriterion("FIELD2 is null");
            return (Criteria) this;
        }

        public Criteria andField2IsNotNull() {
            addCriterion("FIELD2 is not null");
            return (Criteria) this;
        }

        public Criteria andField2EqualTo(String value) {
            addCriterion("FIELD2 =", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2NotEqualTo(String value) {
            addCriterion("FIELD2 <>", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2GreaterThan(String value) {
            addCriterion("FIELD2 >", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD2 >=", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2LessThan(String value) {
            addCriterion("FIELD2 <", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2LessThanOrEqualTo(String value) {
            addCriterion("FIELD2 <=", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2Like(String value) {
            addCriterion("FIELD2 like", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2NotLike(String value) {
            addCriterion("FIELD2 not like", value, "field2");
            return (Criteria) this;
        }

        public Criteria andField2In(List<String> values) {
            addCriterion("FIELD2 in", values, "field2");
            return (Criteria) this;
        }

        public Criteria andField2NotIn(List<String> values) {
            addCriterion("FIELD2 not in", values, "field2");
            return (Criteria) this;
        }

        public Criteria andField2Between(String value1, String value2) {
            addCriterion("FIELD2 between", value1, value2, "field2");
            return (Criteria) this;
        }

        public Criteria andField2NotBetween(String value1, String value2) {
            addCriterion("FIELD2 not between", value1, value2, "field2");
            return (Criteria) this;
        }

        public Criteria andField3IsNull() {
            addCriterion("FIELD3 is null");
            return (Criteria) this;
        }

        public Criteria andField3IsNotNull() {
            addCriterion("FIELD3 is not null");
            return (Criteria) this;
        }

        public Criteria andField3EqualTo(String value) {
            addCriterion("FIELD3 =", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3NotEqualTo(String value) {
            addCriterion("FIELD3 <>", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3GreaterThan(String value) {
            addCriterion("FIELD3 >", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD3 >=", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3LessThan(String value) {
            addCriterion("FIELD3 <", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3LessThanOrEqualTo(String value) {
            addCriterion("FIELD3 <=", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3Like(String value) {
            addCriterion("FIELD3 like", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3NotLike(String value) {
            addCriterion("FIELD3 not like", value, "field3");
            return (Criteria) this;
        }

        public Criteria andField3In(List<String> values) {
            addCriterion("FIELD3 in", values, "field3");
            return (Criteria) this;
        }

        public Criteria andField3NotIn(List<String> values) {
            addCriterion("FIELD3 not in", values, "field3");
            return (Criteria) this;
        }

        public Criteria andField3Between(String value1, String value2) {
            addCriterion("FIELD3 between", value1, value2, "field3");
            return (Criteria) this;
        }

        public Criteria andField3NotBetween(String value1, String value2) {
            addCriterion("FIELD3 not between", value1, value2, "field3");
            return (Criteria) this;
        }

        public Criteria andField4IsNull() {
            addCriterion("FIELD4 is null");
            return (Criteria) this;
        }

        public Criteria andField4IsNotNull() {
            addCriterion("FIELD4 is not null");
            return (Criteria) this;
        }

        public Criteria andField4EqualTo(String value) {
            addCriterion("FIELD4 =", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4NotEqualTo(String value) {
            addCriterion("FIELD4 <>", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4GreaterThan(String value) {
            addCriterion("FIELD4 >", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD4 >=", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4LessThan(String value) {
            addCriterion("FIELD4 <", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4LessThanOrEqualTo(String value) {
            addCriterion("FIELD4 <=", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4Like(String value) {
            addCriterion("FIELD4 like", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4NotLike(String value) {
            addCriterion("FIELD4 not like", value, "field4");
            return (Criteria) this;
        }

        public Criteria andField4In(List<String> values) {
            addCriterion("FIELD4 in", values, "field4");
            return (Criteria) this;
        }

        public Criteria andField4NotIn(List<String> values) {
            addCriterion("FIELD4 not in", values, "field4");
            return (Criteria) this;
        }

        public Criteria andField4Between(String value1, String value2) {
            addCriterion("FIELD4 between", value1, value2, "field4");
            return (Criteria) this;
        }

        public Criteria andField4NotBetween(String value1, String value2) {
            addCriterion("FIELD4 not between", value1, value2, "field4");
            return (Criteria) this;
        }

        public Criteria andField5IsNull() {
            addCriterion("FIELD5 is null");
            return (Criteria) this;
        }

        public Criteria andField5IsNotNull() {
            addCriterion("FIELD5 is not null");
            return (Criteria) this;
        }

        public Criteria andField5EqualTo(String value) {
            addCriterion("FIELD5 =", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5NotEqualTo(String value) {
            addCriterion("FIELD5 <>", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5GreaterThan(String value) {
            addCriterion("FIELD5 >", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD5 >=", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5LessThan(String value) {
            addCriterion("FIELD5 <", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5LessThanOrEqualTo(String value) {
            addCriterion("FIELD5 <=", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5Like(String value) {
            addCriterion("FIELD5 like", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5NotLike(String value) {
            addCriterion("FIELD5 not like", value, "field5");
            return (Criteria) this;
        }

        public Criteria andField5In(List<String> values) {
            addCriterion("FIELD5 in", values, "field5");
            return (Criteria) this;
        }

        public Criteria andField5NotIn(List<String> values) {
            addCriterion("FIELD5 not in", values, "field5");
            return (Criteria) this;
        }

        public Criteria andField5Between(String value1, String value2) {
            addCriterion("FIELD5 between", value1, value2, "field5");
            return (Criteria) this;
        }

        public Criteria andField5NotBetween(String value1, String value2) {
            addCriterion("FIELD5 not between", value1, value2, "field5");
            return (Criteria) this;
        }

        public Criteria andField6IsNull() {
            addCriterion("FIELD6 is null");
            return (Criteria) this;
        }

        public Criteria andField6IsNotNull() {
            addCriterion("FIELD6 is not null");
            return (Criteria) this;
        }

        public Criteria andField6EqualTo(String value) {
            addCriterion("FIELD6 =", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6NotEqualTo(String value) {
            addCriterion("FIELD6 <>", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6GreaterThan(String value) {
            addCriterion("FIELD6 >", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD6 >=", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6LessThan(String value) {
            addCriterion("FIELD6 <", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6LessThanOrEqualTo(String value) {
            addCriterion("FIELD6 <=", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6Like(String value) {
            addCriterion("FIELD6 like", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6NotLike(String value) {
            addCriterion("FIELD6 not like", value, "field6");
            return (Criteria) this;
        }

        public Criteria andField6In(List<String> values) {
            addCriterion("FIELD6 in", values, "field6");
            return (Criteria) this;
        }

        public Criteria andField6NotIn(List<String> values) {
            addCriterion("FIELD6 not in", values, "field6");
            return (Criteria) this;
        }

        public Criteria andField6Between(String value1, String value2) {
            addCriterion("FIELD6 between", value1, value2, "field6");
            return (Criteria) this;
        }

        public Criteria andField6NotBetween(String value1, String value2) {
            addCriterion("FIELD6 not between", value1, value2, "field6");
            return (Criteria) this;
        }

        public Criteria andField7IsNull() {
            addCriterion("FIELD7 is null");
            return (Criteria) this;
        }

        public Criteria andField7IsNotNull() {
            addCriterion("FIELD7 is not null");
            return (Criteria) this;
        }

        public Criteria andField7EqualTo(String value) {
            addCriterion("FIELD7 =", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7NotEqualTo(String value) {
            addCriterion("FIELD7 <>", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7GreaterThan(String value) {
            addCriterion("FIELD7 >", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD7 >=", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7LessThan(String value) {
            addCriterion("FIELD7 <", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7LessThanOrEqualTo(String value) {
            addCriterion("FIELD7 <=", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7Like(String value) {
            addCriterion("FIELD7 like", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7NotLike(String value) {
            addCriterion("FIELD7 not like", value, "field7");
            return (Criteria) this;
        }

        public Criteria andField7In(List<String> values) {
            addCriterion("FIELD7 in", values, "field7");
            return (Criteria) this;
        }

        public Criteria andField7NotIn(List<String> values) {
            addCriterion("FIELD7 not in", values, "field7");
            return (Criteria) this;
        }

        public Criteria andField7Between(String value1, String value2) {
            addCriterion("FIELD7 between", value1, value2, "field7");
            return (Criteria) this;
        }

        public Criteria andField7NotBetween(String value1, String value2) {
            addCriterion("FIELD7 not between", value1, value2, "field7");
            return (Criteria) this;
        }

        public Criteria andField8IsNull() {
            addCriterion("FIELD8 is null");
            return (Criteria) this;
        }

        public Criteria andField8IsNotNull() {
            addCriterion("FIELD8 is not null");
            return (Criteria) this;
        }

        public Criteria andField8EqualTo(String value) {
            addCriterion("FIELD8 =", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8NotEqualTo(String value) {
            addCriterion("FIELD8 <>", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8GreaterThan(String value) {
            addCriterion("FIELD8 >", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD8 >=", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8LessThan(String value) {
            addCriterion("FIELD8 <", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8LessThanOrEqualTo(String value) {
            addCriterion("FIELD8 <=", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8Like(String value) {
            addCriterion("FIELD8 like", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8NotLike(String value) {
            addCriterion("FIELD8 not like", value, "field8");
            return (Criteria) this;
        }

        public Criteria andField8In(List<String> values) {
            addCriterion("FIELD8 in", values, "field8");
            return (Criteria) this;
        }

        public Criteria andField8NotIn(List<String> values) {
            addCriterion("FIELD8 not in", values, "field8");
            return (Criteria) this;
        }

        public Criteria andField8Between(String value1, String value2) {
            addCriterion("FIELD8 between", value1, value2, "field8");
            return (Criteria) this;
        }

        public Criteria andField8NotBetween(String value1, String value2) {
            addCriterion("FIELD8 not between", value1, value2, "field8");
            return (Criteria) this;
        }

        public Criteria andField9IsNull() {
            addCriterion("FIELD9 is null");
            return (Criteria) this;
        }

        public Criteria andField9IsNotNull() {
            addCriterion("FIELD9 is not null");
            return (Criteria) this;
        }

        public Criteria andField9EqualTo(String value) {
            addCriterion("FIELD9 =", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9NotEqualTo(String value) {
            addCriterion("FIELD9 <>", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9GreaterThan(String value) {
            addCriterion("FIELD9 >", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD9 >=", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9LessThan(String value) {
            addCriterion("FIELD9 <", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9LessThanOrEqualTo(String value) {
            addCriterion("FIELD9 <=", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9Like(String value) {
            addCriterion("FIELD9 like", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9NotLike(String value) {
            addCriterion("FIELD9 not like", value, "field9");
            return (Criteria) this;
        }

        public Criteria andField9In(List<String> values) {
            addCriterion("FIELD9 in", values, "field9");
            return (Criteria) this;
        }

        public Criteria andField9NotIn(List<String> values) {
            addCriterion("FIELD9 not in", values, "field9");
            return (Criteria) this;
        }

        public Criteria andField9Between(String value1, String value2) {
            addCriterion("FIELD9 between", value1, value2, "field9");
            return (Criteria) this;
        }

        public Criteria andField9NotBetween(String value1, String value2) {
            addCriterion("FIELD9 not between", value1, value2, "field9");
            return (Criteria) this;
        }

        public Criteria andField10IsNull() {
            addCriterion("FIELD10 is null");
            return (Criteria) this;
        }

        public Criteria andField10IsNotNull() {
            addCriterion("FIELD10 is not null");
            return (Criteria) this;
        }

        public Criteria andField10EqualTo(String value) {
            addCriterion("FIELD10 =", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10NotEqualTo(String value) {
            addCriterion("FIELD10 <>", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10GreaterThan(String value) {
            addCriterion("FIELD10 >", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD10 >=", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10LessThan(String value) {
            addCriterion("FIELD10 <", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10LessThanOrEqualTo(String value) {
            addCriterion("FIELD10 <=", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10Like(String value) {
            addCriterion("FIELD10 like", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10NotLike(String value) {
            addCriterion("FIELD10 not like", value, "field10");
            return (Criteria) this;
        }

        public Criteria andField10In(List<String> values) {
            addCriterion("FIELD10 in", values, "field10");
            return (Criteria) this;
        }

        public Criteria andField10NotIn(List<String> values) {
            addCriterion("FIELD10 not in", values, "field10");
            return (Criteria) this;
        }

        public Criteria andField10Between(String value1, String value2) {
            addCriterion("FIELD10 between", value1, value2, "field10");
            return (Criteria) this;
        }

        public Criteria andField10NotBetween(String value1, String value2) {
            addCriterion("FIELD10 not between", value1, value2, "field10");
            return (Criteria) this;
        }

        public Criteria andField11IsNull() {
            addCriterion("FIELD11 is null");
            return (Criteria) this;
        }

        public Criteria andField11IsNotNull() {
            addCriterion("FIELD11 is not null");
            return (Criteria) this;
        }

        public Criteria andField11EqualTo(String value) {
            addCriterion("FIELD11 =", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11NotEqualTo(String value) {
            addCriterion("FIELD11 <>", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11GreaterThan(String value) {
            addCriterion("FIELD11 >", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD11 >=", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11LessThan(String value) {
            addCriterion("FIELD11 <", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11LessThanOrEqualTo(String value) {
            addCriterion("FIELD11 <=", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11Like(String value) {
            addCriterion("FIELD11 like", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11NotLike(String value) {
            addCriterion("FIELD11 not like", value, "field11");
            return (Criteria) this;
        }

        public Criteria andField11In(List<String> values) {
            addCriterion("FIELD11 in", values, "field11");
            return (Criteria) this;
        }

        public Criteria andField11NotIn(List<String> values) {
            addCriterion("FIELD11 not in", values, "field11");
            return (Criteria) this;
        }

        public Criteria andField11Between(String value1, String value2) {
            addCriterion("FIELD11 between", value1, value2, "field11");
            return (Criteria) this;
        }

        public Criteria andField11NotBetween(String value1, String value2) {
            addCriterion("FIELD11 not between", value1, value2, "field11");
            return (Criteria) this;
        }

        public Criteria andField12IsNull() {
            addCriterion("FIELD12 is null");
            return (Criteria) this;
        }

        public Criteria andField12IsNotNull() {
            addCriterion("FIELD12 is not null");
            return (Criteria) this;
        }

        public Criteria andField12EqualTo(String value) {
            addCriterion("FIELD12 =", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12NotEqualTo(String value) {
            addCriterion("FIELD12 <>", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12GreaterThan(String value) {
            addCriterion("FIELD12 >", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD12 >=", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12LessThan(String value) {
            addCriterion("FIELD12 <", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12LessThanOrEqualTo(String value) {
            addCriterion("FIELD12 <=", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12Like(String value) {
            addCriterion("FIELD12 like", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12NotLike(String value) {
            addCriterion("FIELD12 not like", value, "field12");
            return (Criteria) this;
        }

        public Criteria andField12In(List<String> values) {
            addCriterion("FIELD12 in", values, "field12");
            return (Criteria) this;
        }

        public Criteria andField12NotIn(List<String> values) {
            addCriterion("FIELD12 not in", values, "field12");
            return (Criteria) this;
        }

        public Criteria andField12Between(String value1, String value2) {
            addCriterion("FIELD12 between", value1, value2, "field12");
            return (Criteria) this;
        }

        public Criteria andField12NotBetween(String value1, String value2) {
            addCriterion("FIELD12 not between", value1, value2, "field12");
            return (Criteria) this;
        }

        public Criteria andField13IsNull() {
            addCriterion("FIELD13 is null");
            return (Criteria) this;
        }

        public Criteria andField13IsNotNull() {
            addCriterion("FIELD13 is not null");
            return (Criteria) this;
        }

        public Criteria andField13EqualTo(String value) {
            addCriterion("FIELD13 =", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13NotEqualTo(String value) {
            addCriterion("FIELD13 <>", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13GreaterThan(String value) {
            addCriterion("FIELD13 >", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD13 >=", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13LessThan(String value) {
            addCriterion("FIELD13 <", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13LessThanOrEqualTo(String value) {
            addCriterion("FIELD13 <=", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13Like(String value) {
            addCriterion("FIELD13 like", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13NotLike(String value) {
            addCriterion("FIELD13 not like", value, "field13");
            return (Criteria) this;
        }

        public Criteria andField13In(List<String> values) {
            addCriterion("FIELD13 in", values, "field13");
            return (Criteria) this;
        }

        public Criteria andField13NotIn(List<String> values) {
            addCriterion("FIELD13 not in", values, "field13");
            return (Criteria) this;
        }

        public Criteria andField13Between(String value1, String value2) {
            addCriterion("FIELD13 between", value1, value2, "field13");
            return (Criteria) this;
        }

        public Criteria andField13NotBetween(String value1, String value2) {
            addCriterion("FIELD13 not between", value1, value2, "field13");
            return (Criteria) this;
        }

        public Criteria andField14IsNull() {
            addCriterion("FIELD14 is null");
            return (Criteria) this;
        }

        public Criteria andField14IsNotNull() {
            addCriterion("FIELD14 is not null");
            return (Criteria) this;
        }

        public Criteria andField14EqualTo(String value) {
            addCriterion("FIELD14 =", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14NotEqualTo(String value) {
            addCriterion("FIELD14 <>", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14GreaterThan(String value) {
            addCriterion("FIELD14 >", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD14 >=", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14LessThan(String value) {
            addCriterion("FIELD14 <", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14LessThanOrEqualTo(String value) {
            addCriterion("FIELD14 <=", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14Like(String value) {
            addCriterion("FIELD14 like", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14NotLike(String value) {
            addCriterion("FIELD14 not like", value, "field14");
            return (Criteria) this;
        }

        public Criteria andField14In(List<String> values) {
            addCriterion("FIELD14 in", values, "field14");
            return (Criteria) this;
        }

        public Criteria andField14NotIn(List<String> values) {
            addCriterion("FIELD14 not in", values, "field14");
            return (Criteria) this;
        }

        public Criteria andField14Between(String value1, String value2) {
            addCriterion("FIELD14 between", value1, value2, "field14");
            return (Criteria) this;
        }

        public Criteria andField14NotBetween(String value1, String value2) {
            addCriterion("FIELD14 not between", value1, value2, "field14");
            return (Criteria) this;
        }

        public Criteria andField15IsNull() {
            addCriterion("FIELD15 is null");
            return (Criteria) this;
        }

        public Criteria andField15IsNotNull() {
            addCriterion("FIELD15 is not null");
            return (Criteria) this;
        }

        public Criteria andField15EqualTo(String value) {
            addCriterion("FIELD15 =", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15NotEqualTo(String value) {
            addCriterion("FIELD15 <>", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15GreaterThan(String value) {
            addCriterion("FIELD15 >", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD15 >=", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15LessThan(String value) {
            addCriterion("FIELD15 <", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15LessThanOrEqualTo(String value) {
            addCriterion("FIELD15 <=", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15Like(String value) {
            addCriterion("FIELD15 like", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15NotLike(String value) {
            addCriterion("FIELD15 not like", value, "field15");
            return (Criteria) this;
        }

        public Criteria andField15In(List<String> values) {
            addCriterion("FIELD15 in", values, "field15");
            return (Criteria) this;
        }

        public Criteria andField15NotIn(List<String> values) {
            addCriterion("FIELD15 not in", values, "field15");
            return (Criteria) this;
        }

        public Criteria andField15Between(String value1, String value2) {
            addCriterion("FIELD15 between", value1, value2, "field15");
            return (Criteria) this;
        }

        public Criteria andField15NotBetween(String value1, String value2) {
            addCriterion("FIELD15 not between", value1, value2, "field15");
            return (Criteria) this;
        }

        public Criteria andField16IsNull() {
            addCriterion("FIELD16 is null");
            return (Criteria) this;
        }

        public Criteria andField16IsNotNull() {
            addCriterion("FIELD16 is not null");
            return (Criteria) this;
        }

        public Criteria andField16EqualTo(String value) {
            addCriterion("FIELD16 =", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16NotEqualTo(String value) {
            addCriterion("FIELD16 <>", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16GreaterThan(String value) {
            addCriterion("FIELD16 >", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD16 >=", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16LessThan(String value) {
            addCriterion("FIELD16 <", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16LessThanOrEqualTo(String value) {
            addCriterion("FIELD16 <=", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16Like(String value) {
            addCriterion("FIELD16 like", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16NotLike(String value) {
            addCriterion("FIELD16 not like", value, "field16");
            return (Criteria) this;
        }

        public Criteria andField16In(List<String> values) {
            addCriterion("FIELD16 in", values, "field16");
            return (Criteria) this;
        }

        public Criteria andField16NotIn(List<String> values) {
            addCriterion("FIELD16 not in", values, "field16");
            return (Criteria) this;
        }

        public Criteria andField16Between(String value1, String value2) {
            addCriterion("FIELD16 between", value1, value2, "field16");
            return (Criteria) this;
        }

        public Criteria andField16NotBetween(String value1, String value2) {
            addCriterion("FIELD16 not between", value1, value2, "field16");
            return (Criteria) this;
        }

        public Criteria andField17IsNull() {
            addCriterion("FIELD17 is null");
            return (Criteria) this;
        }

        public Criteria andField17IsNotNull() {
            addCriterion("FIELD17 is not null");
            return (Criteria) this;
        }

        public Criteria andField17EqualTo(String value) {
            addCriterion("FIELD17 =", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17NotEqualTo(String value) {
            addCriterion("FIELD17 <>", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17GreaterThan(String value) {
            addCriterion("FIELD17 >", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD17 >=", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17LessThan(String value) {
            addCriterion("FIELD17 <", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17LessThanOrEqualTo(String value) {
            addCriterion("FIELD17 <=", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17Like(String value) {
            addCriterion("FIELD17 like", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17NotLike(String value) {
            addCriterion("FIELD17 not like", value, "field17");
            return (Criteria) this;
        }

        public Criteria andField17In(List<String> values) {
            addCriterion("FIELD17 in", values, "field17");
            return (Criteria) this;
        }

        public Criteria andField17NotIn(List<String> values) {
            addCriterion("FIELD17 not in", values, "field17");
            return (Criteria) this;
        }

        public Criteria andField17Between(String value1, String value2) {
            addCriterion("FIELD17 between", value1, value2, "field17");
            return (Criteria) this;
        }

        public Criteria andField17NotBetween(String value1, String value2) {
            addCriterion("FIELD17 not between", value1, value2, "field17");
            return (Criteria) this;
        }

        public Criteria andField18IsNull() {
            addCriterion("FIELD18 is null");
            return (Criteria) this;
        }

        public Criteria andField18IsNotNull() {
            addCriterion("FIELD18 is not null");
            return (Criteria) this;
        }

        public Criteria andField18EqualTo(String value) {
            addCriterion("FIELD18 =", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18NotEqualTo(String value) {
            addCriterion("FIELD18 <>", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18GreaterThan(String value) {
            addCriterion("FIELD18 >", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD18 >=", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18LessThan(String value) {
            addCriterion("FIELD18 <", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18LessThanOrEqualTo(String value) {
            addCriterion("FIELD18 <=", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18Like(String value) {
            addCriterion("FIELD18 like", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18NotLike(String value) {
            addCriterion("FIELD18 not like", value, "field18");
            return (Criteria) this;
        }

        public Criteria andField18In(List<String> values) {
            addCriterion("FIELD18 in", values, "field18");
            return (Criteria) this;
        }

        public Criteria andField18NotIn(List<String> values) {
            addCriterion("FIELD18 not in", values, "field18");
            return (Criteria) this;
        }

        public Criteria andField18Between(String value1, String value2) {
            addCriterion("FIELD18 between", value1, value2, "field18");
            return (Criteria) this;
        }

        public Criteria andField18NotBetween(String value1, String value2) {
            addCriterion("FIELD18 not between", value1, value2, "field18");
            return (Criteria) this;
        }

        public Criteria andField19IsNull() {
            addCriterion("FIELD19 is null");
            return (Criteria) this;
        }

        public Criteria andField19IsNotNull() {
            addCriterion("FIELD19 is not null");
            return (Criteria) this;
        }

        public Criteria andField19EqualTo(String value) {
            addCriterion("FIELD19 =", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19NotEqualTo(String value) {
            addCriterion("FIELD19 <>", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19GreaterThan(String value) {
            addCriterion("FIELD19 >", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD19 >=", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19LessThan(String value) {
            addCriterion("FIELD19 <", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19LessThanOrEqualTo(String value) {
            addCriterion("FIELD19 <=", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19Like(String value) {
            addCriterion("FIELD19 like", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19NotLike(String value) {
            addCriterion("FIELD19 not like", value, "field19");
            return (Criteria) this;
        }

        public Criteria andField19In(List<String> values) {
            addCriterion("FIELD19 in", values, "field19");
            return (Criteria) this;
        }

        public Criteria andField19NotIn(List<String> values) {
            addCriterion("FIELD19 not in", values, "field19");
            return (Criteria) this;
        }

        public Criteria andField19Between(String value1, String value2) {
            addCriterion("FIELD19 between", value1, value2, "field19");
            return (Criteria) this;
        }

        public Criteria andField19NotBetween(String value1, String value2) {
            addCriterion("FIELD19 not between", value1, value2, "field19");
            return (Criteria) this;
        }

        public Criteria andField20IsNull() {
            addCriterion("FIELD20 is null");
            return (Criteria) this;
        }

        public Criteria andField20IsNotNull() {
            addCriterion("FIELD20 is not null");
            return (Criteria) this;
        }

        public Criteria andField20EqualTo(String value) {
            addCriterion("FIELD20 =", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20NotEqualTo(String value) {
            addCriterion("FIELD20 <>", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20GreaterThan(String value) {
            addCriterion("FIELD20 >", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20GreaterThanOrEqualTo(String value) {
            addCriterion("FIELD20 >=", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20LessThan(String value) {
            addCriterion("FIELD20 <", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20LessThanOrEqualTo(String value) {
            addCriterion("FIELD20 <=", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20Like(String value) {
            addCriterion("FIELD20 like", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20NotLike(String value) {
            addCriterion("FIELD20 not like", value, "field20");
            return (Criteria) this;
        }

        public Criteria andField20In(List<String> values) {
            addCriterion("FIELD20 in", values, "field20");
            return (Criteria) this;
        }

        public Criteria andField20NotIn(List<String> values) {
            addCriterion("FIELD20 not in", values, "field20");
            return (Criteria) this;
        }

        public Criteria andField20Between(String value1, String value2) {
            addCriterion("FIELD20 between", value1, value2, "field20");
            return (Criteria) this;
        }

        public Criteria andField20NotBetween(String value1, String value2) {
            addCriterion("FIELD20 not between", value1, value2, "field20");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}