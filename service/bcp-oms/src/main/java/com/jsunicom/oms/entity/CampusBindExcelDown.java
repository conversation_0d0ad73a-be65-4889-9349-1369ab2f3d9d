package com.jsunicom.oms.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * .
 *
 * @ClassName: CampusBindExcelDown
 * @Auther: LJ<PERSON>
 * @Date: 2022/3/26 16:40
 * @Version: bcpV1.0
 * @Description: down
 **/
@Data
@Getter
@Setter
public class CampusBindExcelDown extends BaseRowModel implements Serializable {

  //C.CAMPUS_ID,C.SCHOOL_NAME , C.CAMPUS_NAME,A1.PARTNER_NAME,A1.MBL_NBR,C.ORG_CODE,C.CREATE_TIME
  private static final long serialVersionUID = 11153041204154948L;


  @ExcelProperty(index = 0,value = "校区ID")
  @JSONField(name = "CAMPUS_ID")
  private Long campusId;   //null

  @ExcelProperty(index = 1,value = "学校名称")
  @JSONField(name = "SCHOOL_NAME")
  private String schoolName;   //null

  @ExcelProperty(index = 2,value = "校区名称")
  @JSONField(name = "CAMPUS_NAME")
  private String campusName;   //null

  @ExcelProperty(index = 3,value = "校区经理")
  @JSONField(name = "PARTNER_NAME")
  private String partnerName;   //null

  @ExcelProperty(index = 4,value = "校区经理手机号")
  @JSONField(name = "MBL_NBR")
  private String mblNbr;   //null

  @ExcelProperty(index = 5,value = "地市名称")
  @JSONField(name = "ORG_NAME")
  private String orgName;   //null

  @ExcelProperty(index = 6,value = "地市编码")
  @JSONField(name = "ORG_CODE")
  private String orgCode;   //null

  @ExcelProperty(index = 6,value = "创建时间")
  @JSONField(name = "CREATE_TIME")
  private java.sql.Timestamp createTime;   //null


}
