package com.jsunicom.oms.timer.order;

import com.jsunicom.oms.service.order.OrderSchoolReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SchoolOrderHistoryReportJob {

    @Autowired
    private OrderSchoolReportService orderSchoolReportService;

    @XxlJob("schoolOrderHistoryReport")
    public ReturnT<String> synSchoolOrderReport(String param){
        try {
            log.info("SynSchoolReportHistory is starting work");
            orderSchoolReportService.SynSchoolReportHistory();
            log.info("SynSchoolReportHistory is ending");
        }catch (Exception e){
            log.info("schoolOrderHistoryReport异常:{}",e);
            XxlJobLogger.log("schoolOrderHistoryReportException:"+e);
        }
        return ReturnT.SUCCESS;
    }
}
