package com.jsunicom.oms.service.order.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.oms.common.constants.CommonProperties;
import com.jsunicom.oms.mapper.order.TfOrdCustinfoMapper;
import com.jsunicom.oms.mapper.order.TfOrdGoodsMainMapper;
import com.jsunicom.oms.mapper.order.TfOrdMainMapper;
import com.jsunicom.oms.mapper.order.WoActiveValidMapper;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.po.order.req.WoActiveValidBody;
import com.jsunicom.oms.po.order.req.WoActiveValidInModel;
import com.jsunicom.oms.po.order.req.WoActiveValidOrderList;
import com.jsunicom.oms.po.order.req.WoActiveValidOutModel;
import com.jsunicom.oms.po.order.tf.TfOrdCustinfo;
import com.jsunicom.oms.po.order.tf.TfOrdGoodsMain;
import com.jsunicom.oms.service.order.WoActiveValidService;
import com.jsunicom.oms.utils.HttpUtil;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class WoActiveValidServiceImpl implements WoActiveValidService {

    @Autowired
    private WoActiveValidMapper woActiveValidDao;

    @Autowired
    private TfOrdGoodsMainMapper tfOrdGoodsMainMapper;

    @Resource
    private CommonProperties commonProperties;

    @Autowired
    private TfOrdCustinfoMapper tfOrdCustinfoMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;


    @Override
    public WoActiveValidOutModel woActiveValid(WoActiveValidInModel inModel) throws Exception {
        WoActiveValidOutModel outModel = new WoActiveValidOutModel();

        try {
            Map<String, Object> paramMap = checkParam(inModel);

            List<WoActiveValidOrderList> orderLists = getOrderList(paramMap);

            outModel.setOrderList(orderLists);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new Exception("激活校验接口出错," + (StringUtils.isEmpty(e.getMessage()) ? "系统内部异常" : e.getMessage()));
        }

        return outModel;
    }
    private Map<String,Object> checkParam(WoActiveValidInModel inModel) throws Exception {

        Map<String, Object> paramMap = new HashMap<>();
        if (inModel == null) {
            throw new Exception("传入参数不能为空!");
        }
        WoActiveValidBody inBody = inModel.getOrderBody();
        if (inBody == null) {
            throw new Exception("传入参数体不能为空!");
        }
        //传订单号不需要传其他项
        String orderId = inBody.getOrderId();

        if(StringUtils.isEmpty(orderId)){
            String receivePhone = inBody.getReceivePhone();
            String psptId = inBody.getPsptId();
            if (StringUtils.isEmpty(receivePhone) && StringUtils.isEmpty(psptId)) {
                throw new Exception("身份证和联系电话必传一个");
            }
            String iccid = inBody.getIccid();
            if (StringUtils.isEmpty(iccid)) {
                throw new Exception("iccid为必传项");
            }
            if (iccid.trim().length() < 6) {
                throw new Exception("iccid最少须传6位");
            }
            paramMap.put("receivePhone", receivePhone);
            paramMap.put("iccid", iccid);
            paramMap.put("psptId", psptId);

        }else {

            paramMap.put("orderId",orderId);
        }
        paramMap.put("sign", inModel.getOrderHead().getSign());
        return paramMap;

    }

    private List<WoActiveValidOrderList> getOrderList(Map<String, Object> param)throws Exception {
        List<WoActiveValidOrderList> resultList = new ArrayList<>();
        String sign = param.get("sign").toString();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        List<Map<String, Object>> orderList = woActiveValidDao.getActiveOrders(param);

        for (Map<String, Object> eachOrder : orderList) {
            WoActiveValidOrderList eachValidOrder = new WoActiveValidOrderList();
            String staffId = (String) eachOrder.get("STAFF_ID");
            String channelId = (String) eachOrder.get("CHANNEL_ID");
            String orderId = (String) eachOrder.get("ORDER_ID");
            String goodsOrderId = (String) eachOrder.get("GOODS_ORDER_ID");
            String custName = (String) eachOrder.get("CUST_NAME");
            String psptId = (String) eachOrder.get("PSPT_ID");
            String orderState = (String) eachOrder.get("ORDER_STATE");
            String eparchyCode = (String) eachOrder.get("EPARCHY_CODE");
       //     String areaCode = (String) eachOrder.get("AREA_CODE");
            String deiveryType = (String) eachOrder.get("DELIVERY_TYPE");
            Timestamp inTime = (Timestamp)eachOrder.get("IN_TIME");
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String afterTime = (String)eachOrder.get("AFTERTIME");
            Date date = dateFormat.parse(afterTime);
            String canWL = inTime.before(date) ? "1" : "0";

            Date endDate = (Date) eachOrder.get("END_DATE");
            String addresss = (String) eachOrder.get("PSPT_ADDRESSS");
            String linkPerson = (String) eachOrder.get("LINK_PERSON");
            String phone = (String) eachOrder.get("PHONE");
            String goodsId = (String) eachOrder.get("GOODS_ID");

            Map<String, Object> subParam = new HashMap<>();
            subParam.put("orderId", orderId);

            String state = null;
            Map<String, Object> nodeParam = new HashMap<>();
            nodeParam.put("goodsOrderId", goodsOrderId);
            nodeParam.put("orderId", orderId);
            nodeParam.put("orderState", orderState);
            nodeParam.put("canWL", canWL);
            nodeParam.put("sign", sign);
            nodeParam.put("deiveryType", deiveryType);

            //add by zhangll9_20220915_begin   校验号码是否已返档
            nodeParam.put("psptId", psptId);
            nodeParam.put("eparchyCode", eparchyCode);
            //add by zhangll9_end

            // 对外提供接口为1和0，代码也就改成1和0了
            // 查询配送方式, 如果是非DT01, 直接返回2
            state = isCurNodeActive(nodeParam);
            eachValidOrder.setActiveState(state);
            eachValidOrder.setStaffId(staffId);
            eachValidOrder.setChannelId(channelId);
            List<Map<String, Object>> packageInfoList = woActiveValidDao.getPackageInfo(subParam);
            if (packageInfoList != null && !packageInfoList.isEmpty()) {
                for (Map<String, Object> packageInfo : packageInfoList) {
                    String itemType = (String) packageInfo.get("ATTR_CODE");
                    if ("0006".equals(itemType)) {
                        String itemId = (String) packageInfo.get("ATTR_VALUE");
                        if (StringUtils.isNotEmpty(itemId)) {
                            eachValidOrder.setSerialNumber(itemId);
                        }
                    } else if ("0002".equals(itemType)) {
                        String itemName = (String) packageInfo.get("VALUE_NAME");
                        if (StringUtils.isNotEmpty(itemName)) {
                            eachValidOrder.setPackageBrand(itemName);
                        }
                    }
                }
            }

            eachValidOrder.setGoodsOrderId(goodsOrderId);
            eachValidOrder.setGoodsState(orderState);
            eachValidOrder.setOrderCenterId(orderId);
        //    eachValidOrder.setProductOrderId(productOrderId);
            eachValidOrder.setPsptId(psptId);
            eachValidOrder.setCustName(custName);
            eachValidOrder.setEparchyCode(eparchyCode);
            eachValidOrder.setAreaCode(eparchyCode);

            eachValidOrder.setPsptAddress(addresss);
            // LOGGER.info("WoActiveValidOrderListEndDate"+endDate);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            if (ObjectUtils.isEmpty(endDate)){
                eachValidOrder.setPsptValidity("");
            }else{
                log.info("WoActiveValidOrderListFormatEndDate"+simpleDateFormat.format(endDate));
                eachValidOrder.setPsptValidity(simpleDateFormat.format(endDate));
            }
            eachValidOrder.setLinkPerson(linkPerson);
            eachValidOrder.setLinkPhone(phone);
            TdMSysDict dictParam = new TdMSysDict();
            dictParam.setParamType("goodsIdRH");
            dictParam.setParamKey("goodsIdRH");
            TdMSysDict dictRH = tdMSysDictMapper.query(dictParam);
            log.info("WoActiveValidOrderListGoodsIdRH:"+dictRH);
            String goodsIdStr = "";
            if (dictRH != null) {
                goodsIdStr = dictRH.getParamValue();
            }
            eachValidOrder.setIsRongHe(goodsIdStr.contains(goodsId) ? "1" : "0");

            List<Map<String, Object>> activeInfoList = woActiveValidDao.getActiveInfo(subParam);
            if (activeInfoList != null && activeInfoList.size() > 0) {
                eachValidOrder.setActiveTime(Integer.toString(activeInfoList.size()));
                Date createDate = (Date) activeInfoList.get(0).get("CREATE_DATE");
                eachValidOrder.setLastActiveTime(sdf.format(createDate));
            } else {
                eachValidOrder.setActiveTime("0");
            }

            resultList.add(eachValidOrder);
        }

        return resultList;
    }

    private String isCurNodeActive(Map<String, Object> param)throws Exception {

    //    List<Map<String, Object>> curNodeInfos = woActiveValidDao.getCurNodeInfo(param);
        String canWL = param.get("canWL")==null ? null : param.get("canWL").toString();
        String sign = param.get("sign")==null ? null : param.get("sign").toString();
        String deiveryType = param.get("deiveryType")==null ? null : param.get("deiveryType").toString();

        Object orderState = param.get("orderState");
        // 校验号码是否已返档
        if(orderState.equals("0020")){
            //待激活
            String tag =isFanDang(param);
            if(!tag.equals("")){
                return tag;
            }
        }
        if (orderState.equals("0020")) {
            if("0".equals(canWL) && sign != null && sign.startsWith("weixin") && !"DT01".equals(deiveryType)){
                return "2"; // 激活阶段的 微信非侧物流订单 在规定时间后 不允许激活
            }
            return "0"; // 激活阶段, 允许激活
        }

        // 处于“资料补录”环节的订单 返回3
        if (orderState.equals("0030") || orderState.equals("0031")) {
            return "3"; //
        }

        return "1"; // 没有规定阶段的
    }

    //add by zhangll9_20220915   校验号码是否已返档
    private String isFanDang(Map<String, Object> param)throws Exception {
        //增加查询三户资料，若存在在网记录，且身份证号码和订单记录中的证件号一致，则返回4，不一致返回5
        String orderId=param.get("orderId").toString();
        String goodsOrderId=param.get("goodsOrderId").toString();
        String psptIdOrd=param.get("psptId").toString();

        TfOrdGoodsMain tfOrdGoodsMain = tfOrdGoodsMainMapper.selectByPrimaryKey(goodsOrderId);

        String mainNumber="";
        String psptId="";

        if (tfOrdGoodsMain != null) {
            mainNumber=tfOrdGoodsMain.getMainNumber();
        }
        if(StringUtil.isNotBlank(mainNumber)){

            //调用三户资料查询
            Map<String, Object> param1 = new HashMap<String, Object>();
            Map<String, Object> op = null;
            param1.put("eparchyCode", param.get("eparchyCode").toString());
            List<Map<String, Object>> operatorsList = woActiveValidDao.queryExistOperators(param1);
            //新增根据商品类型匹配, 若未匹配到工号, 则使用默认工号
            if (operatorsList != null && !operatorsList.isEmpty()) {
                for (Map<String, Object> operator : operatorsList) {
                    if (StringUtils.equals((String) operator.get("GOODS_TYPE"), tfOrdGoodsMain.getGoodsType())) {
                        op = operator;
                        break;
                    }
                    if (StringUtils.isBlank((String) operator.get("GOODS_TYPE"))) {
                        op = operator;
                    }
                }
            }
            if (op == null) {
                throw new Exception("orderId:" + orderId + "：操作人信息查询为空。。");
            }
            JSONObject rspData= checkUserInfo(mainNumber,orderId,op);
            if(rspData.getString("rspCode").equals("0000")){
                if(rspData.get("data")!=null){
                    JSONObject data= rspData.getJSONObject("data");
                    if(data.containsKey("custInfo") && data.containsKey("userInfo")){
                        psptId=data.getJSONObject("custInfo").getString("certCode");
                    }
                }
            }

            log.info("====三户查询后证件号=="+psptId);

            if(StringUtil.isNotBlank(psptId)){
                //已开户
                log.info("====订单证件号=="+psptIdOrd);
                if(psptId.equals(psptIdOrd)){
                    Map<String, Object> insertMap = new HashMap<>();
             /*       insertMap.put("order_id",orderId);
                    insertMap.put("task_key",taskKey);
                    insertMap.put("eparchy_code",param.get("eparchyCode").toString());
                    insertMap.put("serial_number",mainNumber);
                    insertMap.put("remark","/order/woActiveValid");
                    woActiveValidDao.insertSkipTask(insertMap);*/
                    log.info("====入TF_ORD_SKIPTASK表=="+orderId);
                    return "4"; //该号码已返档,入TF_ORD_SKIPTASK表
                }else{
                    return "5"; //该号码已被其他客户使用
                }
            }

        }

        return "";
    }

    private JSONObject checkUserInfo(String serialNumber,String orderId,Map<String, Object> op)throws Exception{
        JSONObject dataUser =new JSONObject();
        String rspCode="8888";
        String rspDesc="";
        try{
            JSONObject uniBssBody = new JSONObject();
            JSONObject uniBssReq = new JSONObject();
            JSONObject msg = new JSONObject();
            String operatorId=op.get("OPERATOR_ID").toString();
            String city=op.get("CITY").toString();
            String channelId=op.get("CHANNEL_ID").toString();
            String channelType=op.get("CHANNEL_TYPE").toString();
            String url = commonProperties.getSpthreepartcheck();

            msg.put("serialNumber",serialNumber);
            msg.put("province", "34");
            msg.put("city",city);
            msg.put("district",city);
            msg.put("channelType", channelType);
            msg.put("infoList", "CUST");
            msg.put("operatorId", operatorId);
            msg.put("channelId", channelId);
            msg.put("tradeTypeCode", "9999");
            uniBssReq.put("MSG", msg);
            uniBssReq.put("APPKEY", "jsordcen");
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            uniBssReq.put("APPTX", df.format(new Date()));
            uniBssBody.put("SP_THREEPART_CHECK_REQ", uniBssReq);
            JSONObject uniBssAttached = new JSONObject();
            uniBssAttached.put("MEDIA_INFO", "");

            String responseBody = HttpUtil.sendRequest(url, uniBssBody, uniBssAttached,"spthreepartcheck");
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            JSONObject head = responseJson.getJSONObject("UNI_BSS_HEAD");
            if (StringUtils.isNotBlank(head.getString("RESP_CODE"))) {
                if (!head.getString("RESP_CODE").equals("00000")) {
                    rspDesc=head.getString("RESP_DESC");
                }else{
                    //调用成功
                    JSONObject body = responseJson.getJSONObject("UNI_BSS_BODY");
                    JSONObject rsp = body.getJSONObject("SP_THREEPART_CHECK_RSP").getJSONObject("RSP");
                    String code=rsp.getString("RSP_CODE");
                    if(!"0000".equals(code)){
                    }else{
                        rspCode="0000";
                        JSONArray list=rsp.getJSONArray("DATA");
                        if(list.size()>0){
                            JSONObject rspData=(JSONObject)list.get(0);
                            dataUser.put("rspCode",rspCode);
                            dataUser.put("rspDesc",rspDesc);
                            dataUser.put("data",rspData);
                            return dataUser;

                           /* if(rspData.containsKey("custInfo") && rspData.containsKey("userInfo")){

                                dataUser.put("eparchyCode",rspData.getJSONObject("userInfo").getString("eparchyCode"));
                                dataUser.put("certCode",rspData.getJSONObject("custInfo").getString("custInfo"));
                                dataUser.put("custName",rspData.getJSONObject("custInfo").getString("custName"));
                            }*/

                        }

                    }
                }
            } else {
                rspDesc="三户资料查询，响应报文头中的RESP_CODE为空";
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new Exception("orderId" + orderId + "三户校验失败:" + e.getMessage());
        }
        dataUser.put("rspCode",rspCode);
        dataUser.put("rspDesc",rspDesc);
        return dataUser;
    }

}
