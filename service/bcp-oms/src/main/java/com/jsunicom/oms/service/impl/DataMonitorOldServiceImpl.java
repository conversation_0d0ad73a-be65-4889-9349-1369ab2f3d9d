package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.common.DateTimeUtil;
import com.jsunicom.oms.mapper.base.DataMonitorDao;
import com.jsunicom.oms.model.base.BasicModel;
import com.jsunicom.oms.model.base.DataMonitor;
import com.jsunicom.oms.service.DataMonitorFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: DataMonitorOldServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-18  21:22
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class DataMonitorOldServiceImpl implements DataMonitorFacade {
    @Autowired
    private DataMonitorDao dataMonitorDao;
    
    @Override
    public DataMonitor save(DataMonitor dataMonitor) {
        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(operateDesc);
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());
        int cnt=0;
        try{
            cnt = dataMonitorDao.save(dataMonitor);
        }catch (Exception e){
            log.error("数据监控保存异常",e);
        }
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    /**
     *
     * add by yangjinsong  2019-5-30
     *
     * **/
    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, String operateDesc,String preOperateDesc) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(operateDesc);
        dataMonitor.setPreviousOperateDesc(preOperateDesc);
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());
        int cnt=0;
        try{
            cnt = dataMonitorDao.save(dataMonitor);
        }catch (Exception e){
            log.error("数据监控保存异常",e);
        }
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }



    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, BasicModel model) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(model.toString());
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());

        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }

    @Override
    public DataMonitor save(String monitorModules, String monitorType, String dataId, String operateBy, BasicModel model,BasicModel previousModel) {
        DataMonitor dataMonitor = new DataMonitor();
        dataMonitor.setMonitorModules(monitorModules);
        dataMonitor.setMonitorType(monitorType);
        dataMonitor.setDataId(dataId);
        dataMonitor.setOperateBy(operateBy);
        dataMonitor.setOperateDesc(model!=null?model.toString():"");
        dataMonitor.setPreviousOperateDesc(previousModel!=null?previousModel.toString():"");
        dataMonitor.setOperateTime(DateTimeUtil.currentTime());

        int cnt = dataMonitorDao.save(dataMonitor);
        if (cnt <= 0) {
            return null;
        }
        return dataMonitor;
    }
}
