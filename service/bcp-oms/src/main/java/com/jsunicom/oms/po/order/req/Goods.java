package com.jsunicom.oms.po.order.req;

import com.jsunicom.oms.entity.GoodsFees;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description = "订单请求报文商品信息")
@Data
public class Goods implements Serializable {
    private static final long serialVersionUID = 1L;

    private String goodsOrderId;//商品主表id

    private String extGoodsId;//外部订单号（短）

    private String extGoodsDetail;//外部订单号（短）

    private String goodsId;//商品编码

    private String goodsName;//商品名称

    private String goodsDesc;//商品描述

    private String goodsType;//商品类型，参考编码PRD_000001

    private String busiType;//业务类型，参考编码PRD_000002

    private String goodsPrice;//商品价格（元），默认0，精确到分

    private String goodsNum;//商品数量

    private String goodsUrl;//商品图片URL

    private String extWebUrl;//触点商品详情URL

    private String productId;

    private String productName;

    private String productDesc;

    private String productType;

    /**
     * 主号码
     */
    private String mainNumber;
    private List<Products> products;//产品信息
    private List<GoodsProps> goodsProps;//商品属性
    private GoodsCustInfo goodsCustInfo;//商品客户信息

    private String specType;

    private List<GoodsFees> goodsFees;
}
