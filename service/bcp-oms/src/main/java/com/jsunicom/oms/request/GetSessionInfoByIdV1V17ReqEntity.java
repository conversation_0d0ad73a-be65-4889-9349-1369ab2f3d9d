/**
 * Copyright (c) 2016-Now http://open.chinaunicom.cn All rights reserved.
 */
package com.jsunicom.oms.request;


import java.io.Serializable;

public class GetSessionInfoByIdV1V17ReqEntity implements Serializable {
    //http://10.124.150.230:8000/api/hnMarketing/sessionInfoService/getSessionInfoById/v1
    /**
     * 描述： , 长度限制：
     */
     public Req req;

    /**
     * 描述： , 长度限制：
     */
     public Req getReq() {
        return req;
     }

     public Req setReq(Req req) {
        this.req = req;
        return this.req;
     }
public static class Req implements Serializable {
    /**
     * 描述：sessionId , 长度限制：0-100
     */
     public String sessionId;

    /**
     * 描述：sessionId , 长度限制：0-100
     */
     public String getSessionId() {
        return sessionId;
     }

     public Req setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
     }
  }
}
