package com.jsunicom.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.dto.CampusQuery;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.dto.campus.WoSchoolCampusExtendDto;
import com.jsunicom.oms.model.campus.Campus;
import com.jsunicom.oms.po.WoSchoolCampus;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-24-10:10
 */
public interface CampusService {
    ArrayList<Campus> queryCampusList(com.jsunicom.oms.model.campus.CampusQuery campusQuery);

    PageInfo<Map<String, Object>> queryByCondition(CampusQuery campusQuery,String LoginSerialNumber,String LoginStaffNo);

    void modify(WoSchoolCampusExtendDto campus, String createBy);

    boolean isExist(String schoolName, String campusName);

    void save(List<WoSchoolCampus> campuses,String createBy);

    List<Campus> getAllCampus();

    boolean checkLoginUserRole(WoSchoolCampusExtendDto campus, String staffNo) throws Exception;

    /**
     * 导入销售经理数据
     * @param file Excel文件
     * @return 导入结果
     */
    CustomResult upLoadSaleManager(MultipartFile file);

    /**
     * 生成销售经理导入模板（xlsx格式）
     * @param response HttpServletResponse
     */
    void generateSalesManagerTemplate(javax.servlet.http.HttpServletResponse response);
}
