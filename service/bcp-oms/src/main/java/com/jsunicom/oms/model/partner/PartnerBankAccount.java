package com.jsunicom.oms.model.partner;

import com.jsunicom.oms.model.base.BasicModel;

public class PartnerBankAccount extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long partnerId;
    private String partnerName;
    private String partnerType;
    private Long merchantId;
    private String merchantName;
    private Byte cardType;
    private String woAcct;
    private String bankCode;
    private String bankName;
    private String cardNo;
    private String cardName;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;

    private String acctNo;
    private String checkFlag;

    private String partnerCertNo;
    private String loginerName;

    public String getLoginerName() {
        return loginerName;
    }

    public void setLoginerName(String loginerName) {
        this.loginerName = loginerName;
    }

    public String getPartnerCertNo() {
        return partnerCertNo;
    }

    public void setPartnerCertNo(String partnerCertNo) {
        this.partnerCertNo = partnerCertNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public Long getId() {
        return id;
   }

    public void setId(Long id) {
        this.id = id;
   }

    public Long getPartnerId() {
        return partnerId;
   }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
   }

    public String getPartnerName() {
        return partnerName;
   }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
   }

    public String getPartnerType() {
        return partnerType;
   }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType;
   }

    public Long getMerchantId() {
        return merchantId;
   }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
   }

    public String getMerchantName() {
        return merchantName;
   }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
   }

    public Byte getCardType() {
        return cardType;
   }

    public void setCardType(Byte cardType) {
        this.cardType = cardType;
   }

    public String getWoAcct() {
        return woAcct;
   }

    public void setWoAcct(String woAcct) {
        this.woAcct = woAcct;
   }

    public String getBankCode() {
        return bankCode;
   }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
   }

    public String getBankName() {
        return bankName;
   }

    public void setBankName(String bankName) {
        this.bankName = bankName;
   }

    public String getCardNo() {
        return cardNo;
   }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
   }

    public java.sql.Timestamp getCreateTime() {
        return createTime;
   }

    public void setCreateTime(java.sql.Timestamp createTime) {
        this.createTime = createTime;
   }

    public java.sql.Timestamp getUpdateTime() {
        return updateTime;
   }

    public void setUpdateTime(java.sql.Timestamp updateTime) {
        this.updateTime = updateTime;
   }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }
}

/*List columns as follows:
"id", "partner_id", "partner_name", "partner_type", "merchant_id", "merchant_name", "card_type", 
"wo_acct", "bank_code", "bank_name", "card_no", "create_time", "update_time"
*/