package com.jsunicom.oms.controller.salesman;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.model.org.OrgInfo;
import com.jsunicom.oms.model.salesman.AreaInfo;
import com.jsunicom.oms.model.salesman.GridInfo;
import com.jsunicom.oms.model.salesman.SalesManager;
import com.jsunicom.oms.service.DataMonitorFacade;
import com.jsunicom.oms.service.OrgInfoService;
import com.jsunicom.oms.service.SalesManagerFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.controller.salesman
 * @ClassName: SalesManagerController
 * @Author: zhaowang
 * @CreateTime: 2023-03-24  09:46
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/salesman")
public class SalesManagerController extends AbstractSimpleController {
    @Autowired
    private SalesManagerFacade salesManagerService;
    @Autowired
    private OrgInfoService orgInfoService;
    @Autowired
    private DataMonitorFacade dataMonitorFacade;

    @ResponseBody
    @RequestMapping(value = "/findGrid", name = "查询网格")
    public Map<String, String> findGridByCityName() throws Exception {
        Map<String, String> gridInfoMap = salesManagerService.findGridMap();
        return gridInfoMap;
    }

    @ResponseBody
    @RequestMapping(value = "/getCityDepartment", method = RequestMethod.GET)
    public JSONObject getCityDepartment(HttpServletRequest request) {
        JSONObject result = new JSONObject();
//        UserInfoDto userInfo = getCurUser();
//        String orgCode = userInfo.getOrgCode();
        String orgCode = request.getHeader("areaCode");
        boolean isAdmin = false;
        if (StringUtils.isNotEmpty(orgCode)&&!"root".equals(orgCode)) {
            List<OrgInfo> list = orgInfoService.getTheCity(orgCode);
            result.put("ret", "0");
            result.put("errMsg", "");
            result.put("errCode", "");
            result.put("list", list);
            result.put("isAdmin",isAdmin);
            return result;
        }
        isAdmin = true;
        List<OrgInfo> list = orgInfoService.getCityDepartment();
        result.put("ret", "0");
        result.put("errMsg", "");
        result.put("errCode", "");
        result.put("list", list);
        result.put("isAdmin",isAdmin);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/find", method = RequestMethod.GET, name = "销售经理分页查询")
    public PageInfo<SalesManager> findSalesManager(HttpServletRequest request, SalesManager salesManager, int pageNumber, int pageSize) throws Exception {
        log.info("分页查询入参：pageNum={},cntPerPage={},SalesManager={}", pageNumber, pageSize, salesManager);
//        UserInfoDto userInfo = getCurUser();
//        String orgCode = userInfo.getOrgCode();
        String orgCode = request.getHeader("areaCode");
        if (StringUtils.isNotEmpty(salesManager.getOrgCode())) {
            //根据地市编码
            orgCode = salesManager.getOrgCode();
        }
//        String mblNbr = userInfo.getMblNbr();
        String mblNbr = request.getHeader("serialNumber");
        if("root".equals(orgCode)){//省份管理员
            orgCode = "";
        }
        //acctNo 在partner中  查询phone  查询 partner_id  查询classificationId   中台方面的需求 取消
        /*String classificationId=partnerService.queryClassificationIdByPhone(mblNbr);
        if ("9".equals(classificationId)){//是否是校区经理
            salesManager.setName(userInfo.getAcctName());
        }*/
        /*List<Role> roles = userInfo.getRoles();
        List<Long> listId=new ArrayList<>();
        for (Role role : roles) {
            listId.add(role.getId());
        }

        if(listId.contains(18L)){//省份管理员
            orgCode = "";
        }else if (listId.contains(6L)){ //判断是否为地市管理员
            if (orgCode==null){//没有地市的搜索条件  默认查询本地市的校区经理信息
                orgCode=userInfo.getOrgCode();
            }
            if (!StringUtils.equals(orgCode,userInfo.getOrgCode())){//存在地市搜索条件，判断与登录信息地市是否匹配
                throw new RuntimeException("您无权查看其他地市的校区经理信息");
            }
        }else if (listId.contains(15L)){//校区经理角色 id 待定  15待修改
            //限制只能查询自己的信息
            salesManager.setName(userInfo.getAcctName());
        }else {
            //throw new RuntimeException("您无权查看校区经理信息");
            throw new BusinessException("9999","您无权查看校区经理信息");
        }*/
        salesManager.setOrgCode(orgCode);
        salesManager.setDefaultType("0");
        //PagedResult<SalesManager> pageList = salesManagerService.findByPages(salesManager, pageNumber, pageSize);
        PageInfo<SalesManager> pageList = salesManagerService.findByPagesNew(salesManager, pageNumber, pageSize);
        return pageList;
    }

    @ResponseBody
    @RequestMapping(value = "/findArea", name = "查询区县")
    public Map<String, String> findAreaByCityName() throws Exception {
        Map<String, String> areaInfoMap = salesManagerService.findAreaMap();
        return areaInfoMap;
    }

    @ResponseBody
    @RequestMapping(value = "/findAreaByCity", name = "按地市查询区县")
    public List<AreaInfo> findAreaByCityName(@RequestBody AreaInfo areaInfo) throws Exception {
        List<AreaInfo> areaInfoList = salesManagerService.findAreaByCityNo(areaInfo.getCityNo());
        return areaInfoList;
    }

    @ResponseBody
    @RequestMapping(value = "/findGridByCity", name = "按地市查询网格")
    public  List<GridInfo> findGridByCityName(@RequestBody GridInfo gridInfo) throws Exception {
        List<GridInfo> gridInfoList = salesManagerService.findGridByCityNo(gridInfo.getCityNo());
        return gridInfoList;
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST, name = "修改销售经理")
    public JSONObject updateSalesManager(HttpServletRequest request, HttpServletResponse response, HttpSession session, @RequestBody SalesManager salesManager) {
        SalesManager salesManagerBefore=salesManagerService.findById(salesManager.getId());
        JSONObject result = new JSONObject();
        //条线校园判断
        if(salesManager.getBusLine()!=null&&!"20".equals(salesManager.getBusLine())){
            result.put("ret", "1");
            result.put("errMsg", "条线必须为校园，无法修改");
            result.put("errCode", "");
            return result;
        }
        salesManager.setUpdateBy(request.getHeader("staffNo"));
        if(salesManagerService.updateSalesManager(salesManager) == 1){
            result.put("ret", "0");
            result.put("errMsg", "");
            result.put("errCode", "");
            SalesManager salesManagerAfter=salesManagerService.findById(salesManager.getId());
//            dataMonitorFacade.save("sale_manager_config","update",
//                    salesManager.getId().toString(),getCurUser().getAcctNo(),
//                    salesManager.toString());
            dataMonitorFacade.save("sale_manager_config","update",
                    salesManager.getId().toString(),request.getHeader("staffNo"),
                    salesManagerAfter,salesManagerBefore);
        }
        else{
            result.put("ret", "1");
            result.put("errMsg", "修改失败");
            result.put("errCode", "");
        }

        return result;
    }
}
