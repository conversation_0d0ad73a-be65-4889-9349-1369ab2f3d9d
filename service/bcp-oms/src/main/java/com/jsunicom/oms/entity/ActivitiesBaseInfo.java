package com.jsunicom.oms.entity;


import java.io.Serializable;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 
* @TableName activities_base_info
*/

@Data
public class ActivitiesBaseInfo  extends BaseRowModel  implements Serializable{

    /**
    * 活动id
    */
    @TableId
    @ApiModelProperty("活动id")
    private Integer activitiesId;
    /**
    * 活动通知id
    */
    @ApiModelProperty("活动通知id")
    private String activitiesNotifyId;

    /**
     * 校园名称
     */
    @ApiModelProperty("校园名称")
    @ExcelProperty(index = 0,value = "校区名称")
    private String campusName;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    @ExcelProperty(index = 1,value = "活动名称")
    private String activityName;

    /**
    * 活动类型
    */
    @ExcelProperty(index = 2,value = "活动类型")
    private String activityType;

    /**
    * 活动属性
    */
    @ApiModelProperty("活动属性")
//    @ExcelProperty(index = 3,value = "活动属性")
    private String activityAttributes;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @ExcelProperty(index = 4,value = "发起人")
    private String createUser;

    /**
     * 活动日期
     */
    @ApiModelProperty("活动开始日期")
    @ExcelProperty(index = 5,value = "活动开始日期")
    @JSONField(format = "yyyy-MM-d")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date activityStartTime;


    /**
     * 活动日期
     */
    @ApiModelProperty("活动结束日期")
    @ExcelProperty(index = 6,value = "活动结束日期")
    @JSONField(format = "yyyy-MM-d")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date activityEndTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    @ExcelProperty(index = 7, value = "更新人")
    private String updateUser;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @ExcelProperty(index = 8, value = "更新时间")
    @JSONField(format = "yyyy-MM-d")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date updateTime;

    /**
    * 活动状态 0:进行中,1:终止
    */
    @ApiModelProperty("活动状态 0:进行中,1:终止")
    private Integer activitiesStatus;

    /**
    * 校园id
    */
    @ApiModelProperty("校园id")
    private String campusId;


    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    @JSONField(format = "yyyy-MM-d")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date createTime;



}
