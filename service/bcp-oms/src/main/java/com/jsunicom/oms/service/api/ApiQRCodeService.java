package com.jsunicom.oms.service.api;

import com.jsunicom.oms.request.BaseRequest;
import com.jsunicom.oms.response.BaseResponse;
import com.lz.lsf.exception.ServiceException;

/**
 * 生产二维码
 * 
 * <AUTHOR>
 * @since 2015/9/11
 */
public interface ApiQRCodeService {

    /**
     * 生成二维码
     * 
     * @param request
     *            生成二维码请求
     * @return 生成二维码影响
     * @throws ServiceException
     */
    <T extends BaseResponse> T createQRCode(BaseRequest<T> request) throws ServiceException;
}
