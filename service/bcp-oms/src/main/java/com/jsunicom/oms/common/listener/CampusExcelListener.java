package com.jsunicom.oms.common.listener;

import com.alibaba.excel.read.context.AnalysisContext;
import com.jsunicom.oms.entity.CampusExcel;
import com.jsunicom.oms.service.CampusService;
import com.jsunicom.oms.service.OrgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


/**
 * .
 *
 * @ClassName: CampusExcelListener
 * @Auther: LJ.Huang
 * @Date: 2022/3/24 16:17
 * @Version: bcpV1.0
 * @Description: CampusExcelListener
 **/
@Slf4j
public class CampusExcelListener extends ExcelListener {
  private CampusService campusService;

  private OrgInfoService orgInfoService;

  Map<String, String> orgInfos;
  public CampusExcelListener(CampusService campusService, OrgInfoService orgInfoService){
    this.campusService = campusService;
    this.orgInfoService = orgInfoService;
    orgInfos = orgInfoService.getCodeNameMap();
  }

  private String getOrgCode(String name){
    if(null != orgInfos) {
      for (Map.Entry<String, String> m : orgInfos.entrySet()) {
          if(StringUtils.equals(name,m.getValue()))
            return m.getKey();
      }
    }
    return null;
  }
  /**
   * 通过 AnalysisContext 对象还可以获取当前 sheet，当前行等数据
   */
  @Override
  public void invoke(Object object, AnalysisContext context) {
    //String data = ((ImportInfo)object).getRecogCode();
    CampusExcel campusExcel = (CampusExcel)object;//JSONObject.parseObject(data,CampusExcel.class);
    if(null == campusExcel){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据为空，该条记录未处理！");
      return;
    }
    if(StringUtils.isEmpty(campusExcel.getSchoolName())){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据，学校名称为空，该条记录未处理！");
      return;
    }
    if(StringUtils.isEmpty(campusExcel.getCampusName())){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据，校区名称为空，该条记录未处理！");
      return;
    }

    if(StringUtils.isEmpty(campusExcel.getOrgName())){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据，机构为空，该条记录未处理！");
      return;
    }

    //
    String orgCode= getOrgCode(StringUtils.trim(campusExcel.getOrgName()));

   // List<OrgInfo> list =  orgInfoFacade.getCityDepartmentByOrgCodeWithRoot(campusExcel.getOrgCode());

    if(StringUtils.isEmpty(orgCode)){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据，机构不存在，该条记录未处理！");
      return;
    }else{
      campusExcel.setOrgCode(orgCode);
    }


    if(getDatas().contains(campusExcel)){
      getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据与其它行数据重复，该条记录未处理！");
      return;
    }else{

      if(campusService.isExist(campusExcel.getSchoolName(),campusExcel.getCampusName()))
      {
        getMsgList().add("第"+(context.getCurrentRowNum()+1)+"行数据与数据库中的学校名称，校区名称同时重复，该条记录未处理！");
        return;
      }
      else{
        getDatas().add(campusExcel);
      }

    }
  }

  public void setCampusService(CampusService campusService){
    this.campusService = campusService;
  }
  public void setOrgInfoService(OrgInfoService orgInfoService){this.orgInfoService = orgInfoService;}
}
