package com.jsunicom.oms.utils;

import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

public class SftpUtil {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private Session session;

    private ChannelSftp sftp;

    private String userName;

    private String password;

    private String privatekey;

    private String host;

    private int port;

    /**
     * 构造基于密码认证的sftp对象
     * @param userName
     * @param password
     * @param host
     * @param port
     */
    public SftpUtil(String userName, String password, String host, int port)
    {
        this.userName = userName;
        this.password = password;
        this.host = host;
        this.port = port;
    }

    /**
     * 构造基于秘钥认证的sftp对象
     * @param userName
     * @param host
     * @param port
     * @param privatekey
     */
    public SftpUtil(String userName, String host, int port, String privatekey)
    {
        this.userName = userName;
        this.host = host;
        this.port = port;
        this.privatekey = privatekey;
    }

    public SftpUtil(){}

    public void login()
    {
        try {
            JSch jSch = new JSch();
            if (privatekey != null) {
                jSch.addIdentity(privatekey);

            }
            session = jSch.getSession(userName,host,port);

            if(password!=null)
            {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking","no");

            session.setConfig(config);
            session.connect();

            Channel channel = session.openChannel("sftp");
            channel.connect();

            sftp = (ChannelSftp)channel;

        }
        catch(JSchException e)
        {
            e.printStackTrace();
        }
    }

    /**
     * 关闭连接
     */
    public void logout()
    {
        if(sftp != null)
        {
            if(sftp.isConnected())
            {
                sftp.disconnect();
                logger.info("sftp is closed alreay");
            }
        }

        if(session != null)
        {
            if(session.isConnected())
            {
                session.disconnect();
                logger.info("session is closed already");
            }
        }
    }

    /**
     * 将输入流数据上传到sftp作为文件
     * @param directory
     * @param sftpFileName
     * @param input
     * @throws SftpException
     */
    public  void upload(String directory, String sftpFileName, InputStream input) throws SftpException {
        try {
            System.out.println(sftp.pwd());;
            sftp.cd(directory);
        } catch (SftpException e) {
            logger.warn("sftp directory is not exist");
            sftp.mkdir(directory);
            sftp.cd(directory);
        }
        sftp.put(input,sftpFileName);
        logger.info("file:{} is upload successful",sftpFileName);
    }


    /**
     * 将输入流数据上传到sftp作为文件
     * @param directory
     * @param directory
     * @throws SftpException
     */
    public List<ChannelSftp.LsEntry> listDirs(String directory) throws SftpException {
        try {
            sftp.cd(directory);
        } catch (SftpException e) {
            logger.warn("sftp directory is not exist");
            sftp.mkdir(directory);
            sftp.cd(directory);
        }

        List<ChannelSftp.LsEntry> lsEntries = sftp.ls(directory);

        return lsEntries;
    }

    public void upload(String directory,String uploadFile) throws FileNotFoundException, SftpException {
        File file = new File(uploadFile);
        upload(directory,file.getName(),new FileInputStream(file));
    }

    /**
     * 下载文件
     *
     * @param directory
     *            下载目录
     * @param downloadFile
     *            下载的文件名
     * @return 数据流
     * @throws Exception
     */
    public InputStream getStream(String directory, String downloadFile) throws Exception {
        if (directory != null && !"".equals(directory)) {
            sftp.cd(directory);
        }
        return sftp.get(downloadFile);
    }

    /**
     * 将输入流数据上传到sftp作为文件
     * @param directory
     * @param sftpFileName
     * @throws SftpException
     */
    public  void del(String directory, String sftpFileName) throws SftpException {
        try {

            sftp.cd(directory);
        } catch (SftpException e) {
            logger.warn("sftp directory is not exist");
            sftp.mkdir(directory);
            sftp.cd(directory);
        }
        sftp.rm(sftpFileName);
        logger.info("file:{} is upload successful",sftpFileName);
    }

    /*public static void main(String[] args) {
        String sftpusername = "nginx";
        String sftppassword = "ai_nginx151DZXROQ3ZG30";
        String sftphost = "**************";
        Integer sftpport = 22;
        String sftpfilepath = "/data/testupload/docs";
        SftpUtil sftpUtil = new SftpUtil(sftpusername, sftppassword, sftphost, sftpport);
        StringBuilder builder = new StringBuilder();
        try {
            InputStream in = new FileInputStream(new File("D://111111.xlsx"));
            sftpUtil.login();
            sftpUtil.upload(sftpfilepath, "22222", in);
        }catch (Exception e){
            e.printStackTrace();
        }
    }*/

}
