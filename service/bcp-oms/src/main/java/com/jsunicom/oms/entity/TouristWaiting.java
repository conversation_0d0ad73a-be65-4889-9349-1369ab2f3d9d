package com.jsunicom.oms.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class TouristWaiting {
    private Long touristId;
    private String touristName;
    private String touristPhone;
    private String touristCertId;
    private String isAssign;
    private String invitePhoneNumber;
    private Long campusId;
    private String campusName;
    private Long collegeId;
    private String collegeName;
    private String createBy;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String assignPerson;
    private Date assignTime;
}
