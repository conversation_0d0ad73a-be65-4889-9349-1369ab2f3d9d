package com.jsunicom.oms.controller.resource;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.common.annotation.NotNeedResponseCover;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.PageResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.po.resource.TnSerialNumberPool;
import com.jsunicom.oms.service.SessionQueService;
import com.jsunicom.oms.service.resource.SerialNumberService;
import com.jsunicom.oms.service.resource.TnSerialIdleService;
import com.jsunicom.oms.service.resource.TnSerialNumberOperateInfoService;
import com.jsunicom.oms.utils.PageUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/appSerialPoolAndNumber")
@Slf4j
@NotNeedResponseCover
public class AppSerialPoolAndNumberController {
    @Autowired
    private SerialNumberService serialNumberService;
    @Autowired
    private SessionQueService sessionQueService;
    @Resource
    private TnSerialNumberOperateInfoService tnSerialNumberOperateInfoService;
    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Resource
    private TnSerialIdleService tnSerialIdleService;

    @ApiModelProperty("查询号池")
    @PostMapping("/qrySerialPoolList")
    public String qrySerialPoolInfo(@RequestBody String sig, HttpServletRequest request){
        try {
            String decryptInfo = sessionQueService.decryptInfo(sig);
            log.info("qrySerialPoolList reqParam:{}", decryptInfo);
            ObjectMapper mapper = new ObjectMapper();
            TnSerialNumberPool tnSerialNumberPool = mapper.readValue(decryptInfo, TnSerialNumberPool.class);
            UserInfo userInfo = sessionQueService.getSessionInfo(request);
            PageResult pageInfo = PageUtils.getPageResult(serialNumberService.qrySerialPoolInfo(tnSerialNumberPool, userInfo));
            log.info("qrySerialPoolList 查询结果：{}",pageInfo);
            return sessionQueService.encryptInfo(pageInfo);
        } catch (Exception e) {
            PageResult resultFail = new PageResult();
            resultFail.setCode("500");
            resultFail.setMsg("查询号池信息失败!");
            log.error("qrySerialPoolList 查询号池信息异常:{}", e);
            return sessionQueService.encryptInfo(resultFail);
        }
    }
    @ApiModelProperty("查询号码列表")
    @PostMapping("/queryTnSerialIdleList")
    public String queryTnSerialIdleList(@RequestBody String sig, HttpServletRequest request){
//        UserInfo userInfo = Utils.getUserInfo(request.getHeader("User-Info"));
        try {
            String decryptInfo = sessionQueService.decryptInfo(sig);
            log.info("queryTnSerialIdleList reqParam:{}", decryptInfo);
            Map<String, Object> map = new Gson().fromJson(decryptInfo,HashMap.class);
            UserInfo userInfo = sessionQueService.getSessionInfo(request);
            CustomResult customResult = tnSerialIdleService.queryThSerialIdleListByCondition(map, false, userInfo);
            log.info("queryTnSerialIdleList 查询结果：{}",customResult);
            return sessionQueService.encryptInfo(customResult);
        } catch (Exception e) {
            log.error("queryTnSerialIdleList 查询号码列表信息异常:{}", e);
            return sessionQueService.encryptInfo(ResultUtil.error(ResultEnum.ERROR));
        }
    }
    @ApiModelProperty("号码轨迹")
    @PostMapping("/queryOperateInfoList")
    public String queryOperateInfoList(@RequestBody String sig)   {
        try {
            String decryptInfo = sessionQueService.decryptInfo(sig);
            log.info("queryOperateInfoList reqParam:{}", decryptInfo);
            Map<String, Object> map = new Gson().fromJson(decryptInfo,HashMap.class);
            CustomResult customResult = tnSerialNumberOperateInfoService.queryOperateInfoList(map);
            log.info("queryOperateInfoList 查询结果：{}",customResult);
            return sessionQueService.encryptInfo(customResult);
        } catch (Exception e) {
            log.error("queryOperateInfoList 查询号码轨迹列表信息异常:{}", e);
            return sessionQueService.encryptInfo(ResultUtil.error(ResultEnum.ERROR));
        }
    }
    @ApiModelProperty("号码编辑")
    @PostMapping( "/updateTnSerialIdle")
    public String updateTnSerialIdle(@RequestBody String sig, HttpServletRequest request){
        try {
            String decryptInfo = sessionQueService.decryptInfo(sig);
            log.info("updateTnSerialIdle reqParam:{}", decryptInfo);
            Map<String, Object> map = new Gson().fromJson(decryptInfo,HashMap.class);
            UserInfo userInfo = sessionQueService.getSessionInfo(request);
            map.put("staffId", userInfo.getStaffNo());
            CustomResult customResult = tnSerialIdleService.updateTnSerialIdle(map);
            log.info("updateTnSerialIdle 修改结果：{}",customResult);
            return sessionQueService.encryptInfo(customResult);
        } catch (Exception e) {
            log.error("updateTnSerialIdle 查询号池信息异常:{}", e);
            return sessionQueService.encryptInfo(ResultUtil.error(ResultEnum.ERROR));
        }
    }

    @ApiModelProperty("号码状态")
    @PostMapping("/qrySerialIdleState")
    public String qrySerialIdleState(@RequestBody String sig){
        try {
            String decryptInfo = sessionQueService.decryptInfo(sig);
            log.info("qrySerialIdleState reqParam:{}", decryptInfo);
            Map<String, Object> paraMap = new Gson().fromJson(decryptInfo,HashMap.class);
            String type = (String)paraMap.get("type");
            TdMSysDict tdMSysDict = new TdMSysDict();
            tdMSysDict.setParamType("serialIdleState");
            List<TdMSysDict> serialIdleStateList = tdMSysDictMapper.queryAll(tdMSysDict);
            List<Map<String, String>> serialIdleState = new ArrayList<Map<String, String>>();
            for (TdMSysDict mSysDict : serialIdleStateList) {
                Map<String, String> map = new HashMap();
                map.put("key", mSysDict.getParamKey());
                map.put("value", mSysDict.getParamValue());
                map.put("desc", mSysDict.getParam1());
                serialIdleState.add(map);
            }
            Result result = new Result();
            result.setCode(200L);
            result.setMsg("查询成功");
            if ("allState".equals(type)){
                List<Map<String, String>> collect = serialIdleStateList.stream().filter(state -> !state.getParamKey().equals("00") && !state.getParamKey().equals("05") && !state.getParamKey().equals("06"))
                        .map(list -> {
                            Map<String, String> map = new HashMap();
                            map.put("key", list.getParamKey());
                            map.put("value", list.getParamValue());
                            map.put("desc", list.getParam1());
                            return map;
                        })
                        .collect(Collectors.toList());
                result.setData(collect);
                return sessionQueService.encryptInfo(result);
            }
            result.setData(serialIdleState);
            return sessionQueService.encryptInfo(result);
        } catch (Exception e) {
            Result resultFail = new Result();
            resultFail.setCode(500L);
            resultFail.setMsg("查询号码轨迹列表失败!");
            log.error("qrySerialIdleState 查询号码轨迹列表信息异常:{}", e);
            return sessionQueService.encryptInfo(resultFail);
        }
    }
}
