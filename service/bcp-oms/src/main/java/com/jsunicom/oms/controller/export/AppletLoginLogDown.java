package com.jsunicom.oms.controller.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.controller.export
 * @ClassName: AppletLoginLogDown
 * @Author: zhaowang
 * @CreateTime: 2023-11-23  15:53
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class AppletLoginLogDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "日志ID")
    @JSONField(name = "loginId")
    private String loginId;

    @ExcelProperty(index = 0,value = "登录手机号")
    @JSONField(name = "acctNo")
    private String acctNo;

    @ExcelProperty(index = 0,value = "用户姓名")
    @JSONField(name = "userName")
    private String userName;

    @ExcelProperty(index = 0,value = "用户角色")
    @JSONField(name = "roleTypeName")
    private String roleTypeName;

    @ExcelProperty(index = 0,value = "登录时间")
    @JSONField(name = "loginTime")
    private String loginTime;

    @ExcelProperty(index = 0,value = "备注说明")
    @JSONField(name = "remark")
    private String remark;
}
