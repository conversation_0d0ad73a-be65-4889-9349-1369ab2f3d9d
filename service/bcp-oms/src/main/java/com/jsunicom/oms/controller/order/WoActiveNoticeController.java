package com.jsunicom.oms.controller.order;

import com.alibaba.fastjson.JSON;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.po.order.req.WoActiveNoticeInModel;
import com.jsunicom.oms.po.order.req.WoActiveNoticeOutModel;
import com.jsunicom.oms.service.order.WoActiveNoticeService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@Slf4j
public class WoActiveNoticeController {

    @Autowired
    private WoActiveNoticeService woActiveNoticeService;


    @ApiOperation(value = "沃激活通知", notes = "沃激活通知")
    @RequestMapping(value = "/order/woActiveNotice", method = RequestMethod.POST, produces = "application/json")
    public Result woActiveNotice(@RequestBody WoActiveNoticeInModel reqInfo) throws IOException {
        log.info("沃激活通知请求参数:{}",reqInfo);
        Result rspInfo = new Result();

        try {
            log.info("沃激活通知请求参数:" + JSON.toJSONString(reqInfo));
            @SuppressWarnings("unused")
            WoActiveNoticeOutModel result = woActiveNoticeService.woActiveNotice(reqInfo);

            rspInfo.setCode(200L);
            rspInfo.setMsg("成功");
        } catch (Exception e) {
            log.info("沃激活通知报错:{}",e);
            rspInfo.setCode(500L);
            rspInfo.setMsg(e.getMessage());
        }
        log.info("沃激活通知返回参数:" + JSON.toJSONString(rspInfo));
        return rspInfo;
    }
}
