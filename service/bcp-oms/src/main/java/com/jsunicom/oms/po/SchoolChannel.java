package com.jsunicom.oms.po;

import java.io.Serializable;

public class SchoolChannel implements Serializable {

    private static final long serialVersionUID = 1L;
    private String chnlId;
    private String chnlName;
    private String chnlCode;
    private String chnlKindId;
    private String chnlKindName;
    private String areaCode;
    private String areaName;
    private String userAreaCode;
    private String campusId;
    private String campusName;
    private String isRelation;

    public String getChnlId() {
        return chnlId;
    }

    public void setChnlId(String chnlId) {
        this.chnlId = chnlId;
    }

    public String getChnlName() {
        return chnlName;
    }

    public void setChnlName(String chnlName) {
        this.chnlName = chnlName;
    }

    public String getChnlCode() {
        return chnlCode;
    }

    public void setChnlCode(String chnlCode) {
        this.chnlCode = chnlCode;
    }

    public String getChnlKindId() {
        return chnlKindId;
    }

    public void setChnlKindId(String chnlKindId) {
        this.chnlKindId = chnlKindId;
    }

    public String getCampusId() {
        return campusId;
    }

    public void setCampusId(String campusId) {
        this.campusId = campusId;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getUserAreaCode() {
        return userAreaCode;
    }

    public void setUserAreaCode(String userAreaCode) {
        this.userAreaCode = userAreaCode;
    }

    public String getChnlKindName() {
        return chnlKindName;
    }

    public void setChnlKindName(String chnlKindName) {
        this.chnlKindName = chnlKindName;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    public String getIsRelation() {
        return isRelation;
    }

    public void setIsRelation(String isRelation) {
        this.isRelation = isRelation;
    }
}
