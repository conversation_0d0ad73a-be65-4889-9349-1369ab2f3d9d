package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.mapper.partner.UserAgreementPdfDao;
import com.jsunicom.oms.model.partner.UserAgreementPdf;
import com.jsunicom.oms.service.UserAgreementPdfFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: UserAgreementPdfServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-18  21:27
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class UserAgreementPdfServiceImpl implements UserAgreementPdfFacade {
    @Autowired
    UserAgreementPdfDao userAgreementPdfDao;

    @Override
    public int save(UserAgreementPdf userAgreementPdf) {
        return userAgreementPdfDao.save(userAgreementPdf);
    }
}
