package com.jsunicom.oms.common.enums;

/**
 * 消息类型
 * 
 * <AUTHOR>
 * @since 2015/9/30
 */
public enum MsgType {
    /**
     * 文本消息
     */
    TEXT("text", "text"),

    /**
     * 图片消息
     */
    IMAGE("image", null),

    /**
     * 语音消息
     */
    VOICE("voice", null),

    /**
     * 视频消息
     */
    VIDEO("video", null),

    /**
     * 音乐消息
     */
    MUSIC("music", null),

    /**
     * 图文消息
     */
    NEWS("news", "image-text"),

    /**
     * 卡券
     */
    WXCARD("wxcard", null);

    private String value;

    private String alipayVal;

    private MsgType(String value, String alipayVal) {
        this.value = value;
        this.alipayVal = alipayVal;
    }

    public String getAlipayVal() {
        return alipayVal;
    }

    public String getValue() {
        return value;
    }
}
