package com.jsunicom.oms.model.base;

import java.sql.Timestamp;

/**
 * Created by xuc40 on 20180725 for OperationLogForData
 */
public class DataMonitor extends BasicModel {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String monitorModules;
    private String monitorType;
    private String dataId;
    private String operateBy;
    private java.sql.Timestamp operateTime;
    private String operateDesc;
    private String previousOperateDesc;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMonitorModules() {
        return monitorModules;
    }

    public void setMonitorModules(String monitorModules) {
        this.monitorModules = monitorModules;
    }

    public String getMonitorType() {
        return monitorType;
    }

    public void setMonitorType(String monitorType) {
        this.monitorType = monitorType;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getOperateBy() {
        return operateBy;
    }

    public void setOperateBy(String operateBy) {
        this.operateBy = operateBy;
    }

    public Timestamp getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Timestamp operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateDesc() {
        return operateDesc;
    }

    public void setOperateDesc(String operateDesc) {
        this.operateDesc = operateDesc;
    }

    public String getPreviousOperateDesc() {
        return previousOperateDesc;
    }

    public void setPreviousOperateDesc(String previousOperateDesc) {
        this.previousOperateDesc = previousOperateDesc;
    }
}
