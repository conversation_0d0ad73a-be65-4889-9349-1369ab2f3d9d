package com.jsunicom.oms.request;


import com.jsunicom.oms.response.QyBatchInviteMemberResponse;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

/**
 * Created by wentao on 2018/3/6.
 **/
public class QyBatchInviteMemberRequest extends BaseRequest<QyBatchInviteMemberResponse> implements WebchatRequest {
    @Value("${wechat.qiapi}")
    private String profixUrl;

    private List<String> user;

    private List<String> party;

    private List<String> tag;

    public List<String> getUser() {
        return user;
    }

    public void setUser(List<String> user) {
        this.user = user;
    }

    public List<String> getParty() {
        return party;
    }

    public void setParty(List<String> party) {
        this.party = party;
    }

    public List<String> getTag() {
        return tag;
    }

    public void setTag(List<String> tag) {
        this.tag = tag;
    }

    @Override
    public String getRequestUrl() {
        return profixUrl +  "/cgi-bin/batch/invite?access_token=%s";
    }


    @Override
    public Class<QyBatchInviteMemberResponse> getResponseClass() {
        return QyBatchInviteMemberResponse.class;
    }
}
