package com.jsunicom.oms.po.order.req;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class CurtCustReq {
    @JSONField(name = "MSG")
    private MSG MSG; // 消息体
    @JSONField(name = "APPKEY")
    private String APPKEY = "jsordcen"; // 请求系统，分配给调用方的系统编码
    @JSONField(name = "APPTX")
    private String APPTX; // 请求流水，调用方生成唯一不重复的流水号，长度不大于30位

    public CurtCustReq.MSG getMSG() {
        return MSG;
    }

    public CurtCustReq setMSG(CurtCustReq.MSG MSG) {
        this.MSG = MSG;
        return this;
    }

    public String getAPPKEY() {
        return APPKEY;
    }

    public CurtCustReq setAPPKEY(String APPKEY) {
        this.APPKEY = APPKEY;
        return this;
    }

    public String getAPPTX() {
        return APPTX;
    }

    public CurtCustReq setAPPTX(String APPTX) {
        this.APPTX = APPTX;
        return this;
    }

    public static class MSG {
        private String serialNumber; // 	0	号码
        private String photoTag; // 	0	照片标识照片标识：1：已拍照（拍照且照片上传无纸化可以传1，不满足条件不传）
        private String city; // 	0	地市
        private String orderId; // 	0	总部订单ID
        private String channelType; // 	0	渠道类型
        private List<ActorInfo> actorInfo; // 	0	代办人信息
        private String serviceClassCode; // 	0	电信类型编码，移网号码可传0050
        private List<CustInfo> custInfo; // 	0	客户信息
        private String areaCode; // 	0	区号
        private List<Para> para; // 	0	保留字段
        private String province; // 	0	省分
        private String district; // 	0	区县
        private String eModeCode; // 	0	渠道标示（cBSS返档必传）
        private String opeSysType; // 	0	办理业务系统1：ESS 2：cBSS；不传默认ESS
        private String realPersonTag; // 	0	实人认证标志：1：实名-静态  2：实名-动态 （通过人脸比对的可以传1，通过人脸比对及活体认证的可以传2，不满足条件不传）
        private String operTag; // 	0	01  客户资料修改（可修改除证件类型+证件号码之外的其它信息） 02  新用户的客户资料返档 03  证件类型、证件号码变更（包括非关键信息的修改） 04  客户资料修改（可修改除证件类型+证件号码+客户姓名之外的其它信息） 05  老用户的客户资料返档 07  集团用户未返档 08  集团用户已返档
        private String operatorId; // 	0	操作员ID
        private String channelId; // 	0	渠道编码
        private List<DeveloperInfo> developerInfo; // 	0	返档人信息

        public String getSerialNumber() {
            return serialNumber;
        }

        public MSG setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public String getPhotoTag() {
            return photoTag;
        }

        public MSG setPhotoTag(String photoTag) {
            this.photoTag = photoTag;
            return this;
        }

        public String getCity() {
            return city;
        }

        public MSG setCity(String city) {
            this.city = city;
            return this;
        }

        public String getOrderId() {
            return orderId;
        }

        public MSG setOrderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public String getChannelType() {
            return channelType;
        }

        public MSG setChannelType(String channelType) {
            this.channelType = channelType;
            return this;
        }

        public List<ActorInfo> getActorInfo() {
            return actorInfo;
        }

        public MSG setActorInfo(List<ActorInfo> actorInfo) {
            this.actorInfo = actorInfo;
            return this;
        }

        public String getServiceClassCode() {
            return serviceClassCode;
        }

        public MSG setServiceClassCode(String serviceClassCode) {
            this.serviceClassCode = serviceClassCode;
            return this;
        }

        public List<CustInfo> getCustInfo() {
            return custInfo;
        }

        public MSG setCustInfo(List<CustInfo> custInfo) {
            this.custInfo = custInfo;
            return this;
        }

        public String getAreaCode() {
            return areaCode;
        }

        public MSG setAreaCode(String areaCode) {
            this.areaCode = areaCode;
            return this;
        }

        public List<Para> getPara() {
            return para;
        }

        public MSG setPara(List<Para> para) {
            this.para = para;
            return this;
        }

        public String getProvince() {
            return province;
        }

        public MSG setProvince(String province) {
            this.province = province;
            return this;
        }

        public String getDistrict() {
            return district;
        }

        public MSG setDistrict(String district) {
            this.district = district;
            return this;
        }

        public String geteModeCode() {
            return eModeCode;
        }

        public MSG seteModeCode(String eModeCode) {
            this.eModeCode = eModeCode;
            return this;
        }

        public String getOpeSysType() {
            return opeSysType;
        }

        public MSG setOpeSysType(String opeSysType) {
            this.opeSysType = opeSysType;
            return this;
        }

        public String getRealPersonTag() {
            return realPersonTag;
        }

        public MSG setRealPersonTag(String realPersonTag) {
            this.realPersonTag = realPersonTag;
            return this;
        }

        public String getOperTag() {
            return operTag;
        }

        public MSG setOperTag(String operTag) {
            this.operTag = operTag;
            return this;
        }

        public String getOperatorId() {
            return operatorId;
        }

        public MSG setOperatorId(String operatorId) {
            this.operatorId = operatorId;
            return this;
        }

        public String getChannelId() {
            return channelId;
        }

        public MSG setChannelId(String channelId) {
            this.channelId = channelId;
            return this;
        }

        public List<DeveloperInfo> getDeveloperInfo() {
            return developerInfo;
        }

        public MSG setDeveloperInfo(List<DeveloperInfo> developerInfo) {
            this.developerInfo = developerInfo;
            return this;
        }
    }

    public static class ActorInfo {
        private String actorAddress; // 	0	代办人证件地址
        private String actorCertTypeId; // 	0	代办人证件类型
        private String actorName; // 	0	代办人姓名
        private String actorCertNum; // 	0	代办人证件号码
        private String actorPhone; // 	0	代办人号码

        public String getActorAddress() {
            return actorAddress;
        }

        public ActorInfo setActorAddress(String actorAddress) {
            this.actorAddress = actorAddress;
            return this;
        }

        public String getActorCertTypeId() {
            return actorCertTypeId;
        }

        public ActorInfo setActorCertTypeId(String actorCertTypeId) {
            this.actorCertTypeId = actorCertTypeId;
            return this;
        }

        public String getActorName() {
            return actorName;
        }

        public ActorInfo setActorName(String actorName) {
            this.actorName = actorName;
            return this;
        }

        public String getActorCertNum() {
            return actorCertNum;
        }

        public ActorInfo setActorCertNum(String actorCertNum) {
            this.actorCertNum = actorCertNum;
            return this;
        }

        public String getActorPhone() {
            return actorPhone;
        }

        public ActorInfo setActorPhone(String actorPhone) {
            this.actorPhone = actorPhone;
            return this;
        }
    }

    public static class CustInfo {
        private String checkType; // 	0	证件类型是18位身份证时必传，认证类型： 01：本地认证 02：公安认证 03：二代证读卡器
        private String certType; // 	0	证件类型
        private String certAddr; // 	0	证件地址
        private String contactName; // 	0	联系人姓名
        private String postAddress; // 	0	通信地址
        private String custCertPictureUrl; // 	0	客户证件电子图片链接
        private String custName; // 	0	客户名称
        private String custPhone; // 	0	联系电话
        private String certExpireDate; // 	0	证件有效期，格式YYYYMMDD
        private String custId; // 	0	客户标识
        private String cerValidFlag; // 	0	是否长期有效1：长期有效（不填默认有有效期）
        private String custCertPictureName; // 	0	客户证件电子图片名称
        private String certNum; // 	0	证件号码
        private String contactPhone; // 	0	联系人电话

        public String getCheckType() {
            return checkType;
        }

        public CustInfo setCheckType(String checkType) {
            this.checkType = checkType;
            return this;
        }

        public String getCertType() {
            return certType;
        }

        public CustInfo setCertType(String certType) {
            this.certType = certType;
            return this;
        }

        public String getCertAddr() {
            return certAddr;
        }

        public CustInfo setCertAddr(String certAddr) {
            this.certAddr = certAddr;
            return this;
        }

        public String getContactName() {
            return contactName;
        }

        public CustInfo setContactName(String contactName) {
            this.contactName = contactName;
            return this;
        }

        public String getPostAddress() {
            return postAddress;
        }

        public CustInfo setPostAddress(String postAddress) {
            this.postAddress = postAddress;
            return this;
        }

        public String getCustCertPictureUrl() {
            return custCertPictureUrl;
        }

        public CustInfo setCustCertPictureUrl(String custCertPictureUrl) {
            this.custCertPictureUrl = custCertPictureUrl;
            return this;
        }

        public String getCustName() {
            return custName;
        }

        public CustInfo setCustName(String custName) {
            this.custName = custName;
            return this;
        }

        public String getCustPhone() {
            return custPhone;
        }

        public CustInfo setCustPhone(String custPhone) {
            this.custPhone = custPhone;
            return this;
        }

        public String getCertExpireDate() {
            return certExpireDate;
        }

        public CustInfo setCertExpireDate(String certExpireDate) {
            this.certExpireDate = certExpireDate;
            return this;
        }

        public String getCustId() {
            return custId;
        }

        public CustInfo setCustId(String custId) {
            this.custId = custId;
            return this;
        }

        public String getCerValidFlag() {
            return cerValidFlag;
        }

        public CustInfo setCerValidFlag(String cerValidFlag) {
            this.cerValidFlag = cerValidFlag;
            return this;
        }

        public String getCustCertPictureName() {
            return custCertPictureName;
        }

        public CustInfo setCustCertPictureName(String custCertPictureName) {
            this.custCertPictureName = custCertPictureName;
            return this;
        }

        public String getCertNum() {
            return certNum;
        }

        public CustInfo setCertNum(String certNum) {
            this.certNum = certNum;
            return this;
        }

        public String getContactPhone() {
            return contactPhone;
        }

        public CustInfo setContactPhone(String contactPhone) {
            this.contactPhone = contactPhone;
            return this;
        }
    }

    public static class Para {
        private String paraId; // 	0	扩展字段key
        private String paraValue; // 	0	扩展字段value

        public String getParaId() {
            return paraId;
        }

        public Para setParaId(String paraId) {
            this.paraId = paraId;
            return this;
        }

        public String getParaValue() {
            return paraValue;
        }

        public Para setParaValue(String paraValue) {
            this.paraValue = paraValue;
            return this;
        }
    }

    public static class DeveloperInfo {
        private String developerId; // 	0	返档人编码
        private String developerPhone; // 	0	返档人手机号码
        private String agentChannelId; // 	0	代理商渠道编码
        private String developerName; // 	0	返档人姓名

        public String getDeveloperId() {
            return developerId;
        }

        public DeveloperInfo setDeveloperId(String developerId) {
            this.developerId = developerId;
            return this;
        }

        public String getDeveloperPhone() {
            return developerPhone;
        }

        public DeveloperInfo setDeveloperPhone(String developerPhone) {
            this.developerPhone = developerPhone;
            return this;
        }

        public String getAgentChannelId() {
            return agentChannelId;
        }

        public DeveloperInfo setAgentChannelId(String agentChannelId) {
            this.agentChannelId = agentChannelId;
            return this;
        }

        public String getDeveloperName() {
            return developerName;
        }

        public DeveloperInfo setDeveloperName(String developerName) {
            this.developerName = developerName;
            return this;
        }
    }
}