package com.jsunicom.oms.timer.order;

import com.alibaba.fastjson.JSON;
import com.jsunicom.oms.entity.TdOrdSysDict;
import com.jsunicom.oms.service.PhotoProcessService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Configuration
@EnableScheduling
public class SynZRRPhotoJob {
    @Autowired
    private PhotoProcessService photoProcessService;
    @XxlJob("synZRRPhotoJob")
    public ReturnT<String> synZRRPhotoJob(String param) {
        try {
            TdOrdSysDict dict1 = photoProcessService.queryDict("synPhotoJob", "synPhotoConfig");

            if (null == dict1) {
                log.error("##未查询到同步照片的字典表相关配置");
                return  ReturnT.FAIL ;
            }
            // String beginDate = dict1.getParamValue();
            int singleNums = Integer.valueOf(dict1.getParam1());

            log.info("SynZRRPhotoJob生成待同步照片信息开始");
            //synState--同步状态  0 未同步，1 同步成功，2 同步失败  3同步中
            List<Map<String, String>> synPhotoRecords = photoProcessService.querySynPhotoRecords();
            if (null != synPhotoRecords && !synPhotoRecords.isEmpty()) {
                long startTime = System.currentTimeMillis();
                log.info("SynZRRPhotoJob待处理记录数=" + synPhotoRecords.size());
                List<List<Map<String, String>>> lists = photoProcessService.divList(synPhotoRecords, singleNums);
                CountDownLatch latch = new CountDownLatch(lists.size());
                // ExecutorService pool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() + 1);
                for (int i = 0; i < lists.size(); i++) {
                    photoProcessService.synThread(latch, lists.get(i));
                }
                latch.await();
                long endTime = System.currentTimeMillis();
                log.info("SynZRRPhotoJob生成待同步照片信息Job所需时间：" + (endTime - startTime));
            }
            log.info("SynZRRPhotoJob生成待同步照片信息结束，开始同步照片");

            //#################################################################################

            String sourceSystem = "";
            List<TdOrdSysDict> dists = photoProcessService.queryDictList("synZRRPhoto");
            if (dists.size() == 0) {
                log.error("##未查询到同步照片的字典表相关配置");
                return  ReturnT.FAIL ;
            }
            for (TdOrdSysDict dict : dists) {
                sourceSystem = dict.getParamKey();
                //    int photoFileNums = Integer.valueOf(dict.getParam2());
                log.info("paramType为synZRRPhotoJob,ParamKey=" + sourceSystem + "的配置文件: " + JSON.toJSONString(dict));

                int photoNums = Integer.valueOf(dict.getParam2());
               // log.info("paramType为synZRRPhotoJob,ParamKey=" + sourceSystem + "的配置文件: " + JSON.toJSONString(dict));

                try {
                    List<Map<String, String>> synPhotoInfos = photoProcessService.synPhotoInfos(sourceSystem);
                    log.info("SynZRRPhotoJob=" + sourceSystem + "待同步照片记录数=" + synPhotoInfos.size());
                    if (org.springframework.util.CollectionUtils.isEmpty(synPhotoInfos)) {
                        log.info("##SynZRRPhotoJob--" + sourceSystem + "同步照片时未查询到需要同步的记录");
                        continue;
                    }
                    List<List<Map<String, String>>> infoLists = photoProcessService.divList(synPhotoInfos, photoNums);
                    if (null != infoLists && !infoLists.isEmpty()) {
                        //按照photoInfoNums定义的单次打包获取循环次数，并进行拆分
                        int fileCount = infoLists.size();
                        CountDownLatch infoLatch = new CountDownLatch(infoLists.size());
                        for (int i = 0; i < infoLists.size(); i++) {
                            photoProcessService.synPhotoThread(infoLatch, i + 1, fileCount, infoLists.get(i), dict);
                        }
                        infoLatch.await();
                    } else {
                        log.warn("SynZRRPhotoJob--{}没有照片需要同步", sourceSystem);
                    }

                } catch (Exception e) {
                    log.error("SynZRRPhotoJob--" + sourceSystem + "同步照片时发生错误##", e);
                    continue;
                }

            }

            log.info("SynZRRPhotoJob同步照片结束");
            //  log.info("SynZRRPhotoJob同步订单的照片数据到自然人结束");


        } catch (Exception e) {
            log.error("SynZRRPhotoJob同步照片信息时发生错误##", e);
            return  ReturnT.FAIL ;
        }
        return  ReturnT.SUCCESS ;
    }
}
