package com.jsunicom.oms.po.order.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "校园用户可缴费返回报文返回内容节点")
public class SchoolPayQueryRsp {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "可缴费订单列表", required = true, position = 1)
    private List<Order> orderList;

    public List<Order> getOrderList() {
        return orderList;
    }

    public SchoolPayQueryRsp setOrderList(List<Order> orderList) {
        this.orderList = orderList;
        return this;
    }

    public final static class Order {
        private String orderId;
        private String goodsId;
        private String operId;
        private String staffId;
        private String staffName;
        private String staffPhone;
        private String tradeId;
        private String channelId;
        private String cucEparchyCode;
        private String goodsName;
        private String serialNumber;
        private String custName;
        private String payCount;
        private String payAmount;

        public String getOrderId() {
            return orderId;
        }

        public Order setOrderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public String getGoodsId() {
            return goodsId;
        }

        public Order setGoodsId(String goodsId) {
            this.goodsId = goodsId;
            return this;
        }

        public String getOperId() {
            return operId;
        }

        public Order setOperId(String operId) {
            this.operId = operId;
            return this;
        }

        public String getStaffId() {
            return staffId;
        }

        public Order setStaffId(String staffId) {
            this.staffId = staffId;
            return this;
        }

        public String getStaffPhone() {
            return staffPhone;
        }

        public Order setStaffPhone(String staffPhone) {
            this.staffPhone = staffPhone;
            return this;
        }

        public String getStaffName() {
            return staffName;
        }

        public Order setStaffName(String staffName) {
            this.staffName = staffName;
            return this;
        }

        public String getChannelId() {
            return channelId;
        }

        public Order setChannelId(String channelId) {
            this.channelId = channelId;
            return this;
        }

        public String getTradeId() {
            return tradeId;
        }

        public Order setTradeId(String tradeId) {
            this.tradeId = tradeId;
            return this;
        }

        public String getCucEparchyCode() {
            return cucEparchyCode;
        }

        public Order setCucEparchyCode(String cucEparchyCode) {
            this.cucEparchyCode = cucEparchyCode;
            return this;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public Order setGoodsName(String goodsName) {
            this.goodsName = goodsName;
            return this;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public Order setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        public String getCustName() {
            return custName;
        }

        public Order setCustName(String custName) {
            this.custName = custName;
            return this;
        }

        public String getPayCount() {
            return payCount;
        }

        public Order setPayCount(String payCount) {
            this.payCount = payCount;
            return this;
        }

        public String getPayAmount() {
            return payAmount;
        }

        public Order setPayAmount(String payAmount) {
            this.payAmount = payAmount;
            return this;
        }
    }
}
