package com.jsunicom.oms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum FlowStatusEnum {


    /**
     * 流程状态(1：审批中、2：审批完成)
     */
    APPROVAL_IS_PENDING("1", "审批中"),
    APPROVAL_COMPLETED("2", "审批完成");


    private final String status;
    private final String desc;


    public static FlowStatusEnum get(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }

        return Arrays.stream(values())
                .filter(e -> e.getStatus().equals(status))
                .findAny()
                .orElse(null);
    }
}
