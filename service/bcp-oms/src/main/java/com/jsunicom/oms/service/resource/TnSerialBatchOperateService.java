package com.jsunicom.oms.service.resource;

import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.po.resource.SerialImportModel;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * */
public interface TnSerialBatchOperateService {

    /**
     * <p>查询号码的批量操作记录</p>
     * @param map 地市编码 操作工号 操作类型 操作开始时间 操作结束时间
     * @return CustomResult
     * */
    CustomResult queryTnSerialBatchOperateList(Map<String, Object> map, UserInfo userInfo);

    /**
     * <p>批量对号码进行变更操作</p>
     * @param serialNoList 控制层将excel文件预处理后存入该集合
     * @param param 文件上传地址 文件下载地址 号码变更的目标状态 当前登录工号 地市编码
     * @return CustomResult
     * */
    CustomResult importUpdateData(List<SerialImportModel> serialNoList, Map<String, Object> param);

    /**
     * <p>批量删除号码</p>
     * @param serialNoList 控制层将excel文件预处理后存入该集合
     * @param param 文件上传地址 文件下载地址 当前登录工号 地市编码
     * @return CustomResult
     * */
    CustomResult importDelData(List<SerialImportModel> serialNoList, Map<String, Object> param);


}
