package com.jsunicom.oms.mapper;

import com.jsunicom.oms.entity.PartnerInfo;
import com.jsunicom.oms.entity.TouristWaiting;
import com.jsunicom.oms.entity.TreeBean;
import com.jsunicom.oms.po.PartnerInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TouristWaitingMapper {

    List<TouristWaiting> selectTouristWaiting(TouristWaiting touristWaiting);

    List<TreeBean> selectSocietyInfoBySchoolId(String schoolId);

    int deleteTouristWaiting(TouristWaiting touristWaiting);

    int insertSelective(PartnerInfo record);

    long countByExample(PartnerInfoExample example);

    int insertPartnerInfoSelective(PartnerInfo record);

    int updateTouristWaitingById(TouristWaiting touristWaiting);

    List<Map> queryMemberIListById(@Param("societyId")String societyId, @Param("merchantId")String merchantId);

    List<Map> queryParterListById(@Param("campusId")String campusId);

}
