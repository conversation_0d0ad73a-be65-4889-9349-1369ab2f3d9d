package com.jsunicom.oms.service.resource.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.common.constants.CommonProperties;
import com.jsunicom.oms.mapper.resource.TnSerialIdleMapper;
import com.jsunicom.oms.po.resource.TnSerialIdle;
import com.jsunicom.oms.service.resource.ChangeSerialByConditionService;
import com.jsunicom.oms.service.resource.TnSerialIdleService;
import com.jsunicom.oms.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * */

@Service("changeSerialByConditionService")
@Slf4j
public class ChangeSerialByConditionServiceImpl implements ChangeSerialByConditionService {

    @Resource
    private CommonProperties properties;

    private static CommonProperties commonProperties;

    private final static String SUCCESS_CODE = "0000";

    private final static String FAIL_CODE = "8888";

    @Resource
    private TnSerialIdleMapper tnSerialIdleMapper;

    @Resource
    private TnSerialIdleService tnSerialIdleService;

    @PostConstruct
    public void beforeInit() {
        commonProperties = properties;
    }

    @Override
    public void expireMobileSerialNo(TnSerialIdle tnSerialIdle){
        String serialNo = tnSerialIdle.getSerialNo();
        String cityNo = tnSerialIdle.getCityCode();
        //获取发展人信息
        Map<String, Object> developInfo = getDevelopInfo(cityNo);
        try {
            JSONObject responseObject = encapsulatedMessageAndSendRequest(tnSerialIdle, developInfo);
            //获取返回的有效信息
            JSONObject rsp = getRsp(responseObject);
            //如果返回的信息不为空
            if (rsp != null){
                String rspCode = rsp.getString("RSP_CODE");
                String rspDesc = rsp.getString("RSP_DESC");
                JSONArray data = rsp.getJSONArray("DATA");
                if (FAIL_CODE.equals(rspCode) && ObjectUtils.isEmpty(data)){
                    //判断号码是否在网
                    checkRule(rspCode, rspDesc, tnSerialIdle);
                }else if (SUCCESS_CODE.equals(rspCode) && !ObjectUtils.isEmpty(data)){
                    log.info("current number has a fusion relationship,serialNo:{}", serialNo);
                    tnSerialIdleMapper.changeSerialState(serialNo);
                    //添加号码操作轨迹
                    tnSerialIdle.setState("07");
                    tnSerialIdleService.handleOperateInfo(tnSerialIdle, "定时下架", "admin");
                }
            }
        } catch (Exception e) {
            log.error("调用查询号码融合关系报错,errorMessage:{}", e.getMessage());
        }
    }


    @Override
    public void expireFusionSerialNo(TnSerialIdle tnSerialIdle){
        String serialNo = tnSerialIdle.getSerialNo();
        String cityNo = tnSerialIdle.getCityCode();
        //获取发展人信息
        Map<String, Object> developInfo = getDevelopInfo(cityNo);
        try {
            JSONObject responseObject = encapsulatedMessageAndSendRequest(tnSerialIdle, developInfo);
            //获取返回的有效信息
            JSONObject rsp = getRsp(responseObject);
            //如果返回的信息不为空
            if (rsp != null){
                String rspCode = rsp.getString("RSP_CODE");
                String rspDesc = ObjectUtils.isEmpty(rsp.getString("RSP_DESC")) ? "" : rsp.getString("RSP_DESC");
                JSONArray data = rsp.getJSONArray("DATA");
                if (FAIL_CODE.equals(rspCode) && ObjectUtils.isEmpty(data) && rspDesc.contains("未在ES中查询到有效的校园融合绑定关系表数据")){
                    log.info("current number does not have a fusion relationship,serialNo:{}", serialNo);
                    tnSerialIdleMapper.changeSerialState(serialNo);
                    //添加号码操作轨迹
                    tnSerialIdle.setState("07");
                    tnSerialIdleService.handleOperateInfo(tnSerialIdle, "定时下架", "admin");
                }else if (FAIL_CODE.equals(rspCode) && ObjectUtils.isEmpty(data)){
                    //判断号码是否在网
                    checkRule(rspCode, rspDesc, tnSerialIdle);
                }
            }
        } catch (Exception e) {
            log.error("调用查询号码融合关系报错,errorMessage:{}", e.getMessage());
        }
    }

    /**
     * 封装请求报文中的请求体并返回
     * */
    public JSONObject getUniBssBody(String serialNo, String cityNo, Map<String, Object> developInfo){
        JSONObject uniBssBody = new JSONObject();
        JSONObject queryRelationReq = new JSONObject();
        //查询的移网号码
        queryRelationReq.put("SERIAL_NUMBER", serialNo);
        //省份编码
        queryRelationReq.put("PROVINCE_CODE", "34");
        //区县编码
        queryRelationReq.put("CITY_CODE", cityNo);
        //渠道编码
        queryRelationReq.put("CHANNEL_ID", developInfo.get("CHANNELID"));
        //渠道类型
        queryRelationReq.put("CHANNEL_TYPE", developInfo.get("CHANNELTYPE"));
        //部门编码
        queryRelationReq.put("DEPART_ID", developInfo.get("DEPARTID"));
        //三位的地市编码
        queryRelationReq.put("EPARCHY_CODE", developInfo.get("CITYNO"));
        //渠道状态
        queryRelationReq.put("IN_MODE_CODE", developInfo.get("INMODECODE"));
        //请求方流水号
        queryRelationReq.put("REQ_NO", new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date(System.currentTimeMillis())) + (int) (Math.random() * 1000000));
        //对应渠道工号
        queryRelationReq.put("STAFF_ID", developInfo.get("STAFFID"));
        //系统平台信息
        JSONObject systemCodeInfos = new JSONObject();
        systemCodeInfos.put("SYSAPP_ID", commonProperties.getAppKey());
        systemCodeInfos.put("SYSSTAFF_ID", developInfo.get("STAFFID"));
        queryRelationReq.put("SYSTEMCODE_INFOS", systemCodeInfos);
        uniBssBody.put("QUERYRELATION_REQ", queryRelationReq);
        return uniBssBody;
    }

    /**
     * 移网号池和融合号池都需要判断当前号码已在网，该方法统一判断
     * */
    public void checkRule(String rspCode, String rspDesc,TnSerialIdle tnSerialIdle) throws Exception{
        if (ObjectUtils.isEmpty(rspDesc)){
            return;
        }
        if (FAIL_CODE.equals(rspCode) && rspDesc.contains("号码已经在网")){
            log.info("current number is already online,serialNo:{}", tnSerialIdle.getSerialNo());
            tnSerialIdleMapper.changeSerialState(tnSerialIdle.getSerialNo());
            //添加号码操作轨迹
            tnSerialIdle.setState("07");
            tnSerialIdleService.handleOperateInfo(tnSerialIdle, "定时下架", "admin");
        }
    }

    /**
     * 获取返回报文中可用于判断状态的有效信息并进行空值判断
     * */
    public JSONObject getRsp(JSONObject responseObject) throws Exception{
        JSONObject rsp = null;
        JSONObject uniBssBody = responseObject.getJSONObject("UNI_BSS_BODY");
        if (ObjectUtils.isEmpty(uniBssBody)){
            log.info("no first layer response body obtained:UNI_BSS_BODY");
        }else {
            JSONObject queryRelationRsp = uniBssBody.getJSONObject("QUERYRELATION_RSP");
            if (ObjectUtils.isEmpty(queryRelationRsp)){
                log.info("no second layer response body obtained:QUERYRELATION_RSP");
            }else {
                rsp = queryRelationRsp.getJSONObject("RSP");
                if (ObjectUtils.isEmpty(rsp)){
                    log.info("no third layer response body obtained:RSP");
                }
            }
        }
        return rsp;
    }

    /**
     * 获取号码对应地市的发展人信息、工号、发展人渠道信息等
     * */
    public Map<String, Object> getDevelopInfo(String cityNo)  {
        //查询号码所在地市的发展人、渠道
        List<Map<String, Object>> developLists = tnSerialIdleMapper.queryPersonInfoByCityNo(cityNo);
        Map<String, Object> tempMap = new HashMap<>(20);
        for (Map<String, Object> map : developLists){
            if (!ObjectUtils.isEmpty(map.get("DEPARTID"))){
                //如果有部门编码，返回有部门编码的发展人信息
                return map;
            }
            //如果对应的记录都没有发展人编码，任意部门编码调接口
            tempMap = map;
            tempMap.put("DEPARTID", "12345");
        }
        return tempMap;
    }

    /**
     * 填充请求报文并且发送请求返回相应报文
     * */
    public JSONObject encapsulatedMessageAndSendRequest(TnSerialIdle tnSerialIdle, Map<String, Object> developInfo) throws Exception{
        JSONObject uniBssAttached = new JSONObject();
        uniBssAttached.put("MEDIA_INFO", "");
        //填充请求体
        JSONObject uniBssBody = getUniBssBody(tnSerialIdle.getSerialNo(), tnSerialIdle.getCityCode(), developInfo);
        //发送请求
        String response = HttpUtil.sendRequest(commonProperties.getQueryRelationUrl(), uniBssBody, uniBssAttached);
        return JSONObject.parseObject(response);
    }

}
