package com.jsunicom.oms.message.utils;

import com.jsunicom.oms.common.ArgumentChecker;
import com.jsunicom.oms.common.exception.CgwErrorCodes;
import com.lz.lsf.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by wentao on 2018/3/6.
 **/
public class Checker {
    public static void checkNotNull(Object o, String msg) {
        ArgumentChecker.notNull(msg, o);
    }

    public static void checkNotBlank(String o, String msg) {
        if (StringUtils.isEmpty(o)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, msg);
        }
    }

    public static void checkArrayToLong(List e, int size, String msg) {
        if (e != null && e.size() >= size) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, msg);
        }
    }

    public static void checkArrayRange(List e, int min, int max, String msg) {
        if (e != null && (e.size() < min || e.size() > max)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, msg);
        }
    }

    public static void checkIsUrl(String url) {

        boolean isurl = false;
        String regex = "(((https|http)?://)?([a-z0-9]+[.])|(www.))"
                + "\\w+[.|\\/]([a-z0-9]{0,})?[[.]([a-z0-9]{0,})]+((/[\\S&&[^,;\u4E00-\u9FA5]]+)+)?([.][a-z0-9]{0,}+|/?)";//设置正则表达式

        Pattern pat = Pattern.compile(regex.trim());
        Matcher mat = pat.matcher(url.trim());
        //判断是否匹配
        isurl = mat.matches();
        if (!isurl) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, "");
        }
        //URL url1 = new URL(url);

    }

}
