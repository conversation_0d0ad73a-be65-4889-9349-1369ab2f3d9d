package com.jsunicom.oms.service.resource;

import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.po.resource.TnSerialIdle;
import java.util.Map;

public interface TnSerialIdleService {

    CustomResult queryThSerialIdleListByCondition(Map<String, Object> map, boolean isAccurateQuery, UserInfo userInfo);

    CustomResult updateTnSerialIdle(Map<String, Object> param);

    CustomResult queryTnSerialCampusList(Map<String, Object> map, UserInfo userInfo);

    CustomResult delTnSerialIdleBySerialNo(String serialNo);

    int handleOperateInfo(TnSerialIdle tnSerialIdle, String operateType, String staffId);

    boolean saveTnSerialIdle(TnSerialIdle tnSerialIdle, UserInfo userInfo);

    CustomResult querySerialNumber(Map<String, Object> param);

    CustomResult querySchoolSerials(Map<String, Object> param);

    CustomResult changeSerialState(Map<String, Object> param);

}
