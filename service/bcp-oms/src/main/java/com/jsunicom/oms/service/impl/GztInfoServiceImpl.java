package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.model.partner.GztInfo;
import com.jsunicom.oms.mapper.partner.GztInfoDao;
import com.jsunicom.oms.service.GztInfoFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: GztInfoServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-21  22:02
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class GztInfoServiceImpl implements GztInfoFacade {
    @Autowired
    private GztInfoDao gztInfoDao;

    @Override
    public GztInfo findByCityId(String cityId) {
        return gztInfoDao.findByCityId(cityId);
    }

    @Override
    public List<GztInfo> findDataSyn() {
        return gztInfoDao.findDataSyn();
    }
}
