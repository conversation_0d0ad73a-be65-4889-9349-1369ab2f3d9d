package com.jsunicom.oms.entity;

import java.io.Serializable;
import java.util.Date;

public class PartnerInfo implements Serializable {
    private Long id;

    private String partnerType;

    private String partnerName;

    private String acctNo;

    private String partnerCertNo;

    private String mblNbr;

    private String professionType;

    private String orgCode;

    private String provCode;

    private String provName;

    private String cityCode;

    private String cityName;

    private String areaCode;

    private String areaName;

    private String address;

    private String develId;

    private String emploeeType;

    private String isMerAdmin;

    private Long commisAcctId;

    private Long collegeId;

    private Long dormitoryId;

    private Long referenceId;

    private String societyId;

    private Long merchantId;

    private String saleMgrName;

    private String saleMgrPhone;

    private String busLine;

    private String busGrid;

    private String inviteCode;

    private String develChannel;

    private String gztRs;

    private String state;

    private String auditBy;

    private Date auditTime;

    private String auditRefuseReson;

    private String auditRemark;

    private String remark;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private String merchantName;

    private String reserve1;

    private String reserve2;

    private String reserve3;

    private String reserve4;

    private String reserve5;

    private String pic1;

    private String pic2;

    private String pic3;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType == null ? null : partnerType.trim();
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName == null ? null : partnerName.trim();
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo == null ? null : acctNo.trim();
    }

    public String getPartnerCertNo() {
        return partnerCertNo;
    }

    public void setPartnerCertNo(String partnerCertNo) {
        this.partnerCertNo = partnerCertNo == null ? null : partnerCertNo.trim();
    }

    public String getMblNbr() {
        return mblNbr;
    }

    public void setMblNbr(String mblNbr) {
        this.mblNbr = mblNbr == null ? null : mblNbr.trim();
    }

    public String getProfessionType() {
        return professionType;
    }

    public void setProfessionType(String professionType) {
        this.professionType = professionType == null ? null : professionType.trim();
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode == null ? null : provCode.trim();
    }

    public String getProvName() {
        return provName;
    }

    public void setProvName(String provName) {
        this.provName = provName == null ? null : provName.trim();
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode == null ? null : areaCode.trim();
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName == null ? null : areaName.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getDevelId() {
        return develId;
    }

    public void setDevelId(String develId) {
        this.develId = develId == null ? null : develId.trim();
    }

    public String getEmploeeType() {
        return emploeeType;
    }

    public void setEmploeeType(String emploeeType) {
        this.emploeeType = emploeeType == null ? null : emploeeType.trim();
    }

    public String getIsMerAdmin() {
        return isMerAdmin;
    }

    public void setIsMerAdmin(String isMerAdmin) {
        this.isMerAdmin = isMerAdmin == null ? null : isMerAdmin.trim();
    }

    public Long getCommisAcctId() {
        return commisAcctId;
    }

    public void setCommisAcctId(Long commisAcctId) {
        this.commisAcctId = commisAcctId;
    }

    public Long getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Long collegeId) {
        this.collegeId = collegeId;
    }

    public Long getDormitoryId() {
        return dormitoryId;
    }

    public void setDormitoryId(Long dormitoryId) {
        this.dormitoryId = dormitoryId;
    }

    public Long getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Long referenceId) {
        this.referenceId = referenceId;
    }

    public String getSocietyId() {
        return societyId;
    }

    public void setSocietyId(String societyId) {
        this.societyId = societyId == null ? null : societyId.trim();
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getSaleMgrName() {
        return saleMgrName;
    }

    public void setSaleMgrName(String saleMgrName) {
        this.saleMgrName = saleMgrName == null ? null : saleMgrName.trim();
    }

    public String getSaleMgrPhone() {
        return saleMgrPhone;
    }

    public void setSaleMgrPhone(String saleMgrPhone) {
        this.saleMgrPhone = saleMgrPhone == null ? null : saleMgrPhone.trim();
    }

    public String getBusLine() {
        return busLine;
    }

    public void setBusLine(String busLine) {
        this.busLine = busLine == null ? null : busLine.trim();
    }

    public String getBusGrid() {
        return busGrid;
    }

    public void setBusGrid(String busGrid) {
        this.busGrid = busGrid == null ? null : busGrid.trim();
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode == null ? null : inviteCode.trim();
    }

    public String getDevelChannel() {
        return develChannel;
    }

    public void setDevelChannel(String develChannel) {
        this.develChannel = develChannel == null ? null : develChannel.trim();
    }

    public String getGztRs() {
        return gztRs;
    }

    public void setGztRs(String gztRs) {
        this.gztRs = gztRs == null ? null : gztRs.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy == null ? null : auditBy.trim();
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditRefuseReson() {
        return auditRefuseReson;
    }

    public void setAuditRefuseReson(String auditRefuseReson) {
        this.auditRefuseReson = auditRefuseReson == null ? null : auditRefuseReson.trim();
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark == null ? null : auditRemark.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getReserve1() {
        return reserve1;
    }

    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1 == null ? null : reserve1.trim();
    }

    public String getReserve2() {
        return reserve2;
    }

    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2 == null ? null : reserve2.trim();
    }

    public String getReserve3() {
        return reserve3;
    }

    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3 == null ? null : reserve3.trim();
    }

    public String getReserve4() {
        return reserve4;
    }

    public void setReserve4(String reserve4) {
        this.reserve4 = reserve4 == null ? null : reserve4.trim();
    }

    public String getReserve5() {
        return reserve5;
    }

    public void setReserve5(String reserve5) {
        this.reserve5 = reserve5 == null ? null : reserve5.trim();
    }

    public String getPic1() {
        return pic1;
    }

    public void setPic1(String pic1) {
        this.pic1 = pic1 == null ? null : pic1.trim();
    }

    public String getPic2() {
        return pic2;
    }

    public void setPic2(String pic2) {
        this.pic2 = pic2 == null ? null : pic2.trim();
    }

    public String getPic3() {
        return pic3;
    }

    public void setPic3(String pic3) {
        this.pic3 = pic3 == null ? null : pic3.trim();
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
}
