package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSON;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.dto.view.CampusViewDto;
import com.jsunicom.oms.service.SchoolViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-08-29-16:23
 */
@RestController
@RequestMapping(value = "/schoolView")
@Slf4j
public class SchoolViewController extends AbstractSimpleController {
    @Autowired
    private SchoolViewService schoolViewService;
    /**
    *@autor: lkm
    *@time: 2023/8/29 16:26
    *@description: 查询校园团队青创社信息，以及下单激活漏斗分析
    *@param:
    *@return: 0000成功  9999失败
    */
    @GetMapping("/querySchoolInfos")
    public CustomResult querySchoolInfos(String orgCode,String campusId,String dateTime){
        log.info("查询校园团队青创社信息，以及下单激活漏斗分析 orgCode:{},campusId:{},dateTime:{}",orgCode,campusId,dateTime );
        CustomResult customResult=schoolViewService.querySchoolInfos(orgCode,campusId,dateTime);
        return customResult;
    }

    /*
     * @description: 存量、发展用户计算
     * @author: zhaowang
     * @date: 2023/12/25 上午11:20
     * @param: [orgCode, campusId, type = 1 青创社成员、2 渠道]
     * @return: com.jsunicom.oms.common.result.CustomResult
     **/
    @PostMapping("/querySchoolStockAndDevelopUserInfos")
    public CustomResult querySchoolStockAndDevelopUserInfos(@RequestBody CampusViewDto campusViewDto){
        log.info("查询校园存量用户计算，campusViewDto:{}", JSON.toJSONString(campusViewDto));
        CustomResult customResult=schoolViewService.querySchoolStockAndDevelopUserInfos(campusViewDto);
        return customResult;
    }

    /*
     * @description: 查询地市
     * @author: zhaowang
     * @date: 2023/12/25 下午4:49
     * @param: [request]
     * @return: com.jsunicom.oms.common.result.CustomResult
     **/
    @GetMapping("/queryCityAndSchoolInfos")
    public CustomResult queryCityAndSchoolInfos(HttpServletRequest request){
        String orgCode = request.getHeader("areaCode");
        String serialNumber=request.getHeader("serialNumber");
        CustomResult customResult=schoolViewService.queryCityAndSchoolInfos(orgCode,serialNumber);
        return customResult;
    }
}
