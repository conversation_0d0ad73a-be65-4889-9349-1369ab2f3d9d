package com.jsunicom.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.model.salesman.SalesManagerVo;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.po.TaskInfo;
import com.jsunicom.oms.po.WoScYiTask;
import com.jsunicom.oms.po.task.WoScYiTaskBase;
import com.jsunicom.oms.po.task.WoScYiTaskBaseVo;
import com.jsunicom.oms.po.task.WoScYiTaskScene;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service
 * @ClassName: InnovateTaskService
 * @Author: zhaowang
 * @CreateTime: 2023-07-20  17:07
 * @Description: TODO
 * @Version: 1.0
 */
public interface InnovatePCTaskService {
    PageInfo<Map<String, Object>> qryTaskList(WoSchoolParamDto paramDto) throws ParseException;
    WoScYiTask qryTaskByOrderId(String orderId);
    TaskInfo qryTaskInfoByOrderId(String orderId);
    PageInfo<Map<String, Object>> qryTaskBaseList(WoSchoolParamDto paramDto) throws ParseException;
    int saveTaskBaseInfo(WoScYiTaskBase woScYiTaskBase);

    int saveTaskSceneInfo(WoScYiTaskScene woScYiTaskScene);

    Map<String, Object> taskBaseDetail(String taskId);

    List<Map<String, Object>> queryTaskSceneInfo(String taskId);

    PageInfo<SalesManagerVo> getSaleManager(JSONObject jsonObject);

    List<SalesManagerVo> getSaleManagers(JSONObject jsonObject);

    PageInfo<Map<String, Object>> taskBaseStatistics(JSONObject jsonObject);

    List<Map<String, Object>>  queryChannelList(JSONObject jsonObject) throws Exception;

    PageInfo<SalesManagerVo> getSaleManagerTaskDetail(JSONObject jsonObject);
}
