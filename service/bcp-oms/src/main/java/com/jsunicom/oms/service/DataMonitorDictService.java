package com.jsunicom.oms.service;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.po.DataMonitorDict;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service
 * @ClassName: DataMonitorDictService
 * @Author: zhaowang
 * @CreateTime: 2023-03-16  19:35
 * @Description: TODO
 * @Version: 1.0
 */
public interface DataMonitorDictService {
    List<DataMonitorDict> findAll();
}
