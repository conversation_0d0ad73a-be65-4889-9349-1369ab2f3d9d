package com.jsunicom.oms.controller;

import com.jsunicom.oms.common.annotation.NotNeedResponseCover;
import com.jsunicom.oms.response.ResponseResult;
import com.jsunicom.oms.service.LinkConversionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @apiNote 短链接转义长链接并重定向，转义过程为表配置
 * */

@Controller
@Slf4j
@NotNeedResponseCover
@RequestMapping("/r")
public class LinkConversionController {

    @Resource
    private LinkConversionService linkConversionService;

    @GetMapping("/{shortLink}")
    public String linkConversionAndRedirect(@PathVariable String shortLink){
        log.info("linkConversionAndRedirectParam...shortLink:{}", shortLink);
        try {
            ResponseResult conversionResult = linkConversionService.linkConversion(shortLink);
            if (conversionResult.isSuccess()){
                log.info("linkConversionAndRedirectSuccess,shortLink:{},longLink:{}", shortLink, conversionResult.getData());
                return "redirect:" + conversionResult.getData();
            }else {
                return "redirect:https://wosm.js165.com:18000/wosc/fusion/lost?msgTips=" + URLEncoder.encode(conversionResult.getMsg(), "UTF-8");
            }
        }catch (Exception e){
            log.error("linkConversionAndRedirectException...", e);
            return "redirect:https://wosm.js165.com:18000/wosc/fusion/lost?msgTips=%E7%B3%BB%E7%BB%9F%E5%86%85%E9%83%A8%E5%BC%82%E5%B8%B8";
        }
    }

}
