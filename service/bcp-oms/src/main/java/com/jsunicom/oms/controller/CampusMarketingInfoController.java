package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.po.CampusCollegeExtend;
import com.jsunicom.oms.po.CampusRecruitInfo;
import com.jsunicom.oms.po.WoSchoolCampusCollege;
import com.jsunicom.oms.po.WoSchoolCampusMarketingInfo;
import com.jsunicom.oms.service.CampusMarketingInfoService;
import com.jsunicom.oms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Project:CampusMarketingInfoController
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
@Slf4j
@RestController
@RequestMapping(value = "/campusMarketing")
public class CampusMarketingInfoController extends AbstractSimpleController {

    @Resource
    private CampusMarketingInfoService campusMarketingInfoService;

    @RequestMapping(value = "/getMarketingList", method = RequestMethod.POST, name = "查询营销信息")
    public CustomResult getMarketingList(@RequestBody JSONObject params){

        log.info("进入getMarketingList方法："+ JSONObject.toJSONString(params));
        String campusId =  params.getString ("campusId");
        String  marketingType = params.getString("marketingType");
        String  marketingYear = params.getString("marketingYear");

        WoSchoolCampusMarketingInfo record = new WoSchoolCampusMarketingInfo();
        record.setCampusId(Long.valueOf(campusId));
        record.setMarketingType(marketingType);
        record.setMarketingYear(marketingYear);
        List<WoSchoolCampusMarketingInfo> recordList = campusMarketingInfoService.getCollegeList(record);

        if(recordList.isEmpty()){

            JSONObject object = new JSONObject();

            object.put("enrollmentVolume", 0);
            object.put("taskVolume", 0);
            object.put("ywTaskVolume", 0);
            object.put("kdTaskVolume", 0);
            object.put("rhDevelopVolume", 0);
            object.put("qcsDevelopTarget", 0);
            return ResultUtil.errorByJSONObject(object);
        }

        JSONArray objectArray = new JSONArray();
        for (WoSchoolCampusMarketingInfo woSchoolCampusMarketingInfo : recordList){
            JSONObject object = new JSONObject();
            object.put("marketingYear", woSchoolCampusMarketingInfo.getMarketingYear()!= null?woSchoolCampusMarketingInfo.getMarketingYear():"");
            object.put("marketingType", woSchoolCampusMarketingInfo.getMarketingType()!= null?woSchoolCampusMarketingInfo.getMarketingType():"");

            object.put("enrollmentVolume", woSchoolCampusMarketingInfo.getEnrollmentVolume()!= null?woSchoolCampusMarketingInfo.getEnrollmentVolume():0);
            object.put("taskVolume", woSchoolCampusMarketingInfo.getTaskVolume()!=null?woSchoolCampusMarketingInfo.getTaskVolume():0);
            String newStudentStartDate = DateUtils.getDayByTime(woSchoolCampusMarketingInfo.getNewStudentStartDate());
            String oldStudentStartDate = DateUtils.getDayByTime(woSchoolCampusMarketingInfo.getOldStudentStartDate());
            object.put("newStudentStartDate", newStudentStartDate);
            object.put("oldStudentStartDate", oldStudentStartDate);
            object.put("ywTaskVolume", woSchoolCampusMarketingInfo.getYwTaskVolume()!=null?woSchoolCampusMarketingInfo.getYwTaskVolume():0);
            object.put("kdTaskVolume", woSchoolCampusMarketingInfo.getKdTaskVolume()!=null?woSchoolCampusMarketingInfo.getKdTaskVolume():0);
            object.put("rhDevelopVolume", woSchoolCampusMarketingInfo.getRhDevelopVolume()!=null?woSchoolCampusMarketingInfo.getRhDevelopVolume():0);
            object.put("qcsDevelopTarget", woSchoolCampusMarketingInfo.getQcsDevelopTarget()!=null?woSchoolCampusMarketingInfo.getQcsDevelopTarget():0);

            objectArray.add(object);
        }

        return ResultUtil.successByJSONArray(objectArray);
    }

    @RequestMapping(value = "/saveMarketingInfo", method = RequestMethod.POST, name = "营销信息-保存信息")
    public CustomResult saveMarketingInfo(@RequestBody JSONObject params, HttpServletRequest request){

        log.info("saveMarketingInfo："+JSONObject.toJSONString(params));

        try {
            WoSchoolCampusMarketingInfo record = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusMarketingInfo.class);
            List<WoSchoolCampusMarketingInfo> recruitInfoList = campusMarketingInfoService.getCollegeAdd(record);

            if(recruitInfoList.isEmpty()){

                WoSchoolCampusMarketingInfo recordTemp = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusMarketingInfo.class);
                recordTemp.setCreateBy(request.getHeader("staffNo"));
                recordTemp.setCreateTime(new Date());
                campusMarketingInfoService.saveCollegeExtendInfo(recordTemp);

                return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "新增数据成功");

            }else{
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"新增数据已经存在！");
            }

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"保存失败！");
        }

    }

    @RequestMapping(value = "/updateMarketingInfo", method = RequestMethod.POST, name = "营销信息-更新信息")
    public CustomResult updateMarketingInfo(@RequestBody JSONObject params, HttpServletRequest request){

        log.info("updateMarketingInfo："+JSONObject.toJSONString(params));

        try {
            if(!params.containsKey("campusId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            WoSchoolCampusMarketingInfo record = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusMarketingInfo.class);
            record.setUpdateBy(request.getHeader("staffNo"));
            record.setUpdateTime(new Date());

            campusMarketingInfoService.updateCollegeInfo(record);

            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "更新数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"更新失败！");
        }
    }

    @RequestMapping(value = "/deleteMarketingInfo", method = RequestMethod.POST, name = "营销信息-删除信息")
    public CustomResult deleteMarketingInfo(@RequestBody JSONObject params){

        log.info("进入deleteMarketingInfo方法："+JSONObject.toJSONString(params));

        try {

            if(!params.containsKey("campusId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }
            if(!params.containsKey("marketingType")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }
            if(!params.containsKey("marketingYear")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            String campusId =  params.getString("campusId");
            String marketingType =  params.getString("marketingType");
            String marketingYear =  params.getString("marketingYear");

            // 删除院系信息
            campusMarketingInfoService.deleteCollegeInfo(Long.valueOf(campusId),marketingType,marketingYear);


            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "删除数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"删除失败！");
        }

    }
}
