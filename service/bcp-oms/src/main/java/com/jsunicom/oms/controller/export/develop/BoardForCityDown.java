package com.jsunicom.oms.controller.export.develop;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class BoardForCityDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
//    @ExcelProperty(index = 0,value = "账期")
//    @JSONField(name = "dayId")
//    private String dayId;
    @ExcelProperty(index = 1,value = "地市名称")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 2,value = "归属校区")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 3,value = "校区经理姓名")
    @JSONField(name = "partnerName")
    private String partnerName;   //null
    @ExcelProperty(index = 3,value = "校区经理手机号")
    @JSONField(name = "phone")
    private String phone;   //null
    @ExcelProperty(index = 4,value = "青创社数量")
    @JSONField(name = "youthNum")
    private String youthNum;
    @ExcelProperty(index = 5,value = "青创社人员数量")
    @JSONField(name = "memberNum")
    private String memberNum;
    @ExcelProperty(index = 6,value = "已发展用户社员数量")
    @JSONField(name = "devMemNum")
    private String devMemNum;
    @ExcelProperty(index = 7,value = "已发展用户社员覆盖率")
    @JSONField(name = "fgl")
    private String fgl;
    @ExcelProperty(index = 8,value = "用户存量")
    @JSONField(name = "hisMemNum")
    private String hisMemNum;
    @ExcelProperty(index = 9,value = "用户发展量")
    @JSONField(name = "nowMemNum")
    private String nowMemNum;
}
