package com.jsunicom.oms.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "小沃激活校验返回报文订单信息")
public class WoOrderListRsp {
    private static final long serialVersionUID = -2919688958096281893L;

    @ApiModelProperty(value = "订单中心订单编码", required = true, position = 1)
    private String orderCenterId;

    @ApiModelProperty(value = "商品订单编码", required = true, position = 2)
    private String goodsOrderId;

    @ApiModelProperty(value = "产品订单编码", required = true, position = 3)
    private String productOrderId;

    @ApiModelProperty(value = "客户名称", required = true, position = 4)
    private String custName;

    @ApiModelProperty(value = "证件ID", required = true, position = 5)
    private String psptId;

    @ApiModelProperty(value = "开户号码", required = true, position = 6)
    private String serialNumber;

    @ApiModelProperty(value = "套餐品牌", required = true, position = 7)
    private String packageBrand;

    @ApiModelProperty(value = "激活状态", required = true, position = 8)
    private String activeState;

    @ApiModelProperty(value = "激活时间", required = true, position = 9)
    private String activeDate;

    @ApiModelProperty(value = "商品状态", required = true, position = 10)
    private String goodsState;

    @ApiModelProperty(value = "激活次数", required = true, position = 11)
    private String activeTime;

    @ApiModelProperty(value = "最后一次激活时间", required = true, position = 12)
    private String lastActiveTime;

    @ApiModelProperty(value = "号码归属地", required = true, position = 13)
    private String eparchyCode;

    @ApiModelProperty(value = "证件地址", required = true, position = 14)
    private String psptAddress;

    @ApiModelProperty(value = "证件有效期", required = true, position = 15)
    private String psptValidity;

    @ApiModelProperty(value = "联系电话", required = true, position = 16)
    private String linkPhone;

    @ApiModelProperty(value = "联系人", required = true, position = 17)
    private String linkPerson;


    @ApiModelProperty(value = "是否融合", required = true, position = 18)
    private String isRongHe;

    @ApiModelProperty(value = "工号", required = true, position = 19)
    private String staffId;

    @ApiModelProperty(value = "渠道编码", required = true, position = 20)
    private String channelId;

    public String getOrderCenterId() {
        return orderCenterId;
    }

    public void setOrderCenterId(String orderCenterId) {
        this.orderCenterId = orderCenterId;
    }

    public String getGoodsOrderId() {
        return goodsOrderId;
    }

    public void setGoodsOrderId(String goodsOrderId) {
        this.goodsOrderId = goodsOrderId;
    }

    public String getProductOrderId() {
        return productOrderId;
    }

    public void setProductOrderId(String productOrderId) {
        this.productOrderId = productOrderId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getPsptId() {
        return psptId;
    }

    public void setPsptId(String psptId) {
        this.psptId = psptId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getPackageBrand() {
        return packageBrand;
    }

    public void setPackageBrand(String packageBrand) {
        this.packageBrand = packageBrand;
    }

    public String getActiveState() {
        return activeState;
    }

    public void setActiveState(String activeState) {
        this.activeState = activeState;
    }

    public String getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(String activeDate) {
        this.activeDate = activeDate;
    }

    public String getGoodsState() {
        return goodsState;
    }

    public void setGoodsState(String goodsState) {
        this.goodsState = goodsState;
    }

    public String getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(String activeTime) {
        this.activeTime = activeTime;
    }

    public String getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(String lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public String getEparchyCode() {
        return eparchyCode;
    }

    public void setEparchyCode(String eparchyCode) {
        this.eparchyCode = eparchyCode;
    }

}
