package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.mapper.WoSchoolCampusMarketingInfoMapper;
import com.jsunicom.oms.po.*;
import com.jsunicom.oms.service.CampusMarketingInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Project:CampusMarketingInfoServiceImpl
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
@Slf4j
@Service
public class CampusMarketingInfoServiceImpl implements CampusMarketingInfoService {

    @Resource
    private WoSchoolCampusMarketingInfoMapper woSchoolCampusMarketingInfoMapper;

    @Override
    public List<WoSchoolCampusMarketingInfo> getCollegeList(WoSchoolCampusMarketingInfo record) {
        WoSchoolCampusMarketingInfoExample example = new WoSchoolCampusMarketingInfoExample();
        WoSchoolCampusMarketingInfoExample.Criteria criteria = example.createCriteria();
        criteria.andCampusIdEqualTo(record.getCampusId());
        if(StringUtils.isNoneEmpty(record.getMarketingYear())){
            criteria.andMarketingYearEqualTo(record.getMarketingYear());
        }

        if(StringUtils.isNoneEmpty(record.getMarketingType())){
            criteria.andMarketingTypeEqualTo(record.getMarketingType());
        }

        List<WoSchoolCampusMarketingInfo> collegeList = woSchoolCampusMarketingInfoMapper.selectByExample(example);
        return collegeList;
    }

    @Override
    public int saveCollegeExtendInfo(WoSchoolCampusMarketingInfo record){
        return woSchoolCampusMarketingInfoMapper.insertSelective(record);
    }

    @Override
    public List<WoSchoolCampusMarketingInfo> getCollegeAdd(WoSchoolCampusMarketingInfo record) {
        WoSchoolCampusMarketingInfoExample example = new WoSchoolCampusMarketingInfoExample();
        WoSchoolCampusMarketingInfoExample.Criteria criteria = example.createCriteria();
        if(record.getMarketingYear() != null){
            criteria.andMarketingYearEqualTo(record.getMarketingYear());
        }
        if(record.getMarketingType() != null){
            criteria.andMarketingTypeEqualTo(record.getMarketingType());
        }

        List<WoSchoolCampusMarketingInfo> collegeList = woSchoolCampusMarketingInfoMapper.selectByExample(example);
        return collegeList;
    }

    public int updateCollegeInfo(WoSchoolCampusMarketingInfo record)
    {
        return woSchoolCampusMarketingInfoMapper.updateByPrimaryKeySelective(record);
    }
    public int deleteCollegeInfo(Long collegeId,String marketingType,String marketingYear){
        return woSchoolCampusMarketingInfoMapper.deleteByPrimaryKey(collegeId,marketingType,marketingYear);
    }
}
