/**
 * @Title:  ExportClassificationPartnerDto.java
 * @Package com.mucfc.bcp.core.partner.dto
 * @Description: TODO(用一句话描述该文件做什么)
 * @author: 李鑫
 * @date: 2019年5月27日 上午10:44:21
 * @version V1.0
 */
package com.jsunicom.oms.model.partner;


import com.jsunicom.oms.model.base.BasicModel;

/**
 * @ClassName:  ExportClassificationPartnerDto
 * @author: 李鑫
 * @date: 2019年5月27日 上午10:44:21
 * 
 */
public class ExportClassificationPartner extends BasicModel {
    private static final long serialVersionUID = 1L;
	private String partnerId;
	private String partnerName;
	private String cityName;
	private String partnerTel;
	private String merchantId;
	private String merchantName;
	private String classificationName;
	private String classificationFirstName;
	public String getPartnerId() {
		return partnerId;
	}
	public void setPartnerId(String partnerId) {
		this.partnerId = partnerId;
	}
	public String getPartnerName() {
		return partnerName;
	}
	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}
	public String getCityName() {
		return cityName;
	}
	public void setCityName(String cityName) {
		this.cityName = cityName;
	}
	public String getMerchantName() {
		return merchantName;
	}
	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}
	public String getClassificationName() {
		return classificationName;
	}
	public void setClassificationName(String classificationName) {
		this.classificationName = classificationName;
	}
	public String getClassificationFirstName() {
		return classificationFirstName;
	}
	public void setClassificationFirstName(String classificationFirstName) {
		this.classificationFirstName = classificationFirstName;
	}
	public String getPartnerTel() {
		return partnerTel;
	}
	public void setPartnerTel(String partnerTel) {
		this.partnerTel = partnerTel;
	}
	public String getMerchantId() {
		return merchantId;
	}
	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}
}
