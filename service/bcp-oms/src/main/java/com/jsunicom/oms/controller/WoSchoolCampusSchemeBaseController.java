package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.jsunicom.common.cos.ImageWatermarkUtil;
import com.jsunicom.common.cos.PrivateCosClientUtil;
import com.jsunicom.common.cos.PrivateOssClientUtil;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.dto.scheme.CheckSchemeProcessApproveVo;
import com.jsunicom.oms.dto.scheme.SchemeProcessApprove;
import com.jsunicom.oms.dto.scheme.SchoolCampusSchemeDto;
import com.jsunicom.oms.po.WoSchoolCampusSchemaFlow;
import com.jsunicom.oms.service.WoSchoolCampusSchemaFlowService;
import com.jsunicom.oms.service.WoSchoolCampusSchemeBaseService;
import com.jsunicom.oms.service.impl.FileDataTransmitImpl;
import com.jsunicom.oms.utils.CookieUtil;
import com.jsunicom.oms.utils.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


/**
 * 一校一策基础表(wo_school_campus_scheme_base)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/woschool/schemebase")
@Slf4j
public class WoSchoolCampusSchemeBaseController extends AbstractSimpleController {


    @Value("${common.object.cos.woschoolSchemeImgDir:0}")
    private String woschoolSchemeImgDir;
    @Value("${common.object.cos.urlSuffix:0}")
    private String urlSuffix;
    @Autowired
    private WoSchoolCampusSchemeBaseService woSchoolCampusSchemeBaseService;
    @Autowired
    private PrivateCosClientUtil privateCosClientUtil;
    @Autowired
    private WoSchoolCampusSchemaFlowService woSchoolCampusSchemaFlowService;

    @Resource
    private FileDataTransmitImpl fileDataTransmit;

    private static final List<String> FILETYPELIST = Arrays.asList("jpg", "png", "jpeg", "JPG", "PNG", "JPEG");

    /**
     * 添加一校一策基础表
     * @param schemeDto
     * @param request
     * @return
     */
    @PostMapping(value = "add", name = "添加一校一策基础表")
    public CustomResult add(@RequestBody SchoolCampusSchemeDto schemeDto, HttpServletRequest request) {
        String serialNumber = request.getHeader("serialNumber");
        log.info("添加一校一策基础表，入参schemeDto={},serialNumber={}", JSONObject.toJSONString(schemeDto), serialNumber);
        schemeDto.setSerialNumber(serialNumber);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.insert(schemeDto));
    }

    /**
     * 修改一校一策基础表
     * @param schemeDto
     * @param request
     * @return
     */
    @PostMapping(value = "modify", name = "修改一校一策基础表")
    public CustomResult modify(@RequestBody SchoolCampusSchemeDto schemeDto, HttpServletRequest request) {
        String serialNumber = request.getHeader("serialNumber");
        log.info("修改一校一策基础表，入参schemeDto={},serialNumber={}", JSONObject.toJSONString(schemeDto), serialNumber);
        schemeDto.setSerialNumber(serialNumber);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.updateByPrimaryKey(schemeDto));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping(value = "queryOne", name = "查询详情一校一策基础表")
    public CustomResult queryOne(Long id) {
        log.info("查询详情一校一策基础表，入参id={}", id);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.selectByPrimaryKey(id));
    }

    /**
     * 分页查询
     * @return
     */
    @GetMapping(value = "queryPage", name = "分页查询一校一策基础表")
    public CustomResult queryPage(String campusId, String orgCode, Integer pageNo, Integer pageSize) {
        log.info("分页查询一校一策基础表，入参campusId={},orgCode={},pageNo={},pageSize={}", campusId, orgCode, pageNo, pageSize);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.selectPage(campusId, orgCode, pageNo, pageSize));
    }

    /**
     * 删除一校一策基础表
     * @param id
     * @return
     */
    @GetMapping(value = "remove", name = "删除一校一策基础表")
    public CustomResult remove(Long id) {
        log.info("删除一校一策基础表，入参id={}", id);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.deleteByPrimaryKey(id));
    }

    /**
     * 根据校区获取未被绑定策略的团队
     */
    @GetMapping(value = "queryUnbindTeam", name = "根据校区获取未被绑定策略的团队")
    public CustomResult queryUnbindTeam(String campusId, String schemeYear, String marketingType,Integer pageNo, Integer pageSize) {
        log.info("根据校区获取未被绑定策略的团队，入参campusId={},schemeYear={},marketingType={},pageNo={},pageSize={}", campusId, schemeYear, marketingType, pageNo, pageSize);
        return ResultUtil.success(woSchoolCampusSchemeBaseService.queryUnbindTeam(campusId, schemeYear, marketingType, pageNo, pageSize));
    }

    @ResponseBody
    @RequestMapping(value = "/uploadPrivateImg", method = RequestMethod.POST, name = "图片上传")
    public CustomResult uploadPrivateImg(HttpServletRequest request, MultipartFile[] uploadfiles, String campusId, @RequestParam(required = false) String waterMarkDesc) {
        HashMap<String, Object> result = new HashMap<>();
        InputStream inputStream = null;
        File file = null;
        if (uploadfiles.length>0) {
            try {
                StringBuffer sb = new StringBuffer();
                for (MultipartFile uploadfile:uploadfiles){
                    String originalFilename = uploadfile.getOriginalFilename();
                    String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                    String fileName = System.currentTimeMillis() + "." + suffix;
                    if (!FILETYPELIST.contains(suffix)){
                        log.error("uploadFile2AliyunError-fileName:{}", fileName);
                        return ResultUtil.error("9999","不支持该类型文件上传！");
                    }
                    String URL= woschoolSchemeImgDir;
                    String accessPath = urlSuffix + URL;
                    // 创建临时文件
                    file = File.createTempFile("temp", null);
                    // 把multipartFile写入临时文件
                    uploadfile.transferTo(file);
                    // 使用文件创建 inputStream 流
                    inputStream = new FileInputStream(file);

                    //加水印
                    if (waterMarkDesc!=null && !waterMarkDesc.isEmpty()) {
                        InputStream waterInputStream;
                        SimpleDateFormat newDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String dateStr = newDateFormat.format(new Date());
                        String waterMarkContent = waterMarkDesc+" "+ dateStr;
                        waterInputStream = ImageWatermarkUtil.markWithContent(inputStream,waterMarkContent);
                        privateCosClientUtil.uploadFileCOS(waterInputStream,fileName,URL);
                    }else{
                        privateCosClientUtil.uploadFileCOS(inputStream,fileName,URL);
                    }
                    log.info("dir->{},上传文件成功！", URL);
                    if (!accessPath.isEmpty()){
                        sb.append(accessPath).append(fileName).append("|");
                    }
                }
                result.put("imgUrl",sb.deleteCharAt(sb.length() - 1));
            } catch (Exception e) {
                log.error("上传图片异常", e);
                return ResultUtil.error("9999",e.getMessage());
            }
        }
        return ResultUtil.success(result);
    }


    @PostMapping(value = "processApprove", name = "一校一策流程审批")
    public CustomResult processApprove(@RequestBody SchemeProcessApprove processApprove, HttpServletRequest request) {
        String serialNumber = request.getHeader("serialNumber");
        log.info("添加一校一策基础表，入参schemeDto={},serialNumber={}", JSONObject.toJSONString(processApprove), serialNumber);
        JSONObject jsonObject = woSchoolCampusSchemeBaseService.processApprove(processApprove, serialNumber);
        if (jsonObject == null) {
            return ResultUtil.error("9999", "审批失败");
        } else {
//            return ResultUtil.successByJSONObject(woSchoolCampusSchemeBaseService.processApprove(processApprove, serialNumber));
            return ResultUtil.successByJSONObject(jsonObject);
        }
    }


    @PostMapping(value = "getApprovalProgressDetail",name = "查看一校一策流程审批进度")
    public CustomResult getApprovalProgress (@RequestBody CheckSchemeProcessApproveVo checkSchemeProcessApproveVo,HttpServletRequest request){
        String serialNumber = request.getHeader("serialNumber");
        log.info("查看一校一策流程审批进度参数:{}", JSONObject.toJSONString(checkSchemeProcessApproveVo));
        //暂时写死
        checkSchemeProcessApproveVo.setProcessInstanceId("6637ba12-f9a6-11ef-8a1f-e63cae698d52");
        JSONObject jsonObject  =  woSchoolCampusSchemeBaseService.getApprovalProgressDetail(checkSchemeProcessApproveVo);
        if (jsonObject == null) {
            return ResultUtil.error("9999", "暂无数据");
        } else {
            return ResultUtil.successByJSONObject(jsonObject);
        }
    }


    @PostMapping(value = "insertselectRowsByChemaFlow",name = "插入审批流程数据")
    public CustomResult insertselectRowsByChemaFlow (@RequestBody WoSchoolCampusSchemaFlow woSchoolCampusSchemaFlow , HttpServletRequest request){
        String serialNumber = request.getHeader("serialNumber");
        log.info("插入审批流程数据:{}", JSONObject.toJSONString(woSchoolCampusSchemaFlow));
        int insertRow  =  woSchoolCampusSchemaFlowService.insertselectRowsByChemaFlow(woSchoolCampusSchemaFlow);
        if (insertRow > 0) {
            return ResultUtil.success("0000","新增成功");
        } else {
            return ResultUtil.error("9999", "暂无数据");
        }
    }

    //上传Pdf文件
    @PostMapping(value = "/uploadPdf" ,name = "PDF文件上传")
    public CustomResult uploadPdf(@RequestParam("file") MultipartFile file) {

        // 检查文件是否为空
        log.info("上传PDf文件参数:{}", file.toString());
        if (file.isEmpty()) {
            return ResultUtil.error("9999", "文件不能为空");
        }
        // 检查文件类型是否为PDF
        String contentType = file.getContentType();
        if (!contentType.equals("application/pdf")) {
            return ResultUtil.error("9999", "文件类型必须是PDF");
        }
        JSONObject jsonObject = woSchoolCampusSchemeBaseService.uploadPdf(file);

        if(jsonObject == null){
            return ResultUtil.error("9999", "上传文件失败");
        }else{
            return ResultUtil.successByJSONObject(jsonObject);
        }
    }

    //测试上传文件到sfpt
    @PostMapping(value = "/transer" ,name = "测试上传文件到sfpt")
    public void transer() {
        fileDataTransmit.uploadTspXjValidDivision();
    }

    //测试上传文件到oss
    @PostMapping(value = "/testOssUpload" ,name = "测试上传文件到oss")
    public void testOss (@RequestParam("file") MultipartFile file) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("temp", null);
            // 将 MultipartFile 内容传输到临时文件
            file.transferTo(tempFile);
            FileInputStream inputStream = new FileInputStream(tempFile);
            new PrivateOssClientUtil().uploadFileCOS(inputStream,file.getOriginalFilename(),"sxxy/text/");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    //insertdimPubScbSchoolUser
    //测试上传文件到sfpt
    @PostMapping(value = "/testInsertdimPubScbSchoolUser" ,name = "测试拉取国信回传的围栏数据")
    public void testInsertdimPubScbSchoolUser() {
        fileDataTransmit.insertdimPubScbSchoolUser();
    }
}
