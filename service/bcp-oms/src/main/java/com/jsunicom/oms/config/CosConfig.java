package com.jsunicom.oms.config;

import com.alibaba.druid.filter.config.ConfigTools;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024-07-02-17:18
 */
@Configuration
public class CosConfig {
    @Value("${common.object.cos.private.serviceEndpoint:0}")
    private String serviceEndpoint;
    @Value("${common.object.cos.private.accessKey:0}")
    private String accessKey;
    @Value("${common.object.cos.private.secretPrivateKey:0}")
    private String secretPrivateKey;
    @Value("${common.object.cos.private.secretPublicKey:0}")
    private String secretPublicKey;
    @Value("${common.object.cos.private.region:0}")
    private String region;

    @Bean(name="conn")
    public AmazonS3 conn(){
        AmazonS3 conn = null;
        try {
            AWSCredentials credentials = new BasicAWSCredentials(accessKey, ConfigTools.decrypt(secretPublicKey,secretPrivateKey));
            AWSStaticCredentialsProvider awsStaticCredentialsProvider = new AWSStaticCredentialsProvider(credentials);
            ClientConfiguration config = new ClientConfiguration();
            EndpointConfiguration endpointConfiguration = new EndpointConfiguration(serviceEndpoint, region);
            conn = AmazonS3ClientBuilder.standard()
                    .withCredentials(awsStaticCredentialsProvider)
                    .withClientConfiguration(config.withProtocol(Protocol.HTTP).withSignerOverride("S3SignerType"))
                    .withEndpointConfiguration(endpointConfiguration).build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return conn;
    }

}
