package com.jsunicom.oms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.mapper.DevelopCollectMapper;
import com.jsunicom.oms.service.DevelopCollectService;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024-01-31-15:33
 */
@Service
@Slf4j
public class DevelopCollectServiceImpl implements DevelopCollectService {
    @Autowired
    private DevelopCollectMapper developCollectMapper;

    @Override
    public HashMap boardForProvince(String orgCode, String startTime, String endTime) {
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectBoardForProvince(orgCode,startTime,endTime);
        result.put("detail",list);
        List<HashMap> listTotal=developCollectMapper.selectBoardForProvinceTotal(orgCode,startTime,endTime);
        result.put("totalInfo",listTotal);
        return result;
    }

    @Override
    public HashMap boardForCity(String orgCode, String campusName, String phone, String startTime, String endTime,String pageNum,String size) {
        if (StringUtil.isEmpty(pageNum)){
            pageNum="1";
        }
        if (StringUtil.isEmpty(size)){
            size="10";
        }
        PageHelper.startPage(Integer.parseInt(pageNum),Integer.parseInt(size));
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectBoardForCity(orgCode,campusName,phone,startTime,endTime);
        PageInfo pageInfo=new PageInfo(list);
        result.put("detail",pageInfo.getList());
        result.put("total",pageInfo.getTotal());
        List<HashMap> listTotal=developCollectMapper.selectBoardForCityTotal(orgCode,campusName,phone,startTime,endTime);
        result.put("totalInfo",listTotal);
        return result;
    }

    @Override
    public HashMap boardForPartner(String startTime, String endTime, String orgCode, String campusName, String societyName, String phone,String pageNum,String size) {
        if (StringUtil.isEmpty(pageNum)){
            pageNum="1";
        }
        if (StringUtil.isEmpty(size)){
            size="10";
        }
        PageHelper.startPage(Integer.parseInt(pageNum),Integer.parseInt(size));
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectBoardForPartner(orgCode,campusName,phone,startTime,endTime,societyName);
        PageInfo pageInfo=new PageInfo(list);
        result.put("detail",pageInfo.getList());
        result.put("total",pageInfo.getTotal());
        List<HashMap> listTotal=developCollectMapper.selectBoardForPartnerTotal(orgCode,campusName,phone,startTime,endTime,societyName);
        result.put("totalInfo",listTotal);
        return result;
    }

    @Override
    public HashMap channelForProvince(String orgCode, String startTime, String endTime) {
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectChannelForProvince(orgCode,startTime,endTime);
        result.put("detail",list);
        List<HashMap> listTotal=developCollectMapper.selectChannelForProvinceTotal(orgCode,startTime,endTime);
        result.put("totalInfo",listTotal);
        return result;
    }

    @Override
    public HashMap channelForCity(String orgCode, String campusName, String phone, String startTime, String endTime, String pageNum, String size) {
        if (StringUtil.isEmpty(pageNum)){
            pageNum="1";
        }
        if (StringUtil.isEmpty(size)){
            size="10";
        }
        PageHelper.startPage(Integer.parseInt(pageNum),Integer.parseInt(size));
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectChannelForCity(orgCode,campusName,phone,startTime,endTime);
        PageInfo pageInfo=new PageInfo(list);
        result.put("detail",pageInfo.getList());
        result.put("total",pageInfo.getTotal());
        List<HashMap> listTotal=developCollectMapper.selectChannelForCityTotal(orgCode,campusName,phone,startTime,endTime);
        result.put("totalInfo",listTotal);
        return result;
    }

    @Override
    public HashMap channelForPartner(String startTime, String endTime, String orgCode, String campusName, String channelName, String phone, String pageNum, String size) {
        if (StringUtil.isEmpty(pageNum)){
            pageNum="1";
        }
        if (StringUtil.isEmpty(size)){
            size="10";
        }
        PageHelper.startPage(Integer.parseInt(pageNum),Integer.parseInt(size));
        HashMap result=new HashMap();
        List<HashMap> list=developCollectMapper.selectChannelForPartner(orgCode,campusName,phone,startTime,endTime,channelName);
        PageInfo pageInfo=new PageInfo(list);
        result.put("detail",pageInfo.getList());
        result.put("total",pageInfo.getTotal());
        List<HashMap> listTotal=developCollectMapper.selectChannelForPartnerTotal(orgCode,campusName,phone,startTime,endTime,channelName);
        result.put("totalInfo",listTotal);
        return result;
    }
}
