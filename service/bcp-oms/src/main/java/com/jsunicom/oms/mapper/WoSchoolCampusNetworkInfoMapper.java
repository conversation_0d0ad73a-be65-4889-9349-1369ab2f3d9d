package com.jsunicom.oms.mapper;

import com.jsunicom.oms.po.WoSchoolCampusNetworkInfo;
import com.jsunicom.oms.po.WoSchoolCampusNetworkInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WoSchoolCampusNetworkInfoMapper {
    long countByExample(WoSchoolCampusNetworkInfoExample example);

    int deleteByExample(WoSchoolCampusNetworkInfoExample example);

    int deleteByPrimaryKey(Long campusId);

    int insert(WoSchoolCampusNetworkInfo record);

    int insertSelective(WoSchoolCampusNetworkInfo record);

    List<WoSchoolCampusNetworkInfo> selectByExample(WoSchoolCampusNetworkInfoExample example);

    WoSchoolCampusNetworkInfo selectByPrimaryKey(Long campusId);

    int updateByExampleSelective(@Param("record") WoSchoolCampusNetworkInfo record, @Param("example") WoSchoolCampusNetworkInfoExample example);

    int updateByExample(@Param("record") WoSchoolCampusNetworkInfo record, @Param("example") WoSchoolCampusNetworkInfoExample example);

    int updateByPrimaryKeySelective(WoSchoolCampusNetworkInfo record);

    int updateByPrimaryKey(WoSchoolCampusNetworkInfo record);
}