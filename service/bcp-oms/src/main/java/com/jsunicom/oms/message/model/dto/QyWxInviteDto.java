package com.jsunicom.oms.message.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

/**
 * Created by went<PERSON> on 2018/3/5.
 **/
public class QyWxInviteDto extends QyWxBaseDto {
    //需要邀请的成员ID列表, 最多支持1000个。
    private List<String> users;
    //需要邀请的部门ID列表，最多支持100个。
    private List<String> parties;
    //需要邀请的标签ID列表，最多支持100个
    private List<String> tags;

    public List<String> getUsers() {
        return users;
    }

    public void setUsers(List<String> users) {
        this.users = users;
    }

    public List<String> getParties() {
        return parties;
    }

    public void setParties(List<String> parties) {
        this.parties = parties;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("users", users)
                .append("parties", parties)
                .append("tags", tags)
                .toString();
    }
}
