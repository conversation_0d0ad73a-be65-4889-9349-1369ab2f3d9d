package com.jsunicom.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.dto.user.UserInfoDto;
import com.jsunicom.oms.entity.SchoolListInfoDto;
import com.jsunicom.oms.entity.WoSchoolMsgconfExt;
import com.jsunicom.oms.model.woshcool.SchoolInfo;
import com.jsunicom.oms.model.woshcool.WoSchoolMsgBatchConfDto;
import com.jsunicom.oms.model.woshcool.WoSchoolMsgNumConfDto;
import com.jsunicom.oms.po.WoSchoolMsgconf;
import com.jsunicom.oms.po.WoSchoolMsgsendnumConf;
import com.lz.lsf.service.query.PagedResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-23-10:53
 */
public interface WoSchoolService {
    PageInfo<WoSchoolMsgconfExt> findSchoolMsgInfoByPage(WoSchoolMsgconfExt woSchoolMsgConf, Integer pageNo, Integer pageSize);

    WoSchoolMsgconf querySchoolMsgInfo(WoSchoolMsgconf msgConf);

    int updateWoSchoolMsgConf(WoSchoolMsgconfExt woSchoolMsgConf);

    PageInfo<WoSchoolMsgsendnumConf> findWoSchoolMsgSendNumByPage(WoSchoolMsgsendnumConf woSchoolMsgNumConf, Integer pageNo, Integer pageSize);

    PageInfo<SchoolListInfoDto> findByPageWithManagerNew(String schoolName, String schoolManagerMbl, int pageNum, int pageSize, String userOrgCode, String orgCode, Long schoolId);

    SchoolInfo getSchoolById(Long schoolId);

    int updateSchoolInfo(SchoolInfo schoolInfo, Long userPid);

    JSONObject querySchoolMember(String managerPhone, String orgCode);

    CustomResult updateSchoolManager(Long schoolManagerId, Long schoolId, String staffNo);

    PageInfo<Map<String, Object>> querySchoolAppletBySchoolNew(Map<String, Object> params, int pageNum, int pageSize);

    List<Map<String, Object>> querySchoolAppletBySchoolGoodDetail(Map<String, Object> params);

    JSONObject updateSchoolManagerByCampus(Long schoolManagerId, Long parseLong, Long campusId);

    List<WoSchoolMsgNumConfDto> findWoSchoolMsgSendNumDetail(WoSchoolMsgNumConfDto woSchoolMsgNumConfDto);

    int saveBatchSchoolCheckNum(String schoolId, List<String> numList, Long partnerId);

    boolean isSchoolManager(String mblNbr);

    int deleteWoSchoolMsgConf(WoSchoolMsgconf woschoolMsgConf);

    void saveMsgTemplateNumNew(Long templateId, List<String> list, UserInfoDto userInfo);

    int saveWoSchoolMsgConfNew(WoSchoolMsgconf woschoolMsgConf,UserInfoDto userInfoDto);

    PageInfo<WoSchoolMsgBatchConfDto> queryBatch(WoSchoolMsgconf woSchoolMsgConf, int i, int pageSize);

    PageInfo<HashMap> queryBatchPhone(String batchId, int i, int pageSize);

    HashMap approvalBatch(String id,String batchId, String state, String remark,UserInfoDto userInfo);

    void sendMsgByTime();

    void test(String msg,String phone);
}
