package com.jsunicom.oms.common.generator;


import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhaowang
 * @ClassName DataSource
 * @Description TODO 多数据源注解
 * @date : 2021-06-30 17:12
 * @Version 1.0
 **/
public class GeneratorUtil {

	public void generator() throws Exception {
		List<String> warnings = new ArrayList<String>();
		boolean overwrite = true;
		// 指定 逆向工程配置文件
		File configFile = new File("E:\\sxxy\\sx_school_backend\\service\\bcp-oms\\src\\main\\resources\\generator\\generatorConfig.xml");
		ConfigurationParser cp = new ConfigurationParser(warnings);
		Configuration config = cp.parseConfiguration(configFile);
		DefaultShellCallback callback = new DefaultShellCallback(overwrite);
		MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config,
				callback, warnings);
		myBatisGenerator.generate(null);

	}

	public static void main(String[] args) throws Exception {
		try {
			GeneratorUtil generatorSqlmap = new GeneratorUtil();
			generatorSqlmap.generator();
			System.out.println("成功!");
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

}
