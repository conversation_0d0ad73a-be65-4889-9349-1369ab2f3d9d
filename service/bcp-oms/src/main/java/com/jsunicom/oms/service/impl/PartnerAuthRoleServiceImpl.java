package com.jsunicom.oms.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jsunicom.oms.common.constant.RedisKeys;
import com.jsunicom.oms.mapper.partner.PartnerAuthRoleDao;
import com.jsunicom.oms.mapper.partner.PartnerRoleDao;
import com.jsunicom.oms.model.partner.PartnerAuthRole;
import com.jsunicom.oms.model.partner.PartnerRole;
import com.jsunicom.oms.service.DataMonitorFacade;
import com.jsunicom.oms.service.PartnerAuthRoleFacade;
import com.jsunicom.oms.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service.impl
 * @ClassName: PartnerAuthRoleServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-03-18  21:24
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class PartnerAuthRoleServiceImpl implements PartnerAuthRoleFacade {
    @Autowired
    private RedisUtil redisUtils;

    @Autowired
    private PartnerAuthRoleDao partnerAuthRoleDao;
    @Autowired
    private PartnerRoleDao partnerRoleDao;
    @Autowired
    private DataMonitorFacade dataMonitorFacade;

    @Override
    public ArrayList<PartnerAuthRole> findAll() {
        HashMap<String, Object> paramMap = new HashMap<>();
        @SuppressWarnings("unchecked")
        ArrayList<PartnerAuthRole> partnerAuthRoles = partnerAuthRoleDao.find(paramMap);
        return partnerAuthRoles;
    }

    @Override
    public void addPartnerRole(Long partnerId, Long roleId, String createBy) {
        PartnerRole flag = partnerRoleDao.findUnique(partnerId, roleId);
        //当某用户没有该角色时，才添加
        if (null == flag) {
            PartnerRole pRole = new PartnerRole();
            pRole.setPartnerId(partnerId);
            pRole.setRoleId(roleId);
            pRole.setCreateBy(createBy);
            pRole.setCreateTime(new Date());
            pRole.setUpdateBy(createBy);
            pRole.setUpdateTime(new Date());
            partnerRoleDao.save(pRole);
            List<PartnerRole> res = partnerRoleDao.findByPtnerId(partnerId);
//            redisSrv.hset(RedisKeys.ROLE_PARTNERS, "" + partnerId, JSONArray.fromObject(res).toString());
            redisUtils.hset(RedisKeys.ROLE_PARTNERS, "" + partnerId, JSONArray.parseArray(JSON.toJSONString(res)));

            dataMonitorFacade.save("auth_role_partner", "add", "" + pRole.getId(), createBy, "用户增加角色合伙人数据");
        }
    }
}
