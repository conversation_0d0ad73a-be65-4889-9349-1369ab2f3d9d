package com.jsunicom.oms.common.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * .
 *
 * @ClassName: CampusQuery
 * @Auther: L<PERSON><PERSON>
 * @Date: 2022/3/23 16:49
 * @Version: bcpV1.0
 * @Description: query
 **/

@Getter
@Setter
public class CampusQuery{
  private static final long serialVersionUID = 1L;

  private String orgCode;
  private String saleManagerPhone;
  private String campusId;
  private String campusName;
  private List<String> orgCodes;
  private String userOrgCode;
  private String bindId;
  private String type;
  private int pageNumber;
  private int pageSize;
}
