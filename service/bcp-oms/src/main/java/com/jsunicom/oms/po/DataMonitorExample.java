package com.jsunicom.oms.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DataMonitorExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DataMonitorExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesIsNull() {
            addCriterion("monitor_modules is null");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesIsNotNull() {
            addCriterion("monitor_modules is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesEqualTo(String value) {
            addCriterion("monitor_modules =", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesNotEqualTo(String value) {
            addCriterion("monitor_modules <>", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesGreaterThan(String value) {
            addCriterion("monitor_modules >", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesGreaterThanOrEqualTo(String value) {
            addCriterion("monitor_modules >=", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesLessThan(String value) {
            addCriterion("monitor_modules <", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesLessThanOrEqualTo(String value) {
            addCriterion("monitor_modules <=", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesLike(String value) {
            addCriterion("monitor_modules like", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesNotLike(String value) {
            addCriterion("monitor_modules not like", value, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesIn(List<String> values) {
            addCriterion("monitor_modules in", values, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesNotIn(List<String> values) {
            addCriterion("monitor_modules not in", values, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesBetween(String value1, String value2) {
            addCriterion("monitor_modules between", value1, value2, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorModulesNotBetween(String value1, String value2) {
            addCriterion("monitor_modules not between", value1, value2, "monitorModules");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNull() {
            addCriterion("monitor_type is null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNotNull() {
            addCriterion("monitor_type is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeEqualTo(String value) {
            addCriterion("monitor_type =", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotEqualTo(String value) {
            addCriterion("monitor_type <>", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThan(String value) {
            addCriterion("monitor_type >", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThanOrEqualTo(String value) {
            addCriterion("monitor_type >=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThan(String value) {
            addCriterion("monitor_type <", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThanOrEqualTo(String value) {
            addCriterion("monitor_type <=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLike(String value) {
            addCriterion("monitor_type like", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotLike(String value) {
            addCriterion("monitor_type not like", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIn(List<String> values) {
            addCriterion("monitor_type in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotIn(List<String> values) {
            addCriterion("monitor_type not in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeBetween(String value1, String value2) {
            addCriterion("monitor_type between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotBetween(String value1, String value2) {
            addCriterion("monitor_type not between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNull() {
            addCriterion("data_id is null");
            return (Criteria) this;
        }

        public Criteria andDataIdIsNotNull() {
            addCriterion("data_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataIdEqualTo(String value) {
            addCriterion("data_id =", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotEqualTo(String value) {
            addCriterion("data_id <>", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThan(String value) {
            addCriterion("data_id >", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdGreaterThanOrEqualTo(String value) {
            addCriterion("data_id >=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThan(String value) {
            addCriterion("data_id <", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLessThanOrEqualTo(String value) {
            addCriterion("data_id <=", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdLike(String value) {
            addCriterion("data_id like", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotLike(String value) {
            addCriterion("data_id not like", value, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdIn(List<String> values) {
            addCriterion("data_id in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotIn(List<String> values) {
            addCriterion("data_id not in", values, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdBetween(String value1, String value2) {
            addCriterion("data_id between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andDataIdNotBetween(String value1, String value2) {
            addCriterion("data_id not between", value1, value2, "dataId");
            return (Criteria) this;
        }

        public Criteria andOperateByIsNull() {
            addCriterion("operate_by is null");
            return (Criteria) this;
        }

        public Criteria andOperateByIsNotNull() {
            addCriterion("operate_by is not null");
            return (Criteria) this;
        }

        public Criteria andOperateByEqualTo(String value) {
            addCriterion("operate_by =", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByNotEqualTo(String value) {
            addCriterion("operate_by <>", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByGreaterThan(String value) {
            addCriterion("operate_by >", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByGreaterThanOrEqualTo(String value) {
            addCriterion("operate_by >=", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByLessThan(String value) {
            addCriterion("operate_by <", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByLessThanOrEqualTo(String value) {
            addCriterion("operate_by <=", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByLike(String value) {
            addCriterion("operate_by like", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByNotLike(String value) {
            addCriterion("operate_by not like", value, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByIn(List<String> values) {
            addCriterion("operate_by in", values, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByNotIn(List<String> values) {
            addCriterion("operate_by not in", values, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByBetween(String value1, String value2) {
            addCriterion("operate_by between", value1, value2, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateByNotBetween(String value1, String value2) {
            addCriterion("operate_by not between", value1, value2, "operateBy");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNull() {
            addCriterion("operate_time is null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNotNull() {
            addCriterion("operate_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeEqualTo(Date value) {
            addCriterion("operate_time =", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotEqualTo(Date value) {
            addCriterion("operate_time <>", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThan(Date value) {
            addCriterion("operate_time >", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("operate_time >=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThan(Date value) {
            addCriterion("operate_time <", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThanOrEqualTo(Date value) {
            addCriterion("operate_time <=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIn(List<Date> values) {
            addCriterion("operate_time in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotIn(List<Date> values) {
            addCriterion("operate_time not in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeBetween(Date value1, Date value2) {
            addCriterion("operate_time between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotBetween(Date value1, Date value2) {
            addCriterion("operate_time not between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateDescIsNull() {
            addCriterion("operate_desc is null");
            return (Criteria) this;
        }

        public Criteria andOperateDescIsNotNull() {
            addCriterion("operate_desc is not null");
            return (Criteria) this;
        }

        public Criteria andOperateDescEqualTo(String value) {
            addCriterion("operate_desc =", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescNotEqualTo(String value) {
            addCriterion("operate_desc <>", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescGreaterThan(String value) {
            addCriterion("operate_desc >", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescGreaterThanOrEqualTo(String value) {
            addCriterion("operate_desc >=", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescLessThan(String value) {
            addCriterion("operate_desc <", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescLessThanOrEqualTo(String value) {
            addCriterion("operate_desc <=", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescLike(String value) {
            addCriterion("operate_desc like", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescNotLike(String value) {
            addCriterion("operate_desc not like", value, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescIn(List<String> values) {
            addCriterion("operate_desc in", values, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescNotIn(List<String> values) {
            addCriterion("operate_desc not in", values, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescBetween(String value1, String value2) {
            addCriterion("operate_desc between", value1, value2, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andOperateDescNotBetween(String value1, String value2) {
            addCriterion("operate_desc not between", value1, value2, "operateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescIsNull() {
            addCriterion("previous_operate_desc is null");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescIsNotNull() {
            addCriterion("previous_operate_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescEqualTo(String value) {
            addCriterion("previous_operate_desc =", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescNotEqualTo(String value) {
            addCriterion("previous_operate_desc <>", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescGreaterThan(String value) {
            addCriterion("previous_operate_desc >", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescGreaterThanOrEqualTo(String value) {
            addCriterion("previous_operate_desc >=", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescLessThan(String value) {
            addCriterion("previous_operate_desc <", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescLessThanOrEqualTo(String value) {
            addCriterion("previous_operate_desc <=", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescLike(String value) {
            addCriterion("previous_operate_desc like", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescNotLike(String value) {
            addCriterion("previous_operate_desc not like", value, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescIn(List<String> values) {
            addCriterion("previous_operate_desc in", values, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescNotIn(List<String> values) {
            addCriterion("previous_operate_desc not in", values, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescBetween(String value1, String value2) {
            addCriterion("previous_operate_desc between", value1, value2, "previousOperateDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousOperateDescNotBetween(String value1, String value2) {
            addCriterion("previous_operate_desc not between", value1, value2, "previousOperateDesc");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}