package com.jsunicom.oms.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * .
 *
 * @ClassName: CampusExcel
 * @Auther: LJ.Huang
 * @Date: 2022/3/24 16:01
 * @Version: bcpV1.0
 * @Description: CampusExcel
 **/
@Getter
@Setter
public class CampusExcel extends BaseRowModel implements Serializable {

  private static final long serialVersionUID = 2672917869360009062L;

  //@ExcelProperty(index = 0)
  //private Long campusId;   //null
  @ExcelProperty(index = 0)
  private String schoolName;   //null
  @ExcelProperty(index = 1)
  private String campusName;   //null
  @ExcelProperty(index = 2)
  private String orgName;   //null

  @ExcelProperty(index = 3)
  private String tel;   //null
  @ExcelProperty(index = 4)
  private String postCode;   //null
  @ExcelProperty(index = 5)
  private String address;   //null
  @ExcelProperty(index = 6)
  private String remark;   //null

  @ExcelProperty(index = 7)
  private String orgCode;

  @Override
  public boolean equals(Object object){
    if (object instanceof CampusExcel) {
      CampusExcel o = (CampusExcel) object;
      return StringUtils.equals(this.schoolName,o.schoolName) &&
          StringUtils.equals(this.campusName,o.campusName);
    }
    else{
      return false;
    }

  }

}
