package com.jsunicom.oms.service.impl;

import com.jsunicom.oms.mapper.WoSchoolCampusMarketingInfoMapper;
import com.jsunicom.oms.mapper.WoSchoolCampusNetworkInfoMapper;
import com.jsunicom.oms.po.WoSchoolCampusMarketingInfo;
import com.jsunicom.oms.po.WoSchoolCampusMarketingInfoExample;
import com.jsunicom.oms.po.WoSchoolCampusNetworkInfo;
import com.jsunicom.oms.po.WoSchoolCampusNetworkInfoExample;
import com.jsunicom.oms.service.CampusMarketingInfoService;
import com.jsunicom.oms.service.CampusNetworkInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Project:CampusMarketingInfoServiceImpl
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
@Slf4j
@Service
public class CampusNetworkInfoServiceImpl implements CampusNetworkInfoService {

    @Resource
    private WoSchoolCampusNetworkInfoMapper woSchoolCampusNetworkInfoMapper;

    @Override
    public List<WoSchoolCampusNetworkInfo> getCollegeList(WoSchoolCampusNetworkInfo record) {
        WoSchoolCampusNetworkInfoExample example = new WoSchoolCampusNetworkInfoExample();
        WoSchoolCampusNetworkInfoExample.Criteria criteria = example.createCriteria();

        if(record.getCampusId() != null){
            criteria.andCampusIdEqualTo(record.getCampusId());
        }


        List<WoSchoolCampusNetworkInfo> collegeList = woSchoolCampusNetworkInfoMapper.selectByExample(example);
        return collegeList;
    }

    @Override
    public int saveCollegeExtendInfo(WoSchoolCampusNetworkInfo record){
        return woSchoolCampusNetworkInfoMapper.insertSelective(record);
    }

    public int updateCollegeInfo(WoSchoolCampusNetworkInfo record)
    {
        return woSchoolCampusNetworkInfoMapper.updateByPrimaryKeySelective(record);
    }
    public int deleteCollegeInfo(Long collegeId){
        return woSchoolCampusNetworkInfoMapper.deleteByPrimaryKey(collegeId);
    }
}
