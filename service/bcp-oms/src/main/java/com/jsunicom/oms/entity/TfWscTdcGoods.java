package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/11 10:51
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TfWscTdcGoods implements Serializable {
    private static final long serialVersionUID = 1L;
    private String goodsId;
    private String goodsName;
    private String goodsDesc;
    private String goodsType;
    private BigDecimal goodsPrice;
    private String goodsState;
    private Date createDate;
    private Date updateDate;
    private String dependency;
    private String goodsIdExt;

    public static final String GOODS_ID = "GOODS_ID";
    public static final String GOODS_NAME = "GOODS_NAME";
    public static final String GOODS_DESC = "GOODS_DESC";
    public static final String GOODS_TYPE = "GOODS_TYPE";
    public static final String GOODS_PRICE = "GOODS_PRICE";
    public static final String GOODS_STATE = "GOODS_STATE";
    public static final String CREATE_DATE = "CREATE_DATE";
    public static final String UPDATE_DATE = "UPDATE_DATE";
    public static final String DEPENDENCY = "DEPENDENCY";
    public static final String GOODS_ID_EXT = "GOODS_ID_EXT";

}
