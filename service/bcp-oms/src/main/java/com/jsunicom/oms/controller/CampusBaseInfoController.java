package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.entity.WoSchoolCampusResLevelSevenAddr;
import com.jsunicom.oms.po.CampusBaseResult;
import com.jsunicom.oms.po.CampusInfoExtend;
import com.jsunicom.oms.service.CampusBaseInfoService;
import com.jsunicom.oms.service.DictFacade;
import com.jsunicom.oms.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequestMapping(value = "/campusInfo")
public class CampusBaseInfoController {

    @Resource
    private DictFacade dictFacade;

    @Resource
    private CampusBaseInfoService campusBaseInfoService;



    @RequestMapping(value = "/getAllCampusInfo", method = RequestMethod.POST, name = "查询校区/客情/师生信息")
    public CustomResult getAllCampusInfo(@RequestBody HashMap hashMap){

        log.info("进入getAllCampusInfo方法："+JSONObject.toJSONString(hashMap));
        if(!hashMap.containsKey("campusId")){

            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }

        Long campusId = Long.parseLong(hashMap.get("campusId").toString());
        CampusBaseResult selectBaseInfo = campusBaseInfoService.selectBaseInfo(campusId);
        if(null == selectBaseInfo){
            return ResultUtil.error(ResultEnum.USER_NOT_EXIST.getrespCode(),"未查询到校区信息！");
        }

        JSONObject object = new JSONObject();
        object.put("campusId", selectBaseInfo.getCampusId());
        object.put("addrId", selectBaseInfo.getAddrId());
        object.put("campusName", selectBaseInfo.getCampusName());
        object.put("biCampusAddress", selectBaseInfo.getBiCampusAddress());
        object.put("biEducationalNature", selectBaseInfo.getBiEducationalNature());
        object.put("supervisoryUnit", selectBaseInfo.getSupervisoryUnit());
        object.put("biEducationalLevel", selectBaseInfo.getBiEducationalLevel());

        String crBaozhuaLeaderName = selectBaseInfo.getCrBaozhuaLeaderName();
        String crBaozhuaLeaderPhone = selectBaseInfo.getCrBaozhuaLeaderPhone();
        // 主导单位
        object.put("crLeadingCompany", selectBaseInfo.getCrLeadingCompany());
        // 竞争类型
        object.put("crCompetitionType", selectBaseInfo.getCrCompetitionType());
        // 校方关键人
        object.put("crKeyPeopleName", selectBaseInfo.getCrKeyPeopleName());

        // 包抓领导信息
        object.put("crBaozhuaLeaderName", crBaozhuaLeaderName);
        object.put("crBaozhuaLeaderPhone", crBaozhuaLeaderPhone);

        String crLeadingDepartment = selectBaseInfo.getCrLeadingDepartment();

        object.put("crLeadingDepartment", crLeadingDepartment);

        object.put("tsTeacherNumber", selectBaseInfo.getTsTeacherNumber());
        object.put("tsStudentNumber", selectBaseInfo.getTsStudentNumber());
        object.put("tsFirstGradeNumber", selectBaseInfo.getTsFirstGradeNumber());
        object.put("tsNewJuStudent", selectBaseInfo.getTsNewJuStudent());

        return ResultUtil.successByJSONObject(object);

    }

    @RequestMapping(value = "/getAllCampusInfoGbk", method = RequestMethod.POST, name = "查询校区/客情/师生信息-中文")
    public CustomResult getAllCampusInfoGbk(@RequestBody HashMap hashMap){

        log.info("进入getAllCampusInfoGbk方法："+JSONObject.toJSONString(hashMap));
        if(!hashMap.containsKey("campusId")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }

        Long campusId = Long.parseLong(hashMap.get("campusId").toString());
        CampusBaseResult selectBaseInfo = campusBaseInfoService.selectBaseInfo(campusId);
        if(null == selectBaseInfo){
            return ResultUtil.error(ResultEnum.USER_NOT_EXIST.getrespCode(),"未查询到校区信息！");
        }

        JSONObject object = new JSONObject();
        object.put("campusId", selectBaseInfo.getCampusId());
        object.put("campusName", selectBaseInfo.getCampusName());
        object.put("biCampusAddress", selectBaseInfo.getBiCampusAddress());
        object.put("biEducationalNature", getCampusInfo(selectBaseInfo.getBiEducationalNature(), "bi_educational_nature"));
        object.put("supervisoryUnit", getCampusInfo(selectBaseInfo.getSupervisoryUnit(), "supervisory_unit"));
        object.put("biEducationalLevel", getCampusInfo(selectBaseInfo.getBiEducationalLevel(), "bi_educational_level"));
        object.put("reserve1", selectBaseInfo.getReserve1());


        String crBaozhuaLeaderName = selectBaseInfo.getCrBaozhuaLeaderName();
        String crBaozhuaLeaderPhone = selectBaseInfo.getCrBaozhuaLeaderPhone();

        // 主导单位
        object.put("crLeadingCompany", getCampusInfo(selectBaseInfo.getCrLeadingCompany(), "cr_leading_company"));
        // 竞争类型
        object.put("crCompetitionType", getCampusInfo(selectBaseInfo.getCrCompetitionType(), "cr_competition_type"));
        // 校方关键人
        object.put("crKeyPeopleName", selectBaseInfo.getCrKeyPeopleName());

        // 包抓领导信息
        String crBaozhuaLeader = "";
        if(StringUtil.isNotEmpty(crBaozhuaLeaderName)){
            crBaozhuaLeader =  crBaozhuaLeaderName;
        }
        if(StringUtil.isNotEmpty(crBaozhuaLeaderPhone)){
            crBaozhuaLeader =  crBaozhuaLeader + "【" + crBaozhuaLeaderPhone + "】";
        }
        object.put("crBaozhuaLeader", crBaozhuaLeader);

        object.put("crBaozhuaLeaderName", crBaozhuaLeaderName);
        object.put("crBaozhuaLeaderPhone", crBaozhuaLeaderPhone);

        StringBuilder strBuilder = new StringBuilder("");
        String crLeadingDepartment = selectBaseInfo.getCrLeadingDepartment();
        if(null != crLeadingDepartment && !"".equals(crLeadingDepartment)){

            String[] deptArr = crLeadingDepartment.split(",");
            for (String dept : deptArr) {
                strBuilder.append(getCampusInfo(dept, "cr_leading_department"));
                strBuilder.append(" ");
            }
        }
        object.put("crLeadingDepartment", strBuilder.toString());

        object.put("tsTeacherNumber", selectBaseInfo.getTsTeacherNumber());
        object.put("tsStudentNumber", selectBaseInfo.getTsStudentNumber());
        object.put("tsFirstGradeNumber", selectBaseInfo.getTsFirstGradeNumber());
        object.put("tsNewJuStudent", selectBaseInfo.getTsNewJuStudent());

        return ResultUtil.successByJSONObject(object);

    }


    @RequestMapping(value = "/updateCampusInfo", method = RequestMethod.POST, name = "更新和保存信息")
    public CustomResult updateCampusInfo(@RequestBody HashMap hashMap){

        log.info("进入updateCampusInfo方法："+JSONObject.toJSONString(hashMap));

        try {
            if(!hashMap.containsKey("campusId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            CampusInfoExtend campusInfoExtend = JSONObject.parseObject(JSONObject.toJSONString(hashMap), CampusInfoExtend.class);

            campusBaseInfoService.saveOrUpdate(campusInfoExtend);

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"保存失败！");
        }

        return ResultUtil.success();
    }


    private String getCampusInfo(String code, String redisKey)
    {
        return dictFacade.getNameByKeyBack(redisKey, code);
    }

    @RequestMapping(value = "/getCampusAddr", method = RequestMethod.POST, name = "查询校区七级地址")
    public CustomResult getCampusAddr(@RequestBody HashMap hashMap) {
        log.info("进入getCampusAddr方法："+JSONObject.toJSONString(hashMap));
        Long campusId = Long.parseLong(hashMap.get("campusId").toString());
        WoSchoolCampusResLevelSevenAddr woSchoolCampusResLevelSevenAddr = campusBaseInfoService.selectCampusAddrByCampusId(campusId);
        if(null == woSchoolCampusResLevelSevenAddr){
            return ResultUtil.error(ResultEnum.USER_NOT_EXIST.getrespCode(),"未查询到校区信息！");
        }
        return ResultUtil.success(woSchoolCampusResLevelSevenAddr);
    }

}
