package com.jsunicom.oms.utils;

import java.util.Random;
import java.util.UUID;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.utils
 * @ClassName: KeyUtil
 * @Author: zhaowang
 * @CreateTime: 2023-07-18  19:09
 * @Description: TODO
 * @Version: 1.0
 */
public class KeyUtil {
    /*
     * @description: 生成唯一的主键 格式: 时间+随机数
     * @author: zhaowang
     * @date: 2023/7/18 下午7:09
     * @param: [str]
     * @return: java.lang.String
     **/
    public static synchronized String getUniqueKey(String str) {
        Random random = new Random();
        Integer number = random.nextInt(900000) + 100000;
        return str + System.currentTimeMillis()+String.valueOf(number);
    }

    /*
     * @description: 生成唯一的主键 格式: 19位
     * @author: z<PERSON>wang
     * @date: 2023/7/18 下午7:09
     * @param: [str]
     * @return: java.lang.String
     **/
    public static synchronized String getUUIDKey(String str) {
        String uuid= System.currentTimeMillis()+ UUID.randomUUID().toString().replaceAll("-","").substring(0,6);
        return str + uuid;
    }
}
