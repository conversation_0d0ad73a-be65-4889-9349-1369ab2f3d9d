package com.jsunicom.oms.service.resource.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.oms.mapper.resource.TSerialIdleMapper;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.mapper.resource.TnSerialIdleMapper;
import com.jsunicom.oms.po.resource.TSerialIdle;
import com.jsunicom.oms.po.resource.TSerialIdleBackup;
import com.jsunicom.oms.po.resource.response.AopResourcesInfoRsp;
import com.jsunicom.oms.po.resource.response.ResourcesInfpRsp;
import com.jsunicom.oms.service.resource.CommonAstoreService;
import com.jsunicom.oms.service.resource.SymSerialStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Service
@Slf4j
public class SymSerialStateServiceImpl implements SymSerialStateService {

    @Autowired
    private TnSerialIdleMapper tnSerialIdleMapper;

    @Autowired
    private TSerialIdleMapper tSerialIdleMapper;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private CommonAstoreService commonService;

    @Override
    public void clearSerialNumber() {
        tnSerialIdleMapper.clearSerialNumber();
    }

    @Override
    public void backupCleanSerial() {
        log.info("---------------backupCleanSerial start----------------");
        //查Idel表，获取90天前05占有状态号码
        List<Map<String, Object>> serialsList = tnSerialIdleMapper.querySerials();

        if (serialsList != null && serialsList.size() > 0) {
            for (Map<String, Object> s : serialsList) {
                TSerialIdleBackup tSerialIdleBackup = new TSerialIdleBackup();
                String serialNo = s.get("SERIAL_NO").toString();// 号码
                String id = s.get("ID").toString();//删除号池关系时使用
                tSerialIdleBackup.setId(id);
                tSerialIdleBackup.setSerialNo(serialNo);
                tSerialIdleBackup.setState("05");
                tSerialIdleBackup.setCityCode(s.get("CITY_CODE").toString());
                tSerialIdleBackup.setUpdateStaff("backupCleanSerial");
//                tSerialIdleBackup.setUpdateTime(new Date());
                log.info("backupCleanSerial tSerialIdleBackup" + tSerialIdleBackup);
                tnSerialIdleMapper.insertSerialIdleBackUp(tSerialIdleBackup);//将号码备份到T_SERIAL_IDLE_BACKUP表

                // //idel表数据删除、ocuppy表数据删除，号池关系删除，预开户表中如果存在号码则删除
                // dao.delete("BackupCleanSerialSQL.deleteIdelSerial", serialNo);
                // dao.delete("BackupCleanSerialSQL.deleteOcuppySerial", serialNo);
                // dao.delete("BackupCleanSerialSQL.deleteGroupRelation", id);
                // dao.delete("BackupCleanSerialSQL.deleteSchGroupRelation", id); //校园号池关系
                // String accountSerialNo = dao.selectOne("SymSerialStateSQL.querySerialAccount", serialNo);//预开户表中如果存在号码则删除
                // if (!"".equals(accountSerialNo) || accountSerialNo != null) {
                //     dao.delete("BackupCleanSerialSQL.deleteSerialAccount", serialNo);
                // }
            }
        }

        log.info("backupCleanSerial+备份号码总量[" + serialsList.size() + "]");
        log.info("--------------- backupCleanSerial end---------------");
    }

    //预占状态的号码同步总部号码状态
    @Override
    public void changeSerialState() {
        log.info("---------------symSerialState start----------------");
        TdMSysDict sysDict = new TdMSysDict();
        sysDict.setParamType("symSerialState");
        sysDict.setParamKey("symSerialState");
        sysDict = tdMSysDictMapper.query(sysDict);
        if (ObjectUtils.isEmpty(sysDict)){
            log.error("预占号码与总部总部号码状态同步字典项未配置！");
            return;
        }
        final int ONE_THREAD_DEAL = Integer.valueOf(sysDict.getParam3()).intValue();   // 一个线程处理的号码量
        final int CORE_THREAD_COUNT = Integer.valueOf(sysDict.getParam4()).intValue();  //核心线程数
        final int MAX_THREAD_COUNT = Integer.valueOf(sysDict.getParam5()).intValue();
        String cityTemp = sysDict.getParamValue();   //同步地市
        String predate = sysDict.getParam1().toString();     //筛选数据距离现在多久前
        String symNumCount = sysDict.getParam2().toString();   //一次单个地市同步号码数量
        Map<String, Object> querySerialsInput = new HashMap<>(); // 查询当前地市的所有预占号卡信息 - 入参
        querySerialsInput.put("predate",Integer.parseInt(predate));
        querySerialsInput.put("symNumCount",Integer.parseInt(symNumCount));
        String[] cityArr = cityTemp.split("," , 100);
        List<Map<String, Object>> cityCodeList = new ArrayList<>();// 获取地市编码
        for(Object city : cityArr){
            Map<String, Object> cityCode = new HashMap<>();
            cityCode.put("CITY_ID",city);
            cityCodeList.add(cityCode);
        }
        int totalCount = 0;
        if (cityCodeList != null && cityCodeList.size() > 0) {
            List<Future<Integer>> futureList = new ArrayList<>();
            ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("ChangeStateThread-%d").build();
            ExecutorService executorService = new ThreadPoolExecutor(CORE_THREAD_COUNT, MAX_THREAD_COUNT, 3L,
                    TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(MAX_THREAD_COUNT), threadFactory);

            for (Map<String, Object> codeMap : cityCodeList) {
                String cityCode = codeMap.get("CITY_ID").toString();
                List<Map<String, Object>> list = tnSerialIdleMapper.queryPersonInfo(cityCode);// 获取人员和渠道信息
                Map<String, Object> personInfoMap = list.get(0);// 对每个地市只查询一次人员和渠道信息，保存
                querySerialsInput.put("cityCode",cityCode);
                List<Map<String, Object>> serialsList = tnSerialIdleMapper.querySerialsByCity(querySerialsInput);// 查询当前地市的所有预占号卡信息

                log.info("symSerialState serialsList.size():"+serialsList.size());

                if (serialsList != null && serialsList.size() > 0) {
                    totalCount += serialsList.size();
                    int threadNum = serialsList.size() / ONE_THREAD_DEAL;
                    if (threadNum * ONE_THREAD_DEAL < serialsList.size()) {
                        threadNum++;
                    }
                    for (int i = 0; i < threadNum; i++) {
                        List<Map<String, Object>> listPage;
                        if (i < threadNum - 1) {
                            listPage = serialsList.subList(ONE_THREAD_DEAL * i, ONE_THREAD_DEAL * i + ONE_THREAD_DEAL);
                        } else {
                            listPage = serialsList.subList(ONE_THREAD_DEAL * i, serialsList.size());
                        }

                        Future<Integer> future = executorService
                                .submit(new SerialChangeStateHandlerThread(listPage, personInfoMap));
                        futureList.add(future);
                    }
                }
            }
            int handleCount = 0;
            for (int i = 0; i < futureList.size(); i++) {
                try {
                    Future<Integer> future = futureList.get(i);
                    Integer result = future.get();
                    handleCount += ((result == null) ? 0 : result);
                } catch (Exception e) {
                    log.info("symSerialState trace: " + e.getMessage());
                }
            }
            executorService.shutdown();
            log.info("----号码同步总量[" + totalCount + "]，状态修改总量[" + handleCount + "]-----");
            log.info("--------------- symSerialState end---------------");
        }
    }



    private class SerialChangeStateHandlerThread implements Callable<Integer> {
        private List<Map<String, Object>> serialsList;
        private Map<String, Object> personInfoMap;

        public SerialChangeStateHandlerThread(List<Map<String, Object>> listPage, Map<String, Object> infoMap) {
            this.serialsList = listPage;
            this.personInfoMap = infoMap;
        }

        @Override
        public Integer call() {
            String city = personInfoMap.get("CITY_NO").toString();// 地市
            String operatorId = personInfoMap.get("EMPLOYEE_ID").toString();// 操作人
            String channelId = personInfoMap.get("CHL_CODE").toString();// Aop渠道类型
            String channelType = personInfoMap.get("CHL_TYPE_CODE").toString();// Aop渠道编码

            int syncCount = 0;
            for (Map<String, Object> s : serialsList) {
                String serialNo = s.get("SERIAL_NO").toString();// 号码
                String id = s.get("ID").toString();//删除号池关系时使用/
                String result = null;
                log.info("-------[" + serialNo + "]symSerialState begin--------");
                try {
                    HashMap<String, String> inParam = new HashMap<String, String>();
                    inParam.put("city",city);
                    inParam.put("operatorId",operatorId);
                    inParam.put("channelId",channelId);
                    inParam.put("channelType",channelType);
                    inParam.put("serialNo",serialNo);
                    result = commonService.qryNumInfoSwitch(inParam);
//                    result = SimpleHttpUtil.post(url, params);// 3、获取AOP返回的响应报文
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("号码：" + serialNo + "查询失败，错误信息：" + e.getMessage());
                    continue;// 结束本次循环
                }
                log.info("----symSerialState:" + result);

                TdMSysDict sysDict = new TdMSysDict();
                sysDict.setParamKey("JSaopConfig");
                sysDict.setParamType("JSaopConfig");
                sysDict = tdMSysDictMapper.query(sysDict);
                if (ObjectUtils.isEmpty(sysDict)){
                    log.error("qryNumInfoSwitch未配置字典项");
                }
                // TdOrdSysDict tdOrdSysDict =  dao.selectOne("QryNumAstoreSQL.getTdOrdSysDict");
                String qryNumInfoSwitch = sysDict.getParam4(); //snres ：资源中心直连总部AOP接口；  astore_qryNumInfo ： 微服务接口

                // AOP返回报文
                if ("astore_qryNumInfo".equals(qryNumInfoSwitch)) {
                    JSONObject jsonResult = JSONObject.parseObject(result);
                    String numStatus = jsonResult.getString("NUM_STATUS");
                    try{
                        if ("03".equals(numStatus)||"02".equals(numStatus)||"05".equals(numStatus)||"06".equals(numStatus)||"07".equals(numStatus)||"11".equals(numStatus)||"14".equals(numStatus)) {   //  03 上架  02 预留  05 待预配，06 预配，07 预配套包  11 冷冻，14 待审批，
                            //idel表数据修改为占用状态05，占用时间记录为当前时间-90天
                            // dao.update("SymSerialStateSQL.updateIdelStateTime",serialNo);
                            tnSerialIdleMapper.updateIdelStateTime(serialNo);
                            syncCount++;
                        }else if ("04".equals(numStatus)||"08".equals(numStatus)||"09".equals(numStatus)||"10".equals(numStatus)||"16".equals(numStatus)||"15".equals(numStatus)) {  //04 选占  ，08 预占，09 占用未激活，10 占用  16 预开状态  15 订单未激活，
                            // 查询  idel表状态是否为04，若是04，则修改为05
                            // TSerialIdle tSerialIdle = new TSerialIdle();
                            // tSerialIdle.setSerialNo(serialNo).asCondition();// 号码
                            // TSerialIdle tSerIdle = dao.selectOne(tSerialIdle);
                            TSerialIdle tSerIdle = tSerialIdleMapper.queryTSerialIdleBySeraialNo(serialNo);
                            if("04".equals(tSerIdle.getState())){
                                // dao.update("SymSerialStateSQL.updateIdelStateOnly",serialNo);
                                tSerialIdleMapper.updateIdelStateOnly(serialNo);
                                syncCount++;
                            }
                        }else if ("01".equals(numStatus)) {   //  01 空闲
                            //idel表数据修改为占用状态05，占用时间记录为当前时间-90天
                            // dao.update("SymSerialStateSQL.updateIdelStateTime",serialNo);
                            tSerialIdleMapper.updateIdelStateTime(serialNo);
                            syncCount++;
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("号码:[" + serialNo + "]更新失败,错误信息:" + e.getMessage());
                        continue;
                    }
                } else if ("snres".equals(qryNumInfoSwitch)) {
                    AopResourcesInfoRsp aopResInfo = new AopResourcesInfoRsp();
                    aopResInfo = JSON.parseObject(result, AopResourcesInfoRsp.class);
                    List<ResourcesInfpRsp> rspList = aopResInfo.getResourcesRsp();
                    String rscStateCode = "";
                    String numState = "";
                    try {
                        for (ResourcesInfpRsp aopRsp : rspList) {
                            rscStateCode = aopRsp.getRscStateCode();// 号码状态
                            numState =aopRsp.getNumState();
                            if ("0000".equals(rscStateCode)||"0003".equals(rscStateCode)) {
                                //idel表数据修改为占用状态05，占用时间记录为当前时间-90天
                                // dao.update("SymSerialStateSQL.updateIdelStateTime",serialNo);
                                tSerialIdleMapper.updateIdelStateTime(serialNo);
                                syncCount++;
                            }else if ("0001".equals(rscStateCode)) {
                                // 查询  idel表状态是否为04，若是04，则修改为05
                                // TSerialIdle tSerialIdle = new TSerialIdle();
                                // tSerialIdle.setSerialNo(serialNo).asCondition();// 号码
                                // TSerialIdle tSerIdle = dao.selectOne(tSerialIdle);
                                TSerialIdle tSerIdle = tSerialIdleMapper.queryTSerialIdleBySeraialNo(serialNo);
                                if("04".equals(tSerIdle.getState())){
                                    // dao.update("SymSerialStateSQL.updateIdelStateOnly",serialNo);
                                    tSerialIdleMapper.updateIdelStateOnly(serialNo);
                                    syncCount++;
                                }
                            }else if ("0004".equals(rscStateCode)) {
                                if ("01".equals(numState) || "02".equals(numState)|| "03".equals(numState)|| "05".equals(numState)|| "06".equals(numState)|| "07".equals(numState)){
                                    //idel表数据修改为占用状态05，占用时间记录为当前时间-90天
                                    // dao.update("SymSerialStateSQL.updateIdelStateTime",serialNo);
                                    tSerialIdleMapper.updateIdelStateTime(serialNo);
                                    syncCount++;
                                }else if ("04".equals(numState)|| "08".equals(numState)|| "09".equals(numState)){
                                }else{
                                    // dao.update("SymSerialStateSQL.updateIdelStateOnly",serialNo);
                                    tSerialIdleMapper.updateIdelStateOnly(serialNo);
                                    syncCount++;
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("号码:[" + serialNo + "]更新失败,错误信息:" + e.getMessage());
                        continue;
                    }
                }


                log.info("----号码[" + serialNo + "]与总部同步结束-----");
            }
            return syncCount;
        }
    }
}
