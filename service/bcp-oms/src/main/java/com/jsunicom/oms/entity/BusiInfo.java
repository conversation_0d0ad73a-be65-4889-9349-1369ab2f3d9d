package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/13 9:28
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BusiInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private String provinceCode;
    private String regionId;
    private String countyId;
    private String channelId;
    private String channelType;
    private String busiStaffId;
    private String busiType;
    private List<OrderInfo> orderInfos;

}
