package com.jsunicom.oms.po.order.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@ToString
@ApiModel(description = "订单修改请求报文商品信息")
public class GoodsReqInfo{

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商品编码", required = true, position = 1)
	private String goodsId;

	@ApiModelProperty(value = "商品属性", required = true, position = 2)
	private List<GoodsPropsReq> goodsProps;

	@ApiModelProperty(value = "产品", required = true, position = 3)
	private List<ProductsReq> products;

	@ApiModelProperty(value = "客户名称", required = true, position = 4)
	private String custName;

	@ApiModelProperty(value = "性别", required = true, position = 5)
	private String sex;

	@ApiModelProperty(value = "民族", required = true, position = 6)
	private String nation;

	@ApiModelProperty(value = "证件失效日期", required = true, position = 7)
	private Date endDate;

	@ApiModelProperty(value = "证件ID", required = true, position = 8)
	private String psptId;

	@ApiModelProperty(value = "证件地址", required = true, position = 9)
	private String psptAddress;

	@ApiModelProperty(value = "证件照正面", required = true, position = 10)
	private String cardPhotoA;

	@ApiModelProperty(value = "证件照背面", required = true, position = 11)
	private String cardPhotoB;

	@ApiModelProperty(value = "手持证件照", required = true, position = 12)
	private String cardPhotoHand;


}
