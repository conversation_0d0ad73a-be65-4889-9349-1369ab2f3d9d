package com.jsunicom.oms.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WoScYiDepartmentBaseExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYiDepartmentBaseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDepartIdIsNull() {
            addCriterion("DEPART_ID is null");
            return (Criteria) this;
        }

        public Criteria andDepartIdIsNotNull() {
            addCriterion("DEPART_ID is not null");
            return (Criteria) this;
        }

        public Criteria andDepartIdEqualTo(Long value) {
            addCriterion("DEPART_ID =", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdNotEqualTo(Long value) {
            addCriterion("DEPART_ID <>", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdGreaterThan(Long value) {
            addCriterion("DEPART_ID >", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdGreaterThanOrEqualTo(Long value) {
            addCriterion("DEPART_ID >=", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdLessThan(Long value) {
            addCriterion("DEPART_ID <", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdLessThanOrEqualTo(Long value) {
            addCriterion("DEPART_ID <=", value, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdIn(List<Long> values) {
            addCriterion("DEPART_ID in", values, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdNotIn(List<Long> values) {
            addCriterion("DEPART_ID not in", values, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdBetween(Long value1, Long value2) {
            addCriterion("DEPART_ID between", value1, value2, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartIdNotBetween(Long value1, Long value2) {
            addCriterion("DEPART_ID not between", value1, value2, "departId");
            return (Criteria) this;
        }

        public Criteria andDepartNameIsNull() {
            addCriterion("DEPART_NAME is null");
            return (Criteria) this;
        }

        public Criteria andDepartNameIsNotNull() {
            addCriterion("DEPART_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andDepartNameEqualTo(String value) {
            addCriterion("DEPART_NAME =", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameNotEqualTo(String value) {
            addCriterion("DEPART_NAME <>", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameGreaterThan(String value) {
            addCriterion("DEPART_NAME >", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameGreaterThanOrEqualTo(String value) {
            addCriterion("DEPART_NAME >=", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameLessThan(String value) {
            addCriterion("DEPART_NAME <", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameLessThanOrEqualTo(String value) {
            addCriterion("DEPART_NAME <=", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameLike(String value) {
            addCriterion("DEPART_NAME like", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameNotLike(String value) {
            addCriterion("DEPART_NAME not like", value, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameIn(List<String> values) {
            addCriterion("DEPART_NAME in", values, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameNotIn(List<String> values) {
            addCriterion("DEPART_NAME not in", values, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameBetween(String value1, String value2) {
            addCriterion("DEPART_NAME between", value1, value2, "departName");
            return (Criteria) this;
        }

        public Criteria andDepartNameNotBetween(String value1, String value2) {
            addCriterion("DEPART_NAME not between", value1, value2, "departName");
            return (Criteria) this;
        }

        public Criteria andCampusIdIsNull() {
            addCriterion("CAMPUS_ID is null");
            return (Criteria) this;
        }

        public Criteria andCampusIdIsNotNull() {
            addCriterion("CAMPUS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCampusIdEqualTo(Long value) {
            addCriterion("CAMPUS_ID =", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotEqualTo(Long value) {
            addCriterion("CAMPUS_ID <>", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThan(Long value) {
            addCriterion("CAMPUS_ID >", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdGreaterThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID >=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThan(Long value) {
            addCriterion("CAMPUS_ID <", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdLessThanOrEqualTo(Long value) {
            addCriterion("CAMPUS_ID <=", value, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdIn(List<Long> values) {
            addCriterion("CAMPUS_ID in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotIn(List<Long> values) {
            addCriterion("CAMPUS_ID not in", values, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andCampusIdNotBetween(Long value1, Long value2) {
            addCriterion("CAMPUS_ID not between", value1, value2, "campusId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIsNull() {
            addCriterion("SOCIETY_ID is null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIsNotNull() {
            addCriterion("SOCIETY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSocietyIdEqualTo(Long value) {
            addCriterion("SOCIETY_ID =", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotEqualTo(Long value) {
            addCriterion("SOCIETY_ID <>", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThan(Long value) {
            addCriterion("SOCIETY_ID >", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SOCIETY_ID >=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThan(Long value) {
            addCriterion("SOCIETY_ID <", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdLessThanOrEqualTo(Long value) {
            addCriterion("SOCIETY_ID <=", value, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdIn(List<Long> values) {
            addCriterion("SOCIETY_ID in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotIn(List<Long> values) {
            addCriterion("SOCIETY_ID not in", values, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdBetween(Long value1, Long value2) {
            addCriterion("SOCIETY_ID between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andSocietyIdNotBetween(Long value1, Long value2) {
            addCriterion("SOCIETY_ID not between", value1, value2, "societyId");
            return (Criteria) this;
        }

        public Criteria andDepartDutyIsNull() {
            addCriterion("DEPART_DUTY is null");
            return (Criteria) this;
        }

        public Criteria andDepartDutyIsNotNull() {
            addCriterion("DEPART_DUTY is not null");
            return (Criteria) this;
        }

        public Criteria andDepartDutyEqualTo(String value) {
            addCriterion("DEPART_DUTY =", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyNotEqualTo(String value) {
            addCriterion("DEPART_DUTY <>", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyGreaterThan(String value) {
            addCriterion("DEPART_DUTY >", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyGreaterThanOrEqualTo(String value) {
            addCriterion("DEPART_DUTY >=", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyLessThan(String value) {
            addCriterion("DEPART_DUTY <", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyLessThanOrEqualTo(String value) {
            addCriterion("DEPART_DUTY <=", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyLike(String value) {
            addCriterion("DEPART_DUTY like", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyNotLike(String value) {
            addCriterion("DEPART_DUTY not like", value, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyIn(List<String> values) {
            addCriterion("DEPART_DUTY in", values, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyNotIn(List<String> values) {
            addCriterion("DEPART_DUTY not in", values, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyBetween(String value1, String value2) {
            addCriterion("DEPART_DUTY between", value1, value2, "departDuty");
            return (Criteria) this;
        }

        public Criteria andDepartDutyNotBetween(String value1, String value2) {
            addCriterion("DEPART_DUTY not between", value1, value2, "departDuty");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagIsNull() {
            addCriterion("DEFAULT_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagIsNotNull() {
            addCriterion("DEFAULT_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagEqualTo(String value) {
            addCriterion("DEFAULT_FLAG =", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagNotEqualTo(String value) {
            addCriterion("DEFAULT_FLAG <>", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagGreaterThan(String value) {
            addCriterion("DEFAULT_FLAG >", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagGreaterThanOrEqualTo(String value) {
            addCriterion("DEFAULT_FLAG >=", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagLessThan(String value) {
            addCriterion("DEFAULT_FLAG <", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagLessThanOrEqualTo(String value) {
            addCriterion("DEFAULT_FLAG <=", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagLike(String value) {
            addCriterion("DEFAULT_FLAG like", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagNotLike(String value) {
            addCriterion("DEFAULT_FLAG not like", value, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagIn(List<String> values) {
            addCriterion("DEFAULT_FLAG in", values, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagNotIn(List<String> values) {
            addCriterion("DEFAULT_FLAG not in", values, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagBetween(String value1, String value2) {
            addCriterion("DEFAULT_FLAG between", value1, value2, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andDefaultFlagNotBetween(String value1, String value2) {
            addCriterion("DEFAULT_FLAG not between", value1, value2, "defaultFlag");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("STATE is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("STATE is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("STATE =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("STATE <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("STATE >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("STATE >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("STATE <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("STATE <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("STATE like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("STATE not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("STATE in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("STATE not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("STATE between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("STATE not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("CREATED_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("CREATED_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Date value) {
            addCriterion("CREATED_TIME =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Date value) {
            addCriterion("CREATED_TIME <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Date value) {
            addCriterion("CREATED_TIME >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_TIME >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Date value) {
            addCriterion("CREATED_TIME <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_TIME <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Date> values) {
            addCriterion("CREATED_TIME in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Date> values) {
            addCriterion("CREATED_TIME not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Date value1, Date value2) {
            addCriterion("CREATED_TIME between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_TIME not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIsNull() {
            addCriterion("APPROVAL_STATE is null");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIsNotNull() {
            addCriterion("APPROVAL_STATE is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalStateEqualTo(String value) {
            addCriterion("APPROVAL_STATE =", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotEqualTo(String value) {
            addCriterion("APPROVAL_STATE <>", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateGreaterThan(String value) {
            addCriterion("APPROVAL_STATE >", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateGreaterThanOrEqualTo(String value) {
            addCriterion("APPROVAL_STATE >=", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLessThan(String value) {
            addCriterion("APPROVAL_STATE <", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLessThanOrEqualTo(String value) {
            addCriterion("APPROVAL_STATE <=", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateLike(String value) {
            addCriterion("APPROVAL_STATE like", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotLike(String value) {
            addCriterion("APPROVAL_STATE not like", value, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateIn(List<String> values) {
            addCriterion("APPROVAL_STATE in", values, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotIn(List<String> values) {
            addCriterion("APPROVAL_STATE not in", values, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateBetween(String value1, String value2) {
            addCriterion("APPROVAL_STATE between", value1, value2, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalStateNotBetween(String value1, String value2) {
            addCriterion("APPROVAL_STATE not between", value1, value2, "approvalState");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIsNull() {
            addCriterion("APPROVAL_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIsNotNull() {
            addCriterion("APPROVAL_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkEqualTo(String value) {
            addCriterion("APPROVAL_REMARK =", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotEqualTo(String value) {
            addCriterion("APPROVAL_REMARK <>", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkGreaterThan(String value) {
            addCriterion("APPROVAL_REMARK >", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("APPROVAL_REMARK >=", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLessThan(String value) {
            addCriterion("APPROVAL_REMARK <", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLessThanOrEqualTo(String value) {
            addCriterion("APPROVAL_REMARK <=", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkLike(String value) {
            addCriterion("APPROVAL_REMARK like", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotLike(String value) {
            addCriterion("APPROVAL_REMARK not like", value, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkIn(List<String> values) {
            addCriterion("APPROVAL_REMARK in", values, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotIn(List<String> values) {
            addCriterion("APPROVAL_REMARK not in", values, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkBetween(String value1, String value2) {
            addCriterion("APPROVAL_REMARK between", value1, value2, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andApprovalRemarkNotBetween(String value1, String value2) {
            addCriterion("APPROVAL_REMARK not between", value1, value2, "approvalRemark");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("UPDATED_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("UPDATED_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Date value) {
            addCriterion("UPDATED_TIME =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Date value) {
            addCriterion("UPDATED_TIME <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Date value) {
            addCriterion("UPDATED_TIME >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_TIME >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Date value) {
            addCriterion("UPDATED_TIME <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_TIME <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Date> values) {
            addCriterion("UPDATED_TIME in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Date> values) {
            addCriterion("UPDATED_TIME not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATED_TIME between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_TIME not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}