package com.jsunicom.oms.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> zhaowang
 * @ClassName FeignInterceptor
 * @Description TODO 定义Feign拦截器
 * @date : 2021/12/24 下午7:12
 * @Version 1.0
 **/
@Configuration
public class FeignInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
        Map<String, String> headers = getHeaders(getHttpServletRequest());
        for(String headerName : headers.keySet()){
            requestTemplate.header(headerName, getHeaders(getHttpServletRequest()).get(headerName));
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            // 跳过 content-length
            if (key.equals("content-length")){
                continue;
            }
            if(needThisHeader(key)){
                String value = request.getHeader(key);
                map.put(key, value);
            }
        }
        return map;
    }

    private boolean needThisHeader(String headerName){
        //todo:这里写你的逻辑，哪些header需要传递，千万不能把所有的header传下去
        return true;
    }
}
