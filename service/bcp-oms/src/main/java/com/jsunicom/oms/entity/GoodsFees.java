package com.jsunicom.oms.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/12 19:25
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class GoodsFees implements Serializable {
    private static final long serialVersionUID = 1L;

    private String feeType;
    private String feeCode;
    private String orgAmount;
    private String discntAmount;
    private String realAamount;

}
