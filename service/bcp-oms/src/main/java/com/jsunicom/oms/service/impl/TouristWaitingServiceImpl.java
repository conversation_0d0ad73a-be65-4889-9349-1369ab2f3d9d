package com.jsunicom.oms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.entity.PartnerInfo;
import com.jsunicom.oms.entity.TouristWaiting;
import com.jsunicom.oms.entity.TreeBean;
import com.jsunicom.oms.mapper.QcsExtMapper;
import com.jsunicom.oms.mapper.TouristWaitingMapper;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.po.PartnerInfoExample;
import com.jsunicom.oms.service.TouristWaitingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class TouristWaitingServiceImpl implements TouristWaitingService {

    @Autowired
    private TouristWaitingMapper touristWaitingMapper;

    @Autowired
    private QcsExtMapper qcsExtMapper;

    @Override
    public HashMap<String, Object> qryPageTouristInfo(JSONObject jsonObject) {
        int pageNum = jsonObject.getInteger("pageNum");
        int pageSize = jsonObject.getInteger("pageSize");
        TouristWaiting touristWaiting = new TouristWaiting();
        touristWaiting.setTouristName(ObjectUtil.defaultIfBlank(jsonObject.getString("touristName"), ""));
        touristWaiting.setIsAssign(ObjectUtil.defaultIfBlank(jsonObject.getString("isAssign"), ""));
        touristWaiting.setTouristPhone(ObjectUtil.defaultIfBlank(jsonObject.getString("touristPhone"), ""));
        touristWaiting.setCampusId(jsonObject.getLong("campusId"));
        PageHelper.startPage(pageNum, pageSize);
        List<TouristWaiting> resultList = touristWaitingMapper.selectTouristWaiting(touristWaiting);

        PageInfo<TouristWaiting> pageInfo = new PageInfo<>(resultList);
        // 图片需要特殊处理
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("data", pageInfo.getList());
        dataMap.put("totalCount", pageInfo.getTotal());
        return dataMap;
    }

    @Override
    public HashMap<String, Object> qrySocietyInfoBySchoolId(JSONObject jsonObject) {
        HashMap<String, Object> map = new HashMap<>();
        String schoolId = jsonObject.getString("schoolId");
        String schoolName = jsonObject.getString("schoolName");
        try {
            //查青创社信息
            TreeBean treeBean = new TreeBean();
            treeBean.setId(0);
            treeBean.setLabel(schoolName);
            treeBean.setPositionType("0"); //0代表是学校
            List<TreeBean> societyInfo = touristWaitingMapper.selectSocietyInfoBySchoolId(schoolId);
            if (societyInfo.size()>0){
                //查团队
                for (TreeBean societyBean: societyInfo){
                    List<TreeBean> departList = qcsExtMapper.selectDepartInfoByIdModify(societyBean.getId()+"");
                    for (TreeBean departBean:departList){
                        departBean.setPositionType("2"); //2代表是团队
                    }
                    societyBean.setPositionType("1"); //1代表是社团
                    societyBean.setChildren(departList);
                }
            }
            treeBean.setChildren(societyInfo);
            ArrayList<Object> objects = new ArrayList<>();
            objects.add(treeBean);
            map.put("result",objects);
            return map;
        }catch (Exception e){
            log.error(e.getMessage());
            return null;
        }
    }

    @Override
    public int deleteTouristWaiting(JSONObject jsonObject) {
        Long touristId = jsonObject.getLong("touristId");
        TouristWaiting touristWaiting = new TouristWaiting();
        touristWaiting.setTouristId(touristId);
        return touristWaitingMapper.deleteTouristWaiting(touristWaiting);
    }

    @Override
    public long countOrByExample(PartnerInfo record){

        PartnerInfoExample example = new PartnerInfoExample();
        PartnerInfoExample.Criteria criteria = example.createCriteria();

        criteria.andMblNbrEqualTo(record.getMblNbr());
        if(record.getState() != null){
        String stateStr = record.getState();
        String[] splitArray = stateStr.split("|");
        criteria.andStateIn(Arrays.asList(splitArray));
        }


        PartnerInfoExample.Criteria criteriaOr = example.or();
        criteriaOr.andPartnerCertNoEqualTo(record.getPartnerCertNo());
        if(record.getState() != null){
            String stateStr = record.getState();
            String[] splitArray = stateStr.split("|");
            criteriaOr.andStateIn(Arrays.asList(splitArray));
        }
        return touristWaitingMapper.countByExample(example);
    }


    @Override
    public int insertPartnerInfo(PartnerInfo record) {
        return touristWaitingMapper.insertPartnerInfoSelective(record);
    }

    @Override
    public int upDateTouristWaiting(HashMap HashMap) {
        TouristWaiting touristWaiting = new TouristWaiting();
        if(StringUtils.isEmpty(HashMap.get("touristId")+""));
        touristWaiting.setTouristId(Long.parseLong(HashMap.get("touristId")+""));
        touristWaiting.setIsAssign("1");
        return touristWaitingMapper.updateTouristWaitingById(touristWaiting);
    }

    @Override
    public List<Map> queryMemberIListById(JSONObject params) {
        String societyId = params.getString("societyId");
        String merchantId = params.getString("merchantId");
        List<Map> resultList = new ArrayList<>();
        if("0".equals(societyId)&&StringUtils.isEmpty(merchantId)){  //查询校园经理
            String campusId = params.getString("campusId");
            //查询当前客户经理信息
            resultList = touristWaitingMapper.queryParterListById(campusId);
        }else{
            resultList = touristWaitingMapper.queryMemberIListById(societyId,merchantId);
        }
        return resultList;
    }
}
