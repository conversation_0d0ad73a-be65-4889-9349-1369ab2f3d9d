package com.jsunicom.oms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.mapper.GoodsMapper;
import com.jsunicom.oms.mapper.ext.ExtMapper;
import com.jsunicom.oms.mapper.ext.GoodsExtMapper;
import com.jsunicom.oms.po.Goods;
import com.jsunicom.oms.po.GoodsExample;
import com.jsunicom.oms.service.GoodsService;
import com.jsunicom.oms.service.OrgInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-19-22:43
 */
@Service
public class GoodsServiceImpl implements GoodsService {
    @Autowired
    private ExtMapper extMapper;
    @Autowired
    private GoodsMapper goodsMapper;
    @Autowired
    private GoodsExtMapper goodsExtMapper;
    @Autowired
    private OrgInfoService orgInfoService;
    @Override
    public PageInfo<Goods> findByPage(Goods info, Integer pageNo, Integer pageSize) {
        String busLine = info.getBusLine();
        Goods goodsInfo ;
        List<Goods> goodsList = new ArrayList<>();
        List<String> codeList =new ArrayList<>();
        String goodsArea = null;
        if (StringUtils.isNotEmpty(info.getGoodsArea())) {
            //TODO 待确定 如何修改 权限的校验
            codeList = orgInfoService.getAllTreeCode(info.getGoodsArea());
            goodsArea =getGoodsArea(codeList);
        }
        if(!StringUtils.isEmpty(busLine)) {
            for (String line : busLine.split(",")) {
                goodsInfo = new Goods();
                goodsInfo.setCode(info.getCode());
                goodsInfo.setBusLine("%" + line + "%");
                goodsInfo.setGoodsArea(goodsArea);
                goodsInfo.setName(info.getName());
                goodsInfo.setGoodsType(info.getGoodsType());
                goodsInfo.setState(info.getState());
                goodsList.add(goodsInfo);
            }
        }
        List<Goods> pageList;
        if(goodsList.size() > 0) {
            PageHelper.startPage(pageNo,pageSize);
            pageList = extMapper.queryGoodsByBusLines(goodsList);
        }else{
            PageHelper.startPage(pageNo,pageSize);
            info.setGoodsArea(goodsArea);
            pageList = extMapper.findList(info);
        }
        PageInfo<Goods> res=new PageInfo<>(pageList);
        return res;
    }
    private String getGoodsArea(List<String> codeList){
        String goodsArea = null ;
        if(codeList.size()>0){
            goodsArea = "(";
            for(String code:codeList){
                goodsArea = goodsArea+"'"+code+"',";
            }
            goodsArea = goodsArea.substring(0,goodsArea.length()-1)+")";
        }
        return goodsArea;
    }

    @Override
    public Goods findByCode(String code) {
        GoodsExample goodsExample=new GoodsExample();
        goodsExample.createCriteria().andCodeEqualTo(code);
        List<Goods> goods = goodsMapper.selectByExample(goodsExample);
        if (CollectionUtils.isEmpty(goods)){
            return null;
        }
        return goods.get(0);
    }

    @Override
    public void update(Goods info) {
        info.setUpdateTime(new Date());
        GoodsExample goodsExample=new GoodsExample();
        goodsExample.createCriteria().andCodeEqualTo(info.getCode());
        goodsMapper.updateByExampleSelective(info,goodsExample);
    }

    @Override
    public Boolean countPutAwayGoods(Goods info) {
        return extMapper.countPutAwayGoods(info.getId()) > 0;
    }

    @Override
    public int batchPreDeleteGoods(Goods goodsInfo) {
        return extMapper.updateGoodsEndTime(goodsInfo);
    }

    @Override
    public void delete(Goods info,String staffNo) {
        info.setUpdateTime(new Date());
        extMapper.delete(info,staffNo);
    }

    @Override
    public void deletePutAwayGoods(Goods info) {
        extMapper.deletePutAwayGoods(info);
        //删除智慧生活馆的商品信息
        extMapper.deleteSmartLifeGoods(info);
    }

    @Override
    public void updateState(Goods info,String staffNo) {
        info.setUpdateTime(new Date());
        extMapper.updateState(info,staffNo);
    }

    @Override
    public Map<String, Goods> insert(Goods info) {
        String orgCode=info.getGoodsArea();
        String[] orgArray = orgCode.split(",");
        for(String org:orgArray){
            info.setGoodsArea(org);
            info.setCreateTime(new Date());
            info.setUpdateTime(new Date());
            goodsExtMapper.insert(info);
        }
        Map<String,Goods> goodsMap = new HashMap<>();
        goodsMap.put("goods",info);
        return goodsMap;
    }
}
