package com.jsunicom.oms.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2024-01-31-15:34
 */
public interface DevelopCollectMapper {
    List<HashMap> selectBoardForProvinceTotal(@Param("orgCode") String orgCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<HashMap> selectBoardForProvince(@Param("orgCode") String orgCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<HashMap> selectBoardForCity(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<HashMap> selectBoardForCityTotal(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<HashMap> selectBoardForPartner(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("societyName")String societyName);

    List<HashMap> selectBoardForPartnerTotal(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("societyName")String societyName);

    List<HashMap> selectChannelForProvince(@Param("orgCode") String orgCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<HashMap> selectChannelForProvinceTotal(@Param("orgCode") String orgCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<HashMap> selectChannelForCity(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<HashMap> selectChannelForCityTotal(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<HashMap> selectChannelForPartner(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("channelName")String channelName);

    List<HashMap> selectChannelForPartnerTotal(@Param("orgCode")String orgCode, @Param("campusName")String campusName, @Param("phone")String phone, @Param("startTime")String startTime, @Param("endTime")String endTime, @Param("channelName")String channelName);
}
