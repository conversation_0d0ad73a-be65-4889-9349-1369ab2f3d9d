package com.jsunicom.oms.utils;

import com.google.common.base.CaseFormat;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MapKeyTransformation {

    public static List<Map<String, Object>> toReplaceKeyLowForList(List<Map<String, Object>> list) {
        return list.stream().map(k -> {
            Map<String, Object> back = new HashMap();
            k.entrySet().stream().forEach(entry -> {
                back.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey()),entry.getValue());
            });
            return back;
        }).collect(Collectors.toList());
    }

    public static Map<String, Object> toReplaceKeyLowForMap(Map<String, Object> map) {
        Map<String, Object> back = new HashMap();
        map.entrySet().stream().forEach(entry -> {
            back.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey()),entry.getValue());
        });
        return back;
    }

    public static void main(String[] args) {
        String bb = "ABC_DE_FN";
        //大写下划线转换驼峰
        System.out.println(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, bb));
        String orderColumn = "orderColumn";
        //驼峰转换成大写下划线
        orderColumn = CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, orderColumn);
        System.out.println(orderColumn);//ORDER_COLUMN
        String orderColumn1 = "orderColumn";

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("ABC_DE_FN","11");
        map1.put("ORDER_COLUMN","22");
        list.add(map1);
        List<Map<String, Object>> list2 = list.stream().map(k -> {
            Map<String, Object> back = new HashMap();
            k.entrySet().stream().forEach(entry -> {
                back.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey()),entry.getValue());
            });
            return back;
        }).collect(Collectors.toList());
        System.out.println(list2.toString());
    }

}
