package com.jsunicom.oms.timer.order;

import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.common.core.util.IdUtil;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.mapper.order.TfOrdLineMapper;
import com.jsunicom.oms.mapper.order.TfOrdMainMapper;
import com.jsunicom.oms.mapper.order.TlOrdOperatorLogMapper;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.po.order.tf.TfOrdLine;
import com.jsunicom.oms.po.order.tf.TfOrdMain;
import com.jsunicom.oms.po.order.tl.TlOrdOperatorLog;
import com.jsunicom.oms.service.order.CampusOrderService;
import com.jsunicom.oms.service.order.TlOrdOperatorLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 7天未进行资料补录自动退单
 */
@Slf4j
@Component
@EnableScheduling
public class AutoCloseMakePhotoJob {

    @Autowired
    private TfOrdMainMapper tfOrdMainMapper;

    @Autowired
    private CampusOrderService campusOrderService;

    @Autowired
    private TlOrdOperatorLogService tlOrdOperatorLogService;

    //0 0 5 * * ?
    @XxlJob("autoCloseMakePhotoJob")
    public ReturnT<String> autoCloseMakePhotoJob(String param) {
        log.info("=====autoCloseMakePhotoJob start====");
        try {
            //查询超过7天未资料补录的订单
            List<Map<String,String>> orderList = tfOrdMainMapper.queryNoSupplementOrder();
            if (orderList != null && orderList.size() > 0) {
                for (Map<String, String> map : orderList) {
                    try {
                        String orderId = map.get("ORDER_ID");
                        TlOrdOperatorLog operatorLog = new TlOrdOperatorLog();
                        operatorLog.setOrderId(orderId);
                        operatorLog.setNodeCode("checkPhoto");
                        operatorLog.setReasonName("资料补录");
                        operatorLog.setReasonDesc("照片未补录超过7天，订单进入退单流程");
                        operatorLog.setReasonCode("chargeBack");
                        operatorLog.setOperType("autoChargeBack");
                        tlOrdOperatorLogService.recordOrderOperInfo(operatorLog);

                        UserInfo userInfo = new UserInfo();
                        userInfo.setStaffNo("system");
                        userInfo.setStaffName("");
                        userInfo.setSerialNumber("");
                        JSONObject params = new JSONObject();
                        params.put("orderId", orderId);
                /*    params.put("nodeCode","checkPhoto");
                    params.put("nodeName","资料补录");*/
                        Result result=campusOrderService.orderRefund(params, userInfo);
                        if(result.getSuccess()){
                            log.info("【" + map.get("ORDER_ID") + "】订单进入退单流程成功");
                        }else{
                            log.info("【" + map.get("ORDER_ID") + "】订单进入退单流程异常，异常信息："+result.getMsg());
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("【" + map.get("ORDER_ID") + "】订单进入退单流程异常，异常信息：" + e.getMessage());
                    }
                }
            }
            log.info("=====autoCloseMakePhotoJob end====");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("autoCloseMakePhotoJobException:{}",e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
