package com.jsunicom.oms.po.order.req;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class WoActiveNoticeBody implements Serializable {
	private static final long serialVersionUID = 2843612708126191690L;

	@ApiModelProperty(value = "订单中心订单编码", required = true, position = 1)
	private String orderCenterId;

	@ApiModelProperty(value = "商品订单编码", required = true, position = 2)
	private String goodsOrderId;

	@ApiModelProperty(value = "产品订单编码", required = true, position = 3)
	private String productOrderId;

	@ApiModelProperty(value = "开户号码", required = true, position = 4)
	private String serialNumber;

	@ApiModelProperty(value = "实名认证类型（01：优图H5，02：支付宝H5，03：芝麻SDK）", required = true, position = 5)
	private String activeType;

	@ApiModelProperty(value = "活体人脸相似度分数", required = true, position = 6)
	private String liveSource;

	@ApiModelProperty(value = "活体认证结果（Y：成功，N：失败）", required = true, position = 7)
	private String liveMsg;

	@ApiModelProperty(value = "活体认证描述", required = true, position = 8)
	private String liveState;

	@ApiModelProperty(value = "实名认证结果（1：成功，0：失败）", required = true, position = 9)
	private String validResult;

	@ApiModelProperty(value = "实名认证描述", required = true, position = 10)
	private String validDesc;

	@ApiModelProperty(value = "资源信息", required = true, position = 11)
	private List<ResourceInfo> resourceInfo;

	@ApiModelProperty(value = "拍照流水号", required = true, position = 12)
	private String wslPhoneOrderId;

	@ApiModelProperty(value = "操作人信息", required = false, position = 13)
	private Operator operator;

	public String getOrderCenterId() {
		return orderCenterId;
	}

	public void setOrderCenterId(String orderCenterId) {
		this.orderCenterId = orderCenterId;
	}

	public String getGoodsOrderId() {
		return goodsOrderId;
	}

	public void setGoodsOrderId(String goodsOrderId) {
		this.goodsOrderId = goodsOrderId;
	}

	public String getProductOrderId() {
		return productOrderId;
	}

	public void setProductOrderId(String productOrderId) {
		this.productOrderId = productOrderId;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getActiveType() {
		return activeType;
	}

	public void setActiveType(String activeType) {
		this.activeType = activeType;
	}

	public String getLiveSource() {
		return liveSource;
	}

	public void setLiveSource(String liveSource) {
		this.liveSource = liveSource;
	}

	public String getLiveMsg() {
		return liveMsg;
	}

	public void setLiveMsg(String liveMsg) {
		this.liveMsg = liveMsg;
	}

	public String getLiveState() {
		return liveState;
	}

	public void setLiveState(String liveState) {
		this.liveState = liveState;
	}

	public String getValidResult() {
		return validResult;
	}

	public void setValidResult(String validResult) {
		this.validResult = validResult;
	}

	public String getValidDesc() {
		return validDesc;
	}

	public void setValidDesc(String validDesc) {
		this.validDesc = validDesc;
	}

	public List<ResourceInfo> getResourceInfo() {
		return resourceInfo;
	}

	public void setResourceInfo(List<ResourceInfo> resourceInfo) {
		this.resourceInfo = resourceInfo;
	}

	public String getWslPhoneOrderId() {
		return wslPhoneOrderId;
	}

	public void setWslPhoneOrderId(String wslPhoneOrderId) {
		this.wslPhoneOrderId = wslPhoneOrderId;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Operator getOperator() {
		return operator;
	}

	public void setOperator(Operator operator) {
		this.operator = operator;
	}
}
