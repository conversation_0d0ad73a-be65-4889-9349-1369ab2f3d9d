package com.jsunicom.oms.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.model.salesman.SalesManager;
import com.jsunicom.oms.po.*;
import com.jsunicom.oms.service.CampusCollegeInfoService;
import com.jsunicom.oms.service.CampusCollegeRecruitService;
import com.jsunicom.oms.service.DictFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@RestController
@RequestMapping(value = "/collegeSchollInfo")
public class CampusCollegeInfoController {


    @Resource
    private DictFacade dictFacade;


    @Resource
    private CampusCollegeInfoService campusCollegeInfoService;

    @Resource
    private CampusCollegeRecruitService campusCollegeRecruitService;


    @RequestMapping(value = "/getRecruitTopInfo", method = RequestMethod.POST, name = "招生信息-最近5年")
    public CustomResult getRecruitTopInfo(@RequestBody HashMap hashMap){

        log.info("进入getRecruitTopInfo方法："+ JSONObject.toJSONString(hashMap));

        List<CampusRecruitInfo> recruitInfoList = new ArrayList<>();
        CampusRecruitInfo record = new CampusRecruitInfo();
        if(hashMap.containsKey("collegeId")){
            Long collegeId = Long.parseLong(hashMap.get("collegeId").toString());
            record.setCollegeId(collegeId);
            recruitInfoList = campusCollegeRecruitService.getCollegeList(record);
        }else{
            recruitInfoList = campusCollegeRecruitService.getCollegeGroup();
        }

        addMissingGrades(recruitInfoList);

        JSONArray objectArray = new JSONArray();
        for (CampusRecruitInfo campusRecruitInfo : recruitInfoList){
            JSONObject object = new JSONObject();
            String grade = campusRecruitInfo.getGrade();

            object.put("grade", grade);
            object.put("enrollmentNumber", campusRecruitInfo.getEnrollmentNumber());

            objectArray.add(object);
        }

        return ResultUtil.successByJSONArray(objectArray);
    }

    private void addMissingGrades(List<CampusRecruitInfo> recruitInfoList)
    {
        // Assume recruitInfoList is populated with some data

        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
        Set<String> existingGrades = new HashSet<>();

        // Check for existing grades and collect them
        for (CampusRecruitInfo info : recruitInfoList) {
            existingGrades.add(info.getGrade());
        }

        // Add missing grades for the last 5 years
        for (int year = currentYear; year >= currentYear - 4; year--) {
            String grade = String.valueOf(year);
            if (!existingGrades.contains(grade)) {
                CampusRecruitInfo newInfo = new CampusRecruitInfo();
                newInfo.setGrade(grade);
                newInfo.setEnrollmentNumber(0);
                // Set other properties if necessary
                recruitInfoList.add(newInfo);
            }
        }

        // Sort the list in descending order by grade
        recruitInfoList.sort(Comparator.comparing(CampusRecruitInfo::getGrade));

        if (recruitInfoList.size() > 5) {
            int start = recruitInfoList.size() - 5;
            recruitInfoList.subList(0, start).clear();
        }

        for (int i = 0; i < recruitInfoList.size(); i++){
            log.info("排序后的数据：" + recruitInfoList.get(i).getGrade());
        }

    }

    @RequestMapping(value = "/getRecruitInfo", method = RequestMethod.POST, name = "招生信息-查询分页")
    public PageInfo<Object> getRecruitInfo(@RequestBody HashMap hashMap){

        log.info("进入getRecruitInfo方法："+ JSONObject.toJSONString(hashMap));

        int pageNum = 1;
        int pageSize = 500;
        if(hashMap.containsKey("pageNum")){
            pageNum = (int) hashMap.get("pageNum");
        }
        if(hashMap.containsKey("pageSize")){
            pageSize = (int) hashMap.get("pageSize");
        }
        PageHelper.startPage(pageNum, pageSize);

        CampusRecruitInfo record = new CampusRecruitInfo();
        if(hashMap.containsKey("collegeId")){
            Long collegeId = Long.parseLong(hashMap.get("collegeId").toString());
            record.setCollegeId(collegeId);
        }
        if(hashMap.containsKey("recruitId")){
            Long recruitId = Long.parseLong(hashMap.get("recruitId").toString());
            record.setRecruitId(recruitId);
        }

        List<CampusRecruitInfo> recruitInfoList = campusCollegeRecruitService.getCollegeList(record);

        JSONArray objectArray = new JSONArray();
        for (CampusRecruitInfo campusRecruitInfo : recruitInfoList){
            JSONObject object = new JSONObject();
            String grade = campusRecruitInfo.getGrade();

            long collegeId= campusRecruitInfo.getCollegeId();
            WoSchoolCampusCollege recordTemp = new WoSchoolCampusCollege();
            recordTemp.setCollegeId(collegeId);
            List<WoSchoolCampusCollege> collegeNameList = campusCollegeInfoService.selecCollegeInfo(recordTemp);
            String collegeName = "";
            if(!collegeNameList.isEmpty()){
                collegeName = collegeNameList.get(0).getCollegeName();
            }

            object.put("recruitId", campusRecruitInfo.getRecruitId());
            object.put("collegeId", campusRecruitInfo.getCollegeId());
            object.put("collegeName", collegeName);
            object.put("grade", grade);
            object.put("enrollmentNumber", campusRecruitInfo.getEnrollmentNumber());

            objectArray.add(object);
        }

        PageInfo<Object> pageInfo = new PageInfo<>(objectArray);

        return pageInfo;
    }

    @RequestMapping(value = "/updateRecruitInfo", method = RequestMethod.POST, name = "招生信息-更新信息")
    public CustomResult updateRecruitInfo(@RequestBody HashMap hashMap){

        log.info("进入updateRecruitInfo方法："+JSONObject.toJSONString(hashMap));

        try {

            if(!hashMap.containsKey("recruitId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            CampusRecruitInfo record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), CampusRecruitInfo.class);
            record.setUpdatedTime(new Date());
            campusCollegeRecruitService.updateByPrimaryKeySelective(record);

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"更新失败！");
        }

        return ResultUtil.success();
    }

    @RequestMapping(value = "/saveRecruitInfo", method = RequestMethod.POST, name = "招生信息-保存信息")
    public CustomResult saveRecruitInfo(@RequestBody HashMap hashMap){

        log.info("进入saveRecruitInfo方法："+JSONObject.toJSONString(hashMap));

        try {
            if(!hashMap.containsKey("collegeId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }
            CampusRecruitInfo record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), CampusRecruitInfo.class);
            List<CampusRecruitInfo> recruitInfoList = campusCollegeRecruitService.getCollegeList(record);
            if(recruitInfoList.isEmpty()){

                record.setCreatedTime(new Date());
                campusCollegeRecruitService.insertSelective(record);
/*                List<CampusRecruitInfo> recruitInfoListTemp = campusCollegeRecruitService.getCollegeList(record);
                CampusRecruitInfo campusRecruitInfo= recruitInfoListTemp.get(0);
                JSONObject object = new JSONObject();
                object.put("recruitId", campusRecruitInfo.getRecruitId());
                return ResultUtil.successByJSONObject(object);*/
                return ResultUtil.success();

            }else{
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"新增数据已经存在！");
            }

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"保存失败！");
        }

    }

    @RequestMapping(value = "/deleteRecruitInfo", method = RequestMethod.POST, name = "招生信息-删除信息")
    public CustomResult deleteRecruitInfo(@RequestBody HashMap hashMap){

        log.info("进入deleteRecruitInfo方法："+JSONObject.toJSONString(hashMap));

        try {

            if(!hashMap.containsKey("recruitId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            CampusRecruitInfo record = new CampusRecruitInfo();
            Long recruitId = Long.parseLong(hashMap.get("recruitId").toString());
            record.setRecruitId(recruitId);

            campusCollegeRecruitService.deleteSelective(record);

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"删除失败！");
        }

        return ResultUtil.success();
    }

    @RequestMapping(value = "/getDepartmentList", method = RequestMethod.POST, name = "查询院系列表")
    public CustomResult getDepartmentList(@RequestBody HashMap hashMap){

        log.info("进入getCampusList方法："+ JSONObject.toJSONString(hashMap));
        if(!hashMap.containsKey("campusId")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }

        Long campusId = Long.parseLong(hashMap.get("campusId").toString());

        WoSchoolCampusCollege record = new WoSchoolCampusCollege();
        record.setCampusId(campusId);
        record.setState(1);
        List<WoSchoolCampusCollege>  recordList = campusCollegeInfoService.getCollegeList(record);

        if(recordList.isEmpty()){
            return ResultUtil.error(ResultEnum.USER_NOT_EXIST.getrespCode(),"未查询到院系列表！");
        }

        JSONArray objectArray = new JSONArray();
        for (WoSchoolCampusCollege woSchoolCampusCollege : recordList){
            JSONObject object = new JSONObject();
            String collegeName = woSchoolCampusCollege.getCollegeName();
            String collegeId = woSchoolCampusCollege.getCollegeId().toString();
            object.put("collegeName", collegeName);
            object.put("collegeId", collegeId);
            objectArray.add(object);
        }

        return ResultUtil.successByJSONArray(objectArray);
    }

    @RequestMapping(value = "/getDepartmentInfo", method = RequestMethod.POST, name = "院系信息-院系清单")
    public PageInfo<Object>  getDepartmentInfo(@RequestBody HashMap hashMap){

        log.info("进入getCollegeInfo方法："+ JSONObject.toJSONString(hashMap));

        Long campusId = Long.parseLong(hashMap.get("campusId").toString());

        int pageNum = 1;
        int pageSize = 1000;
        if(hashMap.containsKey("pageNum")){
            pageNum = (int) hashMap.get("pageNum");
        }
        if(hashMap.containsKey("pageSize")){
            pageSize = (int) hashMap.get("pageSize");
        }

        PageHelper.startPage(pageNum, pageSize);

        List<CollegeInfoResult>  collegeInfoResultList = campusCollegeInfoService.selectCollegeInfo(campusId);

        JSONArray objectArray = new JSONArray();
        for (CollegeInfoResult collegeInfoResult : collegeInfoResultList){
            JSONObject object = new JSONObject();

            String competitionType = dictFacade.getNameByKey("cr_competition_type", collegeInfoResult.getCompetitionType());
            String keyPeoplePosition = dictFacade.getNameByKey("keyPeoplePosition", collegeInfoResult.getKeyPeoplePosition());
            object.put("campusId", collegeInfoResult.getCampusId());
            object.put("collegeId", collegeInfoResult.getCollegeId());
            object.put("collegeName", collegeInfoResult.getCollegeName());
            object.put("campusName", collegeInfoResult.getCampusName());
            object.put("keyPeopleName", collegeInfoResult.getKeyPeopleName());
            object.put("keyPeoplePositionGbk", keyPeoplePosition);
            object.put("competitionTypeGbk", competitionType);
            object.put("keyPeoplePosition", collegeInfoResult.getKeyPeoplePosition());
            object.put("competitionType", collegeInfoResult.getCompetitionType());

            objectArray.add(object);
        }

        PageInfo<Object> pageInfo = new PageInfo<>(objectArray);

        return pageInfo;
    }

    @RequestMapping(value = "/saveDepartmentInfo", method = RequestMethod.POST, name = "院系信息-保存信息")
    public CustomResult saveDepartmentInfo(@RequestBody HashMap hashMap){

        log.info("进入saveCollegeInfo方法："+JSONObject.toJSONString(hashMap));

        try {

            if(!hashMap.containsKey("campusId") || !hashMap.containsKey("collegeName") || !hashMap.containsKey("createdBy")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            WoSchoolCampusCollege record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), WoSchoolCampusCollege.class);
            long recruitInfoList =campusCollegeInfoService.countCollegeExtendInfo(record);

            if( recruitInfoList == 0){


                List<WoSchoolCampusCollege> recordList = campusCollegeInfoService.getCollegeList(record);
                for (WoSchoolCampusCollege woSchoolCampusCollege : recordList){
                    campusCollegeInfoService.deleteCollegeExtendInfo(woSchoolCampusCollege.getCollegeId());
                    campusCollegeInfoService.deleteCollegeInfo(woSchoolCampusCollege.getCollegeId());
                }

                record.setCreatedTime(new Date());
                record.setUpdatedTime(new Date());
                record.setUpdatedBy(record.getCreatedBy());
                campusCollegeInfoService.saveCollegeInfo(record);


                CampusCollegeExtend recordTemp = JSONObject.parseObject(JSONObject.toJSONString(hashMap), CampusCollegeExtend.class);
                recordTemp.setCollegeId(record.getCollegeId());
                recordTemp.setCreatedTime(new Date());
                recordTemp.setCreatedBy(record.getCreatedBy());
                campusCollegeInfoService.saveCollegeExtendInfo(recordTemp);

                return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "新增数据成功");

            }else{
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"新增数据已经存在！");
            }

        }catch (Exception e){
            e.printStackTrace();
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"保存失败！");
        }

    }


    @RequestMapping(value = "/updateDepartmentInfo", method = RequestMethod.POST, name = "院系信息-更新信息")
    public CustomResult updateDepartmentInfo(@RequestBody HashMap hashMap){

        log.info("进入saveCollegeInfo方法："+JSONObject.toJSONString(hashMap));

        try {
            if(!hashMap.containsKey("collegeId") || !hashMap.containsKey("updatedBy")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            WoSchoolCampusCollege record = JSONObject.parseObject(JSONObject.toJSONString(hashMap), WoSchoolCampusCollege.class);
            record.setUpdatedTime(new Date());
            campusCollegeInfoService.updateCollegeInfo(record);

            CampusCollegeExtend recordTemp = JSONObject.parseObject(JSONObject.toJSONString(hashMap), CampusCollegeExtend.class);
            recordTemp.setUpdatedTime(new Date());
            campusCollegeInfoService.updateCollegeExtendInfo(recordTemp);

            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "更新数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"更新失败！");
        }

    }

    @RequestMapping(value = "/deleteDepartmentInfo", method = RequestMethod.POST, name = "院系信息-删除信息")
    public CustomResult deleteDepartmentInfo(@RequestBody HashMap hashMap){

        log.info("进入saveCollegeInfo方法："+JSONObject.toJSONString(hashMap));

        try {

            if(!hashMap.containsKey("collegeId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            Long collegeId = Long.parseLong(hashMap.get("collegeId").toString());
            // 删除院系信息
            campusCollegeInfoService.deleteCollegeInfo(collegeId);
            campusCollegeInfoService.deleteCollegeExtendInfo(collegeId);

            // 删除院系招生信息
            CampusRecruitInfo record = new CampusRecruitInfo();
            record.setCollegeId(collegeId);
            campusCollegeRecruitService.deleteSelective(record);

            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "删除数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"删除失败！");
        }

    }


}
