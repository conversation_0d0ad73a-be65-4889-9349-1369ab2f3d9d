package com.jsunicom.oms.po.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
public class OrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    //tf_ord_main
    private String orderId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    private String realAmount;
    private String orderAmount;
    private String payResult;
    private String cityCode;
    private String cityName;
    private String orderState;
    private String dealStaffId;
    private String dealStaffName;
    private String dealStaffPhone;
    private String channelId;
    private String busiType;
    private String goodsName;
    private String mainNumber;
    private String iccid;
    private String custName;
    private String phone;
    private String address;
    private String orderNodeState;
    private String orderNodeCode;
    private String orderNodeName;
    private String operTag;
    private String isAudit;
    private String isRefund;
    private String isDetail;
    private String campusName;
    //是否可分配
    private String isShare;

    //本异网标识
    private String numStatus;

    //下单人工号
    private String staffId;
    private String developerName;
    private String receiveProvinceCode;
    private String receiveCityCode;
    private String receiveCountyCode;
    private String qq;

}
