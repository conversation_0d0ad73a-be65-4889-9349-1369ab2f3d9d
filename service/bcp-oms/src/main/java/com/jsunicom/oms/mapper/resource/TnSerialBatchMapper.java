package com.jsunicom.oms.mapper.resource;

import com.jsunicom.oms.po.resource.TnSerialBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface TnSerialBatchMapper {

    int insertTnSerialBatch(TnSerialBatch tnSerialBatch);

    int updateTnSerialBatch(TnSerialBatch tnSerialBatch);

    List<TnSerialBatch> qryTnSerialBatchList(TnSerialBatch tnSerialBatch);

    String qryResultFilePath(@Param("id") String id);

    /**
     * 校验号池是否存在在途的导入任务
     * @param poolCode 号码编码
     * @return 返回不为空表示有在途任务，为空无在途任务
     * */
    List<Integer> checkProcessingTask(@Param("poolCode") String poolCode);

}
