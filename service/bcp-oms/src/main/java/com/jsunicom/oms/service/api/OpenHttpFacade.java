package com.jsunicom.oms.service.api;

import com.alibaba.fastjson.JSONObject;

/**
 * java类简单作用描述
 *
 * @ProjectName: bcp
 * @Package: com.mucfc.bcp.core.partner.facade
 * @ClassName: ${TYPE_NAME}
 * @Description: java类作用描述
 * @Author: 作者姓名
 * @CreateDate: 2018/11/14 17:08
 * @UpdateUser: <PERSON><PERSON>Zhou
 * @UpdateDate: 2018/11/14 17:08
 * @UpdateRemark: The modified content
 * @Version: 1.0
 * <p>Copyright: Copyright (c) 2018</p>
 */
public interface OpenHttpFacade {
    String send2HQ(String url, JSONObject requestJson, String systemCharSet, String requestCharSet) throws Exception;
    //忽略证书
    String send2HQignorSSL(String url, JSONObject requestJson, String systemCharSet, String requestCharSet) throws Exception;
    //使用于校园
    String send2HQCampus(String url, JSONObject requestJson, String systemCharSet, String requestCharSet,String nlptLcdpAccessToken) throws Exception;
    String send2LP(String url, JSONObject requestJson, String systemCharSet, String requestCharSet) throws Exception;
}
