package com.jsunicom.oms.service;

import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.model.partner.PartnerRegister;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.service
 * @ClassName: PartnerRegisterFacade
 * @Author: z<PERSON><PERSON>
 * @CreateTime: 2023-03-18  20:06
 * @Description: TODO
 * @Version: 1.0
 */
public interface PartnerRegisterFacade {
    List<PartnerRegister> findByPhoneAndInitState(String mstaffId);
    void doSychroCheckResult(PartnerRegister partnerRegister);
    void doSychroCheckResultToLP(Partner partner);
    void update(PartnerRegister partnerRegister);
}
