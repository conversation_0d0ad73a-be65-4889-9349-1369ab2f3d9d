package com.jsunicom.oms.model.woshcool;


import com.jsunicom.oms.model.base.BasicModel;

/**
 * Created by delina-tt on 2018/12/24.
 */
public class WoSchoolInfoDto extends BasicModel {
    private Long id;
    private Long merchantId;
    private String merchantName;
    private String legRepName;
    private String legRepPhone;
    private String loginLogo;
    private String wlcmIntroduce;
    private String wlcmLogo;
    private String prodOperId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getLegRepName() {
        return legRepName;
    }

    public void setLegRepName(String legRepName) {
        this.legRepName = legRepName;
    }

    public String getLegRepPhone() {
        return legRepPhone;
    }

    public void setLegRepPhone(String legRepPhone) {
        this.legRepPhone = legRepPhone;
    }

    public String getLoginLogo() {
        return loginLogo;
    }

    public void setLoginLogo(String loginLogo) {
        this.loginLogo = loginLogo;
    }

    public String getWlcmIntroduce() {
        return wlcmIntroduce;
    }

    public void setWlcmIntroduce(String wlcmIntroduce) {
        this.wlcmIntroduce = wlcmIntroduce;
    }

    public String getWlcmLogo() {
        return wlcmLogo;
    }

    public void setWlcmLogo(String wlcmLogo) {
        this.wlcmLogo = wlcmLogo;
    }

    public String getProdOperId() {
        return prodOperId;
    }

    public void setProdOperId(String prodOperId) {
        this.prodOperId = prodOperId;
    }
}
