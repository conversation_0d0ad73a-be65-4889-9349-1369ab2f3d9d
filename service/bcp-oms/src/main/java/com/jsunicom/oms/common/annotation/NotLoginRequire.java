package com.jsunicom.oms.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.oms.common.annotation
 * @ClassName: NotLoginRequire
 * @Author: z<PERSON>wang
 * @CreateTime: 2023-03-24  11:02
 * @Description: TODO 不需要登陆鉴权的方法或类
 * @Version: 1.0
 */
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface NotLoginRequire {
}
