package com.jsunicom.common.core.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class IdUtil {

    /**
     * 生成订单号
     * @return eg: 3422021592887281
     */
    public static Long getId() {
        StringBuilder builder = new StringBuilder("34");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        LocalDate localDate = LocalDate.now();
        String dateString = formatter.format(localDate);

        String timestampString = String.valueOf(System.currentTimeMillis()).substring(5);
        builder.append(dateString).append(timestampString);
        return Long.valueOf(builder.toString());
    }

    /**
     * * 生成订单号
     * @return eg:2212272132486755
     */
    public static String getOrderId() {
        StringBuilder builder = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        LocalDate localDate = LocalDate.now();
        String dateString = formatter.format(localDate);

        String timestampString = String.valueOf(System.currentTimeMillis()).substring(3);
        builder.append(dateString).append(timestampString);
        return builder.toString();
    }

    /**
     * 生成订单号
     * @return 202206251555100020114
     */
    public static String getOrderIdNew() {
        StringBuilder builder = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate localDate = LocalDate.now();
        String dateString = formatter.format(localDate);
        System.out.println(System.currentTimeMillis());
        String timestampString = String.valueOf(System.currentTimeMillis());
        builder.append(dateString).append(timestampString);
        return builder.toString();
    }
}
