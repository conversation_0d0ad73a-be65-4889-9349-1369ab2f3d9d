package com.jsunicom.common.core.entity.po;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
public class TdOrdSmsRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;
    /**
     *业务场景
     */
    private String senceType;
    /**
     *订单来源
     */
    private String orderSource;
    /**
     *订单节点
     */
    private String orderNode;
    /**
     *短信接收类型  1、用户 2、装维 3、2I专员
     */
    private String receiverType;
    /**
     *订单是否在线支付且金额>0
     */
    private String orderIsPayOnline;
    /**
     *短信模板ID
     */
    private String msgId;
}
