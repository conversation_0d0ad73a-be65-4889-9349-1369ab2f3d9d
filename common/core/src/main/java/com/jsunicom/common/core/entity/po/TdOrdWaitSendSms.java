package com.jsunicom.common.core.entity.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * (TdOrdWaitSendSms)实体类
 * <AUTHOR>
 * @since 2021-02-20 10:24:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TdOrdWaitSendSms implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Integer id;
    
    private String orderId;
    
    private String msgId;
    
    private String orderNode;
    
    private Date operateTime;

    private String requestMsg;
    
    private String sendFlag;
}
