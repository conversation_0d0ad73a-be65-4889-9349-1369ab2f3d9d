package com.jsunicom.common.core.exception;


import com.jsunicom.common.core.util.Result;

/**
 * <AUTHOR>
 * @Title: BusinessException
 * @ProjectName credit_control_boot
 * @Description: 业务异常
 * @date 2019/4/269:46
 */
public class BusinessException extends RuntimeException {

    private BusinessException() {
    }

    private Result result;

    public BusinessException(String codeEn) {
        this.result = new Result(codeEn);
    }


    public Result getResult() {
        result.setSuccess(false);
        return result;
    }

}
