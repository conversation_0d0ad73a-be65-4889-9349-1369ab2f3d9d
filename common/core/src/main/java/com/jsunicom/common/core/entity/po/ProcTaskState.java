package com.jsunicom.common.core.entity.po;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 2022/2/17 18:31.
 */
public enum ProcTaskState {

    CURRENT_TASK(2,"当前环节"),
    HISTORY_TASK(3,"历史环节"),
    OTHER(4,"退单切换到其他工作流");


    /**
     * 错误类型码
     */
    private Integer code;
    /**
     * 错误类型描述信息
     */
    private String name;



    ProcTaskState(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
