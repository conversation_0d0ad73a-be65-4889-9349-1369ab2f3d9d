package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:48
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrderDeliver extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private Long orderLineId;
    /**
     * 配送方式 ,10:物流，20：直提；30：自提
    */
	private String postType;
    /**
     * 
    */
	private String addressType;
    /**
     * 
    */
	private String postName;
    /**
     * 收件省
    */
	private String postProvince;
    /**
     * 证件类型表： td_ord_cert_type
    */
	private String postCertType;
    /**
     * 
    */
	private String postCertId;
    /**
     * 
    */
	private String postMobile1;
    /**
     * 
    */
	private String postMobile2;
    /**
     * 收件市，例如“南京”
    */
	private String postCity;
    /**
     * 收件区县，例如“雨花区”
    */
	private String postDist;
    /**
     * 
    */
	private String postStreet;
    /**
     * 收件省份名称
    */
	private String postProvinceName;
    /**
     * 收件地市名称
    */
	private String postCityName;
    /**
     * 收件区县名称
    */
	private String postDistName;
    /**
     * 邮政编码
    */
	private String postCode;
    /**
     * 
    */
	private String postAddress;
    /**
     * 期望配送时间
    */
	private Date postExpectTime;
    /**
     * 实际配送时间
    */
	private Date postActualTime;
    /**
     * 自提、直提点编码
    */
	private String selfPickupCode;
    /**
     * 自提、直提点名称
    */
	private String selfPickupName;
    /**
     * 快递公司
    */
	private String postCompany;
    /**
     * 
    */
	private String postCompanyCode;
    /**
     * 
    */
	private String postRemark;
    /**
     * 
    */
	private String provinceCode;
    /**
     * 
    */
	private String pickupCode;
    /**
     * 
    */
	private String deliverState;
    /**
     * 
    */
	private Date deliverTime;
    /**
     * 
    */
	private Date expectStartTime;
    /**
     * 
    */
	private Date expectEndTime;
    /**
     * 01：不限时间 02：只工作日 03：只有双休日、节假日 04：不送货
    */
	private String expectDeliverDateType;
    /**
     * 
    */
	private String extProvince;
    /**
     * 
    */
	private String extCity;
    /**
     * 
    */
	private String extDist;
    /**
     * 0 本地 1 异地
    */
	private String crossCity;
    /**
     * 
    */
	private String email;
    /**
     * 
    */
	private String phoneState;
    /**
     * 定时配送开始时间
    */
	private Date fixDelvTime;
    /**
     * 定时配送结束时间
    */
	private Date fixDelvEndTime;
    /**
     * 订单明细报表去重标识
    */
	private String repeatTag;
}