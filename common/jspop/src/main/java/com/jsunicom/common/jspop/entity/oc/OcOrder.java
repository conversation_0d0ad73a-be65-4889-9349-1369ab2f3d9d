package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户订单表类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:48
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrder extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private String inModeCode;
    /**
     * 订单生成时间
    */
	private Date createDate;
    /**
     * 客户自助下单的订单受理人默认为接入渠道的工号
    */
	private String staffId;
    /**
     * 
    */
	private String extOrderId;
    /**
     * 下单时间
    */
	private Date orderTime;
    /**
     * 电商新增：01在线支付 02货到付款 03线下现场付款 05白条支付 06计费后收
    */
	private String payMode;
    /**
     * 0:未支付 1:已支付
    */
	private String isPay;
    /**
     * 电商新增:0通过1不通过
    */
	private String checkFlag;
    /**
     * 00:已入库 01:已支付 02:已物流 03:已签收 80:退款已申请 81:退款已审核 82:已退款 91:退货 92:换货 99:退/换货锁定中
    */
	private String orderState;
    /**
     * 
    */
	private String cityCode;
    /**
     * 
    */
	private String eparchyCode;
    /**
     * 
    */
	private String provinceCode;
    /**
     * 
    */
	private String departId;
    /**
     * 
    */
	private String channelId;
    /**
     * 
    */
	private String channelType;
    /**
     * 
    */
	private String userAreaCode;
    /**
     * 
    */
	private String memberId;
    /**
     * 
    */
	private String remark;
    /**
     * 
    */
	private String merchantId;
    /**
     * 商盟id
    */
	private String businessLeagueId;
    /**
     * 
    */
	private String fromSys;
    /**
     * 
    */
	private String busChannel;
    /**
     * 
    */
	private String busiCustAcct;
    /**
     * 
    */
	private String custCertAddr;
    /**
     * 
    */
	private Date custCertEndDate;
    /**
     * 
    */
	private String custCertId;
    /**
     * 
    */
	private String custCertType;
    /**
     * 
    */
	private String custName;
    /**
     * 
    */
	private String custPhone;
    /**
     * 
    */
	private String storeCode;
    /**
     * 
    */
	private String storeName;
    /**
     * 
    */
	private String thirdMailno;
}