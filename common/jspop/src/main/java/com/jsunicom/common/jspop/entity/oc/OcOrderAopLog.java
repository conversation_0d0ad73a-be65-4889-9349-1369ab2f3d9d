package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:48
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrderAopLog extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private Long orderLineId;
    /**
     * 
    */
	private String provinceCode;
    /**
     * 
    */
	private Date requestDate;
    /**
     * 
    */
	private String requestMsg;
    /**
     * 
    */
	private Date responseDate;
    /**
     * 
    */
	private String responseMsg;
    /**
     * 
    */
	private String url;
    /**
     * 
    */
	private String errorMsg;
    /**
     * 调用状态。0成功，1失败
    */
	private String callState;
    /**
     * 业务返回码
    */
	private String respCode;
    /**
     * 业务返回描述
    */
	private String respDesc;
}