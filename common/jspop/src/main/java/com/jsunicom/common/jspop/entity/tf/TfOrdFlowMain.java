package com.jsunicom.common.jspop.entity.tf;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-15 09:45:35
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TfOrdFlowMain extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 流程实例ID
    */
	private String procInstId;
    /**
     * 
    */
	private Long orderId;
    /**
     * 业务标识
    */
	private String procKey;
    /**
     * 流程定义ID
    */
	private String procDefId;
    /**
     * 
    */
	private Integer procInstState;
    /**
     * 
    */
	private Date startTime;
    /**
     * 
    */
	private Date endTime;
}