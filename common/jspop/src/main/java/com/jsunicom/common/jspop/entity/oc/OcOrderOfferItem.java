package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-01 10:33:00
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrderOfferItem extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderLineId;
    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private Long offerId;
    /**
     * 
    */
	private Long itemId;
    /**
     * 
    */
	private String attrType;
    /**
     * 
    */
	private String attrCode;
    /**
     * 
    */
	private String attrValue;
    /**
     * 
    */
	private String attrName;
    /**
     * 
    */
	private String attrValueName;
    /**
     * 
    */
	private Date startDate;
    /**
     * 结束时间
    */
	private Date endDate;
    /**
     * 
    */
	private String modifyTag;
    /**
     * 
    */
	private String provinceCode;
}