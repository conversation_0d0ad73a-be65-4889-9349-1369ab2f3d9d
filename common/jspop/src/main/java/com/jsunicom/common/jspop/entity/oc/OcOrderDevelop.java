package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:48
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrderDevelop extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private Long orderLineId;
    /**
     * 0：用户 1：商品 2：能人
    */
	private String subTypeCode;
    /**
     * 类型值，根据类型编码确定值的含义。 如果类型编码是用户则这里放的是用户标识，如果是商品则放的是商品编码
    */
	private String subTypeValue;
    /**
     * 
    */
	private Date developDate;
    /**
     * 
    */
	private String developManagerId;
    /**
     * 
    */
	private String developManagerName;
    /**
     * 
    */
	private String developStaffId;
    /**
     * 
    */
	private String developStaffName;
    /**
     * 
    */
	private String developNickName;
    /**
     * 
    */
	private String developNickId;
    /**
     * 
    */
	private String developContact;
    /**
     * 
    */
	private String provinceCode;
    /**
     * 
    */
	private String developCity;
    /**
     * 
    */
	private String developDepartName;
    /**
     * 
    */
	private String developDepartId;
    /**
     * 
    */
	private String standardKindCode;
    /**
     * 状态属性：0－增加  1－删除  2－修改
    */
	private String modifyTag;
    /**
     * 
    */
	private String developType;
    /**
     * 
    */
	private String developProvinceCode;
}