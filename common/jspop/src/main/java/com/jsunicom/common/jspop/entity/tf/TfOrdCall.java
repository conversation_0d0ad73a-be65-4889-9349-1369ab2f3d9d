package com.jsunicom.common.jspop.entity.tf;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单外呼请求类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:49
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TfOrdCall extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
    */
	private Long messageId;
    /**
     * 订单编码
    */
	private Long orderId;
    /**
     * 主叫电话
    */
	private String callerNumber;
    /**
     * 被叫电话
    */
	private String calledNumber;
    /**
     * 主叫来电号码
    */
	private String callerDisplayNumber;
    /**
     * 被叫来电号码
    */
	private String calledDisplayNumber;
    /**
     * 申请返回状态编码
    */
	private String callCode;
    /**
     * 申请返回原因描述
    */
	private String callMsg;
    /**
     * 呼叫中心通话流水号
    */
	private String callId;
    /**
     * 呼叫备注
    */
	private String callRemark;
    /**
     * 创建工号
    */
	private String createStaffId;
    /**
     * 创建时间
    */
	private String createDate;
    /**
     * 更新工号
    */
	private String updateStaffId;
    /**
     * 更新时间
    */
	private String updateDate;
    /**
     * 呼叫系统
    */
	private String callSystem;
    /**
     * 环节名称
    */
	private String taskName;
    /**
     * 环节定义名
    */
	private String taskKey;
}