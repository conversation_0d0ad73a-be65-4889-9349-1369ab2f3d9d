package com.jsunicom.common.jslt.identity.dto;

import lombok.Data;

/**
 * @BelongsProject: 20230926
 * @BelongsPackage: com.jsunicom.common.jslt.identity.dto
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2024-05-15  17:11
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class ManagerInfoDto {
    private String manager_name;
    private String manager_phone;
    private String province_code;
    private String province_name;
    private String city_code;
    private String city_name;
    private String campus_name;
    private String addType;
    private String old_manager_name;
    private String old_manager_phone;
}
