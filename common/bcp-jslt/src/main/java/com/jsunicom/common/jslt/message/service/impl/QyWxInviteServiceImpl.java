package com.jsunicom.common.jslt.message.service.impl;


import com.jsunicom.common.jslt.message.model.dto.QyWxInviteDto;
import com.jsunicom.common.jslt.message.service.QyWxInviteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Created by wentao on 2018/3/5.
 **/
@Slf4j
@Service("qyWxInviteServiceImpl")
public class QyWxInviteServiceImpl extends QyWxServiceImplBase implements QyWxInviteService {
    @Override
    public void invite(QyWxInviteDto qyWxInviteDto) {

    }

//    @Autowired
//    private ApiMemberService apiMemberService;
//
//    @Value("${wx.appid}")
//    private String appid;
//
//    @Override
//    public void invite(QyWxInviteDto qyWxInviteDto) {
//        QyBatchInviteMemberRequest request = buildRequest(qyWxInviteDto);
//        QyBatchInviteMemberResponse response = apiMemberService.inviteBatchMember(request);
//        if (!StringUtils.equals(response.getErrCode(), "0")) {
//            throw new BusinessException(response.getErrCode(), response.getErrMsg());
//        }
//    }
//
//    private QyBatchInviteMemberRequest buildRequest(QyWxInviteDto dto) {
//        QyBatchInviteMemberRequest request = new QyBatchInviteMemberRequest();
//        List<String> user = dto.getUsers();
//        List<String> party = dto.getParties();
//        List<String> tag = dto.getTags();
//        request.setUser(user);
//        request.setParty(party);
//        request.setTag(tag);
//        request.setAppId(appid);
//        return request;
//    }


}
