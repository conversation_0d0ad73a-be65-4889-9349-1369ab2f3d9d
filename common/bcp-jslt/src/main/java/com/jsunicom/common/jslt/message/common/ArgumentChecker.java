package com.jsunicom.common.jslt.message.common;

import com.jsunicom.common.jslt.message.common.exception.CgwErrorCodes;
import com.lz.lsf.exception.BusinessException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

public final class ArgumentChecker {

    /**
     * 检查参数不能为null
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void notNull(String argumentName, Object argument) {
        if (argument == null) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查参数必须为null
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void isNull(String argumentName, Object argument) {
        if (argument != null) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查条件是否成立
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @param expression 条件判断
     * @throws BusinessException  WRONG_ARGUMENT
     */
    public static void isTrue(String argumentName, Object argument, boolean expression) {
        if (!expression) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查文本参数长度不为0
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void hasLength(String argumentName, String argument) {
        if (!StringUtils.hasLength(argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查文本参数长度不为0，且有可视字符内容
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void hasText(String argumentName, String argument) {
        if (!StringUtils.hasText(argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查数组参数不为null，并且至少有一个元素
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void notEmpty(String argumentName, Object[] argument) {
        if (ObjectUtils.isEmpty(argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查集合参数不为null，并且至少有一个元素
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void notEmpty(String argumentName, Collection<?> argument) {
        if (CollectionUtils.isEmpty(argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查集合参数不为null，并且至少有一个元素
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void notEmpty(String argumentName, Map<?, ?> argument) {
        if (CollectionUtils.isEmpty(argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查数组参数不含有任何null元素。注意，数组参数如果是null或是空数组，会被忽略
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @throws BusinessException WRONG_ARGUMENT
     */
    public static void noNullElements(String argumentName, Object[] argument) {
        if (argument != null) {
            for (Object element : argument) {
                if (element == null) {
                    throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
                }
            }
        }
    }

    /**
     * 检查参数是否满足正则表达式
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @param regex 正规表达式
     */
    public static void match(String argumentName, String argument, String regex) {
        if (argument == null) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }

        if (!Pattern.matches(regex, argument)) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }

    /**
     * 检查参数是否大于某个值
     * 
     * @param argumentName 参数名称，用于指示问题参数
     * @param argument 参数值
     * @param value 比较值
     */
    public static void biggerThan(String argumentName, int argument, int value) {
        if (argument <= value) {
            throw new BusinessException(CgwErrorCodes.WRONG_ARGUMENT, argumentName, argument);
        }
    }
}
