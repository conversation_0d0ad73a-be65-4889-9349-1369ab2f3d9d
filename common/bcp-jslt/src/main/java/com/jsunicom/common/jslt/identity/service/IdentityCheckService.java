package com.jsunicom.common.jslt.identity.service;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/8.
 */

import com.alibaba.fastjson.JSONObject;
import com.asiainfo.aaop.utils.EncryptUtil;
import com.jsunicom.common.jslt.identity.utils.BlowfishUtil;
import com.jsunicom.common.jslt.identity.utils.HttpCall;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class IdentityCheckService {

    private static org.slf4j.Logger logger = LoggerFactory.getLogger(IdentityCheckService.class);

    //内网
    @Value("${identity.URL_L}")
    String URL_L ;//= "http://132.224.15.139:8088/aaop/qryNormalEjb";
    //外网地址
    @Value("${identity.URL_W}")
    String URL_W ;//= "http://122.194.14.181:8088/aaop/qryNormalEjb";
    @Value("${identity.APP_SECRET}")
    String APP_SECRET ;//= "B931DD1CF4958758F4CCA7348CA0D893";
    @Value("${identity.APP_KEY}")
    String APP_KEY ;//= "1000000220150623165403TFLD";
    @Value("${identity.ROUTE_EPARCHY_CODE}")
    String ROUTE_EPARCHY_CODE ;
    @Value("${identity.PROVINCE_CODE}")
    String PROVINCE_CODE ;
    @Value("${identity.EPARCHY_CODE}")
    String EPARCHY_CODE ;
    @Value("${identity.SYS_CODE}")
    String SYS_CODE ;

    @Value("${identitySecondUrl}")
    private String identitySecondUrl;

    @Value("${identitySecondAppKey}")
    private String identitySecondAppKey;

    @Value("${identitySecondAppSecret}")
    private String identitySecondAppSecret;

    /**
     * 国政通身份证校验能力平台一期
     * @param CUST_NAME 姓名
     * @param PSPT_ID 身份证号码
     * @return true or false
     */
    public boolean identityCheck(String CUST_NAME , String PSPT_ID,String CHANNEL_ID,String CHANNEL_TYPE){
        logger.info(">>>>>>>>>>>>>>>>调用江苏联通接口身份校验>>>>>>>>>>>>>>>>");
        BlowfishUtil crypt = new BlowfishUtil(APP_KEY);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = sdf.format(new Date());
        JSONObject req = new JSONObject();
        JSONObject input = new JSONObject();
        JSONObject head = new JSONObject();
        JSONObject body = new JSONObject();
        JSONObject $ = new JSONObject();
        head.put("TRANS_REQ_ID", dateString);
        head.put("PROCESS_TIME", dateString);
        head.put("APP_SECRET", APP_SECRET);
        head.put("APP_KEY", APP_KEY);
        $.put("OPER_TYPE_CODE", "02");
        switch(PSPT_ID.length()){
            case 15 : $.put("ID_TYPE", "1");break;//身份证类型
            case 17 : $.put("ID_TYPE", "2");break;//身份证类型
            case 18 : $.put("ID_TYPE", "3");break;//身份证类型
            default : return false;
        }
        $.put("ROUTE_EPARCHY_CODE", ROUTE_EPARCHY_CODE);
        $.put("PSPT_ID", PSPT_ID);
        $.put("PROVINCE_CODE", PROVINCE_CODE);
        $.put("EPARCHY_CODE", EPARCHY_CODE);
        $.put("CUST_NAME", CUST_NAME);
        $.put("SYS_CODE", SYS_CODE);
        $.put("TRADE_TYPE", "identityCheck");
//        $.put("CHANNEL_ID", CHANNEL_ID);
//        $.put("CHANNEL_TYPE", CHANNEL_TYPE);
        body.put("$", $);
        input.put("HEAD", head);
        input.put("BODY", crypt.encryptString($.toJSONString()));
        req.put("INPUT", input);

        try {
            logger.info("调用江苏联通接口身份校验，入参：CUST_NAME={},PSPT_ID={},CHANNEL_ID={},CHANNEL_TYPE={},BODY={},request={}", CUST_NAME,PSPT_ID,CHANNEL_ID,CHANNEL_TYPE,$.toJSONString(),req.toJSONString());
            String responseJson = new HttpCall().httpPostWithJSON(URL_L, req, "UTF-8", "UTF-8");
            logger.info(">>>>>>>>>调用江苏联通接口身份校验返回结果{}", responseJson);
            JSONObject json = JSONObject.parseObject(responseJson);

            String resultBody = crypt.decryptString(json.getJSONObject("OUTPUT").getString("BODY"));

            json = JSONObject.parseObject(resultBody);
            String result = json != null ?  json.getString("RESP_CODE") : null;

            if("0000".equals(result)){
                //成功
                logger.info(">>>>>>>>>调用江苏联通接口身份校验成功");
                return true;
            }else{
                return false;
            }
        } catch (Exception ex) {
            logger.info(">>>>>>>>>>>调用江苏联通接口身份校验失败：exception={}", ex.getMessage());
            return false;
        }


    }

    /**
     * 国政通身份证校验能力平台二期
     * @param CUST_NAME 姓名
     * @param PSPT_ID 身份证号码
     * @return true or false
     */
    public boolean identityCheck2(String CUST_NAME , String PSPT_ID,String CHANNEL_ID,String CHANNEL_TYPE){
        logger.info(">>>>>>>>>>>>>>>>调用江苏联通接口身份校验>>>>>>>>>>>>>>>>");
        BlowfishUtil crypt = new BlowfishUtil(APP_KEY);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = sdf.format(new Date());
        //JSONObject req = new JSONObject();
        JSONObject input = new JSONObject();
        JSONObject head = new JSONObject();
        //JSONObject body = new JSONObject();
        JSONObject $ = new JSONObject();
        head.put("TRANS_REQ_ID", dateString);
        head.put("PROCESS_TIME", dateString);
        head.put("APP_SECRET", identitySecondAppSecret);
        head.put("APP_ID", identitySecondAppKey);
        $.put("OPER_TYPE_CODE", "02");
        switch(PSPT_ID.length()){
            case 15 : $.put("ID_TYPE", "1");break;//身份证类型
            case 17 : $.put("ID_TYPE", "2");break;//身份证类型
            case 18 : $.put("ID_TYPE", "3");break;//身份证类型
            default : return false;
        }
        $.put("ROUTE_EPARCHY_CODE", ROUTE_EPARCHY_CODE);
        $.put("PSPT_ID", PSPT_ID);
        $.put("PROVINCE_CODE", PROVINCE_CODE);
        $.put("EPARCHY_CODE", EPARCHY_CODE);
        $.put("CUST_NAME", CUST_NAME);
        $.put("SYS_CODE", SYS_CODE);
        $.put("TRADE_TYPE", "identityCheck");
        $.put("CHANNEL_ID", CHANNEL_ID);
        $.put("CHANNEL_TYPE", CHANNEL_TYPE);
        //body.put("$", $);
        input.put("UNI_BSS_HEAD", createRequestHead(identitySecondAppKey));
        //input.put("BODY", crypt.encryptString($.toJSONString()));
        //body.put("$", crypt.encryptString($.toJSONString()));
        input.put("UNI_BSS_BODY", $);
        //req.put("INPUT", input);
        EncryptUtil encryptUtil=new EncryptUtil();
        try {
            logger.info("调用江苏联通身份校验接口，入参：CUST_NAME={},PSPT_ID={},CHANNEL_ID={},CHANNEL_TYPE={},BODY={},request={}", CUST_NAME,PSPT_ID,CHANNEL_ID,CHANNEL_TYPE,$.toJSONString(),input.toJSONString());
            logger.info("------开始请求江苏联通能力平台身份校验接口，请求url：{},---未加密前的请求报文：{}",identitySecondUrl,input.toJSONString());
            String encryptRequestStr=encryptUtil.encryptRequest(input.toJSONString(),identitySecondAppSecret,"json");
            logger.info("------加密后的身份校验请求报文：{}",encryptRequestStr);
            JSONObject encryptRequestJson=JSONObject.parseObject(encryptRequestStr);
            //String responseJson = new HttpCall().httpPostWithJSON(URL_L, encryptRequestJson, "UTF-8", "UTF-8");
            String responseJson = new HttpCall().httpPostWithJSON2(identitySecondUrl, encryptRequestJson, "UTF-8", "UTF-8");

            logger.info(">>>>>>>>>调用江苏联通接口身份校验返回加密结果{}", responseJson);
            //JSONObject json = JSONObject.parseObject(responseJson);

            //String resultBody = crypt.decryptString(json.getString("UNI_BSS_BODY"));
            String decryptResponseStr = encryptUtil.decryptResponse(responseJson,identitySecondAppSecret,"json");
            logger.info(">>>>>>>>>调用江苏联通接口身份校验解密结果{}", decryptResponseStr);
            JSONObject json = JSONObject.parseObject(decryptResponseStr).getJSONObject("UNI_BSS_BODY");
            //json = JSONObject.parseObject(resultBody);
            String result = json != null ?  json.getString("RESP_CODE") : null;

            if("0000".equals(result)){
                //成功
                logger.info(">>>>>>>>>调用江苏联通接口身份校验成功");
                return true;
            }else{
                return false;
            }
        } catch (Exception ex) {
            logger.info(">>>>>>>>>>>调用江苏联通接口身份校验失败：exception={}", ex.getMessage());
            return false;
        }


    }


    public JSONObject createRequestHead(String appId) {
        JSONObject UNI_BSS_HEAD = new JSONObject();
        UNI_BSS_HEAD.put("APP_ID",appId);
        Timestamp time = new Timestamp(System.currentTimeMillis());
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp=format.format(time);
        UNI_BSS_HEAD.put("TIMESTAMP",timestamp);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String temp = sf.format(time);
        int random = (int) ((Math.random() + 1) * 100000);
        String transID =temp+random;
        UNI_BSS_HEAD.put("TRANS_ID",transID);
        return UNI_BSS_HEAD;
    }
}
